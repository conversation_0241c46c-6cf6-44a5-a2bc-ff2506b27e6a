msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.6.6\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2017-11-22 15:53+0200\n"
"PO-Revision-Date: 2019-03-25 09:21+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <<EMAIL>>\n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.1\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:67
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:369 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Groepen"

#: acf.php:370
msgid "Field Group"
msgstr "Nieuwe groep"

#: acf.php:371 acf.php:403 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New"
msgstr "Nieuwe groep"

#: acf.php:372
msgid "Add New Field Group"
msgstr "Nieuwe groep toevoegen"

#: acf.php:373
msgid "Edit Field Group"
msgstr "Bewerk groep"

#: acf.php:374
msgid "New Field Group"
msgstr "Nieuwe groep"

#: acf.php:375
msgid "View Field Group"
msgstr "Bekijk groep"

#: acf.php:376
msgid "Search Field Groups"
msgstr "Zoek groepen"

#: acf.php:377
msgid "No Field Groups found"
msgstr "Geen groepen gevonden"

#: acf.php:378
msgid "No Field Groups found in Trash"
msgstr "Geen groepen gevonden in de prullenbak"

#: acf.php:401 includes/admin/admin-field-group.php:182
#: includes/admin/admin-field-group.php:275
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:807
msgid "Fields"
msgstr "Velden"

#: acf.php:402
msgid "Field"
msgstr "Veld"

#: acf.php:404
msgid "Add New Field"
msgstr "Nieuw veld"

#: acf.php:405
msgid "Edit Field"
msgstr "Bewerk veld"

#: acf.php:406 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Nieuw veld"

#: acf.php:407
msgid "View Field"
msgstr "Nieuw veld"

#: acf.php:408
msgid "Search Fields"
msgstr "Zoek velden"

#: acf.php:409
msgid "No Fields found"
msgstr "Geen velden gevonden"

#: acf.php:410
msgid "No Fields found in Trash"
msgstr "Geen velden gevonden in de prullenbak"

#: acf.php:449 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Niet actief"

#: acf.php:454
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inactief <span class=\"count\">(%s)</span>"
msgstr[1] "Inactief <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Groep bijgewerkt."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Groep verwijderd."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Groep gepubliceerd."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Groep opgeslagen."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Groep toegevoegd."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Groep gepland voor."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Groep concept bijgewerkt."

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "Locatie"

#: includes/admin/admin-field-group.php:184
msgid "Settings"
msgstr "Instellingen"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "Naar prullenbak. Weet je het zeker?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr "aangevinkt"

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr "Geen aan/uit velden beschikbaar"

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "Titel is verplicht"

#: includes/admin/admin-field-group.php:273
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "kopie"

#: includes/admin/admin-field-group.php:274
#: includes/admin/views/field-group-field-conditional-logic.php:54
#: includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3964
msgid "or"
msgstr "of"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "Hoofdpagina"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr "Zuster velden"

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "Verplaats extra veld"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Dit veld kan niet worden verplaatst totdat de wijzigingen zijn opgeslagen"

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr "Nul"

#: includes/admin/admin-field-group.php:281 includes/input.php:258
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "De gemaakte wijzigingen gaan verloren als je deze pagina verlaat"

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "De string \"field_\" mag niet voor de veld naam staan"

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr "Veld keys"

#: includes/admin/admin-field-group.php:390
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Actief"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "Verplaatsen geslaagd."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Het veld: %s bevindt zich nu in de groep: %s"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "Venster sluiten"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "Selecteer de bestemming voor dit veld"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "Veld verplaatsen"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Actief <span class=\"count\">(%s)</span>"
msgstr[1] "Actief <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Groep gedupliceerd. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s groep gedupliceerd."
msgstr[1] "%s groepen gedupliceerd."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Groep gesynchroniseerd. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s groep gesynchroniseerd."
msgstr[1] "%s groepen gesynchroniseerd."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Synchronisatie beschikbaar"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:355
msgid "Title"
msgstr "Titel"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:382
msgid "Description"
msgstr "Omschrijving"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Status"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr "Pas WordPress aan met krachtige, professionele en slimme velden."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Wat is er nieuw?"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Bekijk wat nieuw is in <a href=\"%s\">versie %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Documentatie (Engels)"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Website"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Documentatie"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Support"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Bedankt voor het ontwikkelen met <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:668
msgid "Duplicate this item"
msgstr "Dupliceer dit item"

#: includes/admin/admin-field-groups.php:668
#: includes/admin/admin-field-groups.php:684
#: includes/admin/views/field-group-field.php:49
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate"
msgstr "Dupliceer"

#: includes/admin/admin-field-groups.php:701
#: includes/fields/class-acf-field-google-map.php:112
#: includes/fields/class-acf-field-relationship.php:656
msgid "Search"
msgstr "Zoeken"

#: includes/admin/admin-field-groups.php:760
#, php-format
msgid "Select %s"
msgstr "Selecteer %s"

#: includes/admin/admin-field-groups.php:768
msgid "Synchronise field group"
msgstr "Synchroniseer groep"

#: includes/admin/admin-field-groups.php:768
#: includes/admin/admin-field-groups.php:798
msgid "Sync"
msgstr "Synchroniseer"

#: includes/admin/admin-field-groups.php:780
msgid "Apply"
msgstr "Toepassen"

#: includes/admin/admin-field-groups.php:798
msgid "Bulk Actions"
msgstr "Bulk acties"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Extra velden"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Upgrade database"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "Controleer websites & upgrade"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "Fout bij valideren"

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "Geen updates beschikbaar."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Add-ons"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Fout</b>. Kan add-ons lijst niet laden"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informatie"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Wat is er nieuw"

#: includes/admin/settings-tools.php:50
#: includes/admin/views/settings-tools-export.php:19
#: includes/admin/views/settings-tools.php:31
msgid "Tools"
msgstr "Tools"

#: includes/admin/settings-tools.php:147 includes/admin/settings-tools.php:380
msgid "No field groups selected"
msgstr "Geen groepen geselecteerd"

#: includes/admin/settings-tools.php:184
#: includes/fields/class-acf-field-file.php:155
msgid "No file selected"
msgstr "Geen bestanden geselecteerd"

#: includes/admin/settings-tools.php:197
msgid "Error uploading file. Please try again"
msgstr "Fout bij het uploaden van bestand. Probeer het nog eens"

#: includes/admin/settings-tools.php:206
msgid "Incorrect file type"
msgstr "Ongeldig bestandstype"

#: includes/admin/settings-tools.php:223
msgid "Import file empty"
msgstr "Importeer bestand is leeg"

#: includes/admin/settings-tools.php:331
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "1 groep geïmporteerd"
msgstr[1] "%s groepen geïmporteerd"

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "Conditionele logica"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "Toon dit veld als"

#: includes/admin/views/field-group-field-conditional-logic.php:103
#: includes/locations.php:247
msgid "is equal to"
msgstr "gelijk is aan"

#: includes/admin/views/field-group-field-conditional-logic.php:104
#: includes/locations.php:248
msgid "is not equal to"
msgstr "is niet gelijk aan"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "en"

#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Nieuwe groep toevoegen"

#: includes/admin/views/field-group-field.php:41
#: pro/fields/class-acf-field-flexible-content.php:403
#: pro/fields/class-acf-field-repeater.php:296
msgid "Drag to reorder"
msgstr "Sleep om te sorteren"

#: includes/admin/views/field-group-field.php:45
#: includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "Bewerk veld"

#: includes/admin/views/field-group-field.php:48
#: includes/fields/class-acf-field-file.php:137
#: includes/fields/class-acf-field-image.php:122
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:342
msgid "Edit"
msgstr "Bewerk"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "Dupliceer veld"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "Verplaats veld naar een andere groep"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "Verplaats"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "Verwijder veld"

#: includes/admin/views/field-group-field.php:51
#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete"
msgstr "Verwijder"

#: includes/admin/views/field-group-field.php:67
msgid "Field Label"
msgstr "Veld label"

#: includes/admin/views/field-group-field.php:68
msgid "This is the name which will appear on the EDIT page"
msgstr "De naam die verschijnt op het edit screen"

#: includes/admin/views/field-group-field.php:77
msgid "Field Name"
msgstr "Veld naam"

#: includes/admin/views/field-group-field.php:78
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Enkel woord, geen spaties. (Liggende) streepjes toegestaan."

#: includes/admin/views/field-group-field.php:87
msgid "Field Type"
msgstr "Soort veld"

#: includes/admin/views/field-group-field.php:98
#: includes/fields/class-acf-field-tab.php:88
msgid "Instructions"
msgstr "Instructies"

#: includes/admin/views/field-group-field.php:99
msgid "Instructions for authors. Shown when submitting data"
msgstr "Toelichting voor gebruikers. Wordt getoond bij invullen van het veld."

#: includes/admin/views/field-group-field.php:108
msgid "Required?"
msgstr "Verplicht?"

#: includes/admin/views/field-group-field.php:131
msgid "Wrapper Attributes"
msgstr "Veld-attributen"

#: includes/admin/views/field-group-field.php:137
msgid "width"
msgstr "Breedte"

#: includes/admin/views/field-group-field.php:152
msgid "class"
msgstr "class"

#: includes/admin/views/field-group-field.php:165
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:177
msgid "Close Field"
msgstr "Veld sluiten"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Volgorde"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:415
#: includes/fields/class-acf-field-radio.php:306
#: includes/fields/class-acf-field-select.php:432
#: pro/fields/class-acf-field-flexible-content.php:582
msgid "Label"
msgstr "Label"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:964
#: pro/fields/class-acf-field-flexible-content.php:595
msgid "Name"
msgstr "Naam"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Sleutel"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Soort"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Geen velden. Klik op <strong>+ Nieuw veld</strong> button om je eerste veld "
"te maken."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Nieuw veld"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Regels"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Maak regels aan om te bepalen op welk edit screen jouw extra velden "
"verschijnen"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Stijl"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standaard (WordPress metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Naadloos (zonder WordPress metabox)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Positie"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Hoog (onder titel)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normaal (onder tekstverwerker)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Zijkant"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Label positionering"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:102
msgid "Top aligned"
msgstr "Boven velden"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:103
msgid "Left aligned"
msgstr "Links naast velden"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Instructie positionering"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Onder label"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Onder veld"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Volgorde nummer"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Groepen met een lage volgorde worden als eerst getoond"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Toon in groeplijst"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Verberg elementen"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Selecteer</b> elementen om <b>te verbergen</b> op het wijzig scherm."

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Indien meerdere groepen op het bewerk scherm worden getoond, komt de groep "
"met de laagste volgorde als eerste."

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Permalink"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Content editor"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Samenvatting"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Reageren"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Reacties"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Revisies"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Auteur"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Pagina-attributen"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:670
msgid "Featured Image"
msgstr "Uitgelichte afbeelding"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Categorieën"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Tags"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Trackbacks verzenden"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Toon deze groep als"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "Upgrade websites"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr "Advanced Custom Fields database upgrade"

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Er is een database upgrade nodig voor de volgende websites. Controleer "
"degene die je wilt updaten en klik %s."

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Website"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Website vereist een database upgrade van %s naar %s"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "Website is up-to-date"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Database upgrade afgerond. <a href=\"%s\">Terug naar netwerk dashboard</a>"

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Het is aan te raden om eerst een backup van de database te maken voordat je "
"de update uitvoert. Weet je zeker dat je de update nu wilt uitvoeren?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Upgrade afgerond"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Bezig met upgraden naar versie %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Herhalen"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Flexibele content"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galerij"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Opties pagina"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Database upgrade vereist"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Bedankt voor het updaten naar %s v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Voordat je aan de slag kunt met de geweldige nieuwe functies, is een "
"database update vereist."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""
"Zorg ervoor dat elke premium add-ons (%s) eerst zijn bijgewerkt naar de "
"laatste versie."

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Lezen van upgrade taken…"

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Database upgrade afgerond. <a href=\"%s\">Bekijk wat nieuw is</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Download & installeer"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Geïnstalleerd"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Welkom bij Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Bedankt voor het updaten! ACF %s is groter dan ooit tevoren. We hopen dat je "
"tevreden bent."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "Een verbeterde extra veld beleving"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Gebruikersvriendelijker"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Inclusief de populaire Select2 bibliotheek, die zowel "
"gebruikersvriendelijker als sneller werkt bij velden als post object, pagina "
"link, taxonomy en selecteer."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Verbeterd design"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Vele velden hebben een make-over gekregen. Nu oogt ACF beter dan ooit! "
"Merkwaardige verschillen vindt je onder andere terug bij de galerij, relatie "
"en oEmbed velden!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Verbeterde data"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Het herontwerp van de dataverwerking zorgt ervoor dat velden los van hun "
"hoofdvelden kunnen functioneren. Hiermee wordt het mogelijk om velden te "
"drag-and-droppen tussen hoofdvelden."

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Vaarwel Add-ons. Hallo PRO!"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""
"We veranderen de manier waarop premium functies worden geleverd, op een gave "
"manier!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Alle 4 de premium add-ons zijn samengevoegd tot een <a href=\"%s\">PRO "
"versie van ACF</a>. Er zijn zowel persoonlijke als developer licenties "
"verkrijgbaar tegen een aantrekkelijke prijs!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Krachtige functies"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO beschikt over krachtige velden en functies zoals: herhaalbare "
"velden, flexibile content layouts, een interactieve fotogalerij veld en de "
"mogelijkheid om optie pagina's aan te maken!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Lees meer over de <a href=\"%s\">ACF PRO functionaliteiten</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Gemakkelijk upgraden"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"Om upgraden gemakkelijk te maken kun je <a href=\"%s\">inloggen met je "
"bestaande winkelaccount</a> en een gratis versie van ACF PRO claimen!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"We hebben een speciale <a href=\"%s\">upgrade gids</a> gemaakt om al je "
"vraagstukken te beantwoorden. Indien je een uitgebreidere vraag hebt, kun je "
"contact opnemen met de <a href=\"%s\">helpdesk</a> (Engelstalig)."

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Onder de motorkap"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "Slimmere veld instellingen"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "ACF slaat velden als individuele post objecten op"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Meer AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr ""
"Steeds meer velden maken gebruik van AJAX gestuurde zoekopdrachten. Dit "
"maakt het laden een stuk sneller"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "Local JSON"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "Het automatisch exporteren naar JSON maakt alles een stuk sneller"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "Betere versie controles"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr ""
"Nieuw is het automatisch exporteren naar JSON. Dit voorkomt problemen "
"tijdens het upgraden van ACF."

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "XML is vervangen door JSON"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr ""
"Importeren / Exporteren gaat nu via JSON. Indien gewenst kan er XML worden "
"gebruikt"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Nieuwe formulieren"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"Velden kunnen nu worden toegewezen aan reacties, widgets en "
"gebruikersformulieren!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "Een nieuw veld voor het embedden van content is toegevoegd"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Nieuwe galerij"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "Het galerij veld heeft een complete facelift ondergaan"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Nieuwe instellingen"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"Nieuwe groep instellingen zijn toegevoegd om label en instructies toe te "
"voegen"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "Betere front-end formulieren"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form() kan nu posts aanmaken/toevoegen na goedkeuring"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Betere validatie"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr ""
"Formulier validatie gaat nu via PHP + AJAX. Indien gewenst kan dit ook via JS"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Relatie veld"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Nieuwe relatieveld instellingen voor filters (Zoeken, Post Type en Taxonomy)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Velden verplaatsen"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""
"Nieuwe veld groep functionaliteiten laat je velden tussen groepen "
"verplaatsen."

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Pagina link"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Nieuwe archief groep in pagina_link veld"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Verbeterde optie pagina's"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"De opties pagina's kunnen nu worden voorzien van zowel hoofd als sub-pagina's"

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr ""
"Wij denken dat u de wijzigingen en vernieuwingen zult waarderen in versie %s."

#: includes/admin/views/settings-tools-export.php:23
msgid "Export Field Groups to PHP"
msgstr "Exporteer groep(en) naar PHP"

#: includes/admin/views/settings-tools-export.php:27
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"De volgende code kun je integreren in je thema. Door de groep(en) te "
"integreren verhoog je de laadsnelheid. Kopieer en plak deze in code in "
"functions.php, of maak een nieuw PHP bestand aan."

#: includes/admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "Selecteer groepen"

#: includes/admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "Exporteer groepen"

#: includes/admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Selecteer de groepen die je wilt exporteren. Maak vervolgens de keuze om de "
"groepen te downloaden als JSON bestand, of genereer de export code in PHP "
"formaat. De PHP export code kun je integreren in je thema."

#: includes/admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "Download export bestand"

#: includes/admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "Genereer export code"

#: includes/admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "Importeer groepen"

#: includes/admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Selecteer het Advanced Custom Fields JSON bestand die je wilt importeren. "
"Klik op de importeer button om het importeren te starten."

#: includes/admin/views/settings-tools.php:77
#: includes/fields/class-acf-field-file.php:35
msgid "Select File"
msgstr "Selecteer bestand"

#: includes/admin/views/settings-tools.php:86
msgid "Import"
msgstr "Importeer"

#: includes/api/api-helpers.php:856
msgid "Thumbnail"
msgstr "Thumbnail"

#: includes/api/api-helpers.php:857
msgid "Medium"
msgstr "Gemiddeld"

#: includes/api/api-helpers.php:858
msgid "Large"
msgstr "Groot"

#: includes/api/api-helpers.php:907
msgid "Full Size"
msgstr "Volledige grootte"

#: includes/api/api-helpers.php:1248 includes/api/api-helpers.php:1831
#: pro/fields/class-acf-field-clone.php:992
msgid "(no title)"
msgstr "(geen titel)"

#: includes/api/api-helpers.php:1868
#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:268
#: includes/fields/class-acf-field-taxonomy.php:986
msgid "Parent"
msgstr "Hoofd"

#: includes/api/api-helpers.php:3885
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Afbeelding breedte moet tenminste %dpx zijn."

#: includes/api/api-helpers.php:3890
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Afbeelding mag niet breder zijn dan %dpx."

#: includes/api/api-helpers.php:3906
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Afbeelding hoogte moet tenminste %dpx zijn."

#: includes/api/api-helpers.php:3911
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Afbeelding mag niet hoger zijn dan %dpx."

#: includes/api/api-helpers.php:3929
#, php-format
msgid "File size must be at least %s."
msgstr "Bestandsgrootte moet tenminste %s zijn."

#: includes/api/api-helpers.php:3934
#, php-format
msgid "File size must must not exceed %s."
msgstr "Bestand mag niet groter zijn dan %s."

#: includes/api/api-helpers.php:3968
#, php-format
msgid "File type must be %s."
msgstr "Bestandstype moet %s zijn."

#: includes/fields.php:144
msgid "Basic"
msgstr "Basis"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Inhoud"

#: includes/fields.php:146
msgid "Choice"
msgstr "Keuze"

#: includes/fields.php:147
msgid "Relational"
msgstr "Relatie"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149
#: includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:384
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:285
#: pro/fields/class-acf-field-clone.php:839
#: pro/fields/class-acf-field-flexible-content.php:552
#: pro/fields/class-acf-field-flexible-content.php:601
#: pro/fields/class-acf-field-repeater.php:450
msgid "Layout"
msgstr "Layout"

#: includes/fields.php:326
msgid "Field type does not exist"
msgstr "Veld type bestaat niet"

#: includes/fields.php:326
msgid "Unknown"
msgstr "Onbekend"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Button groep"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "Keuzes"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "Per regel een keuze"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Om meer controle te krijgen over de keuzes, kun je de naam en het label van "
"elkaar scheiden. Dit doe je op de volgende manier:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "rood : Rood"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:412
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-taxonomy.php:793
#: includes/fields/class-acf-field-user.php:408
msgid "Allow Null?"
msgstr "Mag leeg zijn?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:276
#: includes/fields/class-acf-field-range.php:148
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:410
msgid "Default Value"
msgstr "Standaard waarde"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:277
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:411
msgid "Appears when creating a new post"
msgstr ""
"Vooraf ingevulde waarde die te zien is tijdens het aanmaken van een nieuwe "
"post"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:391
#: includes/fields/class-acf-field-radio.php:292
msgid "Horizontal"
msgstr "Horizontaal"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:390
#: includes/fields/class-acf-field-radio.php:291
msgid "Vertical"
msgstr "Verticaal"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:408
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-image.php:188
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:299
#: includes/fields/class-acf-field-taxonomy.php:833
msgid "Return Value"
msgstr "Output weergeven als"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:409
#: includes/fields/class-acf-field-file.php:201
#: includes/fields/class-acf-field-image.php:189
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:300
msgid "Specify the returned value on front end"
msgstr "Bepaal hier de output weergave"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-radio.php:305
#: includes/fields/class-acf-field-select.php:431
msgid "Value"
msgstr "Waarde"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:416
#: includes/fields/class-acf-field-radio.php:307
#: includes/fields/class-acf-field-select.php:433
msgid "Both (Array)"
msgstr "Beide (Array)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Checkbox"
msgstr "Checkbox"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Selecteer alle"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Nieuwe keuze"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Eigen invoer toestaan"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "‘Eigen invoer’ waarden toestaan"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Eigen invoer opslaan"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Sla ‘eigen invoer’ waarden op als veld keuzes"

#: includes/fields/class-acf-field-checkbox.php:376
#: includes/fields/class-acf-field-select.php:378
msgid "Enter each default value on a new line"
msgstr "Per regel de naam van een keuze"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Toggle"
msgstr "Switch"

#: includes/fields/class-acf-field-checkbox.php:399
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Voeg een extra checkbox toe aan het begin om alle keuzes te selecteren"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Kleurprikker"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Wissen"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Standaard waarde"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Selecteer kleur"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Huidige kleur"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Datumprikker"

#: includes/fields/class-acf-field-date_picker.php:33
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Gereed"

#: includes/fields/class-acf-field-date_picker.php:34
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Vandaag"

#: includes/fields/class-acf-field-date_picker.php:35
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Volgende"

#: includes/fields/class-acf-field-date_picker.php:36
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Vorige"

#: includes/fields/class-acf-field-date_picker.php:37
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Wk "

#: includes/fields/class-acf-field-date_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:181
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Weergeven als"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "De weergave tijdens het aanmaken/bewerken van een post"

#: includes/fields/class-acf-field-date_picker.php:216
#: includes/fields/class-acf-field-date_picker.php:247
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-date_time_picker.php:208
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Eigen invoer:"

#: includes/fields/class-acf-field-date_picker.php:226
msgid "Save Format"
msgstr "Indeling opslaan"

#: includes/fields/class-acf-field-date_picker.php:227
msgid "The format used when saving a value"
msgstr "Het formaat bij opslaan van waarde"

#: includes/fields/class-acf-field-date_picker.php:237
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-relationship.php:697
#: includes/fields/class-acf-field-select.php:426
#: includes/fields/class-acf-field-time_picker.php:124
msgid "Return Format"
msgstr "Output weergeven als"

#: includes/fields/class-acf-field-date_picker.php:238
#: includes/fields/class-acf-field-date_time_picker.php:199
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "De weergave in het thema"

#: includes/fields/class-acf-field-date_picker.php:256
#: includes/fields/class-acf-field-date_time_picker.php:215
msgid "Week Starts On"
msgstr "Week start op"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Datum tijd picker"

#: includes/fields/class-acf-field-date_time_picker.php:33
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Kies tijd"

#: includes/fields/class-acf-field-date_time_picker.php:34
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Tijd"

#: includes/fields/class-acf-field-date_time_picker.php:35
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Uur"

#: includes/fields/class-acf-field-date_time_picker.php:36
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuut"

#: includes/fields/class-acf-field-date_time_picker.php:37
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Seconde"

#: includes/fields/class-acf-field-date_time_picker.php:38
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milliseconde"

#: includes/fields/class-acf-field-date_time_picker.php:39
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microseconde"

#: includes/fields/class-acf-field-date_time_picker.php:40
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Tijdzone"

#: includes/fields/class-acf-field-date_time_picker.php:41
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Nu"

#: includes/fields/class-acf-field-date_time_picker.php:42
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Gereed"

#: includes/fields/class-acf-field-date_time_picker.php:43
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Selecteer"

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "E-mail"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Plaatsvervangende tekst"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Informatie die verschijnt in het veld (verdwijnt zodra je typt)"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:187
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Voorvoegsel"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Informatie die verschijnt voor het veld"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:196
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Navoegsel"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Informatie die verschijnt na het veld"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Bestand"

#: includes/fields/class-acf-field-file.php:36
msgid "Edit File"
msgstr "Bewerk bestand"

#: includes/fields/class-acf-field-file.php:37
msgid "Update File"
msgstr "Update bestand"

#: includes/fields/class-acf-field-file.php:38
#: includes/fields/class-acf-field-image.php:43 includes/media.php:57
#: pro/fields/class-acf-field-gallery.php:44
msgid "Uploaded to this post"
msgstr "Geüpload naar deze post"

#: includes/fields/class-acf-field-file.php:126
msgid "File name"
msgstr "Bestandsnaam"

#: includes/fields/class-acf-field-file.php:130
#: includes/fields/class-acf-field-file.php:233
#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: pro/fields/class-acf-field-gallery.php:690
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Bestandsgrootte"

#: includes/fields/class-acf-field-file.php:139
#: includes/fields/class-acf-field-image.php:124
#: includes/fields/class-acf-field-link.php:140 includes/input.php:269
#: pro/fields/class-acf-field-gallery.php:343
#: pro/fields/class-acf-field-gallery.php:531
msgid "Remove"
msgstr "Verwijder"

#: includes/fields/class-acf-field-file.php:155
msgid "Add File"
msgstr "Voeg bestand toe"

#: includes/fields/class-acf-field-file.php:206
msgid "File Array"
msgstr "Bestand Array"

#: includes/fields/class-acf-field-file.php:207
msgid "File URL"
msgstr "Bestands-URL"

#: includes/fields/class-acf-field-file.php:208
msgid "File ID"
msgstr "Bestands-ID"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:213
#: pro/fields/class-acf-field-gallery.php:655
msgid "Library"
msgstr "Bibliotheek"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:214
#: pro/fields/class-acf-field-gallery.php:656
msgid "Limit the media library choice"
msgstr ""
"Limiteer de keuze van bestanden. Kies voor de gehele media bibliotheek, of "
"alleen de bestanden die geüpload zijn naar de post."

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:219
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:661
msgid "All"
msgstr "Alles"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:220
#: pro/fields/class-acf-field-gallery.php:662
msgid "Uploaded to post"
msgstr "Geüpload naar post"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-image.php:227
#: pro/fields/class-acf-field-gallery.php:669
msgid "Minimum"
msgstr "Minimaal"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-file.php:241
msgid "Restrict which files can be uploaded"
msgstr "Bepaal welke bestanden geüpload mogen worden"

#: includes/fields/class-acf-field-file.php:240
#: includes/fields/class-acf-field-image.php:256
#: pro/fields/class-acf-field-gallery.php:698
msgid "Maximum"
msgstr "Maximaal"

#: includes/fields/class-acf-field-file.php:251
#: includes/fields/class-acf-field-image.php:285
#: pro/fields/class-acf-field-gallery.php:727
msgid "Allowed file types"
msgstr "Toegestane bestandstypen"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:286
#: pro/fields/class-acf-field-gallery.php:728
msgid "Comma separated list. Leave blank for all types"
msgstr "Met komma's gescheiden lijst. Laat leeg voor alle types."

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-google-map.php:40
msgid "Locating"
msgstr "Locatie wordt gezocht..."

#: includes/fields/class-acf-field-google-map.php:41
msgid "Sorry, this browser does not support geolocation"
msgstr "Excuses, deze browser ondersteund geen geolocatie"

#: includes/fields/class-acf-field-google-map.php:113
msgid "Clear location"
msgstr "Wis locatie"

#: includes/fields/class-acf-field-google-map.php:114
msgid "Find current location"
msgstr "Zoek huidige locatie"

#: includes/fields/class-acf-field-google-map.php:117
msgid "Search for address..."
msgstr "Zoek een adres..."

#: includes/fields/class-acf-field-google-map.php:147
#: includes/fields/class-acf-field-google-map.php:158
msgid "Center"
msgstr "Standaard locatie"

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-google-map.php:159
msgid "Center the initial map"
msgstr "Bepaal de standaard locatie van de kaart"

#: includes/fields/class-acf-field-google-map.php:170
msgid "Zoom"
msgstr "Inzoomen"

#: includes/fields/class-acf-field-google-map.php:171
msgid "Set the initial zoom level"
msgstr "Bepaal het zoom niveau van de kaart"

#: includes/fields/class-acf-field-google-map.php:180
#: includes/fields/class-acf-field-image.php:239
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-oembed.php:281
#: pro/fields/class-acf-field-gallery.php:681
#: pro/fields/class-acf-field-gallery.php:710
msgid "Height"
msgstr "Hoogte"

#: includes/fields/class-acf-field-google-map.php:181
msgid "Customise the map height"
msgstr "Wijzig de hoogte van de kaart"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Groep"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:389
msgid "Sub Fields"
msgstr "Sub-velden"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:840
msgid "Specify the style used to render the selected fields"
msgstr "Kies de gebruikte stijl bij het renderen van de geselecteerde velden"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:845
#: pro/fields/class-acf-field-flexible-content.php:612
#: pro/fields/class-acf-field-repeater.php:458
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:846
#: pro/fields/class-acf-field-flexible-content.php:611
#: pro/fields/class-acf-field-repeater.php:457
msgid "Table"
msgstr "Tabel"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:847
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:459
msgid "Row"
msgstr "Rij"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Afbeelding"

#: includes/fields/class-acf-field-image.php:40
msgid "Select Image"
msgstr "Selecteer afbeelding"

#: includes/fields/class-acf-field-image.php:41
#: pro/fields/class-acf-field-gallery.php:42
msgid "Edit Image"
msgstr "Bewerk afbeelding"

#: includes/fields/class-acf-field-image.php:42
#: pro/fields/class-acf-field-gallery.php:43
msgid "Update Image"
msgstr "Update afbeelding"

#: includes/fields/class-acf-field-image.php:44
msgid "All images"
msgstr "Alle afbeeldingen"

#: includes/fields/class-acf-field-image.php:140
msgid "No image selected"
msgstr "Geen afbeelding geselecteerd"

#: includes/fields/class-acf-field-image.php:140
msgid "Add Image"
msgstr "Voeg afbeelding toe"

#: includes/fields/class-acf-field-image.php:194
msgid "Image Array"
msgstr "Afbeelding Array"

#: includes/fields/class-acf-field-image.php:195
msgid "Image URL"
msgstr "Afbeelding URL"

#: includes/fields/class-acf-field-image.php:196
msgid "Image ID"
msgstr "Afbeelding ID"

#: includes/fields/class-acf-field-image.php:203
msgid "Preview Size"
msgstr "Afmeting voorbeeld"

#: includes/fields/class-acf-field-image.php:204
msgid "Shown when entering data"
msgstr "Voorbeeld wordt na het uploaden/selecteren getoond"

#: includes/fields/class-acf-field-image.php:228
#: includes/fields/class-acf-field-image.php:257
#: pro/fields/class-acf-field-gallery.php:670
#: pro/fields/class-acf-field-gallery.php:699
msgid "Restrict which images can be uploaded"
msgstr "Bepaal welke afbeeldingen geüpload mogen worden"

#: includes/fields/class-acf-field-image.php:231
#: includes/fields/class-acf-field-image.php:260
#: includes/fields/class-acf-field-oembed.php:270
#: pro/fields/class-acf-field-gallery.php:673
#: pro/fields/class-acf-field-gallery.php:702
msgid "Width"
msgstr "Breedte"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Selecteer link"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Opent in een nieuw venster/tab"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Link array"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "Link URL"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Bericht"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Nieuwe regels"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Bepaal wat er gebeurt met een nieuwe tekstregel"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Automatisch paragrafen toevoegen"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Automatisch een nieuwe regel maken &lt;br /&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Niets ondernemen"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Toestaan HTML markup te tonen als tekst in plaats van het te renderen"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Nummer"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:157
msgid "Minimum Value"
msgstr "Minimale waarde"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:167
msgid "Maximum Value"
msgstr "Maximale waarde"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:177
msgid "Step Size"
msgstr "Stapgrootte"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Waarde moet numeriek zijn"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Waarde moet gelijk of meer dan zijn %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Waarde moet gelijk of minder zijn dan %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "Vul URL in"

#: includes/fields/class-acf-field-oembed.php:234
#: includes/fields/class-acf-field-taxonomy.php:898
msgid "Error."
msgstr "Fout."

#: includes/fields/class-acf-field-oembed.php:234
msgid "No embed found for the given URL."
msgstr "Geen embed mogelijkheid gevonden voor de gewenste URL."

#: includes/fields/class-acf-field-oembed.php:267
#: includes/fields/class-acf-field-oembed.php:278
msgid "Embed Size"
msgstr "Embed formaat"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Archieven"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:384
#: includes/fields/class-acf-field-relationship.php:623
msgid "Filter by Post Type"
msgstr "Filter op post type"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:392
#: includes/fields/class-acf-field-relationship.php:631
msgid "All post types"
msgstr "Alle post types"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:398
#: includes/fields/class-acf-field-relationship.php:637
msgid "Filter by Taxonomy"
msgstr "Filter op taxonomy"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:406
#: includes/fields/class-acf-field-relationship.php:645
msgid "All taxonomies"
msgstr "Alle taxonomieën"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Archief URL’s toestaan"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:422
#: includes/fields/class-acf-field-select.php:396
#: includes/fields/class-acf-field-user.php:418
msgid "Select multiple values?"
msgstr "Meerdere selecties mogelijk?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Wachtwoord"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:702
msgid "Post Object"
msgstr "Post object"

#: includes/fields/class-acf-field-post_object.php:438
#: includes/fields/class-acf-field-relationship.php:703
msgid "Post ID"
msgstr "Post ID"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Radio button"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Anders namelijk"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Voeg de keuze \"anders” toe voor eigen invulling"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Anders namelijk waarde toevoegen aan keuzes?"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr ""
"Voeg de ingevulde \"anders namelijk\" waarde toe aan de keuzelijst na het "
"opslaan van een post"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Reeks"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relatie"

#: includes/fields/class-acf-field-relationship.php:37
msgid "Minimum values reached ( {min} values )"
msgstr "Minimaal aantal bereikt ( {min} stuks )"

#: includes/fields/class-acf-field-relationship.php:38
msgid "Maximum values reached ( {max} values )"
msgstr "Maximum aantal waarden bereikt ( {max} waarden )"

#: includes/fields/class-acf-field-relationship.php:39
msgid "Loading"
msgstr "Laden"

#: includes/fields/class-acf-field-relationship.php:40
msgid "No matches found"
msgstr "Geen gelijkenis gevonden"

#: includes/fields/class-acf-field-relationship.php:423
msgid "Select post type"
msgstr "Selecteer post type"

#: includes/fields/class-acf-field-relationship.php:449
msgid "Select taxonomy"
msgstr "Selecteer taxonomy"

#: includes/fields/class-acf-field-relationship.php:539
msgid "Search..."
msgstr "Zoeken..."

#: includes/fields/class-acf-field-relationship.php:651
msgid "Filters"
msgstr "Filters"

#: includes/fields/class-acf-field-relationship.php:657
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Post type"

#: includes/fields/class-acf-field-relationship.php:658
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:763
msgid "Taxonomy"
msgstr "Taxonomy"

#: includes/fields/class-acf-field-relationship.php:665
msgid "Elements"
msgstr "Elementen"

#: includes/fields/class-acf-field-relationship.php:666
msgid "Selected elements will be displayed in each result"
msgstr "Selecteer de elementen die moeten worden getoond in elk resultaat"

#: includes/fields/class-acf-field-relationship.php:677
msgid "Minimum posts"
msgstr "Minimale berichten"

#: includes/fields/class-acf-field-relationship.php:686
msgid "Maximum posts"
msgstr "Maximum aantal selecties"

#: includes/fields/class-acf-field-relationship.php:790
#: pro/fields/class-acf-field-gallery.php:800
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s verplicht tenminste %s selectie"
msgstr[1] "%s verplicht tenminste %s selecties"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:785
msgctxt "noun"
msgid "Select"
msgstr "Selecteer"

#: includes/fields/class-acf-field-select.php:38
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Eén resultaat beschikbaar, toets enter om te selecteren."

#: includes/fields/class-acf-field-select.php:39
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultaten beschikbaar, gebruik de pijltjes toetsen om te navigeren."

#: includes/fields/class-acf-field-select.php:40
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Geen resultaten"

#: includes/fields/class-acf-field-select.php:41
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Vul 1 of meer tekens in"

#: includes/fields/class-acf-field-select.php:42
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Vul %d of meer tekens in"

#: includes/fields/class-acf-field-select.php:43
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Verwijderd 1 teken"

#: includes/fields/class-acf-field-select.php:44
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Verwijder %d tekens"

#: includes/fields/class-acf-field-select.php:45
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Je kunt maar 1 item selecteren"

#: includes/fields/class-acf-field-select.php:46
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Je kunt maar %d items selecteren"

#: includes/fields/class-acf-field-select.php:47
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Laad meer resultaten&hellip;"

#: includes/fields/class-acf-field-select.php:48
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Zoeken&hellip;"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Laden mislukt"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:54
msgctxt "verb"
msgid "Select"
msgstr "Selecteer"

#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Uitgebreide weergave"

#: includes/fields/class-acf-field-select.php:416
msgid "Use AJAX to lazy load choices?"
msgstr "AJAX gebruiken om keuzes te laden?"

#: includes/fields/class-acf-field-select.php:427
msgid "Specify the value returned"
msgstr "Bepaal hier de output weergave"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Scheidingsteken"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Tab"

#: includes/fields/class-acf-field-tab.php:82
msgid ""
"The tab field will display incorrectly when added to a Table style repeater "
"field or flexible content field layout"
msgstr "Deze tab zal niet correct worden weergegeven in een herhalende tabel"

#: includes/fields/class-acf-field-tab.php:83
msgid ""
"Use \"Tab Fields\" to better organize your edit screen by grouping fields "
"together."
msgstr "Gebruik tabbladen om velden in het edit screen te organiseren."

#: includes/fields/class-acf-field-tab.php:84
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is "
"defined) will be grouped together using this field's label as the tab "
"heading."
msgstr ""
"Alle velden onder dit \"Tab veld\" zullen worden toegevoegd aan deze tab. "
"Het ingevulde \"Veld label\" dient als benaming van de tab."

#: includes/fields/class-acf-field-tab.php:98
msgid "Placement"
msgstr "Plaatsing"

#: includes/fields/class-acf-field-tab.php:110
msgid "End-point"
msgstr "Eindpunt"

#: includes/fields/class-acf-field-tab.php:111
msgid "Use this field as an end-point and start a new group of tabs"
msgstr "Gebruik dit veld als eindpunt en startpunt van een groep tabbladen"

#: includes/fields/class-acf-field-taxonomy.php:713
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Geen %s"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "None"
msgstr "Geen"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Select the taxonomy to be displayed"
msgstr "Selecteer de weer te geven taxonomie "

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Appearance"
msgstr "Uiterlijk"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Select the appearance of this field"
msgstr "Selecteer het uiterlijk van dit veld"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Multiple Values"
msgstr "Meerdere waardes"

#: includes/fields/class-acf-field-taxonomy.php:781
msgid "Multi Select"
msgstr "Multi-selecteer"

#: includes/fields/class-acf-field-taxonomy.php:783
msgid "Single Value"
msgstr "Enkele waarde"

#: includes/fields/class-acf-field-taxonomy.php:784
msgid "Radio Buttons"
msgstr "Radio buttons"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "Create Terms"
msgstr "Voorwaarden toevoegen"

#: includes/fields/class-acf-field-taxonomy.php:804
msgid "Allow new terms to be created whilst editing"
msgstr "Toestaan dat nieuwe voorwaarden worden aangemaakt terwijl je bewerkt"

#: includes/fields/class-acf-field-taxonomy.php:813
msgid "Save Terms"
msgstr "Voorwaarden opslaan"

#: includes/fields/class-acf-field-taxonomy.php:814
msgid "Connect selected terms to the post"
msgstr "Koppel geselecteerde terms aan een post"

#: includes/fields/class-acf-field-taxonomy.php:823
msgid "Load Terms"
msgstr "Voorwaarden laden"

#: includes/fields/class-acf-field-taxonomy.php:824
msgid "Load value from posts terms"
msgstr "Waarde ophalen van posts terms"

#: includes/fields/class-acf-field-taxonomy.php:838
msgid "Term Object"
msgstr "Term object"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Term ID"
msgstr "Term ID"

#: includes/fields/class-acf-field-taxonomy.php:898
#, php-format
msgid "User unable to add new %s"
msgstr "Gebruiker is niet in staat om nieuwe %s toe te voegen"

#: includes/fields/class-acf-field-taxonomy.php:911
#, php-format
msgid "%s already exists"
msgstr "%s bestaat al"

#: includes/fields/class-acf-field-taxonomy.php:952
#, php-format
msgid "%s added"
msgstr "%s toegevoegd"

#: includes/fields/class-acf-field-taxonomy.php:997
msgid "Add"
msgstr "Nieuwe"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Karakter limiet"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Laat leeg voor geen limiet"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Tekstvlak"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Rijen"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Hoogte (in regels) voor dit tekstvlak"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Tijd picker"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Waar / niet waar"

#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159 includes/input.php:267
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Ja"

#: includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:169 includes/input.php:268
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Nee"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Geeft tekst weer naast de checkbox"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "On tekst"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Tekst die verschijnt bij actief"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Off Text"
msgstr "Off tekst"

#: includes/fields/class-acf-field-true_false.php:166
msgid "Text shown when inactive"
msgstr "Tekst die verschijnt bij inactief"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "URL"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Waarde moet een geldige URL zijn"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Gebruiker"

#: includes/fields/class-acf-field-user.php:393
msgid "Filter by role"
msgstr "Filter op rol"

#: includes/fields/class-acf-field-user.php:401
msgid "All user roles"
msgstr "Alle rollen"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Wysiwyg editor"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual"
msgstr "Visueel"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Click to initialize TinyMCE"
msgstr "Klik om TinyMCE te initialiseren"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Tabs"
msgstr "Tabbladen"

#: includes/fields/class-acf-field-wysiwyg.php:424
msgid "Visual & Text"
msgstr "Visueel & tekst"

#: includes/fields/class-acf-field-wysiwyg.php:425
msgid "Visual Only"
msgstr "Alleen visueel"

#: includes/fields/class-acf-field-wysiwyg.php:426
msgid "Text Only"
msgstr "Alleen tekst"

#: includes/fields/class-acf-field-wysiwyg.php:433
msgid "Toolbar"
msgstr "Toolbar"

#: includes/fields/class-acf-field-wysiwyg.php:443
msgid "Show Media Upload Buttons?"
msgstr "Toon media upload buttons?"

#: includes/fields/class-acf-field-wysiwyg.php:453
msgid "Delay initialization?"
msgstr "Vertraag initialisatie?"

#: includes/fields/class-acf-field-wysiwyg.php:454
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE wordt niet geïnitialiseerd tot veld is aangeklikt"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr "Bewerk groep"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Valideer e-mail"

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:573 pro/options-page.php:81
msgid "Update"
msgstr "Bijwerken"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Bericht bijgewerkt"

#: includes/forms/form-front.php:229
msgid "Spam Detected"
msgstr "Spam gedetecteerd"

#: includes/input.php:259
msgid "Expand Details"
msgstr "Toon details"

#: includes/input.php:260
msgid "Collapse Details"
msgstr "Verberg details"

#: includes/input.php:261
msgid "Validation successful"
msgstr "Validatie geslaagd"

#: includes/input.php:262 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Validatie mislukt"

#: includes/input.php:263
msgid "1 field requires attention"
msgstr "1 veld heeft aandacht nodig"

#: includes/input.php:264
#, php-format
msgid "%d fields require attention"
msgstr "%d velden hebben aandacht nodig"

#: includes/input.php:265
msgid "Restricted"
msgstr "Verplicht"

#: includes/input.php:266
msgid "Are you sure?"
msgstr "Ben je zeker?"

#: includes/input.php:270
msgid "Cancel"
msgstr "Annuleer"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Bericht"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Pagina"

#: includes/locations.php:96
msgid "Forms"
msgstr "Formulieren"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Bijlage"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Alle %s formaten"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Reactie"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Huidige gebruikersrol"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Super beheerder"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Huidige gebruiker"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Ingelogd"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Bekijk voorkant"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Bekijk achterkant"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Menu item"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Menu locaties"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Menu’s "

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Pagina hoofd"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Pagina template"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Standaard template"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Pagina type"

#: includes/locations/class-acf-location-page-type.php:145
msgid "Front Page"
msgstr "Hoofdpagina"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Posts Page"
msgstr "Berichten pagina"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Top Level Page (no parent)"
msgstr "Hoofdpagina (geen hoofd)"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Parent Page (has children)"
msgstr "Hoofdpagina (bevat subitems)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Child Page (has parent)"
msgstr "Subpagina"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Bericht categorie"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Bericht format"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Status"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Bericht taxonomy"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Bericht template"

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "Taxonomy term"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Gebruiker formulier"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Toevoegen / Bewerken"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Registreer"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Rol"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widget"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr "Bewerk"

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr "Bijwerken"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s waarde is verplicht"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Publiceer"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Er zijn geen groepen gevonden voor deze optie pagina. <a href=\"%s\">Maak "
"een extra velden groep</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Fout</b>. Kan niet verbinden met de update server"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Updates"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Licentiecode deactiveren"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Activeer licentiecode"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Licentie informatie"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Om updates te ontvangen vul je hieronder je licentiecode in. Nog geen "
"licentiecode? Bekijk <a href=\"%s\" target=\"_blank\">details & prijzen</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Licentiecode"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Update informatie"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Huidige versie"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Nieuwste versie"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Update beschikbaar"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Update plugin"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Vul uw licentiecode hierboven in om updates te ontvangen"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Controleer op updates"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Upgrade opmerking"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Kloon"

#: pro/fields/class-acf-field-clone.php:808
msgid "Select one or more fields you wish to clone"
msgstr "Selecteer een of meer velden om te klonen"

#: pro/fields/class-acf-field-clone.php:825
msgid "Display"
msgstr "Toon"

#: pro/fields/class-acf-field-clone.php:826
msgid "Specify the style used to render the clone field"
msgstr "Kies de gebruikte stijl bij het renderen van het gekloonde veld"

#: pro/fields/class-acf-field-clone.php:831
msgid "Group (displays selected fields in a group within this field)"
msgstr "Groep (toont geselecteerde velden in een groep binnen dit veld)"

#: pro/fields/class-acf-field-clone.php:832
msgid "Seamless (replaces this field with selected fields)"
msgstr "Naadloos (vervangt dit veld met de geselecteerde velden)"

#: pro/fields/class-acf-field-clone.php:853
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Labels worden getoond als %s"

#: pro/fields/class-acf-field-clone.php:856
msgid "Prefix Field Labels"
msgstr "Prefix veld labels"

#: pro/fields/class-acf-field-clone.php:867
#, php-format
msgid "Values will be saved as %s"
msgstr "Waarden worden opgeslagen als %s"

#: pro/fields/class-acf-field-clone.php:870
msgid "Prefix Field Names"
msgstr "Prefix veld namen"

#: pro/fields/class-acf-field-clone.php:988
msgid "Unknown field"
msgstr "Onbekend veld"

#: pro/fields/class-acf-field-clone.php:1027
msgid "Unknown field group"
msgstr "Onbekend groep"

#: pro/fields/class-acf-field-clone.php:1031
#, php-format
msgid "All fields from %s field group"
msgstr "Alle velden van %s veld groep"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:174
#: pro/fields/class-acf-field-repeater.php:470
msgid "Add Row"
msgstr "Nieuwe regel"

#: pro/fields/class-acf-field-flexible-content.php:34
msgid "layout"
msgstr "layout"

#: pro/fields/class-acf-field-flexible-content.php:35
msgid "layouts"
msgstr "layouts"

#: pro/fields/class-acf-field-flexible-content.php:36
msgid "remove {layout}?"
msgstr "verwijder {layout}?"

#: pro/fields/class-acf-field-flexible-content.php:37
msgid "This field requires at least {min} {identifier}"
msgstr "Dit veld vereist op zijn minst {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:38
msgid "This field has a limit of {max} {identifier}"
msgstr "Dit veld heeft een limiet van {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:39
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Dit veld vereist op zijn minst {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:40
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "Maximum {label} limiet bereikt ({max} {identifier})"

#: pro/fields/class-acf-field-flexible-content.php:41
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} beschikbaar (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:42
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} verplicht (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:43
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexibele content vereist minimaal 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:273
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Klik op de \"%s\" button om een nieuwe lay-out te maken"

#: pro/fields/class-acf-field-flexible-content.php:406
msgid "Add layout"
msgstr "Layout toevoegen"

#: pro/fields/class-acf-field-flexible-content.php:407
msgid "Remove layout"
msgstr "Verwijder layout"

#: pro/fields/class-acf-field-flexible-content.php:408
#: pro/fields/class-acf-field-repeater.php:298
msgid "Click to toggle"
msgstr "Klik om in/uit te klappen"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder Layout"
msgstr "Herorder layout"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder"
msgstr "Herorder"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete Layout"
msgstr "Verwijder layout"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate Layout"
msgstr "Dupliceer layout"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New Layout"
msgstr "Nieuwe layout"

#: pro/fields/class-acf-field-flexible-content.php:628
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:641
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:668
#: pro/fields/class-acf-field-repeater.php:466
msgid "Button Label"
msgstr "Button label"

#: pro/fields/class-acf-field-flexible-content.php:677
msgid "Minimum Layouts"
msgstr "Minimale layouts"

#: pro/fields/class-acf-field-flexible-content.php:686
msgid "Maximum Layouts"
msgstr "Maximale layouts"

#: pro/fields/class-acf-field-gallery.php:41
msgid "Add Image to Gallery"
msgstr "Voeg afbeelding toe aan galerij"

#: pro/fields/class-acf-field-gallery.php:45
msgid "Maximum selection reached"
msgstr "Maximale selectie bereikt"

#: pro/fields/class-acf-field-gallery.php:321
msgid "Length"
msgstr "Lengte"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Onderschrift"

#: pro/fields/class-acf-field-gallery.php:373
msgid "Alt Text"
msgstr "Alt tekst"

#: pro/fields/class-acf-field-gallery.php:544
msgid "Add to gallery"
msgstr "Afbeelding(en) toevoegen"

#: pro/fields/class-acf-field-gallery.php:548
msgid "Bulk actions"
msgstr "Acties"

#: pro/fields/class-acf-field-gallery.php:549
msgid "Sort by date uploaded"
msgstr "Sorteer op datum geüpload"

#: pro/fields/class-acf-field-gallery.php:550
msgid "Sort by date modified"
msgstr "Sorteer op datum aangepast"

#: pro/fields/class-acf-field-gallery.php:551
msgid "Sort by title"
msgstr "Sorteer op titel"

#: pro/fields/class-acf-field-gallery.php:552
msgid "Reverse current order"
msgstr "Keer volgorde om"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Close"
msgstr "Sluiten"

#: pro/fields/class-acf-field-gallery.php:624
msgid "Minimum Selection"
msgstr "Minimale selectie"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Maximum Selection"
msgstr "Maximale selectie"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Insert"
msgstr "Invoegen"

#: pro/fields/class-acf-field-gallery.php:643
msgid "Specify where new attachments are added"
msgstr "Geef aan waar nieuwe bijlagen worden toegevoegd"

#: pro/fields/class-acf-field-gallery.php:647
msgid "Append to the end"
msgstr "Toevoegen aan het einde"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Prepend to the beginning"
msgstr "Toevoegen aan het begin"

#: pro/fields/class-acf-field-repeater.php:36
msgid "Minimum rows reached ({min} rows)"
msgstr "Minimum aantal rijen bereikt ({max} rijen)"

#: pro/fields/class-acf-field-repeater.php:37
msgid "Maximum rows reached ({max} rows)"
msgstr "Maximum aantal rijen bereikt ({max} rijen)"

#: pro/fields/class-acf-field-repeater.php:343
msgid "Add row"
msgstr "Nieuwe regel"

#: pro/fields/class-acf-field-repeater.php:344
msgid "Remove row"
msgstr "Verwijder regel"

#: pro/fields/class-acf-field-repeater.php:419
msgid "Collapsed"
msgstr "Ingeklapt"

#: pro/fields/class-acf-field-repeater.php:420
msgid "Select a sub field to show when row is collapsed"
msgstr "Selecteer een sub-veld om te tonen wanneer rij dichtgeklapt is"

#: pro/fields/class-acf-field-repeater.php:430
msgid "Minimum Rows"
msgstr "Minimum aantal rijen"

#: pro/fields/class-acf-field-repeater.php:440
msgid "Maximum Rows"
msgstr "Maximum aantal rijen"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Er zijn nog geen optie pagina's"

#: pro/options-page.php:51
msgid "Options"
msgstr "Opties"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Opties bijgewerkt"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Om updates te ontvangen vul je op <a href=\"%s\">Updates</a> pagina je "
"licentiecode in. Nog geen licentiecode? Bekijk <a href=\"%s\" target=\"_blank"
"\">details & prijzen</a>."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Disabled"
#~ msgstr "Inactief"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Inactief <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Inactief <span class=\"count\">(%s)</span>"

#~ msgid "Getting Started"
#~ msgstr "Aan de slag"

#~ msgid "Field Types"
#~ msgstr "Veld soorten"

#~ msgid "Functions"
#~ msgstr "Functies"

#~ msgid "Actions"
#~ msgstr "Acties"

#~ msgid "'How to' guides"
#~ msgstr "Veelgestelde vragen"

#~ msgid "Tutorials"
#~ msgstr "Tutorials"

#~ msgid "FAQ"
#~ msgstr "FAQ"

#~ msgid "Created by"
#~ msgstr "Ontwikkeld door"

#~ msgid "Error loading update"
#~ msgstr "Fout bij laden van update"

#~ msgid "Error"
#~ msgstr "Fout"

#~ msgid "See what's new"
#~ msgstr "Bekijk alle vernieuwingen en verbeteringen van"

#~ msgid "eg. Show extra content"
#~ msgstr "bijv. Toon op homepage"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "1 veld vraagt om aandacht"
#~ msgstr[1] "%d velden vragen om aandacht"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Verbindingsfout</b>. Onze excuses, probeer het later nog eens"

#~ msgid "See what's new in"
#~ msgstr "Bekijk alle vernieuwingen en verbeteringen van"

#~ msgid "version"
#~ msgstr "versie"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr ""
#~ "<b>Gelukt!</b>. De importeer tool heeft %s velden en %s groepen "
#~ "geïmporteerd"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Waarschuwing</b>. De importeer functie heeft %s bestaande veldgroepen "
#~ "gedetecteerd en heeft deze genegeerd: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Upgrade ACF"

#~ msgid "Upgrade"
#~ msgstr "Upgrade"

#~ msgid ""
#~ "The following sites require a DB upgrade. Check the ones you want to "
#~ "update and then click “Upgrade Database”."
#~ msgstr ""
#~ "De volgende website vereist een DB upgrade. Selecteer degene die u wilt "
#~ "updaten en klik op “Upgrade database”."

#~ msgid "Upgrading data to"
#~ msgstr "Upgraden van data naar "

#~ msgid "Done"
#~ msgstr "Gereed"

#~ msgid "Today"
#~ msgstr "Vandaag"

#~ msgid "Show a different month"
#~ msgstr "Toon een andere maand"

#~ msgid "Return format"
#~ msgstr "Output weergeven als"

#~ msgid "uploaded to this post"
#~ msgstr "geüpload naar deze post"

#~ msgid "File Name"
#~ msgstr "Bestandsnaam"

#~ msgid "File Size"
#~ msgstr "Bestandsformaat"

#~ msgid "No File selected"
#~ msgstr "Geen bestand geselecteerd"

#~ msgid "Save Options"
#~ msgstr "Opties bijwerken"

#~ msgid "License"
#~ msgstr "Licentie"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Voor het verkrijgen van updates is een licentiesleutel vereist. Indien je "
#~ "niet beschikt over een licentiecode kun je deze aanschaffen, zie:"

#~ msgid "details & pricing"
#~ msgstr "details & kosten"

#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "Voor het verkrijgen van updates is een licentiesleutel vereist. Vul uw "
#~ "licentiecode in op de <a href=\"%s\">Updates</a> pagina, of schaf een "
#~ "licentiecode aan via <a href=\"%s\">details & prijzen</a>."

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "http://www.advancedcustomfields.com/"
#~ msgstr "http://www.advancedcustomfields.com/"

#~ msgid "elliot condon"
#~ msgstr "elliot condon"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Sleep om te sorteren"

#~ msgid "Add new %s "
#~ msgstr "Nieuwe %s "

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Tekst wordt automatisch voorzien van paragrafen door de wp functie: "

#~ msgid "Warning"
#~ msgstr "Waarschuwing"

#~ msgid "Hide / Show All"
#~ msgstr "Verberg / Toon alle"

#~ msgid "Show Field Keys"
#~ msgstr "Toon veld sleutels"

#~ msgid "Pending Review"
#~ msgstr "Wachtend op goedkeuring"

#~ msgid "Draft"
#~ msgstr "Concept"

#~ msgid "Future"
#~ msgstr "Toekomst"

#~ msgid "Private"
#~ msgstr "Privé"

#~ msgid "Revision"
#~ msgstr "Revisie"

#~ msgid "Trash"
#~ msgstr "Afval"

#~ msgid "Top Level Page (parent of 0)"
#~ msgstr "Hoofdpagina (ouder dan 0)"

#~ msgid "Import / Export"
#~ msgstr "Importeer / Exporteer"

#~ msgid "Logged in User Type"
#~ msgstr "Gebruikersrol"

#~ msgid "Field groups are created in order <br />from lowest to highest"
#~ msgstr "Groepen worden gesorteerd van laag naar hoog."

#~ msgid "<b>Select</b> items to <b>hide</b> them from the edit screen"
#~ msgstr ""
#~ "<b>Selecteer</b> elementen die <b>verborgen</b> worden op het edit screen"

#~ msgid ""
#~ "If multiple field groups appear on an edit screen, the first field "
#~ "group's options will be used. (the one with the lowest order number)"
#~ msgstr ""
#~ "Als er meerdere groepen verschijnen op een edit screen, zal de eerste "
#~ "groep worden gebruikt. (degene met het laagste volgorde nummer)"

#~ msgid ""
#~ "We're changing the way premium functionality is delivered in an exiting "
#~ "way!"
#~ msgstr ""
#~ "We hebben de premium mogelijkheden vernieuwd op een geweldige manier!"

#~ msgid "ACF PRO Required"
#~ msgstr "ACF PRO verplicht"

#~ msgid ""
#~ "We have detected an issue which requires your attention: This website "
#~ "makes use of premium add-ons (%s) which are no longer compatible with ACF."
#~ msgstr ""
#~ "We hebben een probleem ontdekt die uw aandacht vereist: Deze website "
#~ "maakt gebruik van add-ons (%s) die niet compatible zijn met de huidige "
#~ "versie van ACF."

#~ msgid ""
#~ "Don't panic, you can simply roll back the plugin and continue using ACF "
#~ "as you know it!"
#~ msgstr ""
#~ "Geen paniek! Je kunt gemakkelijk downgraden naar een vorige versie van "
#~ "ACF."

#~ msgid "Roll back to ACF v%s"
#~ msgstr "Downgrade naar ACF v%s"

#~ msgid "Learn why ACF PRO is required for my site"
#~ msgstr "Ontdek waarom je niet zonder ACF PRO kunt"

#~ msgid "Update Database"
#~ msgstr "Database updaten"

#~ msgid "Data Upgrade"
#~ msgstr "Data geüpgrade"

#~ msgid "Data upgraded successfully."
#~ msgstr "Data is met succes geüpgraded."

#~ msgid "Data is at the latest version."
#~ msgstr "Data beschikt over de laatste versie."

#~ msgid "1 required field below is empty"
#~ msgid_plural "%s required fields below are empty"
#~ msgstr[0] "1 verplicht veld is leeg"
#~ msgstr[1] "%s verplichte velden zijn leeg"

#~ msgid "Controls how HTML tags are rendered"
#~ msgstr "Bepaal hoe HTML tags worden weergegeven"

#~ msgid "No taxonomy filter"
#~ msgstr "Geen taxonomy filter"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "Laad & sla termen op bij post"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr ""
#~ "Laad waarde aan de hand van de post termen en update de post termen bij "
#~ "het opslaan"

#~ msgid "Custom field updated."
#~ msgstr "Extra veld bijgewerkt."

#~ msgid "Custom field deleted."
#~ msgstr "Extra veld verwijderd."

#~ msgid "Field group duplicated! Edit the new \"%s\" field group."
#~ msgstr "Groep gedupliceerd! Bewerk de nieuwe \"%s\" groep."

#~ msgid "Import/Export"
#~ msgstr "Import/Export"

#~ msgid "Column Width"
#~ msgstr "Kolom breedte"

#~ msgid "Attachment Details"
#~ msgstr "Bijlage details"

#~ msgid "Field group restored to revision from %s"
#~ msgstr "Groepen hersteld naar revisie van %s"

#~ msgid "No ACF groups selected"
#~ msgstr "Geen ACF groep geselecteerd"

#~ msgid "Normal"
#~ msgstr "Normaal"

#~ msgid "No Metabox"
#~ msgstr "Geen metabox"

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr ""
#~ "Lees de documentatie, leer de functies kennen en ontdek tips & tricks "
#~ "voor jouw web project."

#~ msgid "Visit the ACF website"
#~ msgstr "Bezoek de ACF website"

#~ msgid "Vote"
#~ msgstr "Stem"

#~ msgid "Follow"
#~ msgstr "Volg op Twitter"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr ""
#~ "Validatie mislukt. E&eacute;n of meer velden hieronder zijn verplicht."

#~ msgid "Add File to Field"
#~ msgstr "+ Bestand toevoegen aan veld"

#~ msgid "Add Image to Field"
#~ msgstr "Add Image to Field"

#~ msgid "Attachment updated"
#~ msgstr "Bijlage bijgewerkt."

#~ msgid "Repeater field deactivated"
#~ msgstr "Repeater Field gedeactiveerd"

#~ msgid "Gallery field deactivated"
#~ msgstr "Gallery field gedeactiveerd"

#~ msgid "Repeater field activated"
#~ msgstr "Repeater field geactiveerd"

#~ msgid "Options page activated"
#~ msgstr "Options page geactiveerd"

#~ msgid "Flexible Content field activated"
#~ msgstr "Flexible Content field geactiveerd"

#~ msgid "Gallery field activated"
#~ msgstr "Gallery field geactiveerd"

#~ msgid "License key unrecognised"
#~ msgstr "Licentie code niet herkend"

#~ msgid ""
#~ "Add-ons can be unlocked by purchasing a license key. Each key can be used "
#~ "on multiple sites."
#~ msgstr ""
#~ "Add-ons kun je activeren door een licentie code te kopen. Elke code kan "
#~ "gebruikt worden op meerdere websites."

#~ msgid "Activation Code"
#~ msgstr "Activatie code"

#~ msgid "Repeater Field"
#~ msgstr "Repeater Field"

#~ msgid "Flexible Content Field"
#~ msgstr "Flexible Content Field"

#~ msgid "Gallery Field"
#~ msgstr "Gallery Field"

#~ msgid "Export Field Groups to XML"
#~ msgstr "Exporteer groepen naar XML"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr ""
#~ "ACF maakt een .xml export bestand die compatibel is met de ingebouwde WP "
#~ "import plugin."

#~ msgid ""
#~ "Imported field groups <b>will</b> appear in the list of editable field "
#~ "groups. This is useful for migrating fields groups between Wp websites."
#~ msgstr ""
#~ "Ge&iuml;mporteerde veld groepen <b>verschijnen</b> in de lijst van "
#~ "beheerbare veld groepen. Dit is handig voor het migreren van veld groepen "
#~ "tussen WP websites."

#~ msgid "Select field group(s) from the list and click \"Export XML\""
#~ msgstr "Selecteer veld groep(en) van van de lijst en klik \"Exporteer XML\""

#~ msgid "Save the .xml file when prompted"
#~ msgstr "Sla de .xml file op wanneer er om gevraagd wordt"

#~ msgid "Navigate to Tools &raquo; Import and select WordPress"
#~ msgstr "Navigeer naar Extra &raquo; Importeren en selecteer WordPress "

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "Installeer de WP import plugin als er naar wordt gevraagd"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "Upload en import je ge&euml;xporteerde .xml bestand"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "Selecteer je gebruiker en negeer import bijlages"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "Dat is het! Happy WordPressing"

#~ msgid "Export XML"
#~ msgstr "Exporteer XML"

#~ msgid "ACF will create the PHP code to include in your theme."
#~ msgstr "ACF maakt de PHP code die je kan integreren in jouw thema."

#~ msgid "Register Field Groups"
#~ msgstr "Registreer veld groepen"

#~ msgid ""
#~ "Please note that if you export and register field groups within the same "
#~ "WP, you will see duplicate fields on your edit screens. To fix this, "
#~ "please move the original field group to the trash or remove the code from "
#~ "your functions.php file."
#~ msgstr ""
#~ "Houd er rekening mee dat wanneer je veld groepen exporteert en "
#~ "registreert in dezelfde WP installatie, ze verschijnen als gedupliceerde "
#~ "velden in je edit screens. Om dit te verhelpen: verwijder de originele "
#~ "veld groepen naar de prullenbak of verwijder de code uit je functions.php "
#~ "bestand."

#~ msgid "Select field group(s) from the list and click \"Create PHP\""
#~ msgstr "Selecteer veld groepen uit de lijst en klik \"Maak PHP\""

#~ msgid "Copy the PHP code generated"
#~ msgstr "Kopieer de gegenereerde PHP code"

#~ msgid "Paste into your functions.php file"
#~ msgstr "Plak in je functions.php bestand"

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr ""
#~ "Om add-ons te activeren, bewerk en gebruik de code in de eerste regels."

#~ msgid "Create PHP"
#~ msgstr "Maak PHP"

#~ msgid "Back to settings"
#~ msgstr "Terug naar instellingen"

#~ msgid "Advanced Custom Fields Settings"
#~ msgstr "Advanced Custom Fields instellingen"

#~ msgid "requires a database upgrade"
#~ msgstr "vereist een database upgrade"

#~ msgid "why?"
#~ msgstr "waarom?"

#~ msgid "Please"
#~ msgstr "Graag"

#~ msgid "backup your database"
#~ msgstr "backup maken van je database"

#~ msgid "then click"
#~ msgstr "vervolgens klikken op"

#~ msgid "Moving user custom fields from wp_options to wp_usermeta'"
#~ msgstr "Verplaats gebruikers eigen velden van wp_options naar wp_usermeta"

#~ msgid "No choices to choose from"
#~ msgstr "Geen keuzes om uit te kiezen"

#~ msgid "Red"
#~ msgstr "Rood"

#~ msgid "Blue"
#~ msgstr "Blauw"

#~ msgid "blue : Blue"
#~ msgstr "blauw : Blauw"

#~ msgid "File Updated."
#~ msgstr "Bestand bijgewerkt."

#~ msgid "Media attachment updated."
#~ msgstr "Media bijlage bijgewerkt."

#~ msgid "Add Selected Files"
#~ msgstr "Geselecteerde bestanden toevoegen"

#~ msgid "+ Add Row"
#~ msgstr "+ Nieuwe regel"

#~ msgid "Field Order"
#~ msgstr "Veld volgorde"

#~ msgid ""
#~ "No fields. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."
#~ msgstr ""
#~ "Geen velden. Klik op \"+ Nieuw sub veld\" button om je eerste veld te "
#~ "maken."

#~ msgid "Docs"
#~ msgstr "Documentatie"

#~ msgid "Close Sub Field"
#~ msgstr "Sub veld sluiten"

#~ msgid "+ Add Sub Field"
#~ msgstr "+ Nieuw sub veld"

#~ msgid "Alternate Text"
#~ msgstr "Alternatieve tekst"

#~ msgid "Thumbnail is advised"
#~ msgstr "Thumbnail wordt geadviseerd"

#~ msgid "Image Updated"
#~ msgstr "Afbeelding bijgwerkt"

#~ msgid "Grid"
#~ msgstr "Grid"

#~ msgid "List"
#~ msgstr "Lijst"

#~ msgid "No images selected"
#~ msgstr "Geen afbeeldingen geselecteerd"

#~ msgid "1 image selected"
#~ msgstr "1 afbeelding geselecteerd"

#~ msgid "{count} images selected"
#~ msgstr "{count} afbeeldingen geselecteerd"

#~ msgid "Added"
#~ msgstr "Toegevoegd"

#~ msgid "Image already exists in gallery"
#~ msgstr "Afbeelding bestaat al galerij"

#~ msgid "Image Updated."
#~ msgstr "Afbeelding bijgewerkt."

#~ msgid "Add selected Images"
#~ msgstr "Voeg geselecteerde afbeeldingen toe"

#~ msgid "Repeater Fields"
#~ msgstr "Velden herhalen"

#~ msgid "Field Instructions"
#~ msgstr "Veld instructies"

#~ msgid "Table (default)"
#~ msgstr "Tabel (standaard)"

#~ msgid "Define how to render html tags"
#~ msgstr "Bepaal hoe HTML tags worden omgezet"

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "Bepaal hoe HTML tags worden omgezet / nieuwe regels"

#~ msgid "Run filter \"the_content\"?"
#~ msgstr "Gebruik filter \"the_content\"?"

#~ msgid "Enable this filter to use shortcodes within the WYSIWYG field"
#~ msgstr "Activeer dit filter om shortcodes te gebruiken in het WYSIWYG veld"

#~ msgid ""
#~ "This format will determin the value saved to the database and returned "
#~ "via the API"
#~ msgstr ""
#~ "De datum wordt in deze indeling opgeslagen in de database en teruggegeven "
#~ "door de API"

#~ msgid "\"yymmdd\" is the most versatile save format. Read more about"
#~ msgstr "\"yymmdd\" is de meest veelzijdige opslaan indeling. Lees meer op"

#~ msgid "jQuery date formats"
#~ msgstr "jQuery datum format"

#~ msgid "This format will be seen by the user when entering a value"
#~ msgstr ""
#~ "Deze indeling wordt gezien door de gebruiker wanneer datum wordt ingevuld"

#~ msgid ""
#~ "\"dd/mm/yy\" or \"mm/dd/yy\" are the most used Display Formats. Read more "
#~ "about"
#~ msgstr ""
#~ "\"dd/mm/yy\" of \"mm/dd/yy\" zijn de meest gebruikte indelingen. Lees "
#~ "meer op"

#~ msgid "Page Specific"
#~ msgstr "Pagina specifiek"

#~ msgid "Post Specific"
#~ msgstr "Bericht specifiek"

#~ msgid "Taxonomy (Add / Edit)"
#~ msgstr "Taxonomy (Nieuwe / bewerk)"

#~ msgid "Media (Edit)"
#~ msgstr "Media (Bewerk)"

#~ msgid "match"
#~ msgstr "komt overeen met"

#~ msgid "all"
#~ msgstr "allen"

#~ msgid "of the above"
#~ msgstr "van hierboven"

#~ msgid "Unlock options add-on with an activation code"
#~ msgstr "Ontgrendel opties add-on met een activatie code"

#~ msgid "Add Fields to Edit Screens"
#~ msgstr "Voeg velden toe aan edit screen"

#~ msgid "Navigate to the"
#~ msgstr "Ga naar de"

#~ msgid "and select WordPress"
#~ msgstr "en selecteer WordPress"

#~ msgid "eg. dd/mm/yy. read more about"
#~ msgstr "bijv. dd/mm/yyyy. Lees meer over"

#~ msgid ""
#~ "Filter posts by selecting a post type<br />\n"
#~ "\t\t\t\tTip: deselect all post types to show all post type's posts"
#~ msgstr ""
#~ "Filter post type door te selecteren<br />\n"
#~ "\t\t\t\tTip: selecteer 'alles' om alle posts van alle post type te tonen"

#~ msgid "Everything Fields deactivated"
#~ msgstr "Everything Fields gedeactiveerd"

#~ msgid "Everything Fields activated"
#~ msgstr "Everything Fields geactiveerd"

#~ msgid "Set to -1 for infinite"
#~ msgstr "Plaats -1 voor oneindig"

#~ msgid "Row Limit"
#~ msgstr "Rij limiet"
