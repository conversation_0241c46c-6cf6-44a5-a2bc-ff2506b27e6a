{"name": "asm89/twig-cache-extension", "description": "Cache fragments of templates directly within Twig.", "keywords": ["twig", "cache", "extension"], "homepage": "https://github.com/asm89/twig-cache-extension", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=5.3.2", "twig/twig": "^1.0|^2.0"}, "require-dev": {"phpunit/phpunit": "^5.0 || ^4.8.10", "doctrine/cache": "~1.0"}, "scripts": {"test": "phpunit"}, "suggest": {"psr/cache-implementation": "To make use of PSR-6 cache implementation via PsrCacheAdapter."}, "autoload": {"psr-4": {"": "lib/"}}, "autoload-dev": {"psr-4": {"": "test/"}}, "extra": {"branch-alias": {"dev-master": "1.4-dev"}}}