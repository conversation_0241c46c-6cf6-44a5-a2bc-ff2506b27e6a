language: php

matrix:
  include:
    - php: 5.6
      dist: xenial
      env: 'COMPOSER_FLAGS="--prefer-stable --prefer-lowest" TWIG_VERSION="^1.0"'
    - php: 5.6
      dist: xenial
      env: TWIG_VERSION="^1.0"
    - php: 7.0
      dist: xenial
      env: TWIG_VERSION="^1.0"
    - php: 7.1
      dist: bionic
      env: TWIG_VERSION="^1.0"
    - php: 7.2
      dist: bionic
      env: TWIG_VERSION="^1.0"
    - php: 7.3
      dist: bionic
      env: TWIG_VERSION="^1.0"
    - php: 7.4
      dist: bionic
      env: TWIG_VERSION="^1.0"
    - php: 7.0
      dist: xenial
      env: TWIG_VERSION="^2.0"
    - php: 7.1
      dist: bionic
      env: TWIG_VERSION="^2.0"
    - php: 7.2
      dist: bionic
      env: TWIG_VERSION="^2.0"
    - php: 7.3
      dist: bionic
      env: TWIG_VERSION="^2.0"
    - php: 7.4
      dist: bionic
      env: TWIG_VERSION="^2.0"

install: composer require twig/twig:${TWIG_VERSION}

script: composer test
