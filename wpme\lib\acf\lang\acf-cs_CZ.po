msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2018-09-06 12:21+1000\n"
"PO-Revision-Date: 2018-12-11 09:20+0100\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: webees.cz s.r.o. <<EMAIL>>\n"
"Language: cs_CZ\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"Plural-Forms: nplurals=3; plural=(n==1) ? 0 : (n>=2 && n<=4) ? 1 : 2;\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:80
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:385 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Skupiny polí"

#: acf.php:386
msgid "Field Group"
msgstr "Skupina polí"

#: acf.php:387 acf.php:419 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New"
msgstr "Přidat nové"

#: acf.php:388
msgid "Add New Field Group"
msgstr "Přidat novou skupinu polí"

#: acf.php:389
msgid "Edit Field Group"
msgstr "Upravit skupinu polí"

#: acf.php:390
msgid "New Field Group"
msgstr "Nová skupina polí"

#: acf.php:391
msgid "View Field Group"
msgstr "Prohlížet skupinu polí"

#: acf.php:392
msgid "Search Field Groups"
msgstr "Hledat skupiny polí"

#: acf.php:393
msgid "No Field Groups found"
msgstr "Nebyly nalezeny žádné skupiny polí"

#: acf.php:394
msgid "No Field Groups found in Trash"
msgstr "V koši nebyly nalezeny žádné skupiny polí"

#: acf.php:417 includes/admin/admin-field-group.php:202
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Pole"

#: acf.php:418
msgid "Field"
msgstr "Pole"

#: acf.php:420
msgid "Add New Field"
msgstr "Přidat nové pole"

#: acf.php:421
msgid "Edit Field"
msgstr "Upravit pole"

#: acf.php:422 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr "Nové pole"

#: acf.php:423
msgid "View Field"
msgstr "Zobrazit pole"

#: acf.php:424
msgid "Search Fields"
msgstr "Vyhledat pole"

#: acf.php:425
msgid "No Fields found"
msgstr "Nenalezeno žádné pole"

#: acf.php:426
msgid "No Fields found in Trash"
msgstr "V koši nenalezeno žádné pole"

#: acf.php:465 includes/admin/admin-field-group.php:384
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Neaktivní"

#: acf.php:470
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Neaktivní <span class=\"count\">(%s)</span>"
msgstr[1] "Neaktivní <span class=\"count\">(%s)</span>"
msgstr[2] "Neaktivních <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Skupina polí aktualizována."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Skupina polí smazána."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Skupina polí publikována."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Skupina polí uložena."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Skupina polí odeslána."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Skupina polí naplánována."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Koncept skupiny polí aktualizován."

#: includes/admin/admin-field-group.php:153
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Řetězec \"pole_\" nesmí být použit na začátku názvu pole"

#: includes/admin/admin-field-group.php:154
msgid "This field cannot be moved until its changes have been saved"
msgstr "Toto pole nelze přesunout, dokud nebudou uloženy jeho změny"

#: includes/admin/admin-field-group.php:155
msgid "Field group title is required"
msgstr "Vyžadován nadpis pro skupinu polí"

#: includes/admin/admin-field-group.php:156
msgid "Move to trash. Are you sure?"
msgstr "Přesunout do koše. Jste si jistí?"

#: includes/admin/admin-field-group.php:157
msgid "No toggle fields available"
msgstr "Žádné zapínatelné pole není k dispozici"

#: includes/admin/admin-field-group.php:158
msgid "Move Custom Field"
msgstr "Přesunout vlastní pole"

#: includes/admin/admin-field-group.php:159
msgid "Checked"
msgstr "Zaškrtnuto"

#: includes/admin/admin-field-group.php:160 includes/api/api-field.php:289
msgid "(no label)"
msgstr "(bez štítku)"

#: includes/admin/admin-field-group.php:161
msgid "(this field)"
msgstr "(toto pole)"

#: includes/admin/admin-field-group.php:162
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "kopírovat"

#: includes/admin/admin-field-group.php:163
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:4073
msgid "or"
msgstr "nebo"

#: includes/admin/admin-field-group.php:164
msgid "Null"
msgstr "Nula"

#: includes/admin/admin-field-group.php:203
msgid "Location"
msgstr "Umístění"

#: includes/admin/admin-field-group.php:204
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Nastavení"

#: includes/admin/admin-field-group.php:354
msgid "Field Keys"
msgstr "Klíče polí"

#: includes/admin/admin-field-group.php:384
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Aktivní"

#: includes/admin/admin-field-group.php:746
msgid "Move Complete."
msgstr "Přesun hotov."

#: includes/admin/admin-field-group.php:747
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Pole %s lze nyní najít ve skupině polí %s"

#: includes/admin/admin-field-group.php:748
msgid "Close Window"
msgstr "Zavřít okno"

#: includes/admin/admin-field-group.php:789
msgid "Please select the destination for this field"
msgstr "Prosím zvolte umístění pro toto pole"

#: includes/admin/admin-field-group.php:796
msgid "Move Field"
msgstr "Přesunout pole"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktivní <span class=\"count\">(%s)</span>"
msgstr[1] "Aktivní <span class=\"count\">(%s)</span>"
msgstr[2] "Aktivních <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Skupina polí duplikována. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s skupina polí duplikována."
msgstr[1] "%s skupiny polí duplikovány."
msgstr[2] "%s skupin polí duplikováno."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Skupina polí synchronizována. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s skupina polí synchronizována."
msgstr[1] "%s skupiny polí synchronizovány."
msgstr[2] "%s skupin polí synchronizováno."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Synchronizace je k dispozici"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:370
msgid "Title"
msgstr "Název"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:397
msgid "Description"
msgstr "Popis"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Stav"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Upravte si WordPress pomocí výkonných, profesionálních a intuitivně "
"použitelných polí."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Seznam změn"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Podívejte se, co je nového ve <a href=\"%s\">verzi %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Zdroje"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Webová stránka"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Dokumentace"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Podpora"

#: includes/admin/admin-field-groups.php:623
#: includes/admin/views/settings-info.php:84
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Děkujeme, že používáte <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:667
msgid "Duplicate this item"
msgstr "Duplikovat tuto položku"

#: includes/admin/admin-field-groups.php:667
#: includes/admin/admin-field-groups.php:683
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate"
msgstr "Duplikovat"

#: includes/admin/admin-field-groups.php:700
#: includes/fields/class-acf-field-google-map.php:164
#: includes/fields/class-acf-field-relationship.php:674
msgid "Search"
msgstr "Hledat"

#: includes/admin/admin-field-groups.php:759
#, php-format
msgid "Select %s"
msgstr "Zvolit %s"

#: includes/admin/admin-field-groups.php:767
msgid "Synchronise field group"
msgstr "Synchronizujte skupinu polí"

#: includes/admin/admin-field-groups.php:767
#: includes/admin/admin-field-groups.php:797
msgid "Sync"
msgstr "Synchronizace"

#: includes/admin/admin-field-groups.php:779
msgid "Apply"
msgstr "Použít"

#: includes/admin/admin-field-groups.php:797
msgid "Bulk Actions"
msgstr "Hromadné akce"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Nástroje"

#: includes/admin/admin-upgrade.php:47 includes/admin/admin-upgrade.php:94
#: includes/admin/admin-upgrade.php:156
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Aktualizovat databázi"

#: includes/admin/admin-upgrade.php:180
msgid "Review sites & upgrade"
msgstr "Zkontrolujte stránky a aktualizujte"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "Vlastní pole"

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Doplňky"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Chyba</b>. Nelze načíst seznam doplňků"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informace"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Co je nového"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Exportovat skupiny polí"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Vytvořit PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Nebyly vybrány žádné skupiny polí"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Exportovaná 1 skupina polí."
msgstr[1] "Exportované %s skupiny polí."
msgstr[2] "Exportovaných %s skupin polí."

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Zvolit skupiny polí"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Vyberte skupiny polí, které chcete exportovat, a vyberte způsob exportu. "
"Použijte tlačítko pro stažení pro exportování do souboru .json, který pak "
"můžete importovat do jiné instalace ACF. Pomocí tlačítka generovat můžete "
"exportovat do kódu PHP, který můžete umístit do vašeho tématu."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Exportovat soubor"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Následující kód lze použít k registraci lokální verze vybrané skupiny polí. "
"Místní skupina polí může poskytnout mnoho výhod, jako jsou rychlejší doby "
"načítání, řízení verzí a dynamická pole / nastavení. Jednoduše zkopírujte a "
"vložte následující kód do souboru functions.php svého motivu nebo jej vložte "
"do externího souboru."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Zkopírovat od schránky"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "Zkopírováno"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importovat skupiny polí"

#: includes/admin/tools/class-acf-admin-tool-import.php:61
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Vyberte Advanced Custom Fields JSON soubor, který chcete importovat. Po "
"klepnutí na tlačítko importu níže bude ACF importovat skupiny polí."

#: includes/admin/tools/class-acf-admin-tool-import.php:66
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "Vybrat soubor"

#: includes/admin/tools/class-acf-admin-tool-import.php:76
msgid "Import File"
msgstr "Importovat soubor"

#: includes/admin/tools/class-acf-admin-tool-import.php:100
#: includes/fields/class-acf-field-file.php:170
msgid "No file selected"
msgstr "Dokument nevybrán"

#: includes/admin/tools/class-acf-admin-tool-import.php:113
msgid "Error uploading file. Please try again"
msgstr "Chyba při nahrávání souboru. Prosím zkuste to znovu"

#: includes/admin/tools/class-acf-admin-tool-import.php:122
msgid "Incorrect file type"
msgstr "Nesprávný typ souboru"

#: includes/admin/tools/class-acf-admin-tool-import.php:139
msgid "Import file empty"
msgstr "Importovaný soubor je prázdný"

#: includes/admin/tools/class-acf-admin-tool-import.php:247
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Importovaná 1 skupina polí"
msgstr[1] "Importované %s skupiny polí"
msgstr[2] "Importovaných %s skupin polí"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Podmíněná logika"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Zobrazit toto pole, pokud"

#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr "a"

#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Přidat skupinu pravidel"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:424
#: pro/fields/class-acf-field-repeater.php:294
msgid "Drag to reorder"
msgstr "Přetažením změníte pořadí"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Upravit pole"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:357
msgid "Edit"
msgstr "Upravit"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Duplikovat pole"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Přesunout pole do jiné skupiny"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Přesunout"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Smazat pole"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete"
msgstr "Smazat"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Štítek pole"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Toto je jméno, které se zobrazí na stránce úprav"

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Jméno pole"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Jedno slovo, bez mezer. Podtržítka a pomlčky jsou povoleny"

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Typ pole"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Instrukce"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instrukce pro autory. Jsou zobrazeny při zadávání dat"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Požadováno?"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Atributy obalového pole"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "šířka"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "třída"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "identifikátor"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Zavřít pole"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Pořadí"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:428
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Label"
msgstr "Štítek"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:939
#: pro/fields/class-acf-field-flexible-content.php:610
msgid "Name"
msgstr "Jméno"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Klíč"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Typ"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Žádná pole. Klikněte na tlačítko<strong>+ Přidat pole</strong> pro vytvoření "
"prvního pole."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Přidat pole"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Pravidla"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Vytváří sadu pravidel pro určení, na kterých stránkách úprav budou použita "
"tato vlastní pole"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Styl"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standardní (WP metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Bezokrajové (bez metaboxu)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Pozice"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Vysoko (po nadpisu)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normální (po obsahu)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Na straně"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Umístění štítků"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Zarovnat shora"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Zarovnat zleva"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Umístění instrukcí"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Pod štítky"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Pod poli"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Pořadové č."

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Skupiny polí s nižším pořadím se zobrazí první"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Zobrazit v seznamu skupin polí"

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "Trvalý odkaz"

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "Editor obsahu"

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "Stručný výpis"

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "Diskuze"

#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "Komentáře"

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "Revize"

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "Adresa"

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "Formát"

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "Atributy stránky"

#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:688
msgid "Featured Image"
msgstr "Uživatelský obrázek"

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "Kategorie"

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "Štítky"

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "Odesílat zpětné linkování odkazů"

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "Skrýt na obrazovce"

#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Zvolte</b> položky, které budou na obrazovce úprav <b>skryté</b>."

#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Pokud se na obrazovce úprav objeví více skupin polí, použije se nastavení "
"dle první skupiny polí (té s nejnižším pořadovým číslem)"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Následující stránky vyžadují upgrade DB. Zaškrtněte ty, které chcete "
"aktualizovat, a poté klikněte na %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "Upgradovat stránky"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Stránky"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Stránky vyžadují aktualizaci databáze z %s na %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr "Stránky jsou aktuální"

#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Aktualizace databáze je dokončena. <a href=\"%s\">Návrat na nástěnku sítě</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr "Vyberte alespoň jednu stránku, kterou chcete upgradovat."

#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Důrazně doporučujeme zálohovat databázi před pokračováním. Opravdu chcete "
"aktualizaci spustit?"

#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr "Aktualizace dat na verzi %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:167
msgid "Upgrade complete."
msgstr "Aktualizace dokončena."

#: includes/admin/views/html-admin-page-upgrade-network.php:176
#: includes/admin/views/html-admin-page-upgrade-network.php:185
#: includes/admin/views/html-admin-page-upgrade.php:78
#: includes/admin/views/html-admin-page-upgrade.php:87
msgid "Upgrade failed."
msgstr "Upgrade se nezdařil."

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Čtení úkolů aktualizace..."

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Upgrade databáze byl dokončen. <a href=\"%s\">Podívejte se, co je nového</a>"

#: includes/admin/views/html-admin-page-upgrade.php:116
#: includes/ajax/class-acf-ajax-upgrade.php:33
msgid "No updates available."
msgstr "K dispozici nejsou žádné aktualizace."

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Zobrazit tuto skupinu polí, pokud"

#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Opakovač"

#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Flexibilní obsah"

#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galerie"

#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Stránka konfigurace"

#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr "Vyžadován upgrade databáze"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Děkujeme vám za aktualizaci na %s v%s!"

#: includes/admin/views/html-notice-upgrade.php:22
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "Tato verze obsahuje vylepšení databáze a vyžaduje upgrade."

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""
"Zkontrolujte také, zda jsou všechny prémiové doplňky ( %s) nejprve "
"aktualizovány na nejnovější verzi."

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Stáhnout a instalovat"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Instalováno"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Vítejte v Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Děkujeme za aktualizaci! ACF %s je větší a lepší než kdykoli předtím. "
"Doufáme, že se vám bude líbit."

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "Plynulejší zážitek"

#: includes/admin/views/settings-info.php:19
msgid "Improved Usability"
msgstr "Vylepšená použitelnost"

#: includes/admin/views/settings-info.php:20
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Zahrnutí oblíbené knihovny Select2 zlepšilo jak použitelnost, tak i rychlost "
"v různých typech polí, včetně objektu příspěvku, odkazu na stránku, "
"taxonomie a možnosti výběru."

#: includes/admin/views/settings-info.php:24
msgid "Improved Design"
msgstr "Zlepšený design"

#: includes/admin/views/settings-info.php:25
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Mnoho polí podstoupilo osvěžení grafiky, aby ACF vypadalo lépe než kdy "
"jindy! Znatelné změny jsou vidět na polích galerie, vztahů a oEmbed "
"(novinka)!"

#: includes/admin/views/settings-info.php:29
msgid "Improved Data"
msgstr "Vylepšené údaje"

#: includes/admin/views/settings-info.php:30
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Přepracování datové architektury umožnilo, aby podřazená pole žila nezávisle "
"na rodičích. To umožňuje jejich přetahování mezi rodičovskými poli!"

#: includes/admin/views/settings-info.php:38
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Sbohem doplňkům. Pozdrav verzi PRO"

#: includes/admin/views/settings-info.php:41
msgid "Introducing ACF PRO"
msgstr "Představujeme ACF PRO"

#: includes/admin/views/settings-info.php:42
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "Měníme způsob poskytování prémiových funkcí vzrušujícím způsobem!"

#: includes/admin/views/settings-info.php:43
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Všechny 4 prémiové doplňky byly spojeny do nové verze <a href=\"%s\">Pro pro "
"ACF</a>. Se svými osobními i vývojovými licencemi je prémiová funkčnost "
"cenově dostupná a přístupnější než kdykoli předtím!"

#: includes/admin/views/settings-info.php:47
msgid "Powerful Features"
msgstr "Výkonné funkce"

#: includes/admin/views/settings-info.php:48
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO obsahuje výkonné funkce, jako jsou opakovatelná data, flexibilní "
"rozložení obsahu, krásné pole galerie a možnost vytvářet další stránky "
"administrátorských voleb!"

#: includes/admin/views/settings-info.php:49
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Přečtěte si další informace o funkcích <a href=\"%s\">ACF PRO</a>."

#: includes/admin/views/settings-info.php:53
msgid "Easy Upgrading"
msgstr "Snadná aktualizace"

#: includes/admin/views/settings-info.php:54
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr ""
"Upgrade na ACF PRO je snadný. Stačí online zakoupit licenci a stáhnout "
"plugin!"

#: includes/admin/views/settings-info.php:55
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""
"Také jsme napsali <a href=\"%s\">průvodce aktualizací</a> na zodpovězení "
"jakýchkoliv dotazů, ale pokud i přes to nějaký máte, kontaktujte prosím náš "
"tým podpory prostřednictvím <a href=\"%s\">Help Desku</a>."

#: includes/admin/views/settings-info.php:64
msgid "New Features"
msgstr "Nové funkce"

#: includes/admin/views/settings-info.php:69
msgid "Link Field"
msgstr "Odkaz pole"

#: includes/admin/views/settings-info.php:70
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr ""
"Pole odkazu poskytuje jednoduchý způsob, jak vybrat nebo definovat odkaz "
"(URL, název, cíl)."

#: includes/admin/views/settings-info.php:74
msgid "Group Field"
msgstr "Skupinové pole"

#: includes/admin/views/settings-info.php:75
msgid "The Group field provides a simple way to create a group of fields."
msgstr "Skupina polí poskytuje jednoduchý způsob vytvoření skupiny polí."

#: includes/admin/views/settings-info.php:79
msgid "oEmbed Field"
msgstr "oEmbed pole"

#: includes/admin/views/settings-info.php:80
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr ""
"oEmbed pole umožňuje snadno vkládat videa, obrázky, tweety, audio a další "
"obsah."

#: includes/admin/views/settings-info.php:84
msgid "Clone Field"
msgstr "Klonovat pole"

#: includes/admin/views/settings-info.php:85
msgid "The clone field allows you to select and display existing fields."
msgstr "Klonované pole umožňuje vybrat a zobrazit existující pole."

#: includes/admin/views/settings-info.php:89
msgid "More AJAX"
msgstr "Více AJAXu"

#: includes/admin/views/settings-info.php:90
msgid "More fields use AJAX powered search to speed up page loading."
msgstr "Více polí využívá vyhledávání pomocí AJAX pro rychlé načítání stránky."

#: includes/admin/views/settings-info.php:94
msgid "Local JSON"
msgstr "Lokální JSON"

#: includes/admin/views/settings-info.php:95
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr ""
"Nová funkce automatického exportu do JSONu zvyšuje rychlost a umožňuje "
"synchronizaci."

#: includes/admin/views/settings-info.php:99
msgid "Easy Import / Export"
msgstr "Snadný import/export"

#: includes/admin/views/settings-info.php:100
msgid "Both import and export can easily be done through a new tools page."
msgstr "Import i export lze snadno provést pomocí nové stránky nástroje."

#: includes/admin/views/settings-info.php:104
msgid "New Form Locations"
msgstr "Umístění nového formuláře"

#: includes/admin/views/settings-info.php:105
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr ""
"Pole lze nyní mapovat na nabídky, položky nabídky, komentáře, widgety a "
"všechny uživatelské formuláře!"

#: includes/admin/views/settings-info.php:109
msgid "More Customization"
msgstr "Další úpravy"

#: includes/admin/views/settings-info.php:110
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr ""
"Byly přidány nové akce a filtry PHP (a JS), které umožňují další úpravy."

#: includes/admin/views/settings-info.php:114
msgid "Fresh UI"
msgstr "Svěží uživatelské rozhraní"

#: includes/admin/views/settings-info.php:115
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr "Celý plugin je redesignován včetně nových typů polí a nastavení!"

#: includes/admin/views/settings-info.php:119
msgid "New Settings"
msgstr "Nová nastavení"

#: includes/admin/views/settings-info.php:120
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr ""
"Bylo přidáno nastavení skupiny polí bylo přidáno pro aktivní, umístění "
"štítků, umístění instrukcí a popis."

#: includes/admin/views/settings-info.php:124
msgid "Better Front End Forms"
msgstr "Lepší vizuální stránka formulářů"

#: includes/admin/views/settings-info.php:125
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr ""
"acf_form() může nyní vytvořit nový příspěvek po odeslání se spoustou nových "
"možností."

#: includes/admin/views/settings-info.php:129
msgid "Better Validation"
msgstr "Lepší validace"

#: includes/admin/views/settings-info.php:130
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr ""
"Validace formuláře nyní probíhá prostřednictvím PHP + AJAX a to ve prospěch "
"pouze JS."

#: includes/admin/views/settings-info.php:134
msgid "Moving Fields"
msgstr "Pohyblivá pole"

#: includes/admin/views/settings-info.php:135
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr ""
"Nová funkčnost skupiny polí umožňuje přesouvání pole mezi skupinami a rodiči."

#: includes/admin/views/settings-info.php:146
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Myslíme si, že změny v %s si zamilujete."

#: includes/api/api-helpers.php:1046
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/api/api-helpers.php:1047
msgid "Medium"
msgstr "Střední"

#: includes/api/api-helpers.php:1048
msgid "Large"
msgstr "Velký"

#: includes/api/api-helpers.php:1097
msgid "Full Size"
msgstr "Plná velikost"

#: includes/api/api-helpers.php:1339 includes/api/api-helpers.php:1912
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(bez názvu)"

#: includes/api/api-helpers.php:3994
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Šířka obrázku musí být alespoň %dpx."

#: includes/api/api-helpers.php:3999
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Šířka obrázku nesmí přesáhnout %dpx."

#: includes/api/api-helpers.php:4015
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Výška obrázku musí být alespoň %dpx."

#: includes/api/api-helpers.php:4020
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Výška obrázku nesmí přesáhnout %dpx."

#: includes/api/api-helpers.php:4038
#, php-format
msgid "File size must be at least %s."
msgstr "Velikost souboru musí být alespoň %s."

#: includes/api/api-helpers.php:4043
#, php-format
msgid "File size must must not exceed %s."
msgstr "Velikost souboru nesmí přesáhnout %s."

#: includes/api/api-helpers.php:4077
#, php-format
msgid "File type must be %s."
msgstr "Typ souboru musí být %s."

#: includes/assets.php:172
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Pokud opustíte tuto stránku, změny, které jste provedli, budou ztraceny"

#: includes/assets.php:175 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr "Vybrat"

#: includes/assets.php:176
msgctxt "verb"
msgid "Edit"
msgstr "Upravit"

#: includes/assets.php:177
msgctxt "verb"
msgid "Update"
msgstr "Aktualizace"

#: includes/assets.php:178
msgid "Uploaded to this post"
msgstr "Nahrán k tomuto příspěvku"

#: includes/assets.php:179
msgid "Expand Details"
msgstr "Rozbalit podrobnosti"

#: includes/assets.php:180
msgid "Collapse Details"
msgstr "Sbalit podrobnosti"

#: includes/assets.php:181
msgid "Restricted"
msgstr "Omezeno"

#: includes/assets.php:182 includes/fields/class-acf-field-image.php:67
msgid "All images"
msgstr "Všechny obrázky"

#: includes/assets.php:185
msgid "Validation successful"
msgstr "Ověření úspěšné"

#: includes/assets.php:186 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Ověření selhalo"

#: includes/assets.php:187
msgid "1 field requires attention"
msgstr "1 pole vyžaduje pozornost"

#: includes/assets.php:188
#, php-format
msgid "%d fields require attention"
msgstr "Několik polí vyžaduje pozornost (%d)"

#: includes/assets.php:191
msgid "Are you sure?"
msgstr "Jste si jistí?"

#: includes/assets.php:192 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Ano"

#: includes/assets.php:193 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Ne"

#: includes/assets.php:194 includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-image.php:141
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:358
#: pro/fields/class-acf-field-gallery.php:546
msgid "Remove"
msgstr "Odstranit"

#: includes/assets.php:195
msgid "Cancel"
msgstr "Zrušit"

#: includes/assets.php:198
msgid "Has any value"
msgstr "Má libovolnou hodnotu"

#: includes/assets.php:199
msgid "Has no value"
msgstr "Nemá hodnotu"

#: includes/assets.php:200
msgid "Value is equal to"
msgstr "Hodnota je rovna"

#: includes/assets.php:201
msgid "Value is not equal to"
msgstr "Hodnota není rovna"

#: includes/assets.php:202
msgid "Value matches pattern"
msgstr "Hodnota odpovídá masce"

#: includes/assets.php:203
msgid "Value contains"
msgstr "Hodnota obsahuje"

#: includes/assets.php:204
msgid "Value is greater than"
msgstr "Hodnota je větší než"

#: includes/assets.php:205
msgid "Value is less than"
msgstr "Hodnota je menší než"

#: includes/assets.php:206
msgid "Selection is greater than"
msgstr "Výběr je větší než"

#: includes/assets.php:207
msgid "Selection is less than"
msgstr "Výběr je menší než"

#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "Typ pole neexistuje"

#: includes/fields.php:308
msgid "Unknown"
msgstr "Neznámý"

#: includes/fields.php:349
msgid "Basic"
msgstr "Základní"

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "Obsah"

#: includes/fields.php:351
msgid "Choice"
msgstr "Volba"

#: includes/fields.php:352
msgid "Relational"
msgstr "Relační"

#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:567
#: pro/fields/class-acf-field-flexible-content.php:616
#: pro/fields/class-acf-field-repeater.php:443
msgid "Layout"
msgstr "Typ zobrazení"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Akordeon"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Otevřít"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Zobrazit tento akordeon jako otevřený při načtení stránky."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Vícenásobné rozbalení"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Povolit otevření tohoto akordeonu bez zavření ostatních."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Koncový bod"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definujte koncový bod pro předchozí akordeon. Tento akordeon nebude "
"viditelný."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Skupina tlačítek"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:359
msgid "Choices"
msgstr "Možnosti"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:360
msgid "Enter each choice on a new line."
msgstr "Zadejte každou volbu na nový řádek."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:360
msgid "For more control, you may specify both a value and label like this:"
msgstr "Pro větší kontrolu můžete zadat jak hodnotu, tak štítek:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:360
msgid "red : Red"
msgstr "cervena : Červená"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-taxonomy.php:784
#: includes/fields/class-acf-field-user.php:409
msgid "Allow Null?"
msgstr "Povolit prázdné?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-select.php:368
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:381
msgid "Default Value"
msgstr "Výchozí hodnota"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:150
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Appears when creating a new post"
msgstr "Objeví se při vytváření nového příspěvku"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Horizontální"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Vertikální"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:205
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Return Value"
msgstr "Vrátit hodnotu"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Zadat konkrétní návratovou hodnotu na frontendu"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:427
msgid "Value"
msgstr "Hodnota"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:429
msgid "Both (Array)"
msgstr "Obě (pole)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Checkbox"
msgstr "Zaškrtávátko"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Přepnout vše"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Přidat novou volbu"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Povolit vlastní"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Povolit přidání 'vlastních' hodnot"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Uložit vlastní"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Uložit 'vlastní' hodnoty do voleb polí"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each default value on a new line"
msgstr "Zadejte každou výchozí hodnotu na nový řádek"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Přepnout"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Přidat zaškrtávátko navíc pro přepnutí všech možností"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Výběr barvy"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Vymazat"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Výchozí nastavení"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Výběr barvy"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Aktuální barva"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Výběr data"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Hotovo"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Dnes"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Následující"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Předchozí"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Týden"

#: includes/fields/class-acf-field-date_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Formát zobrazení"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Formát zobrazený při úpravě příspěvku"

#: includes/fields/class-acf-field-date_picker.php:189
#: includes/fields/class-acf-field-date_picker.php:220
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Vlastní:"

#: includes/fields/class-acf-field-date_picker.php:199
msgid "Save Format"
msgstr "Uložit formát"

#: includes/fields/class-acf-field-date_picker.php:200
msgid "The format used when saving a value"
msgstr "Formát použitý při ukládání hodnoty"

#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:715
#: includes/fields/class-acf-field-select.php:422
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:428
msgid "Return Format"
msgstr "Formát návratové hodnoty"

#: includes/fields/class-acf-field-date_picker.php:211
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "Formát vrácen pomocí funkcí šablony"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "Týden začíná"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Výběr data a času"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Zvolit čas"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Čas"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hodina"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuta"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Vteřina"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisekunda"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunda"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Časové pásmo"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Nyní"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Hotovo"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Vybrat"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "dop"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "od"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "odp"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "do"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Zástupný text"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Zobrazí se v inputu"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Zobrazit před"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:189
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Zobrazí se před inputem"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Zobrazit po"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:198
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Zobrazí se za inputem"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Soubor"

#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "Upravit soubor"

#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "Aktualizovat soubor"

#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr "Jméno souboru"

#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:265
#: includes/fields/class-acf-field-image.php:294
#: pro/fields/class-acf-field-gallery.php:705
#: pro/fields/class-acf-field-gallery.php:734
msgid "File size"
msgstr "Velikost souboru"

#: includes/fields/class-acf-field-file.php:170
msgid "Add File"
msgstr "Přidat soubor"

#: includes/fields/class-acf-field-file.php:221
msgid "File Array"
msgstr "Pole souboru"

#: includes/fields/class-acf-field-file.php:222
msgid "File URL"
msgstr "Adresa souboru"

#: includes/fields/class-acf-field-file.php:223
msgid "File ID"
msgstr "ID souboru"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:230
#: pro/fields/class-acf-field-gallery.php:670
msgid "Library"
msgstr "Knihovna"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:671
msgid "Limit the media library choice"
msgstr "Omezit výběr knihovny médií"

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:236
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:676
msgid "All"
msgstr "Vše"

#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-image.php:237
#: pro/fields/class-acf-field-gallery.php:677
msgid "Uploaded to post"
msgstr "Nahráno k příspěvku"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:244
#: pro/fields/class-acf-field-gallery.php:684
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-file.php:256
msgid "Restrict which files can be uploaded"
msgstr "Omezte, které typy souborů lze nahrát"

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:273
#: pro/fields/class-acf-field-gallery.php:713
msgid "Maximum"
msgstr "Maximum"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:302
#: pro/fields/class-acf-field-gallery.php:742
msgid "Allowed file types"
msgstr "Povolené typy souborů"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:743
msgid "Comma separated list. Leave blank for all types"
msgstr "Seznam oddělený čárkami. Nechte prázdné pro povolení všech typů"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Mapa Google"

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "Je nám líto, ale tento prohlížeč nepodporuje geolokaci"

#: includes/fields/class-acf-field-google-map.php:165
msgid "Clear location"
msgstr "Vymazat polohu"

#: includes/fields/class-acf-field-google-map.php:166
msgid "Find current location"
msgstr "Najít aktuální umístění"

#: includes/fields/class-acf-field-google-map.php:169
msgid "Search for address..."
msgstr "Vyhledat adresu..."

#: includes/fields/class-acf-field-google-map.php:199
#: includes/fields/class-acf-field-google-map.php:210
msgid "Center"
msgstr "Vycentrovat"

#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-google-map.php:211
msgid "Center the initial map"
msgstr "Vycentrovat počáteční zobrazení mapy"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Zoom"
msgstr "Přiblížení"

#: includes/fields/class-acf-field-google-map.php:223
msgid "Set the initial zoom level"
msgstr "Nastavit počáteční úroveň přiblížení"

#: includes/fields/class-acf-field-google-map.php:232
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:696
#: pro/fields/class-acf-field-gallery.php:725
msgid "Height"
msgstr "Výška"

#: includes/fields/class-acf-field-google-map.php:233
msgid "Customise the map height"
msgstr "Upravit výšku mapy"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Skupina"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:379
msgid "Sub Fields"
msgstr "Podřazená pole"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Určení stylu použitého pro vykreslení vybraných polí"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:627
#: pro/fields/class-acf-field-repeater.php:451
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:626
#: pro/fields/class-acf-field-repeater.php:450
msgid "Table"
msgstr "Tabulka"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:628
#: pro/fields/class-acf-field-repeater.php:452
msgid "Row"
msgstr "Řádek"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Obrázek"

#: includes/fields/class-acf-field-image.php:64
msgid "Select Image"
msgstr "Vybrat obrázek"

#: includes/fields/class-acf-field-image.php:65
msgid "Edit Image"
msgstr "Upravit obrázek"

#: includes/fields/class-acf-field-image.php:66
msgid "Update Image"
msgstr "Aktualizovat obrázek"

#: includes/fields/class-acf-field-image.php:157
msgid "No image selected"
msgstr "Není vybrán žádný obrázek"

#: includes/fields/class-acf-field-image.php:157
msgid "Add Image"
msgstr "Přidat obrázek"

#: includes/fields/class-acf-field-image.php:211
msgid "Image Array"
msgstr "Pole obrázku"

#: includes/fields/class-acf-field-image.php:212
msgid "Image URL"
msgstr "Adresa obrázku"

#: includes/fields/class-acf-field-image.php:213
msgid "Image ID"
msgstr "ID obrázku"

#: includes/fields/class-acf-field-image.php:220
msgid "Preview Size"
msgstr "Velikost náhledu"

#: includes/fields/class-acf-field-image.php:221
msgid "Shown when entering data"
msgstr "Zobrazit při zadávání dat"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:685
#: pro/fields/class-acf-field-gallery.php:714
msgid "Restrict which images can be uploaded"
msgstr "Omezte, které typy obrázků je možné nahrát"

#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Width"
msgstr "Šířka"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Odkaz"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Vybrat odkaz"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Otevřít v novém okně/záložce"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Pole odkazů"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "URL adresa odkazu"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Zpráva"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Nové řádky"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Řídí, jak se vykreslují nové řádky"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Automaticky přidávat odstavce"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Automaticky přidávat &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Žádné formátování"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Escapovat HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Nevykreslovat efekt, ale zobrazit značky HTML jako prostý text"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Číslo"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:158
msgid "Minimum Value"
msgstr "Minimální hodnota"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:168
msgid "Maximum Value"
msgstr "Maximální hodnota"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:178
msgid "Step Size"
msgstr "Velikost kroku"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Hodnota musí být číslo"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Hodnota musí být rovna nebo větší než %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Hodnota musí být rovna nebo menší než %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "Vložte URL"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Velikost pro Embed"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Odkaz stránky"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Archivy"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:961
msgid "Parent"
msgstr "Rodič"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:641
msgid "Filter by Post Type"
msgstr "Filtrovat dle typu příspěvku"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:649
msgid "All post types"
msgstr "Všechny typy příspěvků"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:655
msgid "Filter by Taxonomy"
msgstr "Filtrovat dle taxonomie"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:663
msgid "All taxonomies"
msgstr "Všechny taxonomie"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Umožnit URL adresy archivu"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:387
#: includes/fields/class-acf-field-user.php:419
msgid "Select multiple values?"
msgstr "Vybrat více hodnot?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Heslo"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:720
msgid "Post Object"
msgstr "Objekt příspěvku"

#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:721
msgid "Post ID"
msgstr "ID příspěvku"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Přepínač"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Jiné"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Přidat volbu 'jiné', která umožňuje vlastní hodnoty"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Uložit Jiné"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Uložit 'jiné' hodnoty do voleb polí"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Rozmezí"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Vztah"

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "Dosaženo maximálního množství hodnot ( {max} hodnot )"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "Načítání"

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "Nebyly nalezeny žádné výsledky"

#: includes/fields/class-acf-field-relationship.php:441
msgid "Select post type"
msgstr "Zvolit typ příspěvku"

#: includes/fields/class-acf-field-relationship.php:467
msgid "Select taxonomy"
msgstr "Zvolit taxonomii"

#: includes/fields/class-acf-field-relationship.php:557
msgid "Search..."
msgstr "Hledat..."

#: includes/fields/class-acf-field-relationship.php:669
msgid "Filters"
msgstr "Filtry"

#: includes/fields/class-acf-field-relationship.php:675
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Typ příspěvku"

#: includes/fields/class-acf-field-relationship.php:676
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:754
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "Taxonomie"

#: includes/fields/class-acf-field-relationship.php:683
msgid "Elements"
msgstr "Prvky"

#: includes/fields/class-acf-field-relationship.php:684
msgid "Selected elements will be displayed in each result"
msgstr "Vybrané prvky se zobrazí v každém výsledku"

#: includes/fields/class-acf-field-relationship.php:695
msgid "Minimum posts"
msgstr "Minimum příspěvků"

#: includes/fields/class-acf-field-relationship.php:704
msgid "Maximum posts"
msgstr "Maximum příspěvků"

#: includes/fields/class-acf-field-relationship.php:808
#: pro/fields/class-acf-field-gallery.php:815
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s vyžaduje alespoň %s volbu"
msgstr[1] "%s vyžaduje alespoň %s volby"
msgstr[2] "%s vyžaduje alespoň %s voleb"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:776
msgctxt "noun"
msgid "Select"
msgstr "Vybrat"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr ""
"Jeden výsledek je k dispozici, stiskněte klávesu enter pro jeho vybrání."

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d výsledků je k dispozici, použijte šipky nahoru a dolů pro navigaci."

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nebyly nalezeny žádné výsledky"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Prosím zadejte 1 nebo více znaků"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Prosím zadejte %d nebo více znaků"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Prosím odstraňte 1 znak"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Prosím odstraňte %d znaků"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Můžete vybrat pouze 1 položku"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Můžete vybrat pouze %d položek"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Načítání dalších výsledků&hellip;"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Vyhledávání&hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Načítání selhalo"

#: includes/fields/class-acf-field-select.php:397
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Stylizované uživatelské rozhraní"

#: includes/fields/class-acf-field-select.php:407
msgid "Use AJAX to lazy load choices?"
msgstr "K načtení volby použít AJAX lazy load?"

#: includes/fields/class-acf-field-select.php:423
msgid "Specify the value returned"
msgstr "Zadat konkrétní návratovou hodnotu"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Oddělovač"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Záložka"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Umístění"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Definujte koncový bod pro předchozí záložky. Tím se začne nová skupina "
"záložek."

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Nic pro %s"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Select the taxonomy to be displayed"
msgstr "Zvolit zobrazovanou taxonomii"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Appearance"
msgstr "Vzhled"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Select the appearance of this field"
msgstr "Vyberte vzhled tohoto pole"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Multiple Values"
msgstr "Více hodnot"

#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Multi Select"
msgstr "Vícenásobný výběr"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Single Value"
msgstr "Jednotlivá hodnota"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Radio Buttons"
msgstr "Radio přepínače"

#: includes/fields/class-acf-field-taxonomy.php:799
msgid "Create Terms"
msgstr "Vytvořit pojmy"

#: includes/fields/class-acf-field-taxonomy.php:800
msgid "Allow new terms to be created whilst editing"
msgstr "Povolit vytvoření nových pojmů během editace"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Save Terms"
msgstr "Uložit pojmy"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Connect selected terms to the post"
msgstr "Připojte vybrané pojmy k příspěvku"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Load Terms"
msgstr "Nahrát pojmy"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Load value from posts terms"
msgstr "Nahrát pojmy z příspěvků"

#: includes/fields/class-acf-field-taxonomy.php:834
msgid "Term Object"
msgstr "Objekt pojmu"

#: includes/fields/class-acf-field-taxonomy.php:835
msgid "Term ID"
msgstr "ID pojmu"

#: includes/fields/class-acf-field-taxonomy.php:885
#, php-format
msgid "User unable to add new %s"
msgstr "Uživatel není schopen přidat nové %s"

#: includes/fields/class-acf-field-taxonomy.php:895
#, php-format
msgid "%s already exists"
msgstr "%s již existuje"

#: includes/fields/class-acf-field-taxonomy.php:927
#, php-format
msgid "%s added"
msgstr "%s přidán"

#: includes/fields/class-acf-field-taxonomy.php:973
msgid "Add"
msgstr "Přidat"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Limit znaků"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Nechte prázdné pro nastavení bez omezení"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Textové pole"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Řádky"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Nastavuje výšku textového pole"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Výběr času"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Pravda / Nepravda"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Zobrazí text vedle zaškrtávacího políčka"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Text (aktivní)"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Text zobrazený při aktivním poli"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "Text (neaktivní)"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "Text zobrazený při neaktivním poli"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Adresa URL"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Hodnota musí být validní adresa URL"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Uživatel"

#: includes/fields/class-acf-field-user.php:394
msgid "Filter by role"
msgstr "Filtrovat podle role"

#: includes/fields/class-acf-field-user.php:402
msgid "All user roles"
msgstr "Všechny uživatelské role"

#: includes/fields/class-acf-field-user.php:433
msgid "User Array"
msgstr "Pole uživatelů"

#: includes/fields/class-acf-field-user.php:434
msgid "User Object"
msgstr "Objekt uživatele"

#: includes/fields/class-acf-field-user.php:435
msgid "User ID"
msgstr "ID uživatele"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Wysiwyg Editor"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual"
msgstr "Grafika"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:337
msgid "Click to initialize TinyMCE"
msgstr "Klikněte pro inicializaci TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Tabs"
msgstr "Záložky"

#: includes/fields/class-acf-field-wysiwyg.php:395
msgid "Visual & Text"
msgstr "Grafika a text"

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Visual Only"
msgstr "Pouze grafika"

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Text Only"
msgstr "Pouze text"

#: includes/fields/class-acf-field-wysiwyg.php:404
msgid "Toolbar"
msgstr "Lišta nástrojů"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr "Zobrazit tlačítka nahrávání médií?"

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr "Zpoždění inicializace?"

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE nebude inicializován, dokud nekliknete na pole"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:301
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr "Editovat skupinu polí"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Ověřit email"

#: includes/forms/form-front.php:103 pro/fields/class-acf-field-gallery.php:588
#: pro/options-page.php:81
msgid "Update"
msgstr "Aktualizace"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Příspěvek aktualizován"

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr "Zjištěn spam"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Příspěvek"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Stránka"

#: includes/locations.php:96
msgid "Forms"
msgstr "Formuláře"

#: includes/locations.php:243
msgid "is equal to"
msgstr "je rovno"

#: includes/locations.php:244
msgid "is not equal to"
msgstr "není rovno"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Příloha"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Všechny formáty %s"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Komentář"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Aktuální uživatelská role"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Aktuální uživatel"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Přihlášen"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Prohlížíte frontend"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Prohlížíte backend"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Položka nabídky"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Nabídka"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Umístění nabídky"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Nabídky"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Rodičovská stránka"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Šablona stránky"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Výchozí šablona"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Typ stránky"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "Hlavní stránka"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "Stránka příspěvku"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "Stránka nejvyšší úrovně (žádný nadřazený)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "Rodičovská stránka (má potomky)"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "Podřazená stránka (má rodiče)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Rubrika příspěvku"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Formát příspěvku"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Stav příspěvku"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Taxonomie příspěvku"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Šablona příspěvku"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Uživatelský formulář"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Přidat / Editovat"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Registrovat"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Uživatelská role"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widget"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s hodnota je vyžadována"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Publikovat"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Nebyly nalezeny žádné vlastní skupiny polí. <a href=\"%s\">Vytvořit vlastní "
"skupinu polí</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Chyba</b>. Nelze se připojit k serveru a aktualizovat"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Aktualizace"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Deaktivujte licenci"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Aktivujte licenci"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Informace o licenci"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Chcete-li povolit aktualizace, zadejte prosím licenční klíč. Pokud nemáte "
"licenční klíč, přečtěte si <a href=\"%s\">podrobnosti a ceny</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Licenční klíč"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Aktualizovat informace"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Současná verze"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Nejnovější verze"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Aktualizace je dostupná"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Aktualizovat plugin"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Pro odemčení aktualizací zadejte prosím výše svůj licenční klíč"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Zkontrolujte znovu"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Upozornění na aktualizaci"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Klonovat"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Vyberte jedno nebo více polí, které chcete klonovat"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Zobrazovat"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Určení stylu použitého pro vykreslení klonovaných polí"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr "Skupina (zobrazuje vybrané pole ve skupině v tomto poli)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Bezešvé (nahradí toto pole vybranými poli)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Štítky budou zobrazeny jako %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Prefix štítku pole"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Hodnoty budou uloženy jako %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Prefix jména pole"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Neznámé pole"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Skupina neznámých polí"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Všechna pole z skupiny polí %s"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:463
msgid "Add Row"
msgstr "Přidat řádek"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:938
#: pro/fields/class-acf-field-flexible-content.php:1020
msgid "layout"
msgid_plural "layouts"
msgstr[0] "typ zobrazení"
msgstr[1] "typ zobrazení"
msgstr[2] "typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "typy zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:937
#: pro/fields/class-acf-field-flexible-content.php:1019
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Toto pole vyžaduje alespoň {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Toto pole má limit {max}{label}  {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} dostupný (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} povinný (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexibilní obsah vyžaduje minimálně jedno rozložení obsahu"

#: pro/fields/class-acf-field-flexible-content.php:302
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Klikněte na tlačítko \"%s\" níže pro vytvoření vlastního typu zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:427
msgid "Add layout"
msgstr "Přidat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:428
msgid "Remove layout"
msgstr "Odstranit typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:429
#: pro/fields/class-acf-field-repeater.php:296
msgid "Click to toggle"
msgstr "Klikněte pro přepnutí"

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder Layout"
msgstr "Změnit pořadí typu zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder"
msgstr "Změnit pořadí"

#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete Layout"
msgstr "Smazat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate Layout"
msgstr "Duplikovat typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New Layout"
msgstr "Přidat nový typ zobrazení"

#: pro/fields/class-acf-field-flexible-content.php:643
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:656
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:683
#: pro/fields/class-acf-field-repeater.php:459
msgid "Button Label"
msgstr "Nápis tlačítka"

#: pro/fields/class-acf-field-flexible-content.php:692
msgid "Minimum Layouts"
msgstr "Minimální rozložení"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Maximální rozložení"

#: pro/fields/class-acf-field-gallery.php:71
msgid "Add Image to Gallery"
msgstr "Přidat obrázek do galerie"

#: pro/fields/class-acf-field-gallery.php:72
msgid "Maximum selection reached"
msgstr "Maximální výběr dosažen"

#: pro/fields/class-acf-field-gallery.php:336
msgid "Length"
msgstr "Délka"

#: pro/fields/class-acf-field-gallery.php:379
msgid "Caption"
msgstr "Popisek"

#: pro/fields/class-acf-field-gallery.php:388
msgid "Alt Text"
msgstr "Alternativní text"

#: pro/fields/class-acf-field-gallery.php:559
msgid "Add to gallery"
msgstr "Přidat do galerie"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Bulk actions"
msgstr "Hromadné akce"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Sort by date uploaded"
msgstr "Řadit dle data nahrání"

#: pro/fields/class-acf-field-gallery.php:565
msgid "Sort by date modified"
msgstr "Řadit dle data změny"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Sort by title"
msgstr "Řadit dle názvu"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Reverse current order"
msgstr "Převrátit aktuální pořadí"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Close"
msgstr "Zavřít"

#: pro/fields/class-acf-field-gallery.php:639
msgid "Minimum Selection"
msgstr "Minimální výběr"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Maximum Selection"
msgstr "Maximální výběr"

#: pro/fields/class-acf-field-gallery.php:657
msgid "Insert"
msgstr "Vložit"

#: pro/fields/class-acf-field-gallery.php:658
msgid "Specify where new attachments are added"
msgstr "Určete, kde budou přidány nové přílohy"

#: pro/fields/class-acf-field-gallery.php:662
msgid "Append to the end"
msgstr "Přidat na konec"

#: pro/fields/class-acf-field-gallery.php:663
msgid "Prepend to the beginning"
msgstr "Přidat na začátek"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:656
msgid "Minimum rows reached ({min} rows)"
msgstr "Minimální počet řádků dosažen ({min} řádků)"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "Maximální počet řádků dosažen ({max} řádků)"

#: pro/fields/class-acf-field-repeater.php:333
msgid "Add row"
msgstr "Přidat řádek"

#: pro/fields/class-acf-field-repeater.php:334
msgid "Remove row"
msgstr "Odebrat řádek"

#: pro/fields/class-acf-field-repeater.php:412
msgid "Collapsed"
msgstr "Sbaleno"

#: pro/fields/class-acf-field-repeater.php:413
msgid "Select a sub field to show when row is collapsed"
msgstr "Zvolte dílčí pole, které se zobrazí při sbalení řádku"

#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum Rows"
msgstr "Minimum řádků"

#: pro/fields/class-acf-field-repeater.php:433
msgid "Maximum Rows"
msgstr "Maximum řádků"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Neexistuje stránka nastavení"

#: pro/options-page.php:51
msgid "Options"
msgstr "Konfigurace"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Nastavení aktualizováno"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Chcete-li povolit aktualizace, zadejte prosím licenční klíč na stránce <a "
"href=\"%s\">Aktualizace</a>. Pokud nemáte licenční klíč, přečtěte si <a href="
"\"%s\">podrobnosti a ceny</a>."

#. Plugin URI of the plugin/theme
#| msgid "http://www.advancedcustomfields.com/"
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
#| msgid "elliot condon"
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Disabled"
#~ msgstr "Zakázáno"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "<span class=\"count\">(%s)</span> zakázán"
#~ msgstr[1] "<span class=\"count\">(%s)</span> zakázány"
#~ msgstr[2] "<span class=\"count\">(%s)</span> zakázáno"

#~ msgid "Parent fields"
#~ msgstr "Rodičovské pole"

#~ msgid "Sibling fields"
#~ msgstr "Sesterské pole"

#~ msgid "See what's new in"
#~ msgstr "Co je nového v"

#~ msgid "version"
#~ msgstr "verze"

#~ msgid "Getting Started"
#~ msgstr "Začínáme"

#~ msgid "Field Types"
#~ msgstr "Typy polí"

#~ msgid "Functions"
#~ msgstr "Funkce"

#~ msgid "Actions"
#~ msgstr "Akce"

#~ msgid "'How to' guides"
#~ msgstr "Průvodce \"jak na to\""

#~ msgid "Tutorials"
#~ msgstr "Tutoriál"

#~ msgid "Created by"
#~ msgstr "Vytvořil/a"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Úspěch</b>. Nástroj pro import přidal %s skupin polí: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Upozornění</b>. Nástroj pro import rozpoznal %s již existujících "
#~ "skupin polí a ty byly ignorovány: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Aktualizovat ACF"

#~ msgid "Upgrade"
#~ msgstr "Aktualizovat"

#~ msgid "Error"
#~ msgstr "Chyba"

#~ msgid "Error."
#~ msgstr "Chyba."

#~ msgid "Drag and drop to reorder"
#~ msgstr "Chytněte a táhněte pro změnu pořadí"

#~ msgid "Taxonomy Term"
#~ msgstr "Taxonomie"

#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Pro usnadnění aktualizace se <a href=\"%s\">přihlaste do svého obchodu</"
#~ "a> a požádejte o bezplatnou kopii ACF PRO!"

#~ msgid "Under the Hood"
#~ msgstr "Pod kapotou"

#~ msgid "Smarter field settings"
#~ msgstr "Chytřejší nastavení pole"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr "ACF nyní ukládá nastavení polí jako individuální objekty"

#~ msgid "Better version control"
#~ msgstr "Lepší verzování"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "Nový automatický export do formátu JSON umožňuje, aby nastavení polí bylo "
#~ "verzovatelné"

#~ msgid "Swapped XML for JSON"
#~ msgstr "XML vyměněno za JSON"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "Import / Export nyní používá JSON místo XML"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "Bylo přidáno nové pole pro vkládání obsahu"

#~ msgid "New Gallery"
#~ msgstr "Nová galerie"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "Pole pro galerii prošlo potřebovaným vylepšením vzhledu"

#~ msgid "Relationship Field"
#~ msgstr "Vztahová pole"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "Nastavení nových polí pro \"Filtry\" (vyhledávání, typ příspěvku, "
#~ "taxonomie)"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Nová skupina archivů v poli pro výběr page_link"

#~ msgid "Better Options Pages"
#~ msgstr "Vylepšená stránka nastavení"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Nové funkce pro stránku nastavení umožňují vytvoření stránek obou "
#~ "rodičovských i podřízených menu"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Exportujte skupiny polí do PHP"

#~ msgid "Download export file"
#~ msgstr "Stáhnout soubor s exportem"

#~ msgid "Generate export code"
#~ msgstr "Generovat kód pro exportu"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Aktualizace databáze Advanced Custom Fields"

#~ msgid "Upgrading data to"
#~ msgstr "Aktualizace dat na"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Než začnete používat nové úžasné funkce, aktualizujte databázi na "
#~ "nejnovější verzi."

#~ msgid "See what's new"
#~ msgstr "Podívejte se, co je nového"

#~ msgid "Show a different month"
#~ msgstr "Zobrazit jiný měsíc"

#~ msgid "Return format"
#~ msgstr "Formát návratu"

#~ msgid "uploaded to this post"
#~ msgstr "nahrán k tomuto příspěvku"

#~ msgid "File Size"
#~ msgstr "Velikost souboru"

#~ msgid "No File selected"
#~ msgstr "Nebyl vybrán žádný soubor"

#~ msgid "Locating"
#~ msgstr "Určování polohy"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Berte prosím na vědomí, že veškerý text musí projít přes funkce "
#~ "wordpressu "

#~ msgid "No embed found for the given URL."
#~ msgstr "Pro danou adresu URL nebyl nalezen žádný embed."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Dosaženo minimálního množství hodnot ( {min} hodnot )"

#~ msgid "Warning"
#~ msgstr "Varování"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Pole záložky se zobrazí nesprávně, pokud je přidáno do opakovače v "
#~ "tabulkovém stylu nebo do flexibilního pole"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Chcete-li lépe uspořádat obrazovku úprav, použijte seskupování polí "
#~ "pomocí Záložek."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Všechna pole následující po této záložce (až po další záložku nebo konec "
#~ "výpisu) budou seskupena a jako nadpis bude použit štítek záložky."

#~ msgid "Add new %s "
#~ msgstr "Přidat novou %s "

#~ msgid "None"
#~ msgstr "Žádný"

#~ msgid "eg. Show extra content"
#~ msgstr "např. Zobrazit dodatečný obsah"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Chyba připojení</b>. Omlouváme se, zkuste to znovu"

#~ msgid "Save Options"
#~ msgstr "Uložit nastavení"

#~ msgid "License"
#~ msgstr "Licence"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Pro odemčení aktualizací prosím zadejte níže svůj licenční klíč. Pokud "
#~ "nemáte licenční klíč, prosím navštivte"

#~ msgid "details & pricing"
#~ msgstr "detaily a ceny"

#~ msgid "remove {layout}?"
#~ msgstr "odstranit {layout}?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Toto pole vyžaduje alespoň {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Maximální {label} limit dosažen ({max} {identifier})"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "Custom field updated."
#~ msgstr "Vlastní pole aktualizováno."

#~ msgid "Custom field deleted."
#~ msgstr "Vlastní pole smazáno."

#~ msgid "Field group restored to revision from %s"
#~ msgstr "Skupina polí obnovena z revize %s"

#~ msgid "Error: Field Type does not exist!"
#~ msgstr "Chyba: Typ pole neexistuje!"

#~ msgid "Full"
#~ msgstr "Plný"

#~ msgid "No ACF groups selected"
#~ msgstr "Nejsou vybrány žádné ACF skupiny"

#~ msgid "Add Fields to Edit Screens"
#~ msgstr "Přidat pole na obrazovky úprav"

#~ msgid "Customise the edit page"
#~ msgstr "Přizpůsobit stránku úprav"

#~ msgid "Parent Page"
#~ msgstr "Rodičovská stránka"

#~ msgid "Child Page"
#~ msgstr "Podstránka"

#~ msgid "Normal"
#~ msgstr "Normální"

#~ msgid "Standard Metabox"
#~ msgstr "Standardní metabox"

#~ msgid "No Metabox"
#~ msgstr "Žádný metabox"

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr ""
#~ "Přečtěte si dokumentaci, naučte se funkce a objevte zajímavé tipy &amp; "
#~ "triky pro váš další webový projekt."

#~ msgid "Visit the ACF website"
#~ msgstr "Navštívit web ACF"

#~ msgid "Vote"
#~ msgstr "Hlasujte"

#~ msgid "Follow"
#~ msgstr "Následujte"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "Ověřování selhalo. Jedno nebo více polí níže je povinné."

#~ msgid "Add File to Field"
#~ msgstr "+ Přidat soubor do pole"

#~ msgid "Add Image to Field"
#~ msgstr "Přidat obrázek do pole"

#~ msgid "Attachment updated"
#~ msgstr "Příloha aktualizována."

#~ msgid "No Custom Field Group found for the options page"
#~ msgstr "Žádná vlastní skupina polí nebyla pro stránku konfigurace nalezena"

#~ msgid "Repeater field deactivated"
#~ msgstr "Opakovací pole deaktivováno"

#~ msgid "Options page deactivated"
#~ msgstr "Stránka konfigurace deaktivována"

#~ msgid "Flexible Content field deactivated"
#~ msgstr "Pole flexibilního pole deaktivováno"

#~ msgid "Gallery field deactivated"
#~ msgstr "Pole galerie deaktivováno"

#~ msgid "Repeater field activated"
#~ msgstr "Opakovací pole aktivováno"

#~ msgid "Options page activated"
#~ msgstr "Stránka konfigurace aktivována"

#~ msgid "Flexible Content field activated"
#~ msgstr "Pole flexibilního obsahu aktivováno"

#~ msgid "Gallery field activated"
#~ msgstr "Pole galerie aktivováno"

#~ msgid "License key unrecognised"
#~ msgstr "Licenční klíč nebyl rozpoznán"

#~ msgid "Activate Add-ons."
#~ msgstr "Aktivovat přídavky."

#~ msgid ""
#~ "Add-ons can be unlocked by purchasing a license key. Each key can be used "
#~ "on multiple sites."
#~ msgstr ""
#~ "Přídavky mohou být odemčeny zakoupením licenčního klíče. Každý klíč může "
#~ "být použit na více webech."

#~ msgid "Find Add-ons"
#~ msgstr "Hledat přídavky"

#~ msgid "Activation Code"
#~ msgstr "Aktivační kód"

#~ msgid "Repeater Field"
#~ msgstr "Opakovací pole"

#~ msgid "Deactivate"
#~ msgstr "Deaktivovat"

#~ msgid "Activate"
#~ msgstr "Aktivovat"

#~ msgid "Flexible Content Field"
#~ msgstr "Pole flexibilního obsahu"

#~ msgid "Gallery Field"
#~ msgstr "Pole galerie"

#~ msgid "Export Field Groups to XML"
#~ msgstr "Exportovat skupiny polí do XML"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr ""
#~ "ACF vytvoří soubor .xml exportu, který je kompatibilní s originálním "
#~ "importním pluginem WP."

#~ msgid ""
#~ "Imported field groups <b>will</b> appear in the list of editable field "
#~ "groups. This is useful for migrating fields groups between Wp websites."
#~ msgstr ""
#~ "Importované skupiny polí <b>budou</b> zobrazeny v seznamu upravitelných "
#~ "skupin polí. Toto je užitečné pro přesouvání skupin polí mezi WP weby."

#~ msgid "Select field group(s) from the list and click \"Export XML\""
#~ msgstr "Vyberte skupinu(y) polí ze seznamu a klikněte na \"Export XML\""

#~ msgid "Save the .xml file when prompted"
#~ msgstr "Uložte .xml soubor při požádání"

#~ msgid "Navigate to Tools &raquo; Import and select WordPress"
#~ msgstr "Otevřete Nástroje &raquo; Import a vyberte WordPress"

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "Nainstalujte importní WP plugin, pokud jste o to požádáni"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "Nahrajte a importujte váš exportovaný .xml soubor"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "Vyberte vašeho uživatele a ignorujte možnost Importovat přílohy"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "To je vše! Veselé WordPressování!"

#~ msgid "Export XML"
#~ msgstr "Exportovat XML"

#~ msgid "ACF will create the PHP code to include in your theme."
#~ msgstr "ACF vytvoří PHP kód pro vložení do vaší šablony."

#~ msgid "Register Field Groups"
#~ msgstr "Registrovat skupiny polí"

#~ msgid ""
#~ "Registered field groups <b>will not</b> appear in the list of editable "
#~ "field groups. This is useful for including fields in themes."
#~ msgstr ""
#~ "Registrované skupiny polí <b>nebudou</b> zobrazeny v seznamu "
#~ "upravitelných skupin polí. Toto je užitečné při používání polí v "
#~ "šablonách."

#~ msgid ""
#~ "Please note that if you export and register field groups within the same "
#~ "WP, you will see duplicate fields on your edit screens. To fix this, "
#~ "please move the original field group to the trash or remove the code from "
#~ "your functions.php file."
#~ msgstr ""
#~ "Mějte prosím na paměti, že pokud exportujete a registrujete skupiny polí "
#~ "v rámci stejného WordPressu, uvidíte na obrazovkách úprav duplikovaná "
#~ "pole. Pro nápravu prosím přesuňte původní skupinu polí do koše nebo "
#~ "odstraňte kód ze souboru functions.php."

#~ msgid "Select field group(s) from the list and click \"Create PHP\""
#~ msgstr "Vyberte skupinu(y) polí ze seznamu a klikněte na \"Vytvořit  PHP\""

#~ msgid "Copy the PHP code generated"
#~ msgstr "Zkopírujte vygenerovaný PHP kód"

#~ msgid "Paste into your functions.php file"
#~ msgstr "Vložte jej do vašeho souboru functions.php"

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr ""
#~ "K aktivací kteréhokoli přídavku upravte a použijte kód na prvních "
#~ "několika řádcích."

#~ msgid "Back to settings"
#~ msgstr "Zpět na nastavení"

#~ msgid ""
#~ "/**\n"
#~ " * Activate Add-ons\n"
#~ " * Here you can enter your activation codes to unlock Add-ons to use in "
#~ "your theme. \n"
#~ " * Since all activation codes are multi-site licenses, you are allowed to "
#~ "include your key in premium themes. \n"
#~ " * Use the commented out code to update the database with your activation "
#~ "code. \n"
#~ " * You may place this code inside an IF statement that only runs on theme "
#~ "activation.\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Aktivovat přídavky\n"
#~ " * Zde můžete vložit váš aktivační kód pro odemčení přídavků k použití ve "
#~ "vaší šabloně. \n"
#~ " * Jelikož jsou všechny aktivační kódy licencovány pro použití na více "
#~ "webech, můžete je použít ve vaší premium šabloně. \n"
#~ " * Použijte zakomentovaný kód pro aktualizaci databáze s vaším aktivačním "
#~ "kódem. \n"
#~ " * Tento kód můžete vložit dovnitř IF konstrukce, která proběhne pouze po "
#~ "aktivaci šablony.\n"
#~ " */"

#~ msgid ""
#~ "/**\n"
#~ " * Register field groups\n"
#~ " * The register_field_group function accepts 1 array which holds the "
#~ "relevant data to register a field group\n"
#~ " * You may edit the array as you see fit. However, this may result in "
#~ "errors if the array is not compatible with ACF\n"
#~ " * This code must run every time the functions.php file is read\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Registrace skupiny polí\n"
#~ " * Funkce register_field_group akceptuje pole, které obsahuje relevatní "
#~ "data k registraci skupiny polí\n"
#~ " * Pole můžete upravit podle potřeb. Může to ovšem vyústit v pole "
#~ "nekompatibilní s ACF\n"
#~ " * Tento kód musí proběhnout při každém čtení souboru functions.php\n"
#~ " */"

#~ msgid "No field groups were selected"
#~ msgstr "Nebyly vybrány žádné skupiny polí"

#~ msgid "Advanced Custom Fields Settings"
#~ msgstr "Nastavení Pokročilých vlastních polí"

#~ msgid "requires a database upgrade"
#~ msgstr "vyžaduje aktualizaci databáze"

#~ msgid "why?"
#~ msgstr "proč?"

#~ msgid "Please"
#~ msgstr "Prosím"

#~ msgid "backup your database"
#~ msgstr "zálohujte svou databázi"

#~ msgid "then click"
#~ msgstr "a pak klikněte"

#~ msgid "Modifying field group options 'show on page'"
#~ msgstr "Úprava možnosti skupiny polí 'zobrazit na stránce'"

#~ msgid "Modifying field option 'taxonomy'"
#~ msgstr "Úprava možností pole 'taxonomie'"

#~ msgid "No choices to choose from"
#~ msgstr "Žádné možnosti, z nichž by bylo možné vybírat"

#~ msgid "Enter your choices one per line"
#~ msgstr "Vložte vaše možnosti po jedné na řádek"

#~ msgid "Red"
#~ msgstr "Červená"

#~ msgid "Blue"
#~ msgstr "Modrá"

#~ msgid "blue : Blue"
#~ msgstr "modra: Modrá"

#~ msgid "eg. dd/mm/yy. read more about"
#~ msgstr "např. dd/mm/yy. přečtěte si více"

#~ msgid "File Updated."
#~ msgstr "Soubor aktualizován."

#~ msgid "No File Selected"
#~ msgstr "Nebyl vybrán žádný soubor"

#~ msgid "Attachment ID"
#~ msgstr "ID přílohy"

#~ msgid "Media attachment updated."
#~ msgstr "Příloha aktualizována."

#~ msgid "No files selected"
#~ msgstr "Nebyly vybrány žádné soubory."

#~ msgid "Add Selected Files"
#~ msgstr "Přidat vybrané soubory"

#~ msgid "+ Add Row"
#~ msgstr "+ Přidat řádek"

#~ msgid "Field Order"
#~ msgstr "Pořadí pole"

#~ msgid ""
#~ "No fields. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."
#~ msgstr ""
#~ "Žádná pole. Klikněte na tlačítko \"+ Přidat podpole\" pro vytvoření "
#~ "prvního pole."

#~ msgid "Edit this Field"
#~ msgstr "Upravit toto pole"

#~ msgid "Read documentation for this field"
#~ msgstr "Přečtěte si dokumentaci pro toto pole"

#~ msgid "Docs"
#~ msgstr "Dokumenty"

#~ msgid "Duplicate this Field"
#~ msgstr "Duplikovat toto pole"

#~ msgid "Delete this Field"
#~ msgstr "Smazat toto pole"

#~ msgid "Save Field"
#~ msgstr "Uložit pole"

#~ msgid "Close Sub Field"
#~ msgstr "Zavřít podpole"

#~ msgid "+ Add Sub Field"
#~ msgstr "+ Přidat podpole"

#~ msgid "Thumbnail is advised"
#~ msgstr "Je doporučen náhled"

#~ msgid "Image Updated"
#~ msgstr "Obrázek aktualizován"

#~ msgid "Grid"
#~ msgstr "Mřížka"

#~ msgid "List"
#~ msgstr "Seznam"

#~ msgid "No images selected"
#~ msgstr "Není vybrán žádný obrázek"

#~ msgid "1 image selected"
#~ msgstr "1 vybraný obrázek"

#~ msgid "{count} images selected"
#~ msgstr "{count} vybraných obrázků"

#~ msgid "Image already exists in gallery"
#~ msgstr "Obrázek v galerii už existuje"

#~ msgid "Image Added"
#~ msgstr "Obrázek přidán"

#~ msgid "Image Updated."
#~ msgstr "Obrázek aktualizován."

#~ msgid "Image Object"
#~ msgstr "Objekt obrázku"

#~ msgid "Add selected Images"
#~ msgstr "Přidat vybrané obrázky"

#~ msgid "Filter from Taxonomy"
#~ msgstr "Filtrovat z taxonomie"

#~ msgid "Repeater Fields"
#~ msgstr "Opakovací pole"

#~ msgid "Table (default)"
#~ msgstr "Tabulka (výchozí)"

#~ msgid "Formatting"
#~ msgstr "Formátování"

#~ msgid "Define how to render html tags"
#~ msgstr "Definujte způsob vypisování HTML tagů"

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "Definujte způsob výpisu HTML tagů / nových řádků"

#~ msgid "auto &lt;br /&gt;"
#~ msgstr "auto &lt;br /&gt;"

#~ msgid "new_field"
#~ msgstr "nove_pole"

#~ msgid "Field Instructions"
#~ msgstr "Instrukce pole"

#~ msgid "Logged in User Type"
#~ msgstr "Typ přihlášeného uživatele"

#~ msgid "Page Specific"
#~ msgstr "Specifická stránka"

#~ msgid "Post Specific"
#~ msgstr "Specifický příspěvek"

#~ msgid "Taxonomy (Add / Edit)"
#~ msgstr "Taxonomie (přidat / upravit)"

#~ msgid "User (Add / Edit)"
#~ msgstr "Uživatel (přidat / upravit)"

#~ msgid "Media (Edit)"
#~ msgstr "Media (upravit)"

#~ msgid "match"
#~ msgstr "souhlasí"

#~ msgid "all"
#~ msgstr "vše"

#~ msgid "any"
#~ msgstr "libovolné"

#~ msgid "of the above"
#~ msgstr "z uvedeného"

#~ msgid "Unlock options add-on with an activation code"
#~ msgstr "Odemkněte přídavek konfigurace s aktivačním kódem"

#~ msgid "Field groups are created in order <br />from lowest to highest."
#~ msgstr ""
#~ "Skupiny polí jsou vytvořeny v pořadí <br /> od nejnižšího k nejvyššímu."

#~ msgid "<b>Select</b> items to <b>hide</b> them from the edit screen"
#~ msgstr "<b>Vybrat</b> položky pro <b>skrytí</b> z obrazovky úprav"

#~ msgid ""
#~ "If multiple field groups appear on an edit screen, the first field "
#~ "group's options will be used. (the one with the lowest order number)"
#~ msgstr ""
#~ "Pokud se na obrazovce úprav objeví několik skupin polí, bude použito "
#~ "nastavení první skupiny. (s nejnižším pořadovým číslem)"

#~ msgid "Everything Fields deactivated"
#~ msgstr "Všechna pole deaktivována"

#~ msgid "Everything Fields activated"
#~ msgstr "Všechna pole aktivována"

#~ msgid "Navigate to the"
#~ msgstr "Běžte na"

#~ msgid "and select WordPress"
#~ msgstr "a vyberte WordPress"

#~ msgid ""
#~ "Filter posts by selecting a post type<br />\n"
#~ "\t\t\t\tTip: deselect all post types to show all post type's posts"
#~ msgstr ""
#~ "Filtrovat příspěvky výběrem typu příspěvku<br />\n"
#~ "\t\t\t\tTip: zrušte výběr všech typů příspěvku pro zobrazení příspěvků "
#~ "všech typů příspěvků"

#~ msgid "Set to -1 for infinite"
#~ msgstr "Nastavte na -1 pro nekonečno"

#~ msgid "Row Limit"
#~ msgstr "Limit řádků"
