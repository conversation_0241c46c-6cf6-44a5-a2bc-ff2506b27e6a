msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.7.10\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2019-01-15 11:15+1000\n"
"PO-Revision-Date: 2019-02-06 15:35+0100\n"
"Last-Translator: Werbelinie AG <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: de_CH\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.2\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

# @ acf
#: acf.php:80
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

# @ acf
#: acf.php:362 includes/admin/admin.php:58
msgid "Field Groups"
msgstr "Feld-Gruppen"

# @ acf
#: acf.php:363
msgid "Field Group"
msgstr "Feld-Gruppe"

# @ acf
#: acf.php:364 acf.php:396 includes/admin/admin.php:59
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New"
msgstr "Erstellen"

# @ acf
#: acf.php:365
msgid "Add New Field Group"
msgstr "Neue Feld-Gruppe erstellen"

# @ acf
#: acf.php:366
msgid "Edit Field Group"
msgstr "Feld-Gruppe bearbeiten"

# @ acf
#: acf.php:367
msgid "New Field Group"
msgstr "Neue Feld-Gruppe"

# @ acf
#: acf.php:368
msgid "View Field Group"
msgstr "Feld-Gruppe anzeigen"

# @ acf
#: acf.php:369
msgid "Search Field Groups"
msgstr "Feld-Gruppen suchen"

# @ acf
#: acf.php:370
msgid "No Field Groups found"
msgstr "Keine Feld-Gruppen gefunden"

# @ acf
#: acf.php:371
msgid "No Field Groups found in Trash"
msgstr "Keine Feld-Gruppen im Papierkorb gefunden"

# @ acf
#: acf.php:394 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:529
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Felder"

# @ acf
#: acf.php:395
msgid "Field"
msgstr "Feld"

# @ acf
#: acf.php:397
msgid "Add New Field"
msgstr "Feld hinzufügen"

# @ acf
#: acf.php:398
msgid "Edit Field"
msgstr "Feld bearbeiten"

# @ acf
#: acf.php:399 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr "Neues Feld"

# @ acf
#: acf.php:400
msgid "View Field"
msgstr "Feld anzeigen"

# @ acf
#: acf.php:401
msgid "Search Fields"
msgstr "Felder suchen"

# @ acf
#: acf.php:402
msgid "No Fields found"
msgstr "Keine Felder gefunden"

# @ acf
#: acf.php:403
msgid "No Fields found in Trash"
msgstr "Keine Feld-Gruppen im Papierkorb gefunden"

#: acf.php:442 includes/admin/admin-field-group.php:402
#: includes/admin/admin-field-groups.php:586
msgid "Inactive"
msgstr "Inaktiv"

#: acf.php:447
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inaktiv <span class=\"count\">(%s)</span>"
msgstr[1] "Inaktiv <span class=\"count\">(%s)</span>"

# @ acf
#: includes/admin/admin-field-group.php:86
#: includes/admin/admin-field-group.php:87
#: includes/admin/admin-field-group.php:89
msgid "Field group updated."
msgstr "Feld-Gruppe aktualisiert."

# @ acf
#: includes/admin/admin-field-group.php:88
msgid "Field group deleted."
msgstr "Feld-Gruppe gelöscht."

# @ acf
#: includes/admin/admin-field-group.php:91
msgid "Field group published."
msgstr "Feld-Gruppe veröffentlicht."

# @ acf
#: includes/admin/admin-field-group.php:92
msgid "Field group saved."
msgstr "Feld-Gruppe gesichert."

# @ acf
#: includes/admin/admin-field-group.php:93
msgid "Field group submitted."
msgstr "Feld-Gruppe übertragen."

# @ acf
#: includes/admin/admin-field-group.php:94
msgid "Field group scheduled for."
msgstr "Feld-Gruppe geplant für."

# @ acf
#: includes/admin/admin-field-group.php:95
msgid "Field group draft updated."
msgstr "Entwurf der Feld-Gruppe aktualisiert."

# @ acf
#: includes/admin/admin-field-group.php:171
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Der Feldname darf nicht mit \"field_\" beginnen"

# @ acf
#: includes/admin/admin-field-group.php:172
msgid "This field cannot be moved until its changes have been saved"
msgstr "Diese Feld kann nicht verschoben werden, bevor es gesichert wurde"

# @ acf
#: includes/admin/admin-field-group.php:173
msgid "Field group title is required"
msgstr "Es ist ein Titel für die Feld-Gruppe erforderlich"

# @ acf
#: includes/admin/admin-field-group.php:174
msgid "Move to trash. Are you sure?"
msgstr "Wirklich in den Papierkorb verschieben?"

# @ acf
#: includes/admin/admin-field-group.php:175
msgid "No toggle fields available"
msgstr "Es liegen keine Auswahl-Feld-Typen vor"

# @ acf
#: includes/admin/admin-field-group.php:176
msgid "Move Custom Field"
msgstr "Benutzerdefiniertes Feld verschieben"

# @ acf
#: includes/admin/admin-field-group.php:177
msgid "Checked"
msgstr "Ausgewählt"

# @ acf
#: includes/admin/admin-field-group.php:178 includes/api/api-field.php:320
msgid "(no label)"
msgstr "(ohne Beschreibung)"

# @ acf
#: includes/admin/admin-field-group.php:179
msgid "(this field)"
msgstr "(Dieses Feld)"

# @ acf
#: includes/admin/admin-field-group.php:180
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "kopieren"

# @ acf
#: includes/admin/admin-field-group.php:181
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3998
msgid "or"
msgstr "oder"

# @ acf
#: includes/admin/admin-field-group.php:182
msgid "Null"
msgstr "Null"

# @ acf
#: includes/admin/admin-field-group.php:221
msgid "Location"
msgstr "Position"

#: includes/admin/admin-field-group.php:222
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Einstellungen"

#: includes/admin/admin-field-group.php:372
msgid "Field Keys"
msgstr "Feldschlüssel"

#: includes/admin/admin-field-group.php:402
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Aktiviert"

# @ acf
#: includes/admin/admin-field-group.php:771
msgid "Move Complete."
msgstr "Verschieben erfolgreich abgeschlossen."

# @ acf
#: includes/admin/admin-field-group.php:772
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Das Feld \"%s\" wurde in die %s Feld-Gruppe verschoben"

# @ acf
#: includes/admin/admin-field-group.php:773
msgid "Close Window"
msgstr "Schliessen"

# @ acf
#: includes/admin/admin-field-group.php:814
msgid "Please select the destination for this field"
msgstr "In welche Feld-Gruppe solle dieses Feld verschoben werden"

# @ acf
#: includes/admin/admin-field-group.php:821
msgid "Move Field"
msgstr "Feld verschieben"

#: includes/admin/admin-field-groups.php:89
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Veröffentlicht <span class=\"count\">(%s)</span>"
msgstr[1] "Veröffentlicht <span class=\"count\">(%s)</span>"

# @ acf
#: includes/admin/admin-field-groups.php:156
#, php-format
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Feld-Gruppe dupliziert."
msgstr[1] "%s Feld-Gruppen dupliziert."

# @ acf
#: includes/admin/admin-field-groups.php:243
#, php-format
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Feld-Gruppe synchronisiert."
msgstr[1] "%s Feld-Gruppen synchronisiert."

# @ acf
#: includes/admin/admin-field-groups.php:413
#: includes/admin/admin-field-groups.php:576
msgid "Sync available"
msgstr "Synchronisierung verfügbar"

# @ acf
#: includes/admin/admin-field-groups.php:526 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:372
msgid "Title"
msgstr "Titel"

# @ acf
#: includes/admin/admin-field-groups.php:527
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:399
msgid "Description"
msgstr "Beschreibung"

#: includes/admin/admin-field-groups.php:528
msgid "Status"
msgstr "Status"

# @ acf
#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:626
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""
"Passen Sie WordPress mit leistungsstarken, professionellen und intuitiven "
"Feldern an."

# @ acf
#: includes/admin/admin-field-groups.php:628
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Versionshinweise"

#: includes/admin/admin-field-groups.php:633
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Sieh dir die Neuerungen in <a href=\"%s\">Version%s</a> an."

# @ acf
#: includes/admin/admin-field-groups.php:636
msgid "Resources"
msgstr "Dokumentation (engl.)"

#: includes/admin/admin-field-groups.php:638
msgid "Website"
msgstr "Webseite"

#: includes/admin/admin-field-groups.php:639
msgid "Documentation"
msgstr "Dokumentation"

#: includes/admin/admin-field-groups.php:640
msgid "Support"
msgstr "Hilfe"

#: includes/admin/admin-field-groups.php:642
#: includes/admin/views/settings-info.php:84
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:647
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Danke für die Verwendung von <a href=\"%s\">ACF</a>."

# @ acf
#: includes/admin/admin-field-groups.php:686
msgid "Duplicate this item"
msgstr "Dieses Element duplizieren"

# @ acf
#: includes/admin/admin-field-groups.php:686
#: includes/admin/admin-field-groups.php:702
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate"
msgstr "Duplizieren"

# @ acf
#: includes/admin/admin-field-groups.php:719
#: includes/fields/class-acf-field-google-map.php:165
#: includes/fields/class-acf-field-relationship.php:593
msgid "Search"
msgstr "Suchen"

# @ acf
#: includes/admin/admin-field-groups.php:778
#, php-format
msgid "Select %s"
msgstr "%s auswählen"

# @ acf
#: includes/admin/admin-field-groups.php:786
msgid "Synchronise field group"
msgstr "Synchronisiere Feld-Gruppe"

# @ acf
#: includes/admin/admin-field-groups.php:786
#: includes/admin/admin-field-groups.php:816
msgid "Sync"
msgstr "Synchronisieren"

#: includes/admin/admin-field-groups.php:798
msgid "Apply"
msgstr "Anwenden"

# @ acf
#: includes/admin/admin-field-groups.php:816
msgid "Bulk Actions"
msgstr "Massenverarbeitung"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Werkzeuge"

# @ acf
#: includes/admin/admin-upgrade.php:47 includes/admin/admin-upgrade.php:94
#: includes/admin/admin-upgrade.php:156
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Aktualisiere Datenbank"

# @ acf
#: includes/admin/admin-upgrade.php:180
msgid "Review sites & upgrade"
msgstr "Übersicht Seiten & Aktualisierungen"

# @ acf
#: includes/admin/admin.php:54 includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "Eigene Felder"

# @ acf
#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Info"

# @ acf
#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Was gibt es Neues"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Feld-Gruppen exportieren"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "PHP generieren"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Keine Feld-Gruppe ausgewählt"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Eine Feldgruppe exportiert."
msgstr[1] "%s Feldgruppen exportiert."

# @ acf
#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Felder-Gruppen auswählen"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Entscheide zuerst welche Felder-Gruppen Du exportieren möchtest und wähle im "
"Anschluss das Format in das exportiert werden soll. Klicke den \"JSON-Datei "
"exportieren\"-Button, um eine JSON-Datei zu erhalten, welche Du dann in "
"einer anderen ACF-Installation importieren kannst. Wähle den \"Erstelle PHP-"
"Code\"-Button, um PHP-Code zu erhalten, den Du im Anschluss in der functions."
"php Deines Themes einfügen kannst."

# @ acf
#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Datei exportieren"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Der nachfolgende Code kann dazu verwendet werden eine lokale Version der "
"ausgewählten Feld-Gruppe(n) zu registrieren. Eine lokale Feld-Gruppe bietet "
"viele Vorteile; schnellere Ladezeiten, Versionskontrolle sowie dynamische "
"Felder und Einstellungen. Kopiere einfach folgenden Code und füge ihn in die "
"functions.php oder eine externe Datei in Deinem Theme ein."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "In Zwischenablage kopieren"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "Kopiert"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Feld-Gruppen importieren"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:61
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Wähle die Advanced Custom Fields JSON-Datei aus, welche Du importieren "
"möchtest. Nach dem Klicken des Importieren-Buttons wird ACF die Felder-"
"Gruppen hinzufügen."

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:66
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "Datei auswählen"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:76
msgid "Import File"
msgstr "Datei importieren"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:99
#: includes/fields/class-acf-field-file.php:170
msgid "No file selected"
msgstr "Keine Datei ausgewählt"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:109
msgid "Error uploading file. Please try again"
msgstr "Fehler beim Upload. Bitte erneut versuchen"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:115
msgid "Incorrect file type"
msgstr "Falscher Dateityp"

# @ acf
#: includes/admin/tools/class-acf-admin-tool-import.php:129
msgid "Import file empty"
msgstr "Die importierte Datei ist leer"

#: includes/admin/tools/class-acf-admin-tool-import.php:235
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Eine Feldgruppe importiert"
msgstr[1] "%s Feldgruppen importiert"

# @ acf
#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Bedingungen für die Anzeige"

# @ acf
#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Zeige dieses Feld, wenn"

# @ acf
#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr "und"

# @ acf
#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Regel-Gruppe hinzufügen"

# @ acf
#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:424
#: pro/fields/class-acf-field-repeater.php:294
msgid "Drag to reorder"
msgstr "Ziehen zum Sortieren"

# @ acf
#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Feld bearbeiten"

# @ acf
#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:359
msgid "Edit"
msgstr "Bearbeiten"

# @ acf
#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Feld duplizieren"

# @ acf
#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Feld in eine andere Gruppe verschieben"

# @ acf
#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Verschieben"

# @ acf
#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Feld löschen"

# @ acf
#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete"
msgstr "Löschen"

# @ acf
#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Bezeichnung"

# @ acf
#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Dieser Name wird in der Bearbeitungs-Ansicht eines Beitrags angezeigt"

# @ acf
#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Feld-Name"

# @ acf
#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"Nur ein Wort ohne Leerzeichen; es sind nur Unterstriche und Bindestriche als "
"Sonderzeichen erlaubt"

# @ acf
#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Feld-Typ"

# @ acf
#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Anweisungen"

# @ acf
#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "Anweisungen für Autoren werden in der Bearbeitungs-Ansicht angezeigt"

# @ acf
#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Erforderlich?"

# @ acf
#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Wrapper-Attribute"

# @ acf
#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "Breite"

# @ acf
#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "Klasse"

# @ acf
#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "ID"

# @ acf
#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Feld schliessen"

# @ acf
#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Reihenfolge"

# @ acf
#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:433
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Label"
msgstr "Name"

# @ acf
#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:939
#: pro/fields/class-acf-field-flexible-content.php:610
msgid "Name"
msgstr "Feld-Name"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Feld-Schlüssel"

# @ acf
#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Typ"

# @ acf
#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Es sind noch keine Felder angelegt. Klicke den <strong>+ Feld hinzufügen-"
"Button</strong> und erstelle Dein erstes Feld."

# @ acf
#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Feld hinzufügen"

# @ acf
#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Regeln"

# @ acf
#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Erstelle ein Regelwerk das festlegt welche Bearbeitungs-Ansichten diese Feld-"
"Gruppe nutzen dürfen"

# @ acf
#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Stil"

# @ acf
#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "WP-Metabox (Standard)"

# @ acf
#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Übergangslos ohne Metabox"

# @ acf
#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Position"

# @ acf
#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Nach dem Titel vor dem Inhalt"

# @ acf
#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Nach dem Inhalt (Standard)"

# @ acf
#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Seitlich neben dem Inhalt"

# @ acf
#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Platzierung Beschriftung"

# @ acf
#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Über dem Feld"

# @ acf
#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Links neben dem Feld"

# @ acf
#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Platzierung der Hinweise"

# @ acf
#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Unterhalb der Beschriftung"

# @ acf
#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Unterhalb der Felder"

# @ acf
#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Sortiernr."

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Feld-Gruppen mit einem niedrigeren Wert werden zuerst angezeigt"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Wird in der Feld-Gruppen-Liste angezeigt"

# @ acf
#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "Permalink"

# @ acf
#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "Inhalts-Editor"

# @ acf
#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "Kurzfassung"

# @ acf
#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "Diskussion"

# @ acf
#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "Kommentare"

# @ acf
#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "Revisionen"

# @ acf
#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "Kurzlink"

# @ acf
#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "Autor"

# @ acf
#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "Format"

# @ acf
#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "Seiten-Attribute"

# @ acf
#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:607
msgid "Featured Image"
msgstr "Beitragsbild"

# @ acf
#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "Kategorien"

# @ acf
#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "Schlagworte"

# @ acf
#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "Sende Trackbacks"

# @ acf
#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "Verstecken"

# @ acf
#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<strong>Ausgewählte</strong> Elemente werden <strong>versteckt</strong>."

# @ acf
#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Sind für einen Bearbeiten-Dialog mehrere Felder-Gruppen definiert, werden "
"die Optionen der ersten Felder-Gruppe angewendet (die mit der niedrigsten "
"Nummer für die Reihenfolge)."

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Die folgenden Seiten ben&ouml;tigen ein DB Upgrade. W&auml;hle jene aus, die "
"du aktualisieren willst und klicke dann %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "Seiten aktualisieren"

# @ acf
#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Seite"

# @ acf
#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Die Seite erfordert eine Datenbank-Aktualisierung von %s auf %s"

# @ acf
#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr "Seite ist aktuell"

# @ acf
#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Datenbank-Aktualisierung fertiggestellt. <a href=\"%s\">Zum Netzwerk "
"Dashboard</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr ""
"Bitte wählen Sie mindestens eine Seite aus, um ein Upgrade durchzuführen."

# @ acf
#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Es wird dringend dazu angeraten, dass Du Deine Datenbank sicherst, bevor Du "
"fortfährst. Bist Du sicher, dass Du die Aktualisierung jetzt durchführen "
"willst?"

# @ acf
#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr "Aktualisiere Daten auf Version %s"

# @ default
#: includes/admin/views/html-admin-page-upgrade-network.php:167
msgid "Upgrade complete."
msgstr "Upgrade abgeschlossen"

#: includes/admin/views/html-admin-page-upgrade-network.php:176
#: includes/admin/views/html-admin-page-upgrade-network.php:185
#: includes/admin/views/html-admin-page-upgrade.php:78
#: includes/admin/views/html-admin-page-upgrade.php:87
msgid "Upgrade failed."
msgstr "Upgrade gescheitert."

# @ acf
#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Lese anstehende Aufgaben für die Aktualisierung..."

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Datenbank-Upgrade abgeschlossen. <a href=\"%s\">Schau was neu ist</a>"

# @ acf
#: includes/admin/views/html-admin-page-upgrade.php:116
#: includes/ajax/class-acf-ajax-upgrade.php:33
msgid "No updates available."
msgstr "Keine Aktualisierungen verfügbar."

#: includes/admin/views/html-admin-tools.php:21
msgid "Back to all tools"
msgstr "Zurück zu allen Werkzeugen"

# @ acf
#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Zeige diese Felder, wenn"

# @ acf
#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Wiederholung"

# @ acf
#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Flexible Inhalte"

# @ acf
#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galerie"

# @ acf
#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Options-Seite"

# @ acf
#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr "Es ist eine Datenbank-Aktualisierung notwendig"

# @ acf
#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Danke für die Aktualisierung auf %s v%s!"

#: includes/admin/views/html-notice-upgrade.php:22
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Diese Version enthält Verbesserungen an Ihrer Datenbank und erfordert ein "
"Upgrade."

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Stelle bitte ebenfalls sicher, dass alle Premium-Add-ons (%s) vorab auf die "
"neueste Version aktualisiert wurden."

# @ acf
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Zusatz-Module"

# @ acf
#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Download & Installieren"

# @ acf
#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Installiert"

# @ acf
#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Willkommen bei Advanced Custom Fields"

# @ acf
#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Danke fürs Aktualisieren! ACF %s ist besser denn je. Wir hoffen es wird Dir "
"genauso gut gefallen wie uns."

# @ acf
#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "Eine reibungslosere Erfahrung"

# @ acf
#: includes/admin/views/settings-info.php:19
msgid "Improved Usability"
msgstr "Verbesserte Benutzerfreundlichkeit"

# @ acf
#: includes/admin/views/settings-info.php:20
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Durch die Einführung der beliebten Select2 Bibliothek wurde sowohl die "
"Benutzerfreundlichkeit als auch die Geschwindigkeit einiger Feldtypen wie "
"Beitrags-Objekte, Seiten-Links, Taxonomien sowie von Auswahl-Feldern "
"signifikant verbessert."

# @ acf
#: includes/admin/views/settings-info.php:24
msgid "Improved Design"
msgstr "Verbessertes Design"

# @ acf
#: includes/admin/views/settings-info.php:25
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Viele Felder wurden visuell überarbeitet, damit ACF besser denn je aussieht! "
"Die markantesten Änderungen erfuhren das Galerie-, Beziehungs- sowie das "
"nagelneue oEmbed-Feld!"

# @ acf
#: includes/admin/views/settings-info.php:29
msgid "Improved Data"
msgstr "Verbesserte Datenstruktur"

# @ acf
#: includes/admin/views/settings-info.php:30
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Die Neugestaltung der Datenarchitektur erlaubt es, dass Felder unabhängig "
"von ihren übergeordneten Feldern existieren können. Dies ermöglicht, dass "
"Felder per Drag-and-Drop, in und aus ihren übergeordneten Feldern verschoben "
"werden können!"

# @ acf
#: includes/admin/views/settings-info.php:38
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Macht's gut Add-ons&hellip; Hallo PRO"

# @ acf
#: includes/admin/views/settings-info.php:41
msgid "Introducing ACF PRO"
msgstr "Wir dürfen vorstellen&hellip; ACF PRO"

# @ acf
#: includes/admin/views/settings-info.php:42
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""
"Wir haben die Art und Weise mit der die Premium-Funktionalität zur Verfügung "
"gestellt wird geändert - das \"wie\" dürfte Dich begeistern!"

# @ acf
#: includes/admin/views/settings-info.php:43
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Alle vier, vormals separat erhältlichen, Premium-Add-ons wurden in der neuen "
"<a href=\"%s\">Pro-Version von ACF</a> zusammengefasst. Besagte Premium-"
"Funktionalität, erhältlich in einer Einzel- sowie einer Entwickler-Lizenz, "
"ist somit erschwinglicher denn je!"

# @ acf
#: includes/admin/views/settings-info.php:47
msgid "Powerful Features"
msgstr "Leistungsstarke Funktionen"

# @ acf
#: includes/admin/views/settings-info.php:48
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO enthält leistungsstarke Funktionen wie wiederholbare Daten, Flexible "
"Inhalte-Layouts, ein wunderschönes Galerie-Feld sowie die Möglichkeit "
"zusätzliche Options-Seiten im Admin-Bereich anzulegen!"

# @ acf
#: includes/admin/views/settings-info.php:49
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Lies mehr über die <a href=\"%s\">ACF PRO Funktionen</a>."

# @ acf
#: includes/admin/views/settings-info.php:53
msgid "Easy Upgrading"
msgstr "Kinderleichte Aktualisierung"

#: includes/admin/views/settings-info.php:54
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr ""
"Die Aktualisierung auf ACF PRO ist einfach. Kaufen Sie einfach eine Lizenz "
"online und laden Sie das Plugin herunter!"

# @ acf
#: includes/admin/views/settings-info.php:55
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""
"Um möglichen Fragen vorzubeugen haben wir haben eine <a href=\"%s\"> "
"Anleitung für den Aktualisierungs-Prozess (Engl.)</a> verfasst. Sollten "
"dennoch Fragen aufgeworfen werden, kontaktiere bitte unser <a href=\"%s\"> "
"Support-Team </a>."

#: includes/admin/views/settings-info.php:64
msgid "New Features"
msgstr "Neue Features"

# @ acf
#: includes/admin/views/settings-info.php:69
msgid "Link Field"
msgstr "Link Feld"

#: includes/admin/views/settings-info.php:70
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr ""
"Das Link-Feld bietet eine einfache Möglichkeit, einen Link auszuwählen oder "
"zu definieren (URL, Titel, Ziel)."

# @ acf
#: includes/admin/views/settings-info.php:74
msgid "Group Field"
msgstr "Gruppenfeld"

#: includes/admin/views/settings-info.php:75
msgid "The Group field provides a simple way to create a group of fields."
msgstr ""
"Das Gruppenfeld bietet eine einfache Möglichkeit, eine Gruppe von Feldern zu "
"schaffen."

# @ acf
#: includes/admin/views/settings-info.php:79
msgid "oEmbed Field"
msgstr "oEmbed Feld"

#: includes/admin/views/settings-info.php:80
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr ""
"Das oEmbed-Feld ermöglicht eine einfache Möglichkeit, Videos, Bilder, "
"Tweets, Audio und andere Inhalte einzubetten."

# @ acf
#: includes/admin/views/settings-info.php:84
msgid "Clone Field"
msgstr "Klonen Feld"

#: includes/admin/views/settings-info.php:85
msgid "The clone field allows you to select and display existing fields."
msgstr ""
"Das Klon-Feld ermöglicht es Ihnen, bestehende Felder auszuwählen und "
"anzuzeigen."

# @ acf
#: includes/admin/views/settings-info.php:89
msgid "More AJAX"
msgstr "Mehr AJAX"

# @ acf
#: includes/admin/views/settings-info.php:90
msgid "More fields use AJAX powered search to speed up page loading."
msgstr ""
"Mehr Felder verwenden nun eine AJAX-basierte Suche, die die Ladezeiten von "
"Seiten deutlich verringert."

# @ acf
#: includes/admin/views/settings-info.php:94
msgid "Local JSON"
msgstr "Lokales JSON"

# @ acf
#: includes/admin/views/settings-info.php:95
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr ""
"Der neue automatische Export in JSON verbessert die Geschwindigkeit und "
"ermöglicht eine Synchronisierung."

# @ acf
#: includes/admin/views/settings-info.php:99
msgid "Easy Import / Export"
msgstr "Einfacher Import / Export"

#: includes/admin/views/settings-info.php:100
msgid "Both import and export can easily be done through a new tools page."
msgstr ""
"Sowohl der Import als auch der Export können einfach über eine neue "
"Werkzeugseite erfolgen."

# @ acf
#: includes/admin/views/settings-info.php:104
msgid "New Form Locations"
msgstr "Neue Positionen für Formulare"

# @ acf
#: includes/admin/views/settings-info.php:105
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr ""
"Felder können nun auch Menüs, Menüpunkten, Kommentaren, Widgets und allen "
"Benutzer-Formularen zugeordnet werden!"

# @ acf
#: includes/admin/views/settings-info.php:109
msgid "More Customization"
msgstr "Weitere Anpassungen"

#: includes/admin/views/settings-info.php:110
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr ""
"Neue PHP (und JS)-Aktionen und Filter wurden hinzugefügt, um mehr "
"Anpassungen zu ermöglichen."

#: includes/admin/views/settings-info.php:114
msgid "Fresh UI"
msgstr "Frisches UI"

#: includes/admin/views/settings-info.php:115
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr ""
"Ein Design-Refresh für das gesamte Plugin, inklusive neue Feldtypen, "
"Einstellungen und Design wurde eingeführt!"

# @ acf
#: includes/admin/views/settings-info.php:119
msgid "New Settings"
msgstr "Neue Einstellungen"

# @ acf
#: includes/admin/views/settings-info.php:120
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr ""
"Die Feldgruppen wurden um die Einstellungen für das Aktivieren und "
"Deaktivieren der Gruppe, die Platzierung von Beschriftungen und Anweisungen "
"sowie eine Beschreibung erweitert."

# @ acf
#: includes/admin/views/settings-info.php:124
msgid "Better Front End Forms"
msgstr "Verbesserte Front-End-Formulare"

# @ acf
#: includes/admin/views/settings-info.php:125
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr ""
"acf_form() kann jetzt einen neuen Beitrag direkt beim Senden erstellen "
"inklusive vieler neuer Einstellungsmöglichkeiten."

# @ acf
#: includes/admin/views/settings-info.php:129
msgid "Better Validation"
msgstr "Bessere Validierung"

# @ acf
#: includes/admin/views/settings-info.php:130
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr ""
"Die Formular-Validierung wird nun mit Hilfe von PHP + AJAX erledigt, anstatt "
"nur JS zu verwenden."

# @ acf
#: includes/admin/views/settings-info.php:134
msgid "Moving Fields"
msgstr "Verschiebbare Felder"

# @ acf
#: includes/admin/views/settings-info.php:135
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr ""
"Die neue Feld-Gruppen-Funktionalität erlaubt es ein Feld zwischen Gruppen "
"und übergeordneten Gruppen frei zu verschieben."

# @ acf
#: includes/admin/views/settings-info.php:146
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Wir glauben Du wirst die Änderungen in %s lieben."

# @ acf
#: includes/api/api-helpers.php:1011
msgid "Thumbnail"
msgstr "Miniaturbild"

# @ acf
#: includes/api/api-helpers.php:1012
msgid "Medium"
msgstr "Mittel"

# @ acf
#: includes/api/api-helpers.php:1013
msgid "Large"
msgstr "Gross"

# @ acf
#: includes/api/api-helpers.php:1062
msgid "Full Size"
msgstr "Volle Grösse"

# @ acf
#: includes/api/api-helpers.php:1831 includes/api/api-term.php:147
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(ohne Titel)"

# @ acf
#: includes/api/api-helpers.php:3919
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Die Breite des Bildes muss mindestens %dpx sein."

# @ acf
#: includes/api/api-helpers.php:3924
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Die Breite des Bildes darf %dpx nicht überschreiten."

# @ acf
#: includes/api/api-helpers.php:3940
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Die Höhe des Bildes muss mindestens %dpx sein."

# @ acf
#: includes/api/api-helpers.php:3945
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Die Höhe des Bild darf %dpx nicht überschreiten."

# @ acf
#: includes/api/api-helpers.php:3963
#, php-format
msgid "File size must be at least %s."
msgstr "Die Dateigrösse muss mindestens %s sein."

# @ acf
#: includes/api/api-helpers.php:3968
#, php-format
msgid "File size must must not exceed %s."
msgstr "Die Dateigrösse darf %s nicht überschreiten."

# @ acf
#: includes/api/api-helpers.php:4002
#, php-format
msgid "File type must be %s."
msgstr "Der Dateityp muss %s sein."

# @ acf
#: includes/assets.php:168
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Die vorgenommenen Änderungen gehen verloren wenn diese Seite verlassen wird"

#: includes/assets.php:171 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr "Auswählen"

#: includes/assets.php:172
msgctxt "verb"
msgid "Edit"
msgstr "Bearbeiten"

#: includes/assets.php:173
msgctxt "verb"
msgid "Update"
msgstr "Aktualisieren"

# @ acf
#: includes/assets.php:174
msgid "Uploaded to this post"
msgstr "Zu diesem Beitrag hochgeladen"

# @ acf
#: includes/assets.php:175
msgid "Expand Details"
msgstr "Details einblenden"

# @ acf
#: includes/assets.php:176
msgid "Collapse Details"
msgstr "Details ausblenden"

#: includes/assets.php:177
msgid "Restricted"
msgstr "Eingeschränkt"

# @ acf
#: includes/assets.php:178 includes/fields/class-acf-field-image.php:67
msgid "All images"
msgstr "Alle Bilder"

# @ acf
#: includes/assets.php:181
msgid "Validation successful"
msgstr "Überprüfung erfolgreich"

# @ acf
#: includes/assets.php:182 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Überprüfung fehlgeschlagen"

# @ acf
#: includes/assets.php:183
msgid "1 field requires attention"
msgstr "Für 1 Feld ist eine Aktualisierung notwendig"

# @ acf
#: includes/assets.php:184
#, php-format
msgid "%d fields require attention"
msgstr "Für %d Felder ist eine Aktualisierung notwendig"

# @ acf
#: includes/assets.php:187
msgid "Are you sure?"
msgstr "Sind Sie sicher?"

# @ acf
#: includes/assets.php:188 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Ja"

# @ acf
#: includes/assets.php:189 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Nein"

# @ acf
#: includes/assets.php:190 includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-image.php:141
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:360
#: pro/fields/class-acf-field-gallery.php:549
msgid "Remove"
msgstr "Entfernen"

#: includes/assets.php:191
msgid "Cancel"
msgstr "Abbrechen"

#: includes/assets.php:194
msgid "Has any value"
msgstr "Hat beliebigen Wert"

#: includes/assets.php:195
msgid "Has no value"
msgstr "Hat keinen Wert"

# @ acf
#: includes/assets.php:196
msgid "Value is equal to"
msgstr "Wert entspricht"

# @ acf
#: includes/assets.php:197
msgid "Value is not equal to"
msgstr "Wert entspricht nicht"

# @ acf
#: includes/assets.php:198
msgid "Value matches pattern"
msgstr "Wert entspricht regulärem Ausdruck"

#: includes/assets.php:199
msgid "Value contains"
msgstr "Wert enthält"

# @ acf
#: includes/assets.php:200
msgid "Value is greater than"
msgstr "Wert ist grösser als"

# @ acf
#: includes/assets.php:201
msgid "Value is less than"
msgstr "Wert ist geringer als"

#: includes/assets.php:202
msgid "Selection is greater than"
msgstr "Die Auswahl ist grösser als"

# @ acf
#: includes/assets.php:203
msgid "Selection is less than"
msgstr "Auswahl ist geringer als"

# @ acf
#: includes/assets.php:206 includes/forms/form-comment.php:166
#: pro/admin/admin-options-page.php:325
msgid "Edit field group"
msgstr "Feld-Gruppen bearbeiten"

# @ acf
#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "Feld-Typ existiert nicht"

#: includes/fields.php:308
msgid "Unknown"
msgstr "Unbekannt"

# @ acf
#: includes/fields.php:349
msgid "Basic"
msgstr "Grundlage"

# @ acf
#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "Inhalt"

# @ acf
#: includes/fields.php:351
msgid "Choice"
msgstr "Auswahl"

# @ acf
#: includes/fields.php:352
msgid "Relational"
msgstr "Relational"

# @ acf
#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

# @ acf
#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:567
#: pro/fields/class-acf-field-flexible-content.php:616
#: pro/fields/class-acf-field-repeater.php:443
msgid "Layout"
msgstr "Layout"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Akkordeon"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Offen"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Zeigen Sie dieses Akkordeon geöffnet an, wenn die Seite lädt."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Multi-expandieren"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Lassen Sie dieses Akkordeon öffnen, ohne andere zu schliessen."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Endpunkt"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definieren Sie einen Endpunkt für das bisherige Akkordeon zu stoppen. Dieses "
"Akkordeon wird nicht zu sehen sein."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Button Gruppe"

# @ acf
#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "Auswahlmöglichkeiten"

# @ acf
#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "Jede Auswahlmöglichkeit in separater Zeile eingeben."

# @ acf
#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Für eine bessere Darstellung, kannst Du auch einen Wert und dazu dessen "
"Beschriftung definieren:"

# @ acf
#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "rot : Rot"

# @ acf
#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:382
#: includes/fields/class-acf-field-taxonomy.php:784
#: includes/fields/class-acf-field-user.php:393
msgid "Allow Null?"
msgstr "NULL-Werte zulassen?"

# @ acf
#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-select.php:373
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:381
msgid "Default Value"
msgstr "Standardwert"

# @ acf
#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:150
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Appears when creating a new post"
msgstr "Erscheint bei der Erstellung eines neuen Beitrags"

# @ acf
#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Horizontal"

# @ acf
#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Vertikal"

# @ acf
#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:205
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Return Value"
msgstr "Rückgabewert"

# @ acf
#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Legt den Rückgabewert für das Front-End fest"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:432
msgid "Value"
msgstr "Wert"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:434
msgid "Both (Array)"
msgstr "Beide (Array)"

# @ acf
#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Checkbox"
msgstr "Checkbox"

# @ acf
#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Alle auswählen"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Neue Auswahlmöglichkeit hinzufügen"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Erlaube benutzerdefinierte Felder"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Erlaube das Hinzufügen benutzerdefinierter Werte"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Benutzerdefinierte Werte sichern"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr ""
"Sichere benutzerdefinierte Werte zu den Auswahlmöglichkeiten des Feldes"

# @ acf
#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:374
msgid "Enter each default value on a new line"
msgstr "Jeden Standardwert in einer neuen Zeile eingeben"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Auswählen"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Hänge eine zusätzliche Checkbox an mit der man alle Optionen auswählen kann"

# @ acf
#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Farbe"

# @ acf
#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Leeren"

# @ acf
#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Standard"

# @ acf
#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Farbe auswählen"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Aktuelle Farbe"

# @ acf
#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Datum"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Schliessen"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Heute"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Weiter"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Zurück"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "KW"

# @ acf
#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Darstellungs-Format"

# @ acf
#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Das Datums-Format für die Anzeige in der Bearbeitungs-Ansicht"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Benutzerdefiniert:"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Format sichern"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "Das verwendete Format, wenn der Wert gesichert wird"

# @ acf
#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:634
#: includes/fields/class-acf-field-select.php:427
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:412
msgid "Return Format"
msgstr "Rückgabewert"

# @ acf
#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "Das Datums-Format für die Ausgabe in den Template-Funktionen"

# @ acf
#: includes/fields/class-acf-field-date_picker.php:227
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "Die Woche beginnt am"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Datum/Uhrzeit"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Zeit setzen"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Zeit"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Stunde"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minute"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekunde"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisekunde"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunde"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Zeitzone"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Jetzt"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Schliessen"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Auswählen"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

# @ acf
#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "E-Mail"

# @ acf
#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Platzhalter-Text"

# @ acf
#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Platzhalter-Text solange keine Eingabe im Feld vorgenommen wurde"

# @ acf
#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Voranstellen"

# @ acf
#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:189
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Wird dem Eingabefeld vorangestellt"

# @ acf
#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Anhängen"

# @ acf
#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:198
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Wird dem Eingabefeld hinten angestellt"

# @ acf
#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Datei"

# @ acf
#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "Datei bearbeiten"

# @ acf
#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "Datei aktualisieren"

#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr "Dateiname"

# @ acf
#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:265
#: includes/fields/class-acf-field-image.php:294
#: pro/fields/class-acf-field-gallery.php:708
#: pro/fields/class-acf-field-gallery.php:737
msgid "File size"
msgstr "Dateigrösse"

# @ acf
#: includes/fields/class-acf-field-file.php:170
msgid "Add File"
msgstr "Datei hinzufügen"

# @ acf
#: includes/fields/class-acf-field-file.php:221
msgid "File Array"
msgstr "Datei-Array"

# @ acf
#: includes/fields/class-acf-field-file.php:222
msgid "File URL"
msgstr "Datei-URL"

# @ acf
#: includes/fields/class-acf-field-file.php:223
msgid "File ID"
msgstr "Datei-ID"

# @ acf
#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:230
#: pro/fields/class-acf-field-gallery.php:673
msgid "Library"
msgstr "Medienübersicht"

# @ acf
#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:674
msgid "Limit the media library choice"
msgstr "Beschränkt die Auswahl in der Medienübersicht"

# @ acf
#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:236
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:679
msgid "All"
msgstr "Alle"

# @ acf
#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-image.php:237
#: pro/fields/class-acf-field-gallery.php:680
msgid "Uploaded to post"
msgstr "Für den Beitrag hochgeladen"

# @ acf
#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:244
#: pro/fields/class-acf-field-gallery.php:687
msgid "Minimum"
msgstr "Minimum"

# @ acf
#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-file.php:256
msgid "Restrict which files can be uploaded"
msgstr ""
"Erlaubt nur das Hochladen von Dateien die die angegebenen Eigenschaften "
"erfüllen"

# @ acf
#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:273
#: pro/fields/class-acf-field-gallery.php:716
msgid "Maximum"
msgstr "Maximum"

# @ acf
#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:302
#: pro/fields/class-acf-field-gallery.php:745
msgid "Allowed file types"
msgstr "Erlaubte Datei-Formate"

# @ acf
#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:746
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Komma separierte Liste; ein leeres Feld bedeutet alle Dateiformate sind "
"erlaubt"

# @ acf
#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google Maps"

# @ acf
#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "Dieser Browser unterstützt keine Geo-Lokation"

# @ acf
#: includes/fields/class-acf-field-google-map.php:166
msgid "Clear location"
msgstr "Position löschen"

# @ acf
#: includes/fields/class-acf-field-google-map.php:167
msgid "Find current location"
msgstr "Aktuelle Position finden"

# @ acf
#: includes/fields/class-acf-field-google-map.php:170
msgid "Search for address..."
msgstr "Nach der Adresse suchen..."

# @ acf
#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-google-map.php:211
msgid "Center"
msgstr "Kartenmittelpunkt"

# @ acf
#: includes/fields/class-acf-field-google-map.php:201
#: includes/fields/class-acf-field-google-map.php:212
msgid "Center the initial map"
msgstr "Der Mittelpunkt der Ausgangskarte"

# @ acf
#: includes/fields/class-acf-field-google-map.php:223
msgid "Zoom"
msgstr "Zoom"

# @ acf
#: includes/fields/class-acf-field-google-map.php:224
msgid "Set the initial zoom level"
msgstr "Legt die Zoomstufe der Karte fest"

# @ acf
#: includes/fields/class-acf-field-google-map.php:233
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:699
#: pro/fields/class-acf-field-gallery.php:728
msgid "Height"
msgstr "Höhe"

# @ acf
#: includes/fields/class-acf-field-google-map.php:234
msgid "Customize the map height"
msgstr "Passt die Höhe der Karte an"

# @ acf
#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Gruppe"

# @ acf
#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:379
msgid "Sub Fields"
msgstr "Wiederholungsfelder"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Gib an, wie die ausgewählten Felder angezeigt werden sollen"

# @ acf
#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:627
#: pro/fields/class-acf-field-repeater.php:451
msgid "Block"
msgstr "Block"

# @ acf
#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:626
#: pro/fields/class-acf-field-repeater.php:450
msgid "Table"
msgstr "Tabelle"

# @ acf
#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:628
#: pro/fields/class-acf-field-repeater.php:452
msgid "Row"
msgstr "Reihe"

# @ acf
#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Bild"

# @ acf
#: includes/fields/class-acf-field-image.php:64
msgid "Select Image"
msgstr "Bild auswählen"

# @ acf
#: includes/fields/class-acf-field-image.php:65
msgid "Edit Image"
msgstr "Bild bearbeiten"

# @ acf
#: includes/fields/class-acf-field-image.php:66
msgid "Update Image"
msgstr "Bild aktualisieren"

# @ acf
#: includes/fields/class-acf-field-image.php:157
msgid "No image selected"
msgstr "Kein Bild ausgewählt"

# @ acf
#: includes/fields/class-acf-field-image.php:157
msgid "Add Image"
msgstr "Bild hinzufügen"

# @ acf
#: includes/fields/class-acf-field-image.php:211
msgid "Image Array"
msgstr "Bild-Array"

# @ acf
#: includes/fields/class-acf-field-image.php:212
msgid "Image URL"
msgstr "Bild-URL"

# @ acf
#: includes/fields/class-acf-field-image.php:213
msgid "Image ID"
msgstr "Bild-ID"

# @ acf
#: includes/fields/class-acf-field-image.php:220
msgid "Preview Size"
msgstr "Masse der Vorschau"

# @ acf
#: includes/fields/class-acf-field-image.php:221
msgid "Shown when entering data"
msgstr "Legt fest welche Masse die Vorschau in der Bearbeitungs-Ansicht hat"

# @ acf
#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Restrict which images can be uploaded"
msgstr ""
"Erlaubt nur das Hochladen von Bildern, die die angegebenen Eigenschaften "
"erfüllen"

# @ acf
#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:691
#: pro/fields/class-acf-field-gallery.php:720
msgid "Width"
msgstr "Breite"

# @ acf
#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Link"

# @ acf
#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Link auswählen"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Öffnet in einem neuen Fenster/Tab"

# @ acf
#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Link Array"

# @ acf
#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "Link URL"

# @ acf
#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Nachricht"

# @ acf
#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Neue Zeilen"

# @ acf
#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Legt fest wie Zeilenumbrüche gehandhabt werden"

# @ acf
#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Absätze automatisch hinzufügen"

# @ acf
#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Zeilenumbrüche ( &lt;br&gt; ) automatisch hinzufügen"

# @ acf
#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Keine Formatierung"

# @ acf
#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "HTML enkodieren"

# @ acf
#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Bei aktiver Option wird HTML Code als solcher angezeigt und nicht "
"interpretiert"

# @ acf
#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Numerisch"

# @ acf
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:158
msgid "Minimum Value"
msgstr "Mindestwert"

# @ acf
#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:168
msgid "Maximum Value"
msgstr "Maximalwert"

# @ acf
#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:178
msgid "Step Size"
msgstr "Schrittweite"

# @ acf
#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Wert muss eine Zahl sein"

# @ acf
#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Wert muss grösser oder gleich %d sein"

# @ acf
#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Wert muss kleiner oder gleich %d sein"

# @ acf
#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

# @ acf
#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "URL eingeben"

# @ acf
#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Masse"

# @ acf
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Seiten-Link"

# @ acf
#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Archive"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:961
msgid "Parent"
msgstr "Eltern"

# @ acf
#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:560
msgid "Filter by Post Type"
msgstr "Nach Post Types filtern"

# @ acf
#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:568
msgid "All post types"
msgstr "Alle verfügbaren Post Types"

# @ acf
#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:574
msgid "Filter by Taxonomy"
msgstr "Nach Taxonomien filtern"

# @ acf
#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:582
msgid "All taxonomies"
msgstr "Alle Taxonomien"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Archiv URLs erlauben"

# @ acf
#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:392
#: includes/fields/class-acf-field-user.php:403
msgid "Select multiple values?"
msgstr "Mehrere Werte auswählbar?"

# @ acf
#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Passwort"

# @ acf
#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:639
msgid "Post Object"
msgstr "Beitrags-Objekt"

# @ acf
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:640
msgid "Post ID"
msgstr "Beitrags-ID"

# @ acf
#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Radio-Button"

# @ acf
#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Sonstige"

# @ acf
#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr ""
"Fügt die Option 'Sonstige' hinzu, welche erlaubt, benutzerdefinierte Werte "
"hinzuzufügen"

# @ acf
#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "'Sonstige' speichern"

# @ acf
#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Füge 'Sonstige'-Werte zu den Auswahl Optionen hinzu"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Range"

# @ acf
#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Beziehung"

# @ acf
#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "Maximum der Einträge mit ({max} Einträge) erreicht"

# @ acf
#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "Lade"

# @ acf
#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "Keine Übereinstimmung gefunden"

# @ acf
#: includes/fields/class-acf-field-relationship.php:411
msgid "Select post type"
msgstr "Beitrag-Typ auswählen"

# @ acf
#: includes/fields/class-acf-field-relationship.php:420
msgid "Select taxonomy"
msgstr "Taxonomie auswählen"

# @ acf
#: includes/fields/class-acf-field-relationship.php:477
msgid "Search..."
msgstr "Suchen..."

# @ acf
#: includes/fields/class-acf-field-relationship.php:588
msgid "Filters"
msgstr "Filter"

# @ acf
#: includes/fields/class-acf-field-relationship.php:594
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Beitrags-Typ"

# @ acf
#: includes/fields/class-acf-field-relationship.php:595
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:754
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "Taxonomie"

# @ acf
#: includes/fields/class-acf-field-relationship.php:602
msgid "Elements"
msgstr "Elemente"

# @ acf
#: includes/fields/class-acf-field-relationship.php:603
msgid "Selected elements will be displayed in each result"
msgstr "Die ausgewählten Elemente werden in jedem Ergebnis mit angezeigt"

# @ acf
#: includes/fields/class-acf-field-relationship.php:614
msgid "Minimum posts"
msgstr "Min. Anzahl der Beiträge"

# @ acf
#: includes/fields/class-acf-field-relationship.php:623
msgid "Maximum posts"
msgstr "Max. Anzahl der Beiträge"

# @ acf
#: includes/fields/class-acf-field-relationship.php:727
#: pro/fields/class-acf-field-gallery.php:818
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s benötigt mindestens %s Selektion"
msgstr[1] "%s benötigt mindestens %s Selektionen"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:776
msgctxt "noun"
msgid "Select"
msgstr "Auswahlmenü"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Ein Resultat gefunden, mit Enter auswählen."

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d Resultate gefunden, benutze die Pfeiltasten um zu navigieren."

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Keine Übereinstimmungen gefunden"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Bitte eins oder mehrere Zeichen eingeben"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Bitte %d mehr Zeichen eingeben"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Bitte ein Zeichen löschen"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Bitte %d Zeichen löschen"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Du kannst du ein Resultat wählen"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Du kannst nur %d Resultate auswählen"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Lade weitere Resultate&hellip;"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Suche&hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Fehler beim Laden"

# @ acf
#: includes/fields/class-acf-field-select.php:402
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Modernes Auswahlfeld"

# @ acf
#: includes/fields/class-acf-field-select.php:412
msgid "Use AJAX to lazy load choices?"
msgstr "AJAX zum Laden der Einträge aktivieren?"

#: includes/fields/class-acf-field-select.php:428
msgid "Specify the value returned"
msgstr "Rückgabewert festlegen"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Trennelement"

# @ acf
#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Tab"

# @ acf
#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Platzierung Tabs"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Definiert einen Endpunkt an dem die vorangegangenen Tabs enden. Das ist der "
"Startpunkt für eine neue Gruppe an Tabs."

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Keine %s"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Select the taxonomy to be displayed"
msgstr "Wähle die Taxonomie, welche angezeigt werden soll"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Appearance"
msgstr "Anzeige"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Select the appearance of this field"
msgstr "Wähle das Aussehen für dieses Feld"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Multiple Values"
msgstr "Mehrere Werte"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Multi Select"
msgstr "Auswahlmenü"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Single Value"
msgstr "Einzelne Werte"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Radio Buttons"
msgstr "Radio Button"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:799
msgid "Create Terms"
msgstr "Neue Einträge erlauben"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:800
msgid "Allow new terms to be created whilst editing"
msgstr "Erlaube das Erstellen neuer Einträge beim Editieren"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Save Terms"
msgstr "Einträge speichern"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Connect selected terms to the post"
msgstr "Speichert die ausgewählten Einträge auch im Beitrag"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Load Terms"
msgstr "Einträge laden"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Load value from posts terms"
msgstr "Den Wert von den Einträgen des Beitrags laden"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:834
msgid "Term Object"
msgstr "Begriffs-Objekt"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:835
msgid "Term ID"
msgstr "Begriffs-ID"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:885
#, php-format
msgid "User unable to add new %s"
msgstr "Der Benutzer kann keine neue %s hinzufügen"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:895
#, php-format
msgid "%s already exists"
msgstr "%s ist bereits vorhanden"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:927
#, php-format
msgid "%s added"
msgstr "%s hinzugefügt"

# @ acf
#: includes/fields/class-acf-field-taxonomy.php:973
msgid "Add"
msgstr "Hinzufügen"

# @ acf
#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Text einzeilig"

# @ acf
#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Zeichenbegrenzung"

# @ acf
#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Ein leeres Eingabefeld bedeutet keine Begrenzung"

# @ acf
#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Text mehrzeilig"

# @ acf
#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Zeilenanzahl"

# @ acf
#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Definiert die Höhe des Textfelds"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Uhrzeit"

# @ acf
#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Ja/Nein"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Zeigt Text neben der Checkbox"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Wenn aktiv"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Angezeigter Text im aktiven Zustand"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "Wenn inaktiv"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "Angezeigter Text im inaktiven Zustand"

# @ acf
#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "URL"

# @ acf
#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Bitte eine gültige URL eingeben"

# @ acf
#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Benutzer"

# @ acf
#: includes/fields/class-acf-field-user.php:378
msgid "Filter by role"
msgstr "Filtere nach Benutzerrollen"

# @ acf
#: includes/fields/class-acf-field-user.php:386
msgid "All user roles"
msgstr "Alle Benutzerrollen"

# @ acf
#: includes/fields/class-acf-field-user.php:417
msgid "User Array"
msgstr "Benutzer-Array"

# @ acf
#: includes/fields/class-acf-field-user.php:418
msgid "User Object"
msgstr "Benutzer-Objekt"

# @ acf
#: includes/fields/class-acf-field-user.php:419
msgid "User ID"
msgstr "Benutzer ID"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "WYSIWYG-Editor"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual"
msgstr "Visuell"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:331
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:337
msgid "Click to initialize TinyMCE"
msgstr "Klicken um TinyMCE zu initialisieren"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Tabs"
msgstr "Tabs"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:395
msgid "Visual & Text"
msgstr "Visuell & Text"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Visual Only"
msgstr "Nur Visuell"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Text Only"
msgstr "Nur Text"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:404
msgid "Toolbar"
msgstr "Werkzeugleiste"

# @ acf
#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr "Button zum Hochladen von Medien anzeigen?"

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr "Initialisierung verzögern?"

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE wird nicht initialisiert bis das Feld geklickt wird"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "E-Mail bestätigen"

# @ acf
#: includes/forms/form-front.php:103 pro/fields/class-acf-field-gallery.php:591
#: pro/options-page.php:81
msgid "Update"
msgstr "Aktualisieren"

# @ acf
#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Beitrag aktualisiert"

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr "Spam erkannt"

# @ acf
#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Beitrag"

# @ acf
#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Seite"

# @ acf
#: includes/locations.php:96
msgid "Forms"
msgstr "Formulare"

# @ acf
#: includes/locations.php:243
msgid "is equal to"
msgstr "ist gleich"

# @ acf
#: includes/locations.php:244
msgid "is not equal to"
msgstr "ist ungleich"

# @ acf
#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Dateianhang"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Alle %s Formate"

# @ acf
#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Kommentar"

# @ acf
#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Aktuelle Benutzer-Rolle"

# @ acf
#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Super-Admin"

# @ acf
#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Aktueller Benutzer"

# @ acf
#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Ist angemeldet"

# @ acf
#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Ist im Front-End"

# @ acf
#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Ist im Back-End"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Menüelement"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Menü"

# @ acf
#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Menüpositionen"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Menüs"

# @ acf
#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Übergeordnete Seite"

# @ acf
#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Seiten-Template"

# @ acf
#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Standard-Template"

# @ acf
#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Seitentyp"

# @ acf
#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "Startseite"

# @ acf
#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "Beitrags-Seite"

# @ acf
#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "Seite ohne übergeordnete Seiten"

# @ acf
#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "Übergeordnete Seite (mit Unterseiten)"

# @ acf
#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "Unterseite (mit übergeordneter Seite)"

# @ acf
#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Beitrags-Kategorie"

# @ acf
#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Beitrags-Format"

# @ acf
#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Beitrags-Status"

# @ acf
#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Beitrags-Taxonomie"

# @ acf
#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Beitrags-Vorlage"

# @ acf
#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Benutzer-Formular"

# @ acf
#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Hinzufügen / Bearbeiten"

# @ acf
#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Registrieren"

# @ acf
#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Benutzerrolle"

# @ acf
#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widget"

# @ acf
#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s Wert ist notwendig"

# @ acf
#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

# @ acf
#: pro/admin/admin-options-page.php:198
msgid "Publish"
msgstr "Veröffentlichen"

# @ acf
#: pro/admin/admin-options-page.php:204
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Keine Feld-Gruppen für die Options-Seite gefunden. <a href=\"%s\">Erstelle "
"eine Feld-Gruppe</a>"

# @ acf
#: pro/admin/admin-updates.php:49
msgid "<b>Error</b>. Could not connect to update server"
msgstr ""
"<b>Fehler</b>. Verbindung zum Update-Server konnte nicht hergestellt werden"

# @ acf
#: pro/admin/admin-updates.php:118 pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Aktualisierungen"

#: pro/admin/admin-updates.php:191
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Fehler</b>. Konnte das Update-Paket nicht authentifizieren. Bitte "
"überprüfen Sie noch einmal oder reaktivieren Sie Ihre ACF PRO-Lizenz."

# @ acf
#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Lizenz deaktivieren"

# @ acf
#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Lizenz aktivieren"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Lizenzinformationen"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Bitte gib unten deinen Lizenzschlüssel ein, um Updates freizuschalten. "
"Solltest du keinen Lizenzschlüssel haben, findest du hier <a href=\"%s\" "
"target=\"_blank\">Details & Preise</a>."

# @ acf
#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Lizenzschlüssel"

# @ acf
#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Aktualisierungsinformationen"

# @ acf
#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Installierte Version"

# @ acf
#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Aktuellste Version"

# @ acf
#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Aktualisierung verfügbar"

# @ acf
#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Plugin aktualisieren"

# @ acf
#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr ""
"Bitte gib oben Deinen Lizenzschlüssel ein um die Update-Fähigkeit "
"freizuschalten"

# @ acf
#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Erneut suchen"

# @ acf
#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Aktualisierungs-Hinweis"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Klonen"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Wähle eines oder mehrere Felder aus, das/die du klonen willst"

# @ acf
#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Anzeige"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Gib an, wie die geklonten Felder ausgegeben werden sollen"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Gruppe (zeigt die ausgewählten Felder in einer Gruppe innerhalb dieses Felds "
"an)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Nahtlos (ersetzt dieses Feld mit den ausgewählten Feldern)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Bezeichnungen werden angezeigt als %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Präfix für Feld Bezeichnungen"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Werte werden gespeichert als %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Präfix für Feld Namen"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Unbekanntes Feld"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Unbekannte Feld-Gruppe"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Alle Felder der %s Feld-Gruppe"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:463
msgid "Add Row"
msgstr "Eintrag hinzufügen"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:938
#: pro/fields/class-acf-field-flexible-content.php:1020
msgid "layout"
msgid_plural "layouts"
msgstr[0] "Layout"
msgstr[1] "Layouts"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "Einträge"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:937
#: pro/fields/class-acf-field-flexible-content.php:1019
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Dieses Feld erfordert mindestens {min} {label} {identifier}"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Dieses Feld erlaubt höchstens {max} {label} {identifier}"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} möglich (max {max})"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} erforderlich (min {min})"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexibler Inhalt benötigt mindestens ein Layout"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:302
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Klicke \"%s\" zum Erstellen des Layouts"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:427
msgid "Add layout"
msgstr "Layout hinzufügen"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:428
msgid "Remove layout"
msgstr "Layout entfernen"

#: pro/fields/class-acf-field-flexible-content.php:429
#: pro/fields/class-acf-field-repeater.php:296
msgid "Click to toggle"
msgstr "Zum Auswählen anklicken"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder Layout"
msgstr "Layout sortieren"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder"
msgstr "Sortieren"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete Layout"
msgstr "Layout löschen"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate Layout"
msgstr "Layout duplizieren"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New Layout"
msgstr "Neues Layout hinzufügen"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:643
msgid "Min"
msgstr "Min"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:656
msgid "Max"
msgstr "Max"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:683
#: pro/fields/class-acf-field-repeater.php:459
msgid "Button Label"
msgstr "Button-Beschriftung"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:692
msgid "Minimum Layouts"
msgstr "Minimum Layouts"

# @ acf
#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Maximum Layouts"

# @ acf
#: pro/fields/class-acf-field-gallery.php:71
msgid "Add Image to Gallery"
msgstr "Bild zur Galerie hinzufügen"

# @ acf
#: pro/fields/class-acf-field-gallery.php:72
msgid "Maximum selection reached"
msgstr "Maximale Auswahl erreicht"

# @ acf
#: pro/fields/class-acf-field-gallery.php:338
msgid "Length"
msgstr "Länge"

#: pro/fields/class-acf-field-gallery.php:381
msgid "Caption"
msgstr "Beschriftung"

#: pro/fields/class-acf-field-gallery.php:390
msgid "Alt Text"
msgstr "Alt Text"

# @ acf
#: pro/fields/class-acf-field-gallery.php:562
msgid "Add to gallery"
msgstr "Zur Galerie hinzufügen"

# @ acf
#: pro/fields/class-acf-field-gallery.php:566
msgid "Bulk actions"
msgstr "Massenverarbeitung"

# @ acf
#: pro/fields/class-acf-field-gallery.php:567
msgid "Sort by date uploaded"
msgstr "Sortiere nach Upload-Datum"

# @ acf
#: pro/fields/class-acf-field-gallery.php:568
msgid "Sort by date modified"
msgstr "Sortiere nach Änderungs-Datum"

# @ acf
#: pro/fields/class-acf-field-gallery.php:569
msgid "Sort by title"
msgstr "Sortiere nach Titel"

# @ acf
#: pro/fields/class-acf-field-gallery.php:570
msgid "Reverse current order"
msgstr "Aktuelle Sortierung umkehren"

# @ acf
#: pro/fields/class-acf-field-gallery.php:588
msgid "Close"
msgstr "Schliessen"

# @ acf
#: pro/fields/class-acf-field-gallery.php:642
msgid "Minimum Selection"
msgstr "Minimale Auswahl"

# @ acf
#: pro/fields/class-acf-field-gallery.php:651
msgid "Maximum Selection"
msgstr "Maximale Auswahl"

#: pro/fields/class-acf-field-gallery.php:660
msgid "Insert"
msgstr "Einfügen"

#: pro/fields/class-acf-field-gallery.php:661
msgid "Specify where new attachments are added"
msgstr "Gib an, wo neue Anhänge eingefügt werden sollen"

#: pro/fields/class-acf-field-gallery.php:665
msgid "Append to the end"
msgstr "Am Schluss anhängen"

#: pro/fields/class-acf-field-gallery.php:666
msgid "Prepend to the beginning"
msgstr "Vor Beginn einfügen"

# @ acf
#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:656
msgid "Minimum rows reached ({min} rows)"
msgstr "Minimum der Einträge mit ({min} Reihen) erreicht"

# @ acf
#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "Maximum der Einträge mit ({max} Reihen) erreicht"

# @ acf
#: pro/fields/class-acf-field-repeater.php:333
msgid "Add row"
msgstr "Eintrag hinzufügen"

# @ acf
#: pro/fields/class-acf-field-repeater.php:334
msgid "Remove row"
msgstr "Eintrag löschen"

#: pro/fields/class-acf-field-repeater.php:412
msgid "Collapsed"
msgstr "Zugeklappt"

#: pro/fields/class-acf-field-repeater.php:413
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Wähle welches der Wiederholungsfelder im zugeklappten Zustand angezeigt "
"werden soll"

# @ acf
#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum Rows"
msgstr "Minimum der Einträge"

# @ acf
#: pro/fields/class-acf-field-repeater.php:433
msgid "Maximum Rows"
msgstr "Maximum der Einträge"

# @ acf
#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Keine Options-Seiten vorhanden"

# @ acf
#: pro/options-page.php:51
msgid "Options"
msgstr "Optionen"

# @ acf
#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Optionen aktualisiert"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Bitte gib auf der Seite <a href=\"%s\">Aktualisierungen</a> deinen "
"Lizenzschlüssel ein, um Updates zu aktivieren. Solltest du keinen "
"Lizenzschlüssel haben, findest du hier <a href=\"%s\" target=\"_blank"
"\">Details & Preise</a>."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

# @ acf
#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

# @ acf
#~ msgid "Parent fields"
#~ msgstr "Übergeordnete Felder"

# @ acf
#~ msgid "Sibling fields"
#~ msgstr "Geschwister-Felder"

# @ acf
#~ msgid "%s field group duplicated."
#~ msgid_plural "%s field groups duplicated."
#~ msgstr[0] "%s Feld-Gruppe dupliziert."
#~ msgstr[1] "%s Feld-Gruppen dupliziert."

# @ acf
#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "%s Feld-Gruppe synchronisiert."
#~ msgstr[1] "%s Feld-Gruppen synchronisiert."

#~ msgid "Error validating request"
#~ msgstr "Fehler beim Überprüfen der Anfrage"

# @ acf
#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr ""
#~ "<b>Fehler</b>. Die Liste der Zusatz-Module kann nicht geladen werden"

# @ acf
#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Advanced Custom Fields Datenbank Aktualisierung"

# @ acf
#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Bevor Du die tollen neuen Funktionen nutzen kannst muss die Datenbank "
#~ "aktualisiert werden."

# @ acf
#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Wir haben den Aktualisierungsprozess so einfach wie möglich gehalten; <a "
#~ "href=\"%s\">melde  Dich mit Deinem Store-Account an</a> und fordere ein "
#~ "Gratisexemplar von ACF PRO an!"

# @ acf
#~ msgid "Under the Hood"
#~ msgstr "Unter der Haube"

# @ acf
#~ msgid "Smarter field settings"
#~ msgstr "Intelligentere Feld-Einstellungen"

# @ acf
#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr ""
#~ "ACF speichert nun die Feld-Einstellungen als individuelle Beitrags-Objekte"

# @ acf
#~ msgid "Better version control"
#~ msgstr "Verbesserte Versionskontrolle"

# @ acf
#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "Die neue JSON Export Funktionalität erlaubt die Versionskontrolle von "
#~ "Feld-Einstellungen"

# @ acf
#~ msgid "Swapped XML for JSON"
#~ msgstr "JSON ersetzt XML"

# @ acf
#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "Das Import- und Export-Modul nutzt nun JSON anstelle XML"

# @ acf
#~ msgid "New Forms"
#~ msgstr "Neue Formulare"

# @ acf
#~ msgid "A new field for embedding content has been added"
#~ msgstr "Ein neues Feld für das Einbetten von Inhalten wurde hinzugefügt"

# @ acf
#~ msgid "New Gallery"
#~ msgstr "Neue Galerie"

# @ acf
#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr ""
#~ "Das Galerie-Feld wurde einem längst überfälligen Face-Lifting unterzogen"

# @ acf
#~ msgid "Relationship Field"
#~ msgstr "Beziehungs-Feld"

# @ acf
#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "Neue Einstellungen innerhalb des Beziehungs-Feldes um nach Suche, "
#~ "Beitrags-Typ und oder Taxonomie filtern zu können"

# @ acf
#~ msgid "New archives group in page_link field selection"
#~ msgstr ""
#~ "Im neuen Seitenlink-Feld werden alle Archiv-URL's der verfügbaren Custom "
#~ "Post Types in einer Options-Gruppe zusammengefasst"

# @ acf
#~ msgid "Better Options Pages"
#~ msgstr "Verbesserte Options-Seiten"

# @ acf
#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Neue Funktionen für die Options-Seite erlauben die Erstellung von Menüs "
#~ "für übergeordnete Seiten sowie Unterseiten"

# @ acf
#~ msgid "Export Field Groups to PHP"
#~ msgstr "Exportieren der Feld-Gruppen nach PHP"

# @ acf
#~ msgid "Download export file"
#~ msgstr "JSON-Datei exportieren"

# @ acf
#~ msgid "Generate export code"
#~ msgstr "Erstelle PHP-Code"

# @ acf
#~ msgid "Import"
#~ msgstr "Importieren"

# @ acf
#~ msgid "Locating"
#~ msgstr "Lokalisiere"

#~ msgid "Error."
#~ msgstr "Fehler."

# @ acf
#~ msgid "No embed found for the given URL."
#~ msgstr "Keine Inhalte für die eingegebene URL gefunden."

# @ acf
#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Minimum der Einträge mit ({min} Einträge) erreicht"

# @ acf
#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Ein Tab-Feld wird nicht korrekt dargestellt, wenn es zu einem "
#~ "Wiederholung- oder Flexible-Inhalte-Feld im Tabellen-Layout eingebunden "
#~ "ist"

# @ acf
#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Mit \"Tab Feldern\" können Felder für eine bessere Struktur im Editor in "
#~ "Tabs zusammengefasst werden."

# @ acf
#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Alle Felder, die auf dieses \"Tab Feld\" folgen (oder bis ein weiteres "
#~ "\"Tab Feld\" definiert ist), werden in einem Tab mit dem Namen dieses "
#~ "Felds zusammengefasst."

# @ acf
#~ msgid "None"
#~ msgstr "Nur Text"

# @ acf
#~ msgid "Taxonomy Term"
#~ msgstr "Taxonomie"

# @ acf
#~ msgid "remove {layout}?"
#~ msgstr "{layout} löschen?"

# @ acf
#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Dieses Feld erfordert mindestens {min} {identifier}"

# @ acf
#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Maximale {label}-Anzahl erreicht ({max} {identifier})"

# @ acf
#~ msgid "Getting Started"
#~ msgstr "Erste Schritte"

# @ acf
#~ msgid "Field Types"
#~ msgstr "Feld-Typen"

# @ acf
#~ msgid "Functions"
#~ msgstr "Funktionen"

# @ acf
#~ msgid "Actions"
#~ msgstr "Aktionen"

#~ msgid "How to"
#~ msgstr "Kurzanleitungen"

# @ acf
#~ msgid "Tutorials"
#~ msgstr "Tutorials"

#~ msgid "FAQ"
#~ msgstr "FAQ"

#~ msgid "Term meta upgrade not possible (termmeta table does not exist)"
#~ msgstr ""
#~ "Begriff Meta-Upgrade nicht möglich (termmeta Tabelle nicht existiert)"

# @ acf
#~ msgid "Error"
#~ msgstr "Fehler"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "Für 1 Feld ist eine Aktualisierung notwendig"
#~ msgstr[1] "Für %d Felder ist eine Aktualisierung notwendig"

#~ msgid ""
#~ "Error validating ACF PRO license URL (website does not match). Please re-"
#~ "activate your license"
#~ msgstr ""
#~ "Fehler bei der Validierung der ACF PRO Lizenz URL (Webseite stimmt nicht "
#~ "überein). Bitte Lizenz reaktivieren"

#~ msgid "Disabled"
#~ msgstr "Deaktiviert"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Deaktiviert <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Deaktiviert <span class=\"count\">(%s)</span>"

# @ acf
#~ msgid "'How to' guides"
#~ msgstr "Kurzanleitungen"

# @ acf
#~ msgid "Created by"
#~ msgstr "Erstellt von"

#~ msgid "Error loading update"
#~ msgstr "Fehler beim Laden des Update"

# @ acf
#~ msgid "See what's new"
#~ msgstr "Was ist neu"

# @ acf
#~ msgid "eg. Show extra content"
#~ msgstr "z.B. Zeige zusätzliche Inhalte"

#~ msgid ""
#~ "Error validating license URL (website does not match). Please re-activate "
#~ "your license"
#~ msgstr ""
#~ "Fehler bei der Überprüfung der Lizenz URL (Webseite stimmt nicht "
#~ "überein). Bitte reaktiviere deine Lizenz"

# @ acf
#~ msgid "Select"
#~ msgstr "Auswählen"

# @ acf
#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr ""
#~ "<b>Verbindungsfehler</b>. Entschuldige, versuche es bitte später noch "
#~ "einmal"

# @ acf
#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Erfolgreich</b>. Der Import hat %s Feld-Gruppen hinzugefügt: %s"

# @ acf
#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Warnung</b>. Der Import hat %s Feld-Gruppen erkannt, die schon "
#~ "vorhanden sind und diese ignoriert: %s"

# @ acf
#~ msgid "Upgrade ACF"
#~ msgstr "Aktualisiere ACF"

# @ acf
#~ msgid "Upgrade"
#~ msgstr "Aktualisieren"

# @ acf
#~ msgid ""
#~ "The following sites require a DB upgrade. Check the ones you want to "
#~ "update and then click “Upgrade Database”."
#~ msgstr ""
#~ "Die folgenden Seiten erfordern eine Datenbank-Aktualisierung. Markiere "
#~ "die gewünschten Seiten und klicke \\\"Aktualisiere Datenbank\\\"."

# @ acf
#~ msgid "Done"
#~ msgstr "Fertig"

# @ acf
#~ msgid "Today"
#~ msgstr "Heute"

# @ acf
#~ msgid "Show a different month"
#~ msgstr "Zeige einen anderen Monat"

# @ acf
#~ msgid "See what's new in"
#~ msgstr "Neuerungen in"

# @ acf
#~ msgid "version"
#~ msgstr "Version"

#~ msgid "Upgrading data to"
#~ msgstr "Aktualisiere Daten auf"

# @ acf
#~ msgid "Return format"
#~ msgstr "Rückgabe-Format"

# @ acf
#~ msgid "uploaded to this post"
#~ msgstr "zu diesem Beitrag hochgeladen"

# @ acf
#~ msgid "File Name"
#~ msgstr "Dateiname"

# @ acf
#~ msgid "File Size"
#~ msgstr "Dateigrösse"

# @ acf
#~ msgid "No File selected"
#~ msgstr "Keine Datei ausgewählt"

# @ acf
#~ msgid "Save Options"
#~ msgstr "Optionen speichern"

# @ acf
#~ msgid "License"
#~ msgstr "Lizenz"

# @ acf
#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Um die Aktualisierungs-Fähigkeit freizuschalten, trage bitte Deinen "
#~ "Lizenzschlüssel im darunterliegenden Feld ein. Solltest Du noch keinen "
#~ "Lizenzschlüssel besitzen, informiere Dich bitte hier über die"

# @ acf
#~ msgid "details & pricing"
#~ msgstr "Details und Preise."

# @ acf
#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "Um die Aktualisierungen freizuschalten, trage bitte Deinen "
#~ "Lizenzschlüssel auf der <a href=\"%s\">Aktualisierungen</a>-Seite ein. "
#~ "Solltest Du noch keinen Lizenzschlüssel besitzen, informiere Dich bitte "
#~ "hier über die <a href=\"%s\">Details und Preise</a>"

# @ acf
#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

# @ acf
#~ msgid "http://www.advancedcustomfields.com/"
#~ msgstr "http://www.advancedcustomfields.com/"

# @ acf
#~ msgid "elliot condon"
#~ msgstr "elliot condon"

# @ acf
#~ msgid "Drag and drop to reorder"
#~ msgstr "Mittels Drag-and-Drop die Reihenfolge ändern"

# @ acf
#~ msgid "Add new %s "
#~ msgstr "Neue %s "

#~ msgid "Sync Available"
#~ msgstr "Synchronisierung verfügbar"

# @ acf
#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Bitte beachte, dass der gesamte Text zuerst durch eine WordPress Funktion "
#~ "gefiltert wird. Siehe: "

# @ acf
#~ msgid "Warning"
#~ msgstr "Warnung"

# @ acf
#~ msgid "Show Field Keys"
#~ msgstr "Zeige Feld-Schlüssel"

# @ acf
#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr ""
#~ "Felder-Gruppen werden nach diesem Wert sortiert, vom niedrigsten zum "
#~ "höchsten Wert."

# @ acf
#~ msgid "Hide / Show All"
#~ msgstr "Alle Verstecken"

# @ acf
#~ msgid "5.2.6"
#~ msgstr "5.2.6"

# @ acf
#~ msgid "Sync Terms"
#~ msgstr "Einträge synchronisieren"
