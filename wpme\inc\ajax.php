<?php
// 示例：

function x_form()
{
    if (_res('action') == 'x_form') {
        $text =json_encode($_REQUEST);
        $client = new \GuzzleHttp\Client();
        $url = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8f45f213-1825-46fe-b185-4ff0b7524b56';
        $request = $client->request('POST', $url, ['json' => ['msgtype' => 'text','text' => ['content'=>$text]]]);
    }
    die;
}

add_action('wp_ajax_nopriv_x_form', 'x_form');
add_action('wp_ajax_x_form', 'x_form');
 
