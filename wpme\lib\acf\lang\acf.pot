#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2019-01-31 12:36+0100\n"
"PO-Revision-Date: 2015-06-11 13:00+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: en_AU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.1\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:80
msgid "Advanced Custom Fields"
msgstr ""

#: acf.php:363 includes/admin/admin.php:58
msgid "Field Groups"
msgstr ""

#: acf.php:364
msgid "Field Group"
msgstr ""

#: acf.php:365 acf.php:397 includes/admin/admin.php:59
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New"
msgstr ""

#: acf.php:366
msgid "Add New Field Group"
msgstr ""

#: acf.php:367
msgid "Edit Field Group"
msgstr ""

#: acf.php:368
msgid "New Field Group"
msgstr ""

#: acf.php:369
msgid "View Field Group"
msgstr ""

#: acf.php:370
msgid "Search Field Groups"
msgstr ""

#: acf.php:371
msgid "No Field Groups found"
msgstr ""

#: acf.php:372
msgid "No Field Groups found in Trash"
msgstr ""

#: acf.php:395 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:529
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr ""

#: acf.php:396
msgid "Field"
msgstr ""

#: acf.php:398
msgid "Add New Field"
msgstr ""

#: acf.php:399
msgid "Edit Field"
msgstr ""

#: acf.php:400 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr ""

#: acf.php:401
msgid "View Field"
msgstr ""

#: acf.php:402
msgid "Search Fields"
msgstr ""

#: acf.php:403
msgid "No Fields found"
msgstr ""

#: acf.php:404
msgid "No Fields found in Trash"
msgstr ""

#: acf.php:443 includes/admin/admin-field-group.php:402
#: includes/admin/admin-field-groups.php:586
msgid "Inactive"
msgstr ""

#: acf.php:448
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: includes/acf-field-functions.php:823
#: includes/admin/admin-field-group.php:178
msgid "(no label)"
msgstr ""

#: includes/acf-field-group-functions.php:816
#: includes/admin/admin-field-group.php:180
msgid "copy"
msgstr ""

#: includes/admin/admin-field-group.php:86
#: includes/admin/admin-field-group.php:87
#: includes/admin/admin-field-group.php:89
msgid "Field group updated."
msgstr ""

#: includes/admin/admin-field-group.php:88
msgid "Field group deleted."
msgstr ""

#: includes/admin/admin-field-group.php:91
msgid "Field group published."
msgstr ""

#: includes/admin/admin-field-group.php:92
msgid "Field group saved."
msgstr ""

#: includes/admin/admin-field-group.php:93
msgid "Field group submitted."
msgstr ""

#: includes/admin/admin-field-group.php:94
msgid "Field group scheduled for."
msgstr ""

#: includes/admin/admin-field-group.php:95
msgid "Field group draft updated."
msgstr ""

#: includes/admin/admin-field-group.php:171
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""

#: includes/admin/admin-field-group.php:172
msgid "This field cannot be moved until its changes have been saved"
msgstr ""

#: includes/admin/admin-field-group.php:173
msgid "Field group title is required"
msgstr ""

#: includes/admin/admin-field-group.php:174
msgid "Move to trash. Are you sure?"
msgstr ""

#: includes/admin/admin-field-group.php:175
msgid "No toggle fields available"
msgstr ""

#: includes/admin/admin-field-group.php:176
msgid "Move Custom Field"
msgstr ""

#: includes/admin/admin-field-group.php:177
msgid "Checked"
msgstr ""

#: includes/admin/admin-field-group.php:179
msgid "(this field)"
msgstr ""

#: includes/admin/admin-field-group.php:181
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3990
msgid "or"
msgstr ""

#: includes/admin/admin-field-group.php:182
msgid "Null"
msgstr ""

#: includes/admin/admin-field-group.php:221
msgid "Location"
msgstr ""

#: includes/admin/admin-field-group.php:222
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr ""

#: includes/admin/admin-field-group.php:372
msgid "Field Keys"
msgstr ""

#: includes/admin/admin-field-group.php:402
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr ""

#: includes/admin/admin-field-group.php:771
msgid "Move Complete."
msgstr ""

#: includes/admin/admin-field-group.php:772
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr ""

#: includes/admin/admin-field-group.php:773
msgid "Close Window"
msgstr ""

#: includes/admin/admin-field-group.php:814
msgid "Please select the destination for this field"
msgstr ""

#: includes/admin/admin-field-group.php:821
msgid "Move Field"
msgstr ""

#: includes/admin/admin-field-groups.php:89
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-field-groups.php:156
#, php-format
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-field-groups.php:243
#, php-format
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/admin-field-groups.php:413
#: includes/admin/admin-field-groups.php:576
msgid "Sync available"
msgstr ""

#: includes/admin/admin-field-groups.php:526 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:372
msgid "Title"
msgstr ""

#: includes/admin/admin-field-groups.php:527
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:399
msgid "Description"
msgstr ""

#: includes/admin/admin-field-groups.php:528
msgid "Status"
msgstr ""

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:626
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr ""

#: includes/admin/admin-field-groups.php:628
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr ""

#: includes/admin/admin-field-groups.php:633
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr ""

#: includes/admin/admin-field-groups.php:636
msgid "Resources"
msgstr ""

#: includes/admin/admin-field-groups.php:638
msgid "Website"
msgstr ""

#: includes/admin/admin-field-groups.php:639
msgid "Documentation"
msgstr ""

#: includes/admin/admin-field-groups.php:640
msgid "Support"
msgstr ""

#: includes/admin/admin-field-groups.php:642
#: includes/admin/views/settings-info.php:84
msgid "Pro"
msgstr ""

#: includes/admin/admin-field-groups.php:647
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr ""

#: includes/admin/admin-field-groups.php:686
msgid "Duplicate this item"
msgstr ""

#: includes/admin/admin-field-groups.php:686
#: includes/admin/admin-field-groups.php:702
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate"
msgstr ""

#: includes/admin/admin-field-groups.php:719
#: includes/fields/class-acf-field-google-map.php:165
#: includes/fields/class-acf-field-relationship.php:593
msgid "Search"
msgstr ""

#: includes/admin/admin-field-groups.php:778
#, php-format
msgid "Select %s"
msgstr ""

#: includes/admin/admin-field-groups.php:786
msgid "Synchronise field group"
msgstr ""

#: includes/admin/admin-field-groups.php:786
#: includes/admin/admin-field-groups.php:816
msgid "Sync"
msgstr ""

#: includes/admin/admin-field-groups.php:798
msgid "Apply"
msgstr ""

#: includes/admin/admin-field-groups.php:816
msgid "Bulk Actions"
msgstr ""

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr ""

#: includes/admin/admin-upgrade.php:47 includes/admin/admin-upgrade.php:94
#: includes/admin/admin-upgrade.php:156
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr ""

#: includes/admin/admin-upgrade.php:180
msgid "Review sites & upgrade"
msgstr ""

#: includes/admin/admin.php:54 includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr ""

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr ""

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] ""
msgstr[1] ""

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:52
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:62
msgid "Import File"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:85
#: includes/fields/class-acf-field-file.php:170
msgid "No file selected"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:93
msgid "Error uploading file. Please try again"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:98
msgid "Incorrect file type"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:107
msgid "Import file empty"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:138
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] ""
msgstr[1] ""

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr ""

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr ""

#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr ""

#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr ""

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:424
#: pro/fields/class-acf-field-repeater.php:294
msgid "Drag to reorder"
msgstr ""

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr ""

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:359
msgid "Edit"
msgstr ""

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr ""

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr ""

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr ""

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr ""

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete"
msgstr ""

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr ""

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr ""

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr ""

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr ""

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr ""

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr ""

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr ""

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr ""

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr ""

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr ""

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr ""

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr ""

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr ""

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:433
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Label"
msgstr ""

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:939
#: pro/fields/class-acf-field-flexible-content.php:610
msgid "Name"
msgstr ""

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr ""

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr ""

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr ""

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr ""

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr ""

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr ""

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr ""

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr ""

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr ""

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr ""

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr ""

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr ""

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr ""

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr ""

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr ""

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr ""

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr ""

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr ""

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr ""

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr ""

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr ""

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr ""

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr ""

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr ""

#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr ""

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr ""

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr ""

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr ""

#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr ""

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr ""

#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:607
msgid "Featured Image"
msgstr ""

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr ""

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr ""

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr ""

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr ""

#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""

#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:167
msgid "Upgrade complete."
msgstr ""

#: includes/admin/views/html-admin-page-upgrade-network.php:176
#: includes/admin/views/html-admin-page-upgrade-network.php:185
#: includes/admin/views/html-admin-page-upgrade.php:78
#: includes/admin/views/html-admin-page-upgrade.php:87
msgid "Upgrade failed."
msgstr ""

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr ""

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""

#: includes/admin/views/html-admin-page-upgrade.php:116
#: includes/ajax/class-acf-ajax-upgrade.php:33
msgid "No updates available."
msgstr ""

#: includes/admin/views/html-admin-tools.php:21
msgid "Back to all tools"
msgstr ""

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:22
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""

#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr ""

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr ""

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr ""

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr ""

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr ""

#: includes/admin/views/settings-info.php:19
msgid "Improved Usability"
msgstr ""

#: includes/admin/views/settings-info.php:20
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""

#: includes/admin/views/settings-info.php:24
msgid "Improved Design"
msgstr ""

#: includes/admin/views/settings-info.php:25
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""

#: includes/admin/views/settings-info.php:29
msgid "Improved Data"
msgstr ""

#: includes/admin/views/settings-info.php:30
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""

#: includes/admin/views/settings-info.php:38
msgid "Goodbye Add-ons. Hello PRO"
msgstr ""

#: includes/admin/views/settings-info.php:41
msgid "Introducing ACF PRO"
msgstr ""

#: includes/admin/views/settings-info.php:42
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""

#: includes/admin/views/settings-info.php:43
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""

#: includes/admin/views/settings-info.php:47
msgid "Powerful Features"
msgstr ""

#: includes/admin/views/settings-info.php:48
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""

#: includes/admin/views/settings-info.php:49
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr ""

#: includes/admin/views/settings-info.php:53
msgid "Easy Upgrading"
msgstr ""

#: includes/admin/views/settings-info.php:54
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr ""

#: includes/admin/views/settings-info.php:55
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""

#: includes/admin/views/settings-info.php:64
msgid "New Features"
msgstr ""

#: includes/admin/views/settings-info.php:69
msgid "Link Field"
msgstr ""

#: includes/admin/views/settings-info.php:70
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr ""

#: includes/admin/views/settings-info.php:74
msgid "Group Field"
msgstr ""

#: includes/admin/views/settings-info.php:75
msgid "The Group field provides a simple way to create a group of fields."
msgstr ""

#: includes/admin/views/settings-info.php:79
msgid "oEmbed Field"
msgstr ""

#: includes/admin/views/settings-info.php:80
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr ""

#: includes/admin/views/settings-info.php:84
msgid "Clone Field"
msgstr ""

#: includes/admin/views/settings-info.php:85
msgid "The clone field allows you to select and display existing fields."
msgstr ""

#: includes/admin/views/settings-info.php:89
msgid "More AJAX"
msgstr ""

#: includes/admin/views/settings-info.php:90
msgid "More fields use AJAX powered search to speed up page loading."
msgstr ""

#: includes/admin/views/settings-info.php:94
msgid "Local JSON"
msgstr ""

#: includes/admin/views/settings-info.php:95
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr ""

#: includes/admin/views/settings-info.php:99
msgid "Easy Import / Export"
msgstr ""

#: includes/admin/views/settings-info.php:100
msgid "Both import and export can easily be done through a new tools page."
msgstr ""

#: includes/admin/views/settings-info.php:104
msgid "New Form Locations"
msgstr ""

#: includes/admin/views/settings-info.php:105
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr ""

#: includes/admin/views/settings-info.php:109
msgid "More Customization"
msgstr ""

#: includes/admin/views/settings-info.php:110
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr ""

#: includes/admin/views/settings-info.php:114
msgid "Fresh UI"
msgstr ""

#: includes/admin/views/settings-info.php:115
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr ""

#: includes/admin/views/settings-info.php:119
msgid "New Settings"
msgstr ""

#: includes/admin/views/settings-info.php:120
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr ""

#: includes/admin/views/settings-info.php:124
msgid "Better Front End Forms"
msgstr ""

#: includes/admin/views/settings-info.php:125
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr ""

#: includes/admin/views/settings-info.php:129
msgid "Better Validation"
msgstr ""

#: includes/admin/views/settings-info.php:130
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr ""

#: includes/admin/views/settings-info.php:134
msgid "Moving Fields"
msgstr ""

#: includes/admin/views/settings-info.php:135
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr ""

#: includes/admin/views/settings-info.php:146
#, php-format
msgid "We think you'll love the changes in %s."
msgstr ""

#: includes/api/api-helpers.php:1011
msgid "Thumbnail"
msgstr ""

#: includes/api/api-helpers.php:1012
msgid "Medium"
msgstr ""

#: includes/api/api-helpers.php:1013
msgid "Large"
msgstr ""

#: includes/api/api-helpers.php:1062
msgid "Full Size"
msgstr ""

#: includes/api/api-helpers.php:1831 includes/api/api-term.php:147
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr ""

#: includes/api/api-helpers.php:3911
#, php-format
msgid "Image width must be at least %dpx."
msgstr ""

#: includes/api/api-helpers.php:3916
#, php-format
msgid "Image width must not exceed %dpx."
msgstr ""

#: includes/api/api-helpers.php:3932
#, php-format
msgid "Image height must be at least %dpx."
msgstr ""

#: includes/api/api-helpers.php:3937
#, php-format
msgid "Image height must not exceed %dpx."
msgstr ""

#: includes/api/api-helpers.php:3955
#, php-format
msgid "File size must be at least %s."
msgstr ""

#: includes/api/api-helpers.php:3960
#, php-format
msgid "File size must must not exceed %s."
msgstr ""

#: includes/api/api-helpers.php:3994
#, php-format
msgid "File type must be %s."
msgstr ""

#: includes/assets.php:168
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""

#: includes/assets.php:171 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr ""

#: includes/assets.php:172
msgctxt "verb"
msgid "Edit"
msgstr ""

#: includes/assets.php:173
msgctxt "verb"
msgid "Update"
msgstr ""

#: includes/assets.php:174
msgid "Uploaded to this post"
msgstr ""

#: includes/assets.php:175
msgid "Expand Details"
msgstr ""

#: includes/assets.php:176
msgid "Collapse Details"
msgstr ""

#: includes/assets.php:177
msgid "Restricted"
msgstr ""

#: includes/assets.php:178 includes/fields/class-acf-field-image.php:67
msgid "All images"
msgstr ""

#: includes/assets.php:181
msgid "Validation successful"
msgstr ""

#: includes/assets.php:182 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr ""

#: includes/assets.php:183
msgid "1 field requires attention"
msgstr ""

#: includes/assets.php:184
#, php-format
msgid "%d fields require attention"
msgstr ""

#: includes/assets.php:187
msgid "Are you sure?"
msgstr ""

#: includes/assets.php:188 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr ""

#: includes/assets.php:189 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr ""

#: includes/assets.php:190 includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-image.php:141
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:360
#: pro/fields/class-acf-field-gallery.php:549
msgid "Remove"
msgstr ""

#: includes/assets.php:191
msgid "Cancel"
msgstr ""

#: includes/assets.php:194
msgid "Has any value"
msgstr ""

#: includes/assets.php:195
msgid "Has no value"
msgstr ""

#: includes/assets.php:196
msgid "Value is equal to"
msgstr ""

#: includes/assets.php:197
msgid "Value is not equal to"
msgstr ""

#: includes/assets.php:198
msgid "Value matches pattern"
msgstr ""

#: includes/assets.php:199
msgid "Value contains"
msgstr ""

#: includes/assets.php:200
msgid "Value is greater than"
msgstr ""

#: includes/assets.php:201
msgid "Value is less than"
msgstr ""

#: includes/assets.php:202
msgid "Selection is greater than"
msgstr ""

#: includes/assets.php:203
msgid "Selection is less than"
msgstr ""

#: includes/assets.php:206 includes/forms/form-comment.php:166
#: pro/admin/admin-options-page.php:325
msgid "Edit field group"
msgstr ""

#: includes/fields.php:308
msgid "Field type does not exist"
msgstr ""

#: includes/fields.php:308
msgid "Unknown"
msgstr ""

#: includes/fields.php:349
msgid "Basic"
msgstr ""

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr ""

#: includes/fields.php:351
msgid "Choice"
msgstr ""

#: includes/fields.php:352
msgid "Relational"
msgstr ""

#: includes/fields.php:353
msgid "jQuery"
msgstr ""

#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:567
#: pro/fields/class-acf-field-flexible-content.php:616
#: pro/fields/class-acf-field-repeater.php:443
msgid "Layout"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr ""

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr ""

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr ""

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:382
#: includes/fields/class-acf-field-taxonomy.php:784
#: includes/fields/class-acf-field-user.php:393
msgid "Allow Null?"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-select.php:373
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:381
msgid "Default Value"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:150
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Appears when creating a new post"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:205
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Return Value"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:432
msgid "Value"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:434
msgid "Both (Array)"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Checkbox"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:374
msgid "Enter each default value on a new line"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:634
#: includes/fields/class-acf-field-select.php:427
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:412
msgid "Return Format"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:227
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr ""

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr ""

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr ""

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr ""

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr ""

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:189
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr ""

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr ""

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:198
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr ""

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr ""

#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr ""

#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr ""

#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr ""

#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:265
#: includes/fields/class-acf-field-image.php:294
#: pro/fields/class-acf-field-gallery.php:708
#: pro/fields/class-acf-field-gallery.php:737
msgid "File size"
msgstr ""

#: includes/fields/class-acf-field-file.php:170
msgid "Add File"
msgstr ""

#: includes/fields/class-acf-field-file.php:221
msgid "File Array"
msgstr ""

#: includes/fields/class-acf-field-file.php:222
msgid "File URL"
msgstr ""

#: includes/fields/class-acf-field-file.php:223
msgid "File ID"
msgstr ""

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:230
#: pro/fields/class-acf-field-gallery.php:673
msgid "Library"
msgstr ""

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:674
msgid "Limit the media library choice"
msgstr ""

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:236
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:679
msgid "All"
msgstr ""

#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-image.php:237
#: pro/fields/class-acf-field-gallery.php:680
msgid "Uploaded to post"
msgstr ""

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:244
#: pro/fields/class-acf-field-gallery.php:687
msgid "Minimum"
msgstr ""

#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-file.php:256
msgid "Restrict which files can be uploaded"
msgstr ""

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:273
#: pro/fields/class-acf-field-gallery.php:716
msgid "Maximum"
msgstr ""

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:302
#: pro/fields/class-acf-field-gallery.php:745
msgid "Allowed file types"
msgstr ""

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:746
msgid "Comma separated list. Leave blank for all types"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:166
msgid "Clear location"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:167
msgid "Find current location"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:170
msgid "Search for address..."
msgstr ""

#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-google-map.php:211
msgid "Center"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:201
#: includes/fields/class-acf-field-google-map.php:212
msgid "Center the initial map"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:223
msgid "Zoom"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:224
msgid "Set the initial zoom level"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:233
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:699
#: pro/fields/class-acf-field-gallery.php:728
msgid "Height"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:234
msgid "Customize the map height"
msgstr ""

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr ""

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:379
msgid "Sub Fields"
msgstr ""

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr ""

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:627
#: pro/fields/class-acf-field-repeater.php:451
msgid "Block"
msgstr ""

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:626
#: pro/fields/class-acf-field-repeater.php:450
msgid "Table"
msgstr ""

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:628
#: pro/fields/class-acf-field-repeater.php:452
msgid "Row"
msgstr ""

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr ""

#: includes/fields/class-acf-field-image.php:64
msgid "Select Image"
msgstr ""

#: includes/fields/class-acf-field-image.php:65
msgid "Edit Image"
msgstr ""

#: includes/fields/class-acf-field-image.php:66
msgid "Update Image"
msgstr ""

#: includes/fields/class-acf-field-image.php:157
msgid "No image selected"
msgstr ""

#: includes/fields/class-acf-field-image.php:157
msgid "Add Image"
msgstr ""

#: includes/fields/class-acf-field-image.php:211
msgid "Image Array"
msgstr ""

#: includes/fields/class-acf-field-image.php:212
msgid "Image URL"
msgstr ""

#: includes/fields/class-acf-field-image.php:213
msgid "Image ID"
msgstr ""

#: includes/fields/class-acf-field-image.php:220
msgid "Preview Size"
msgstr ""

#: includes/fields/class-acf-field-image.php:221
msgid "Shown when entering data"
msgstr ""

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Restrict which images can be uploaded"
msgstr ""

#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:691
#: pro/fields/class-acf-field-gallery.php:720
msgid "Width"
msgstr ""

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr ""

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr ""

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr ""

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr ""

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr ""

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr ""

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr ""

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr ""

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr ""

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr ""

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr ""

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr ""

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr ""

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:158
msgid "Minimum Value"
msgstr ""

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:168
msgid "Maximum Value"
msgstr ""

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:178
msgid "Step Size"
msgstr ""

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr ""

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr ""

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr ""

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr ""

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr ""

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:961
msgid "Parent"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:560
msgid "Filter by Post Type"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:568
msgid "All post types"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:574
msgid "Filter by Taxonomy"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:582
msgid "All taxonomies"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:392
#: includes/fields/class-acf-field-user.php:403
msgid "Select multiple values?"
msgstr ""

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr ""

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:639
msgid "Post Object"
msgstr ""

#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:640
msgid "Post ID"
msgstr ""

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr ""

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr ""

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr ""

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr ""

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr ""

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:411
msgid "Select post type"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:420
msgid "Select taxonomy"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:477
msgid "Search..."
msgstr ""

#: includes/fields/class-acf-field-relationship.php:588
msgid "Filters"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:594
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:595
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:754
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:602
msgid "Elements"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:603
msgid "Selected elements will be displayed in each result"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:614
msgid "Minimum posts"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:623
msgid "Maximum posts"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:727
#: pro/fields/class-acf-field-gallery.php:818
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] ""
msgstr[1] ""

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:776
msgctxt "noun"
msgid "Select"
msgstr ""

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr ""

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr ""

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr ""

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr ""

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr ""

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr ""

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr ""

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr ""

#: includes/fields/class-acf-field-select.php:402
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr ""

#: includes/fields/class-acf-field-select.php:412
msgid "Use AJAX to lazy load choices?"
msgstr ""

#: includes/fields/class-acf-field-select.php:428
msgid "Specify the value returned"
msgstr ""

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr ""

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr ""

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr ""

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Select the taxonomy to be displayed"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Appearance"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Select the appearance of this field"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Multiple Values"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Multi Select"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Single Value"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Radio Buttons"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:799
msgid "Create Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:800
msgid "Allow new terms to be created whilst editing"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Save Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Connect selected terms to the post"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Load Terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Load value from posts terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:834
msgid "Term Object"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:835
msgid "Term ID"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:885
#, php-format
msgid "User unable to add new %s"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:895
#, php-format
msgid "%s already exists"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:927
#, php-format
msgid "%s added"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:973
msgid "Add"
msgstr ""

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr ""

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr ""

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr ""

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:213
#, php-format
msgid "Value must not exceed %d characters"
msgstr ""

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr ""

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr ""

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr ""

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr ""

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr ""

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr ""

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr ""

#: includes/fields/class-acf-field-user.php:378
msgid "Filter by role"
msgstr ""

#: includes/fields/class-acf-field-user.php:386
msgid "All user roles"
msgstr ""

#: includes/fields/class-acf-field-user.php:417
msgid "User Array"
msgstr ""

#: includes/fields/class-acf-field-user.php:418
msgid "User Object"
msgstr ""

#: includes/fields/class-acf-field-user.php:419
msgid "User ID"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:331
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:337
msgid "Click to initialize TinyMCE"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Tabs"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:395
msgid "Visual & Text"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Visual Only"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Text Only"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:404
msgid "Toolbar"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initalized until field is clicked"
msgstr ""

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr ""

#: includes/forms/form-front.php:103 pro/fields/class-acf-field-gallery.php:591
#: pro/options-page.php:81
msgid "Update"
msgstr ""

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr ""

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr ""

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr ""

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr ""

#: includes/locations.php:96
msgid "Forms"
msgstr ""

#: includes/locations.php:243
msgid "is equal to"
msgstr ""

#: includes/locations.php:244
msgid "is not equal to"
msgstr ""

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr ""

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr ""

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr ""

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr ""

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr ""

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr ""

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr ""

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr ""

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr ""

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr ""

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr ""

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr ""

#: includes/locations/class-acf-location-page-template.php:87
#: includes/locations/class-acf-location-post-template.php:134
msgid "Default Template"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr ""

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr ""

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr ""

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr ""

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr ""

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr ""

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr ""

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr ""

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr ""

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr ""

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr ""

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr ""

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr ""

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr ""

#: pro/admin/admin-options-page.php:198
msgid "Publish"
msgstr ""

#: pro/admin/admin-options-page.php:204
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""

#: pro/admin/admin-updates.php:49
msgid "<b>Error</b>. Could not connect to update server"
msgstr ""

#: pro/admin/admin-updates.php:118 pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr ""

#: pro/admin/admin-updates.php:191
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr ""

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr ""

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr ""

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr ""

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr ""

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr ""

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr ""

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr ""

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr ""

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr ""

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr ""

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr ""

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr ""

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr ""

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:463
msgid "Add Row"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:938
#: pro/fields/class-acf-field-flexible-content.php:1020
msgid "layout"
msgid_plural "layouts"
msgstr[0] ""
msgstr[1] ""

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:937
#: pro/fields/class-acf-field-flexible-content.php:1019
msgid "This field requires at least {min} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:302
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:427
msgid "Add layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:428
msgid "Remove layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:429
#: pro/fields/class-acf-field-repeater.php:296
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:643
msgid "Min"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:656
msgid "Max"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:683
#: pro/fields/class-acf-field-repeater.php:459
msgid "Button Label"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:692
msgid "Minimum Layouts"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:71
msgid "Add Image to Gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:72
msgid "Maximum selection reached"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:338
msgid "Length"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:381
msgid "Caption"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:390
msgid "Alt Text"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:562
msgid "Add to gallery"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:566
msgid "Bulk actions"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:567
msgid "Sort by date uploaded"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:568
msgid "Sort by date modified"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:569
msgid "Sort by title"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:570
msgid "Reverse current order"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:588
msgid "Close"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:642
msgid "Minimum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:651
msgid "Maximum Selection"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:660
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:661
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:665
msgid "Append to the end"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:666
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:656
msgid "Minimum rows reached ({min} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:333
msgid "Add row"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:334
msgid "Remove row"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:412
msgid "Collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:413
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum Rows"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:433
msgid "Maximum Rows"
msgstr ""

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr ""

#: pro/options-page.php:51
msgid "Options"
msgstr ""

#: pro/options-page.php:82
msgid "Options Updated"
msgstr ""

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr ""

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr ""
