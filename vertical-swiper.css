/* 垂直轮播样式 */
#niankan-container {
    position: relative;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.swiper-container {
    width: 100%;
    height: 80vh;
    position: relative;
}

.swiper-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.3s ease;
    opacity: 0.6;
    transform: scale(0.8);
    height: auto;
}

.swiper-slide-active {
    opacity: 1;
    transform: scale(1);
    z-index: 10;
}

.swiper-slide-prev,
.swiper-slide-next {
    opacity: 0.6;
    transform: scale(0.8);
}

/* 图片容器样式 */
.swiper-slide .e-con-inner {
    width: 100%;
    display: flex;
    justify-content: center;
}

.swiper-slide .elementor-widget-container {
    width: 100%;
    text-align: center;
}

.swiper-slide img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

/* 激活状态的图片样式 */
.swiper-slide-active img {
    transform: scale(1.1);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

/* 导航按钮样式 */
.swiper-button-next,
.swiper-button-prev {
    color: #fff;
    background: rgba(0, 0, 0, 0.5);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    margin-top: 0;
    transform: rotate(90deg);
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 20px;
}

.swiper-button-next {
    right: 20px;
    top: 60%;
}

.swiper-button-prev {
    right: 20px;
    top: 40%;
    left: auto;
}

/* 分页器样式 */
.swiper-pagination {
    right: 10px !important;
    left: auto !important;
    top: 50% !important;
    transform: translateY(-50%);
    width: auto !important;
    height: auto !important;
}

.swiper-pagination-bullet {
    width: 12px;
    height: 12px;
    background: rgba(255, 255, 255, 0.5);
    margin: 8px 0 !important;
    display: block;
}

.swiper-pagination-bullet-active {
    background: #fff;
    transform: scale(1.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .swiper-container {
        height: 70vh;
    }
    
    .swiper-slide img {
        border-radius: 10px;
    }
    
    .swiper-slide-active img {
        transform: scale(1.05);
    }
    
    .swiper-button-next,
    .swiper-button-prev {
        width: 40px;
        height: 40px;
    }
    
    .swiper-button-next:after,
    .swiper-button-prev:after {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .swiper-container {
        height: 60vh;
    }
    
    .swiper-slide {
        transform: scale(0.85);
    }
    
    .swiper-slide-active {
        transform: scale(1);
    }
    
    .swiper-slide-active img {
        transform: scale(1.02);
    }
}

/* 平滑过渡效果 */
.swiper-slide-transition-start,
.swiper-slide-transition-end {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 隐藏原始容器的子元素，只显示轮播 */
#niankan-container .e-con-inner > .e-con {
    display: none;
}

/* 显示轮播容器 */
#niankan-container .swiper-container {
    display: block;
}
