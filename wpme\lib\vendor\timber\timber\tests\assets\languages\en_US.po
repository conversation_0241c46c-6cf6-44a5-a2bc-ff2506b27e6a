# Copyright (C) 2014 the WordPress team
# This file is distributed under the GNU General Public License v2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Twenty Fifteen 1.0\n"
"Report-Msgid-Bugs-To: http://wordpress.org/tags/twentyfifteen\n"
"POT-Creation-Date: 2014-12-14 12:26:59+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2015-04-06 19:57-0500\n"
"Last-Translator: \n"
"Language-Team: \n"
"X-Generator: Poedit 1.7.5\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Language: en_GB\n"

#: 404.php:17
msgid "Oops! That page can&rsquo;t be found."
msgstr ""

#: 404.php:21
msgid "It looks like nothing was found at this location. Maybe try a search?"
msgstr ""

#: archive.php:49 index.php:46 search.php:38
msgid "Previous page"
msgstr ""

#: archive.php:50 index.php:47 search.php:39
msgid "Next page"
msgstr ""

#: archive.php:51 content-link.php:40 content-page.php:29 content.php:42
#: image.php:63 index.php:48 search.php:40
msgid "Page"
msgstr ""

#: author-bio.php:12
msgid "Published by"
msgstr ""

#: author-bio.php:34
msgid "View all posts by %s"
msgstr ""

#: comments.php:28
msgctxt "comments title"
msgid "One thought on &ldquo;%2$s&rdquo;"
msgid_plural "%1$s thoughts on &ldquo;%2$s&rdquo;"
msgstr[0] ""
msgstr[1] ""

#: comments.php:53
msgid "Comments are closed."
msgstr ""

#. translators: %s: Name of current post
#: content-link.php:31 content.php:33 inc/template-tags.php:237
msgid "Continue reading %s"
msgstr ""

#: content-link.php:36 content-page.php:25 content.php:38 image.php:59
msgid "Pages:"
msgstr ""

#: content-link.php:56 content-page.php:35 content-search.php:28
#: content-search.php:33 content.php:57 image.php:71
msgid "Edit"
msgstr ""

#: content-none.php:15
msgid "Nothing Found"
msgstr ""

#: content-none.php:22
msgid ""
"Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr ""

#: content-none.php:26
msgid ""
"Sorry, but nothing matched your search terms. Please try again with some "
"different keywords."
msgstr ""

#: content-none.php:31
msgid ""
"It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps "
"searching can help."
msgstr ""

#. Author URI of the plugin/theme
#: footer.php:25
msgid "https://wordpress.org/"
msgstr ""

#: footer.php:25
msgid "Proudly powered by %s"
msgstr ""

#: functions.php:85
msgid "Primary Menu"
msgstr ""

#: functions.php:86
msgid "Social Links Menu"
msgstr ""

#: functions.php:133
msgid "Widget Area"
msgstr ""

#: functions.php:135
msgid "Add widgets here to appear in your sidebar."
msgstr ""

#. translators: If there are characters in your language that are not supported
#. by Noto Sans, translate this to 'off'. Do not translate into your own
#. language.
#: functions.php:158
msgctxt "Noto Sans font: on or off"
msgid "on"
msgstr ""

#. translators: If there are characters in your language that are not supported
#. by Noto Serif, translate this to 'off'. Do not translate into your own
#. language.
#: functions.php:163
msgctxt "Noto Serif font: on or off"
msgid "on"
msgstr ""

#. translators: If there are characters in your language that are not supported
#. by Inconsolata, translate this to 'off'. Do not translate into your own
#. language.
#: functions.php:168
msgctxt "Inconsolata font: on or off"
msgid "on"
msgstr ""

#. translators: To add an additional character subset specific to your
#. language, translate this to 'greek', 'cyrillic', 'devanagari' or
#. 'vietnamese'. Do not translate into your own language.
#: functions.php:173
msgctxt "Add new subset (greek, cyrillic, devanagari, vietnamese)"
msgid "no-subset"
msgstr ""

#: functions.php:231
msgid "expand child menu"
msgstr ""

#: functions.php:232
msgid "collapse child menu"
msgstr ""

#: header.php:27
msgid "Skip to content"
msgstr ""

#: header.php:44
msgid "Menu and widgets"
msgstr ""

#: image.php:24
msgid "Previous Image"
msgstr ""

#: image.php:24
msgid "Next Image"
msgstr ""

#: image.php:84
msgctxt "Parent post link"
msgid ""
"<span class=\"meta-nav\">Published in</span><span class=\"post-title\">"
"%title</span>"
msgstr ""

#: inc/back-compat.php:37 inc/back-compat.php:47 inc/back-compat.php:60
msgid ""
"Twenty Fifteen requires at least WordPress version 4.1. You are running "
"version %s. Please upgrade and try again."
msgstr ""

#: inc/customizer.php:36
msgid "Base Color Scheme"
msgstr ""

#: inc/customizer.php:51
msgid "Header and Sidebar Text Color"
msgstr ""

#: inc/customizer.php:52 inc/customizer.php:68 inc/customizer.php:73
msgid "Applied to the header on small screens and the sidebar on wide screens."
msgstr ""

#: inc/customizer.php:67
msgid "Header and Sidebar Background Color"
msgstr ""

#: inc/customizer.php:97
msgid "Default"
msgstr ""

#: inc/customizer.php:108
msgid "Dark"
msgstr ""

#: inc/customizer.php:119
msgid "Yellow"
msgstr ""

#: inc/customizer.php:130
msgid "Pink"
msgstr ""

#: inc/customizer.php:141
msgid "Purple"
msgstr ""

#: inc/customizer.php:152
msgid "Blue"
msgstr ""

#: inc/template-tags.php:23
msgid "Comment navigation"
msgstr ""

#: inc/template-tags.php:26
msgid "Older Comments"
msgstr ""

#: inc/template-tags.php:30
msgid "Newer Comments"
msgstr ""

#: inc/template-tags.php:49
msgid "Featured"
msgstr ""

#: inc/template-tags.php:55
msgctxt "Used before post format."
msgid "Format"
msgstr ""

#: inc/template-tags.php:76
msgctxt "Used before publish date."
msgid "Posted on"
msgstr ""

#: inc/template-tags.php:85
msgctxt "Used before post author name."
msgid "Author"
msgstr ""

#: inc/template-tags.php:91 inc/template-tags.php:99
msgctxt "Used between list items, there is a space after the comma."
msgid ", "
msgstr ""

#: inc/template-tags.php:94
msgctxt "Used before category names."
msgid "Categories"
msgstr ""

#: inc/template-tags.php:102
msgctxt "Used before tag names."
msgid "Tags"
msgstr ""

#: inc/template-tags.php:113
msgctxt "Used before full size attachment link."
msgid "Full size"
msgstr ""

#: inc/template-tags.php:122
msgid "Leave a comment"
msgstr ""

#: inc/template-tags.php:122
msgid "1 Comment"
msgstr ""

#: inc/template-tags.php:122
msgid "% Comments"
msgstr ""

#: search.php:18
msgid "Search Results for: %s"
msgstr ""

#: single.php:33
msgid "Next"
msgstr ""

#: single.php:34
msgid "Next post:"
msgstr ""

#: single.php:36
msgid "Previous"
msgstr ""

#: single.php:37
msgid "Previous post:"
msgstr ""

#. Theme Name of the plugin/theme
msgid "Twenty Fifteen"
msgstr ""

#. Theme URI of the plugin/theme
msgid "https://wordpress.org/themes/twentyfifteen"
msgstr ""

#. Description of the plugin/theme
msgid ""
"Our 2015 default theme is clean, blog-focused, and designed for clarity. "
"Twenty Fifteen's simple, straightforward typography is readable on a wide "
"variety of screen sizes, and suitable for multiple languages. We designed it "
"using a mobile-first approach, meaning your content takes center-stage, "
"regardless of whether your visitors arrive by smartphone, tablet, laptop, or "
"desktop computer."
msgstr ""

#. Author of the plugin/theme
msgid "the WordPress team"
msgstr ""

#. Test
msgid "thingy"
msgstr "Cheesy Poofs"
