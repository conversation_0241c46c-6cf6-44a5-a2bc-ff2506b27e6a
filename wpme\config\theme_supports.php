<?php


  // Add default posts and comments RSS feed links to head.
  add_theme_support('automatic-feed-links');


  add_theme_support('menus');
  register_nav_menus(array(
    'mainmenu'   => esc_html__('主要菜单', 'qx'),
    'footmenu'   => esc_html__('底部菜单', 'qx'),
    'sidemenu'   => esc_html__('内容中心侧边菜单', 'qx'),
  ));

  /**
   * 注册边栏
   **/
  register_sidebar(array(
    'name'          => __('默认侧边栏', 'qx'),
    'id'            => 'sidebar',
    'description'   => __('默认侧边栏信息', 'qx'),
    'before_widget' => '<aside class="widget %2$s">',
    'after_widget'  => '</aside>',
    'before_title'  => '<h3 class="widget-title">',
    'after_title'   => '</h3>'
  ));
  /*
   * Let WordPress manage the document title.
   * By adding theme support, we declare that this theme does not use a
   * hard-coded <title> tag in the document head, and expect WordPress to
   * provide it for us.
   */
  add_theme_support('title-tag');

  /*
   * Enable support for Post Thumbnails on posts and pages.
   *
   * @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
   */
  add_theme_support('post-thumbnails');

     // Set custom thumbnail dimensions
    // set_post_thumbnail_size( 300, 250, true );

  /*
   * Switch default core markup for search form, comment form, and comments
   * to output valid HTML5.
   */
  add_theme_support(
      'html5',
      array(
      'comment-form',
      'comment-list',
      'gallery',
      'caption',
    )
  );

  /*
   * Enable support for Post Formats.
   *
   * See: https://codex.wordpress.org/Post_Formats
   */
  add_theme_support(
      'post-formats',
      array(
      'aside',
      'image',
      'video',
      'quote',
      'link',
      'gallery',
      'audio',
    )
  );
