---
title: "Upgrade to 1.0"
menu:
  main:
    parent: "upgrade-guides"
---

A significant part of this 1.0 release is removing lots of deprecated functions and methods that go back to the earliest designs for Timber in early 2013.

## Should I upgrade?

Maybe. Read on:

### I have a legacy site
If you wrote a theme in 2014, it's running fine and the development of the site has concluded: stop right there! Do not upgrade! While there are some slight performance benefits (and not having the warning light on the plugin dashboard) it's not worth it to you. Close this window and forget you ever heard about Timber 1.0

### I have a site under continued development

Maybe. There are going to be tradeoffs with whatever route you pick. I recommend testing your site thoroughly with a staging or local copy to find all the issues before applying Timber 1.0 to production. However, you're the best judge of how much potential refactoring you're looking at and whether it's worth the benefits.

### I am starting a new site.

Definitely. You'll get the benefits of being on the newest platform. We have some cool stuff ahead that you'll want to include in your development workflow, and of course you'll learn about the newest/best ways to make Timber themes.

## Routes
The Routes feature has now been totally deprecated. If you have custom routes you'll need to add the [Upstatement/routes](https://github.com/upstatement/routes) repo to your theme? How to do this?

### 1. Add to Composer (optional, but recommended)
Simply add `Upstatement/routes` to your `composer.json` file like so:

```json
"require": {
    "timber/timber":"1.*",
    "advanced-custom-fields/advanced-custom-fields-pro": "*",
    "Upstatement/routes": "*"
},
```

Here's a full [Gist](https://gist.github.com/jarednova/dc84cf14735a870dbe3d2763e94095a1) of what this looks like. Run a `composer install` to update your theme's dependencies.

_Note: because we're not monsters, the [Upstatement/routes] repo will continue to be included with Timber for the next several versions_

### 2. Add to your theme

If you haven't already, you'll need to load Composer's autoload file into your theme via:

```php
<?php
/* functions.php */
require_once('vendor/autoload.php');
```

### 3. Update your Routes

Now just update the PHP for your old routes to the new ones. It'll be basically the same code, but with cleaner syntax **and a different arguments order for the `::load` method**

**Before**

```php
<?php
Timber::add_route('myfoo/bar', 'my_callback_function');
Timber::add_route('my-events/:event', function($params) {
    $query = new WP_Query('post_type=event');
    Timber::load_view('single.php', $query, 200, $params);
});
```

**After**

```php
<?php
Routes::map('myfoo/bar', 'my_callback_function');
Routes::map('my-events/:event', function($params) {
    $query = new WP_Query('post_type=event');
    /* please note the different order of arguments vs. Timber::load_template */
    Routes::load('single.php', $params, $query, 200);
});
```

... and that's the hardest part, done!

## Post Object Properties

* If you're using `{{ post.permalink }}` or `{{ post.get_permalink }}` you should replace with `{{ post.link }}`
* If you're using `{{ post.url }}` or `{{ post.get_url }}` you should replace with `{{ post.link }}`
* If you're using `{{ post.thumbnail.url }}` or `{{ post.get_thumbnail.url }}` you should replace with `{{ post.thumbnail.src }}`

## Static methods

Way back in `0.18`, a new helper library was added for URLs. In my infinite wisdom I called it `TimberURLHelper`. Previously old static methods in `TimberHelper` still worked (like `TimberHelper::get_current_url`) — they don't any longer. The full list of methods you'll need to update is...

* `TimberHelper::get_current_url` --- becomes ---> `Timber\URLHelper::get_current_url()`
* `TimberHelper::is_url` --- becomes ---> `Timber\URLHelper::is_url()`
* `TimberHelper::get_path_base` --- becomes ---> `Timber\URLHelper::get_path_base()`
* `TimberHelper::get_rel_url` --- becomes ---> `Timber\URLHelper::get_rel_url()`
* `TimberHelper::is_local` --- becomes ---> `Timber\URLHelper::is_local()`
* `TimberHelper::get_full_path` --- becomes ---> `Timber\URLHelper::get_full_path()`
* `TimberHelper::get_rel_path` --- becomes ---> `Timber\URLHelper::get_rel_path()`
* `TimberHelper::remove_double_slashes` --- becomes ---> `Timber\URLHelper::remove_double_slashes()`
* `TimberHelper::prepend_to_url` --- becomes ---> `Timber\URLHelper::prepend_to_url()`
* `TimberHelper::preslashit` --- becomes ---> `Timber\URLHelper::preslashit()`
* `TimberHelper::is_external` --- becomes ---> `Timber\URLHelper::is_external()`
* `TimberHelper::download_url` --- becomes ---> `Timber\URLHelper::download_url()`
* `TimberHelper::get_params` --- becomes ---> `Timber\URLHelper::get_params()`

## Deprecated

Many of the aliases with `get_*` are now deprecated. For awhile, we've recommended using `post.title` instead of `post.get_title`, `post.thumbnail` instead of `post.get_thumbnail`, etc. In future versions this will be enforced.
