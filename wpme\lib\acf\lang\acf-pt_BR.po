msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields PRO 5.4\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2017-11-22 09:03-0200\n"
"PO-Revision-Date: 2018-02-06 10:06+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <<EMAIL>>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 1.8.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:67
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:369 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Grupos de Campos"

#: acf.php:370
msgid "Field Group"
msgstr "Grupo de Campos"

#: acf.php:371 acf.php:403 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New"
msgstr "Adicionar Novo"

#: acf.php:372
msgid "Add New Field Group"
msgstr "Adicionar Novo Grupo de Campos"

#: acf.php:373
msgid "Edit Field Group"
msgstr "Editar Grupo de Campos"

#: acf.php:374
msgid "New Field Group"
msgstr "Novo Grupo de Campos"

#: acf.php:375
msgid "View Field Group"
msgstr "Ver Grupo de Campos"

#: acf.php:376
msgid "Search Field Groups"
msgstr "Pesquisar Grupos de Campos"

#: acf.php:377
msgid "No Field Groups found"
msgstr "Nenhum Grupo de Campos encontrado"

#: acf.php:378
msgid "No Field Groups found in Trash"
msgstr "Nenhum Grupo de Campos encontrado na Lixeira"

#: acf.php:401 includes/admin/admin-field-group.php:182
#: includes/admin/admin-field-group.php:275
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:807
msgid "Fields"
msgstr "Campos"

#: acf.php:402
msgid "Field"
msgstr "Campo"

#: acf.php:404
msgid "Add New Field"
msgstr "Adicionar Novo Campo"

#: acf.php:405
msgid "Edit Field"
msgstr "Editar Campo"

#: acf.php:406 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Novo Campo"

#: acf.php:407
msgid "View Field"
msgstr "Ver Campo"

#: acf.php:408
msgid "Search Fields"
msgstr "Pesquisar Campos"

#: acf.php:409
msgid "No Fields found"
msgstr "Nenhum Campo encontrado"

#: acf.php:410
msgid "No Fields found in Trash"
msgstr "Nenhum Campo encontrado na Lixeira"

#: acf.php:449 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Inativo"

#: acf.php:454
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Ativo <span class=\"count\">(%s)</span>"
msgstr[1] "Ativos <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Grupo de campos atualizado"

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Grupo de campos excluído."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Grupo de campos publicado."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Grupo de campos salvo."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Grupo de campos enviado."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Grupo de campos agendando."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Rascunho do grupo de campos atualizado."

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "Localização"

#: includes/admin/admin-field-group.php:184
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Configurações"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "Mover para a lixeira. Você tem certeza?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr "selecionado"

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr "Nenhum campo de opções disponível"

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "O título do grupo de campos é obrigatório"

#: includes/admin/admin-field-group.php:273
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "copiar"

#: includes/admin/admin-field-group.php:274
#: includes/admin/views/field-group-field-conditional-logic.php:54
#: includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3959
msgid "or"
msgstr "ou"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "Campos superiores"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr "Campos do mesmo grupo"

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "Mover Campo Personalizado"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr "Este campo não pode ser movido até que suas alterações sejam salvas"

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr "Vazio"

#: includes/admin/admin-field-group.php:281 includes/input.php:258
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "As alterações feitas serão perdidas se você sair desta página"

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "O termo “field_” não pode ser utilizado no início do nome de um campo"

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr "Chaves dos Campos"

#: includes/admin/admin-field-group.php:390
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Ativo"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "Movimentação realizada."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "O campo %s pode agora ser encontrado no grupo de campos %s"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "Fechar Janela"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "Selecione o destino para este campo"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "Mover Campo"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Ativo <span class=\"count\">(%s)</span>"
msgstr[1] "Ativos <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Grupo de campos duplicado. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s grupo de campos duplicado."
msgstr[1] "%s grupos de campos duplicados."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Grupo de campos sincronizado. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s grupo de campos sincronizado."
msgstr[1] "%s grupos de campos sincronizados."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Sincronização disponível"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:355
msgid "Title"
msgstr "Título"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:382
msgid "Description"
msgstr "Descrição"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Status"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Personalize o WordPress com campos personalizados profissionais, poderosos e "
"intuitivos."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Registro de alterações"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Veja o que há de novo na <a href=\"%s\">versão %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Recursos (em inglês)"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Website"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Documentação"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Suporte"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "Profissional"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Obrigado por criar com <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:667
msgid "Duplicate this item"
msgstr "Duplicar este item"

#: includes/admin/admin-field-groups.php:667
#: includes/admin/admin-field-groups.php:683
#: includes/admin/views/field-group-field.php:49
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate"
msgstr "Duplicar"

#: includes/admin/admin-field-groups.php:700
#: includes/fields/class-acf-field-google-map.php:112
#: includes/fields/class-acf-field-relationship.php:656
msgid "Search"
msgstr "Pesquisa"

#: includes/admin/admin-field-groups.php:759
#, php-format
msgid "Select %s"
msgstr "Selecionar %s"

#: includes/admin/admin-field-groups.php:767
msgid "Synchronise field group"
msgstr "Sincronizar grupo de campos"

#: includes/admin/admin-field-groups.php:767
#: includes/admin/admin-field-groups.php:797
msgid "Sync"
msgstr "Sincronizar"

#: includes/admin/admin-field-groups.php:779
msgid "Apply"
msgstr "Aplicar"

#: includes/admin/admin-field-groups.php:797
msgid "Bulk Actions"
msgstr "Ações em massa"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Ferramentas"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Campos Personalizados"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Atualizar Banco de Dados"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "Revisar sites e atualizar"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "Erro ao validar solicitação"

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "Nenhuma atualização disponível."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Complementos"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Erro</b>. Não foi possível carregar a lista de complementos"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informações"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "O que há de novo"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Exportar Grupos de Campos"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Nenhum grupo de campos selecionado"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Exportado 1 grupo de campos"
msgstr[1] "Importados %s grupos de campos"

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Selecionar Grupo de Campos"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Selecione os grupos de campos que deseja exportar e escolha o método de "
"exportação. Para exportar um arquivo do tipo .json (que permitirá a "
"importação dos grupos em uma outra instalação do ACF) utilize o botão de "
"download. Para obter o código em PHP (que você poderá depois inserir em seu "
"tema), utilize o botão de gerar o código."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Exportar arquivo"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"O código a seguir poderá ser usado para registrar uma versão local do(s) "
"grupo(s) de campo selecionado(s). Um grupo de campos local pode fornecer "
"muitos benefícios, tais como um tempo de carregamento mais rápido, controle "
"de versão e campos/configurações dinâmicas. Basta copiar e colar o seguinte "
"código para o arquivo functions.php do seu tema ou incluí-lo dentro de um "
"arquivo externo."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr ""

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importar Grupos de Campos"

#: includes/admin/tools/class-acf-admin-tool-import.php:61
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Selecione o arquivo JSON do Advanced Custom Fields que deseja importar. "
"Depois de clicar no botão importar abaixo, o ACF fará a importação dos "
"grupos de campos."

#: includes/admin/tools/class-acf-admin-tool-import.php:66
#: includes/fields/class-acf-field-file.php:35
msgid "Select File"
msgstr "Selecionar Arquivo"

#: includes/admin/tools/class-acf-admin-tool-import.php:76
msgid "Import File"
msgstr "Importar arquivo"

#: includes/admin/tools/class-acf-admin-tool-import.php:100
#: includes/fields/class-acf-field-file.php:159
msgid "No file selected"
msgstr "Nenhum arquivo selecionado"

#: includes/admin/tools/class-acf-admin-tool-import.php:113
msgid "Error uploading file. Please try again"
msgstr "Erro ao realizar o upload do arquivo. Tente novamente"

#: includes/admin/tools/class-acf-admin-tool-import.php:122
msgid "Incorrect file type"
msgstr "Tipo de arquivo incorreto"

#: includes/admin/tools/class-acf-admin-tool-import.php:139
msgid "Import file empty"
msgstr "Arquivo de importação vazio"

#: includes/admin/tools/class-acf-admin-tool-import.php:247
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Importado 1 grupo de campos"
msgstr[1] "Importados %s grupos de campos"

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "Condições para exibição"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "Mostrar este campo se"

#: includes/admin/views/field-group-field-conditional-logic.php:103
#: includes/locations.php:247
msgid "is equal to"
msgstr "é igual a"

#: includes/admin/views/field-group-field-conditional-logic.php:104
#: includes/locations.php:248
msgid "is not equal to"
msgstr "não é igual a"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "e"

#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Adicionar grupo de regras"

#: includes/admin/views/field-group-field.php:41
#: pro/fields/class-acf-field-flexible-content.php:403
#: pro/fields/class-acf-field-repeater.php:296
msgid "Drag to reorder"
msgstr "Arraste para reorganizar"

#: includes/admin/views/field-group-field.php:45
#: includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "Editar campo"

#: includes/admin/views/field-group-field.php:48
#: includes/fields/class-acf-field-file.php:141
#: includes/fields/class-acf-field-image.php:122
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:342
msgid "Edit"
msgstr "Editar"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "Duplicar campo"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "Mover campo para outro grupo"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "Mover"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "Excluir campo"

#: includes/admin/views/field-group-field.php:51
#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete"
msgstr "Excluir"

#: includes/admin/views/field-group-field.php:67
msgid "Field Label"
msgstr "Rótulo do Campo"

#: includes/admin/views/field-group-field.php:68
msgid "This is the name which will appear on the EDIT page"
msgstr "Este é o nome que irá aparecer na página de EDIÇÃO"

#: includes/admin/views/field-group-field.php:77
msgid "Field Name"
msgstr "Nome do Campo"

#: includes/admin/views/field-group-field.php:78
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr ""
"Uma única palavra, sem espaços. Traço inferior (_) e traços (-) permitidos"

#: includes/admin/views/field-group-field.php:87
msgid "Field Type"
msgstr "Tipo de Campo"

#: includes/admin/views/field-group-field.php:98
msgid "Instructions"
msgstr "Instruções"

#: includes/admin/views/field-group-field.php:99
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instrução para os autores. Exibido quando se está enviando dados"

#: includes/admin/views/field-group-field.php:108
msgid "Required?"
msgstr "Obrigatório?"

#: includes/admin/views/field-group-field.php:131
msgid "Wrapper Attributes"
msgstr "Atributos do Wrapper"

#: includes/admin/views/field-group-field.php:137
msgid "width"
msgstr "largura"

#: includes/admin/views/field-group-field.php:152
msgid "class"
msgstr "classe"

#: includes/admin/views/field-group-field.php:165
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:177
msgid "Close Field"
msgstr "Fechar Campo"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Ordem"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:415
#: includes/fields/class-acf-field-radio.php:306
#: includes/fields/class-acf-field-select.php:432
#: pro/fields/class-acf-field-flexible-content.php:582
msgid "Label"
msgstr "Rótulo"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:964
#: pro/fields/class-acf-field-flexible-content.php:595
msgid "Name"
msgstr "Nome"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Chave"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tipo"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Nenhum campo. Clique no botão <strong>+ Adicionar Campo</strong> para criar "
"seu primeiro campo."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Adicionar Campo"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Regras"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Crie um conjunto de regras para determinar quais telas de edição utilizarão "
"estes campos personalizados"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Estilo"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Padrão (metabox do WP)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Sem bordas (sem metabox)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Posição"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Superior (depois do título)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normal (depois do editor de conteúdo)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Lateral"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Posicionamento do rótulo"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Alinhado ao Topo"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Alinhado à Esquerda"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Posicionamento das instruções"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Abaixo dos rótulos"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Abaixo dos campos"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Nº. de Ordem"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Grupos de campos com a menor numeração aparecerão primeiro"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Exibido na lista de grupos de campos"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Ocultar na tela"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Selecione</b> os itens que deverão ser <b>ocultados</b> da tela de edição"

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Se vários grupos de campos aparecem em uma tela de edição, as opções do "
"primeiro grupo de campos é a que será utilizada (aquele com o menor número "
"de ordem)"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Link permanente"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Editor de Conteúdo"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Resumo"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Discussão"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Comentários"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Revisões"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Formato"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Atributos da Página"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:670
msgid "Featured Image"
msgstr "Imagem Destacada"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Categorias"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Tags"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Enviar Trackbacks"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Mostrar este grupo de campos se"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "Revisar sites e atualizar"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr "Atualização do Banco de Dados do Advanced Custom Fields"

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"O banco de dados dos sites abaixo precisam ser atualizados. Verifique os que "
"você deseja atualizar e clique %s."

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Site"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Site requer atualização do banco de dados da versão %s para %s"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "Site está atualizado"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Atualização do Banco de Dados realizada. <a href=\"%s\">Retornar para o "
"painel da rede</a>"

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"É altamente recomendado fazer um backup do seu banco de dados antes de "
"continuar. Você tem certeza que deseja atualizar agora?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Atualização realizada"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Atualizando os dados para a versão %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Repetidor"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Conteúdo Flexível"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galeria"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Página de Opções"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Atualização do Banco de Dados Necessária"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Obrigado por atualizar para o %s v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Antes de começar a utilizar as novas e incríveis funcionalidades, por favor "
"atualize seus banco de dados para a versão mais recente."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""
"Certifique-se que todos os complementos premium (%s) foram atualizados para "
"a última versão."

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Lendo as tarefas de atualização…"

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Atualização do banco de dados concluída. <a href=\"%s\">Veja o que há de "
"novo</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Fazer Download e Instalar"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Instalado"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Bem-vindo ao Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Obrigado por atualizar! O ACF %s está maior e melhor do que nunca. Esperamos "
"que você goste."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "Uma experiência de uso mais simples e mais agradável"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Melhoria da Usabilidade"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Incluir a popular biblioteca Select2 nos possibilitou aperfeiçoar a "
"usabilidade e a performance de diversos tipos de campos, como o objeto do "
"post, link da página, taxonomias e seleções."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Melhorias no Design"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Muitos campos passaram por uma atualização visual para tornar o ACF mais "
"bonito do que nunca! As mudanças mais visíveis podem ser vistas na galeria, "
"no campo de relação e no novo campo oEmbed!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Aprimoramento dos Dados"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Ao redefinir a arquitetura de dados promovemos mais autonomia aos sub "
"campos, que podem agora funcionar de forma mais independente e serem "
"arrastados e reposicionados entre diferentes campos."

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Adeus Complementos. Olá PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "Apresentando o ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""
"Estamos mudando a forma como as funcionalidades premium são disponibilizadas "
"para um modo ainda melhor!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Todos os 4 add-ons premium foram combinados na nova <a href=“%s”>versão Pro "
"do ACF</a>. Com licenças pessoais e para desenvolvedores, as funcionalidades "
"premium estão mais acessíveis do que nunca!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Funcionalidades poderosas"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"O ACF PRO contém funcionalidades incríveis como o campo de dados "
"repetitivos, layouts de conteúdo flexíveis, um belíssimo campo de galeria e "
"a capacidade de criar páginas de opções adicionais!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr ""
"Leia mais sobre as <a href=“%s”>funcionalidades do ACF PRO</a> (em inglês)."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Fácil Atualização"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"Para facilitar a atualização, <a href=“%s”>faça o login na sua conta</a> e "
"solicite sua cópia gratuita do ACF PRO!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"Também escrevemos um <a href=“%s”>guia de atualização</a> (em inglês) para "
"esclarecer qualquer dúvida, mas se você tiver alguma questão, entre em "
"contato com nosso time de suporte através do <a href=“%s”>help desk</a>"

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Nos bastidores"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "Definições de campo mais inteligentes"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "O ACF agora salva as definições dos campos como posts individuais"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Mais AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr ""
"Mais campos utilizam pesquisas em AJAX para acelerar o carregamento da página"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "JSON Local"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr ""
"Melhor performance com a nova funcionalidade de exportação automática para "
"JSON"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "Melhor controle de versões"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr ""
"A nova função de exportação automática para JSON permite que as definições "
"do campo sejam controladas por versão"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "Troca de XML para JSON"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr ""
"As funcionalidades de Importar/ Exportar agora utilizam JSON ao invés de XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Novos espaços de Formulários"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"Os Campos agora podem ser inseridos nos comentários, widgets e em todos os "
"formulários de usuários!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "Foi adicionado o novo campo oEmbed para incorporar conteúdo"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Nova Galeria"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "O campo de Galeria passou por uma transformação muito necessária"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Novas Definições"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"Opções de posicionamento do rótulo e da instrução foram adicionadas aos "
"grupos de campos"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "Formulários Frontend aperfeiçoados"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "A função acf_form() agora pode criar um novo post ao ser utilizada"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Melhor Validação"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr ""
"A validação dos formulários agora é feita através de PHP + AJAX ao invés de "
"apenas JS"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Campo de Relação"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Nova função de ‘Filtro’ (Busca, Tipo de Post, Taxonomia) para o campo de "
"Relação"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Movimentação de Campos"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""
"O novo recurso agora permite que você mova um campo entre diferentes grupos "
"grupos (e até mesmo outros campos)"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Link da Página"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Nova opção de selecionar Arquivos no campo de Link da Página"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Páginas de Opções aperfeiçoadas"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"Novas funções para as páginas de opções permitem a criação tanto de páginas "
"principais quanto de sub-páginas"

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Achamos que você vai adorar as mudanças na versão %s."

#: includes/api/api-helpers.php:858
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/api/api-helpers.php:859
msgid "Medium"
msgstr "Média"

#: includes/api/api-helpers.php:860
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:909
msgid "Full Size"
msgstr "Tamanho Original"

#: includes/api/api-helpers.php:1250 includes/api/api-helpers.php:1823
#: pro/fields/class-acf-field-clone.php:992
msgid "(no title)"
msgstr "(sem título)"

#: includes/api/api-helpers.php:3880
#, php-format
msgid "Image width must be at least %dpx."
msgstr "A largura da imagem deve ter pelo menos %dpx."

#: includes/api/api-helpers.php:3885
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "A largura da imagem não pode ser maior que %dpx."

#: includes/api/api-helpers.php:3901
#, php-format
msgid "Image height must be at least %dpx."
msgstr "A altura da imagem deve ter pelo menos %dpx."

#: includes/api/api-helpers.php:3906
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "A altura da imagem não pode ser maior que %dpx."

#: includes/api/api-helpers.php:3924
#, php-format
msgid "File size must be at least %s."
msgstr "O tamanho do arquivo deve ter pelo menos %s."

#: includes/api/api-helpers.php:3929
#, php-format
msgid "File size must must not exceed %s."
msgstr "O tamanho do arquivo não pode ser maior que %s."

#: includes/api/api-helpers.php:3963
#, php-format
msgid "File type must be %s."
msgstr "O tipo de arquivo deve ser %s."

#: includes/fields.php:144
msgid "Basic"
msgstr "Básico"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Conteúdo"

#: includes/fields.php:146
msgid "Choice"
msgstr "Escolha"

#: includes/fields.php:147
msgid "Relational"
msgstr "Relacional"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149
#: includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:384
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:285
#: pro/fields/class-acf-field-clone.php:839
#: pro/fields/class-acf-field-flexible-content.php:552
#: pro/fields/class-acf-field-flexible-content.php:601
#: pro/fields/class-acf-field-repeater.php:450
msgid "Layout"
msgstr "Layout"

#: includes/fields.php:326
msgid "Field type does not exist"
msgstr "Tipo de campo não existe"

#: includes/fields.php:326
msgid "Unknown"
msgstr "Desconhecido"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Acordeão"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Abrir"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Exibe esse acordeão como aberto ao carregar a página."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Expansão-multipla"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others. "
msgstr "Permite que esse acordeão abra sem fechar os outros."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Ponto final"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Define um ponto final para que o acordeão anterior pare. Esse acordeão não "
"será visível."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Grupo de botões"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "Escolhas"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "Digite cada opção em uma nova linha."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Para mais controle, você pode especificar tanto os valores quanto os "
"rótulos, como nos exemplos:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "vermelho : Vermelho"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:412
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-taxonomy.php:793
#: includes/fields/class-acf-field-user.php:408
msgid "Allow Null?"
msgstr "Permitir Nulo?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:276
#: includes/fields/class-acf-field-range.php:148
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:410
msgid "Default Value"
msgstr "Valor Padrão"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:277
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:411
msgid "Appears when creating a new post"
msgstr "Aparece quando o novo post é criado"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:391
#: includes/fields/class-acf-field-radio.php:292
msgid "Horizontal"
msgstr "Horizontal"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:390
#: includes/fields/class-acf-field-radio.php:291
msgid "Vertical"
msgstr "Vertical"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:408
#: includes/fields/class-acf-field-file.php:204
#: includes/fields/class-acf-field-image.php:188
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:299
#: includes/fields/class-acf-field-taxonomy.php:833
msgid "Return Value"
msgstr "Valor Retornado"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:409
#: includes/fields/class-acf-field-file.php:205
#: includes/fields/class-acf-field-image.php:189
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:300
msgid "Specify the returned value on front end"
msgstr "Especifique a forma como os valores serão retornados no front-end"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-radio.php:305
#: includes/fields/class-acf-field-select.php:431
msgid "Value"
msgstr "Valor"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:416
#: includes/fields/class-acf-field-radio.php:307
#: includes/fields/class-acf-field-select.php:433
msgid "Both (Array)"
msgstr "Ambos (Array)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Checkbox"
msgstr "Checkbox"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Selecionar Tudo"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Adicionar nova opção"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Permitir personalização"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Permite adicionar valores personalizados"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Salvar personalização"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Salva valores personalizados nas opções do campo"

#: includes/fields/class-acf-field-checkbox.php:376
#: includes/fields/class-acf-field-select.php:378
msgid "Enter each default value on a new line"
msgstr "Digite cada valor padrão em uma nova linha"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Toggle"
msgstr "Selecionar Tudo"

#: includes/fields/class-acf-field-checkbox.php:399
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Incluir um checkbox adicional que marca (ou desmarca) todas as opções"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Seletor de Cor"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Limpar"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Padrão"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Selecionar Cor"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Cor Atual"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Seletor de Data"

#: includes/fields/class-acf-field-date_picker.php:33
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Concluído"

#: includes/fields/class-acf-field-date_picker.php:34
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Hoje"

#: includes/fields/class-acf-field-date_picker.php:35
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Próximo"

#: includes/fields/class-acf-field-date_picker.php:36
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Anterior"

#: includes/fields/class-acf-field-date_picker.php:37
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sem"

#: includes/fields/class-acf-field-date_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:181
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Formato de Exibição"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "O formato que será exibido ao editar um post"

#: includes/fields/class-acf-field-date_picker.php:216
#: includes/fields/class-acf-field-date_picker.php:247
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-date_time_picker.php:208
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Customizado:"

#: includes/fields/class-acf-field-date_picker.php:226
msgid "Save Format"
msgstr "Salvar formato"

#: includes/fields/class-acf-field-date_picker.php:227
msgid "The format used when saving a value"
msgstr "O formato usado ao salvar um valor"

#: includes/fields/class-acf-field-date_picker.php:237
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-relationship.php:697
#: includes/fields/class-acf-field-select.php:426
#: includes/fields/class-acf-field-time_picker.php:124
msgid "Return Format"
msgstr "Formato dos Dados"

#: includes/fields/class-acf-field-date_picker.php:238
#: includes/fields/class-acf-field-date_time_picker.php:199
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "O formato que será retornado através das funções de template"

#: includes/fields/class-acf-field-date_picker.php:256
#: includes/fields/class-acf-field-date_time_picker.php:215
msgid "Week Starts On"
msgstr "Semana começa em"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Seletor de Data e Hora"

#: includes/fields/class-acf-field-date_time_picker.php:33
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Selecione a hora"

#: includes/fields/class-acf-field-date_time_picker.php:34
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:35
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Hora"

#: includes/fields/class-acf-field-date_time_picker.php:36
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuto"

#: includes/fields/class-acf-field-date_time_picker.php:37
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Segundo"

#: includes/fields/class-acf-field-date_time_picker.php:38
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milissegundo"

#: includes/fields/class-acf-field-date_time_picker.php:39
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microssegundo"

#: includes/fields/class-acf-field-date_time_picker.php:40
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fuso Horário"

#: includes/fields/class-acf-field-date_time_picker.php:41
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Agora"

#: includes/fields/class-acf-field-date_time_picker.php:42
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Pronto"

#: includes/fields/class-acf-field-date_time_picker.php:43
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Selecionar"

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Texto Placeholder"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Texto que aparecerá dentro do campo (até que algo seja digitado)"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:187
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Prefixo"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Texto que aparecerá antes do campo"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:196
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Sufixo"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Texto que aparecerá após o campo"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Arquivo"

#: includes/fields/class-acf-field-file.php:36
msgid "Edit File"
msgstr "Editar Arquivo"

#: includes/fields/class-acf-field-file.php:37
msgid "Update File"
msgstr "Atualizar Arquivo"

#: includes/fields/class-acf-field-file.php:38
#: includes/fields/class-acf-field-image.php:43 includes/media.php:57
#: pro/fields/class-acf-field-gallery.php:44
msgid "Uploaded to this post"
msgstr "Anexado ao post"

#: includes/fields/class-acf-field-file.php:130
msgid "File name"
msgstr "Nome do arquivo"

#: includes/fields/class-acf-field-file.php:134
#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: pro/fields/class-acf-field-gallery.php:690
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Tamanho"

#: includes/fields/class-acf-field-file.php:143
#: includes/fields/class-acf-field-image.php:124
#: includes/fields/class-acf-field-link.php:140 includes/input.php:269
#: pro/fields/class-acf-field-gallery.php:343
#: pro/fields/class-acf-field-gallery.php:531
msgid "Remove"
msgstr "Remover"

#: includes/fields/class-acf-field-file.php:159
msgid "Add File"
msgstr "Adicionar Arquivo"

#: includes/fields/class-acf-field-file.php:210
msgid "File Array"
msgstr "Array do arquivo"

#: includes/fields/class-acf-field-file.php:211
msgid "File URL"
msgstr "URL do Arquivo"

#: includes/fields/class-acf-field-file.php:212
msgid "File ID"
msgstr "ID do Arquivo"

#: includes/fields/class-acf-field-file.php:219
#: includes/fields/class-acf-field-image.php:213
#: pro/fields/class-acf-field-gallery.php:655
msgid "Library"
msgstr "Biblioteca"

#: includes/fields/class-acf-field-file.php:220
#: includes/fields/class-acf-field-image.php:214
#: pro/fields/class-acf-field-gallery.php:656
msgid "Limit the media library choice"
msgstr "Limitar a escolha da biblioteca de mídia"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:219
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:661
msgid "All"
msgstr "Todos"

#: includes/fields/class-acf-field-file.php:226
#: includes/fields/class-acf-field-image.php:220
#: pro/fields/class-acf-field-gallery.php:662
msgid "Uploaded to post"
msgstr "Anexado ao post"

#: includes/fields/class-acf-field-file.php:233
#: includes/fields/class-acf-field-image.php:227
#: pro/fields/class-acf-field-gallery.php:669
msgid "Minimum"
msgstr "Mínimo"

#: includes/fields/class-acf-field-file.php:234
#: includes/fields/class-acf-field-file.php:245
msgid "Restrict which files can be uploaded"
msgstr "Limita o tamanho dos arquivos que poderão ser carregados"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:256
#: pro/fields/class-acf-field-gallery.php:698
msgid "Maximum"
msgstr "Máximo"

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:285
#: pro/fields/class-acf-field-gallery.php:727
msgid "Allowed file types"
msgstr "Tipos de arquivos permitidos"

#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-image.php:286
#: pro/fields/class-acf-field-gallery.php:728
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Lista separada por vírgulas. Deixe em branco para permitir todos os tipos"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Mapa do Google"

#: includes/fields/class-acf-field-google-map.php:40
msgid "Locating"
msgstr "Localizando"

#: includes/fields/class-acf-field-google-map.php:41
msgid "Sorry, this browser does not support geolocation"
msgstr "O seu navegador não suporta o recurso de geolocalização"

#: includes/fields/class-acf-field-google-map.php:113
msgid "Clear location"
msgstr "Limpar a localização"

#: includes/fields/class-acf-field-google-map.php:114
msgid "Find current location"
msgstr "Encontre a localização atual"

#: includes/fields/class-acf-field-google-map.php:117
msgid "Search for address..."
msgstr "Pesquisar endereço…"

#: includes/fields/class-acf-field-google-map.php:147
#: includes/fields/class-acf-field-google-map.php:158
msgid "Center"
msgstr "Centro"

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-google-map.php:159
msgid "Center the initial map"
msgstr "Centro inicial do mapa"

#: includes/fields/class-acf-field-google-map.php:170
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:171
msgid "Set the initial zoom level"
msgstr "Definir o nível do zoom inicial"

#: includes/fields/class-acf-field-google-map.php:180
#: includes/fields/class-acf-field-image.php:239
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-oembed.php:281
#: pro/fields/class-acf-field-gallery.php:681
#: pro/fields/class-acf-field-gallery.php:710
msgid "Height"
msgstr "Altura"

#: includes/fields/class-acf-field-google-map.php:181
msgid "Customise the map height"
msgstr "Personalizar a altura do mapa"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Grupo"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:389
msgid "Sub Fields"
msgstr "Sub Campos"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:840
msgid "Specify the style used to render the selected fields"
msgstr "Especifique o estilo utilizado para exibir os campos selecionados"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:845
#: pro/fields/class-acf-field-flexible-content.php:612
#: pro/fields/class-acf-field-repeater.php:458
msgid "Block"
msgstr "Bloco"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:846
#: pro/fields/class-acf-field-flexible-content.php:611
#: pro/fields/class-acf-field-repeater.php:457
msgid "Table"
msgstr "Tabela"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:847
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:459
msgid "Row"
msgstr "Linha"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Imagem"

#: includes/fields/class-acf-field-image.php:40
msgid "Select Image"
msgstr "Selecionar Imagem"

#: includes/fields/class-acf-field-image.php:41
#: pro/fields/class-acf-field-gallery.php:42
msgid "Edit Image"
msgstr "Editar Imagem"

#: includes/fields/class-acf-field-image.php:42
#: pro/fields/class-acf-field-gallery.php:43
msgid "Update Image"
msgstr "Atualizar Imagem"

#: includes/fields/class-acf-field-image.php:44
msgid "All images"
msgstr "Todas as imagens"

#: includes/fields/class-acf-field-image.php:140
msgid "No image selected"
msgstr "Nenhuma imagem selecionada"

#: includes/fields/class-acf-field-image.php:140
msgid "Add Image"
msgstr "Adicionar Imagem"

#: includes/fields/class-acf-field-image.php:194
msgid "Image Array"
msgstr "Array da Imagem"

#: includes/fields/class-acf-field-image.php:195
msgid "Image URL"
msgstr "URL da Imagem"

#: includes/fields/class-acf-field-image.php:196
msgid "Image ID"
msgstr "ID da Imagem"

#: includes/fields/class-acf-field-image.php:203
msgid "Preview Size"
msgstr "Tamanho da Pré-visualização"

#: includes/fields/class-acf-field-image.php:204
msgid "Shown when entering data"
msgstr "Exibido ao inserir os dados"

#: includes/fields/class-acf-field-image.php:228
#: includes/fields/class-acf-field-image.php:257
#: pro/fields/class-acf-field-gallery.php:670
#: pro/fields/class-acf-field-gallery.php:699
msgid "Restrict which images can be uploaded"
msgstr "Limita as imagens que poderão ser carregadas"

#: includes/fields/class-acf-field-image.php:231
#: includes/fields/class-acf-field-image.php:260
#: includes/fields/class-acf-field-oembed.php:270
#: pro/fields/class-acf-field-gallery.php:673
#: pro/fields/class-acf-field-gallery.php:702
msgid "Width"
msgstr "Largura"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Selecionar Link"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Abre em uma nova janela/aba"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Array do Link"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "URL do Link"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Mensagem"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Novas Linhas"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Controla como as novas linhas são renderizadas"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Adicionar parágrafos automaticamente"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Adicionar &lt;br&gt; automaticamente"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Sem Formatação"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Ignorar HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Permitir que a marcação HTML seja exibida como texto ao invés de ser "
"renderizada"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Número"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:157
msgid "Minimum Value"
msgstr "Valor Mínimo"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:167
msgid "Maximum Value"
msgstr "Valor Máximo"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:177
msgid "Step Size"
msgstr "Tamanho das frações"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "O valor deve ser um número"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "O valor deve ser igual ou maior que %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "O valor deve ser igual ou menor que %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "Digite a URL"

#: includes/fields/class-acf-field-oembed.php:234
#: includes/fields/class-acf-field-taxonomy.php:898
msgid "Error."
msgstr "Erro."

#: includes/fields/class-acf-field-oembed.php:234
msgid "No embed found for the given URL."
msgstr "Nenhuma mídia incorporada encontrada na URL fornecida."

#: includes/fields/class-acf-field-oembed.php:267
#: includes/fields/class-acf-field-oembed.php:278
msgid "Embed Size"
msgstr "Tamanho da Mídia incorporada"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Arquivos"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:268
#: includes/fields/class-acf-field-taxonomy.php:986
msgid "Parent"
msgstr "Página de Nível mais Alto (sem mãe)"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:384
#: includes/fields/class-acf-field-relationship.php:623
msgid "Filter by Post Type"
msgstr "Filtrar por Tipo de Post"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:392
#: includes/fields/class-acf-field-relationship.php:631
msgid "All post types"
msgstr "Todos os tipos de posts"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:398
#: includes/fields/class-acf-field-relationship.php:637
msgid "Filter by Taxonomy"
msgstr "Filtrar por Taxonomia"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:406
#: includes/fields/class-acf-field-relationship.php:645
msgid "All taxonomies"
msgstr "Todas as taxonomias"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Permitir URLs do Arquivo"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:422
#: includes/fields/class-acf-field-select.php:396
#: includes/fields/class-acf-field-user.php:418
msgid "Select multiple values?"
msgstr "Selecionar vários valores?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Senha"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:702
msgid "Post Object"
msgstr "Objeto do Post"

#: includes/fields/class-acf-field-post_object.php:438
#: includes/fields/class-acf-field-relationship.php:703
msgid "Post ID"
msgstr "ID do Post"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Botão de Rádio"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Outro"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr ""
"Adicionar uma opção ‘Outro’ para permitir a inserção de valores "
"personalizados"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Salvar Outro"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr ""
"Salvar os valores personalizados inseridos na opção ‘Outros’ na lista de "
"escolhas do campo"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Faixa"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relação"

#: includes/fields/class-acf-field-relationship.php:37
msgid "Minimum values reached ( {min} values )"
msgstr "Quantidade mínima atingida ( {min} item(s) )"

#: includes/fields/class-acf-field-relationship.php:38
msgid "Maximum values reached ( {max} values )"
msgstr "Quantidade máxima atingida ( {max} item(s) )"

#: includes/fields/class-acf-field-relationship.php:39
msgid "Loading"
msgstr "Carregando"

#: includes/fields/class-acf-field-relationship.php:40
msgid "No matches found"
msgstr "Nenhuma correspondência encontrada"

#: includes/fields/class-acf-field-relationship.php:423
msgid "Select post type"
msgstr "Selecione o tipo de post"

#: includes/fields/class-acf-field-relationship.php:449
msgid "Select taxonomy"
msgstr "Selecione a taxonomia"

#: includes/fields/class-acf-field-relationship.php:539
msgid "Search..."
msgstr "Pesquisar…"

#: includes/fields/class-acf-field-relationship.php:651
msgid "Filters"
msgstr "Filtros"

#: includes/fields/class-acf-field-relationship.php:657
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Tipo de Post"

#: includes/fields/class-acf-field-relationship.php:658
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:763
msgid "Taxonomy"
msgstr "Taxonomia"

#: includes/fields/class-acf-field-relationship.php:665
msgid "Elements"
msgstr "Elementos"

#: includes/fields/class-acf-field-relationship.php:666
msgid "Selected elements will be displayed in each result"
msgstr "Os elementos selecionados serão exibidos em cada resultado do filtro"

#: includes/fields/class-acf-field-relationship.php:677
msgid "Minimum posts"
msgstr "Qtde. mínima de posts"

#: includes/fields/class-acf-field-relationship.php:686
msgid "Maximum posts"
msgstr "Qtde. máxima de posts"

#: includes/fields/class-acf-field-relationship.php:790
#: pro/fields/class-acf-field-gallery.php:800
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s requer a seleção de ao menos %s item"
msgstr[1] "%s requer a seleção de ao menos %s itens"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:785
msgctxt "noun"
msgid "Select"
msgstr "Seleção"

#: includes/fields/class-acf-field-select.php:38
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Um resultado localizado, pressione Enter para selecioná-lo."

#: includes/fields/class-acf-field-select.php:39
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d resultados localizados, utilize as setas para cima ou baixo para navegar."

#: includes/fields/class-acf-field-select.php:40
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nenhuma correspondência encontrada"

#: includes/fields/class-acf-field-select.php:41
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Digite 1 ou mais caracteres"

#: includes/fields/class-acf-field-select.php:42
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Digite %d ou mais caracteres"

#: includes/fields/class-acf-field-select.php:43
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Apague 1 caractere"

#: includes/fields/class-acf-field-select.php:44
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Apague %d caracteres"

#: includes/fields/class-acf-field-select.php:45
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Você pode selecionar apenas 1 item"

#: includes/fields/class-acf-field-select.php:46
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Você pode selecionar apenas %d itens"

#: includes/fields/class-acf-field-select.php:47
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Carregando mais resultados&hellip;"

#: includes/fields/class-acf-field-select.php:48
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Pesquisando&hellip;"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Falha ao carregar"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:54
msgctxt "verb"
msgid "Select"
msgstr "Selecionar"

#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Interface do campo aprimorada"

#: includes/fields/class-acf-field-select.php:416
msgid "Use AJAX to lazy load choices?"
msgstr "Utilizar AJAX para carregar opções?"

#: includes/fields/class-acf-field-select.php:427
msgid "Specify the value returned"
msgstr "Especifique a forma como os valores serão retornados"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Separador"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Aba"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Posicionamento"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Utilizar este campo como um ponto final e iniciar um novo grupo de abas."

#: includes/fields/class-acf-field-taxonomy.php:713
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Sem %s"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "None"
msgstr "Nenhuma"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Select the taxonomy to be displayed"
msgstr "Selecione a taxonomia que será exibida"

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Appearance"
msgstr "Aparência"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Select the appearance of this field"
msgstr "Selecione a aparência deste campo"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Multiple Values"
msgstr "Vários valores"

#: includes/fields/class-acf-field-taxonomy.php:781
msgid "Multi Select"
msgstr "Seleção Múltipla"

#: includes/fields/class-acf-field-taxonomy.php:783
msgid "Single Value"
msgstr "Um único valor"

#: includes/fields/class-acf-field-taxonomy.php:784
msgid "Radio Buttons"
msgstr "Botões de Rádio"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "Create Terms"
msgstr "Criar Termos"

#: includes/fields/class-acf-field-taxonomy.php:804
msgid "Allow new terms to be created whilst editing"
msgstr "Permite que novos termos sejam criados diretamente na tela de edição"

#: includes/fields/class-acf-field-taxonomy.php:813
msgid "Save Terms"
msgstr "Salvar Termos"

#: includes/fields/class-acf-field-taxonomy.php:814
msgid "Connect selected terms to the post"
msgstr "Atribui e conecta os termos selecionados ao post"

#: includes/fields/class-acf-field-taxonomy.php:823
msgid "Load Terms"
msgstr "Carregar Termos"

#: includes/fields/class-acf-field-taxonomy.php:824
msgid "Load value from posts terms"
msgstr "Carrega os termos que estão atribuídos ao post"

#: includes/fields/class-acf-field-taxonomy.php:838
msgid "Term Object"
msgstr "Objeto do Termo"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Term ID"
msgstr "ID do Termo"

#: includes/fields/class-acf-field-taxonomy.php:898
#, php-format
msgid "User unable to add new %s"
msgstr "Usuário incapaz de adicionar novo(a) %s"

#: includes/fields/class-acf-field-taxonomy.php:911
#, php-format
msgid "%s already exists"
msgstr "%s já existe"

#: includes/fields/class-acf-field-taxonomy.php:952
#, php-format
msgid "%s added"
msgstr "%s adicionado(a)"

#: includes/fields/class-acf-field-taxonomy.php:997
msgid "Add"
msgstr "Adicionar"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Limite de Caracteres"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Deixe em branco para nenhum limite"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Área de Texto"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Linhas"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Define a altura da área de texto"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Seletor de Hora"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Verdadeiro / Falso"

#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159 includes/input.php:267
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Sim"

#: includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:169 includes/input.php:268
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Não"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Exibe texto ao lado da caixa de seleção"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "No Texto"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Texto exibido quando ativo"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Off Text"
msgstr "Fora do texto"

#: includes/fields/class-acf-field-true_false.php:166
msgid "Text shown when inactive"
msgstr "Texto exibido quando inativo"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Você deve fornecer uma URL válida"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Usuário"

#: includes/fields/class-acf-field-user.php:393
msgid "Filter by role"
msgstr "Filtrar por função"

#: includes/fields/class-acf-field-user.php:401
msgid "All user roles"
msgstr "Todas as funções de usuários"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Editor Wysiwyg"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual"
msgstr "Visual"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Texto"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Click to initialize TinyMCE"
msgstr "Clique para inicializar o TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Tabs"
msgstr "Abas"

#: includes/fields/class-acf-field-wysiwyg.php:424
msgid "Visual & Text"
msgstr "Visual & Texto"

#: includes/fields/class-acf-field-wysiwyg.php:425
msgid "Visual Only"
msgstr "Apenas Visual"

#: includes/fields/class-acf-field-wysiwyg.php:426
msgid "Text Only"
msgstr "Apenas Texto"

#: includes/fields/class-acf-field-wysiwyg.php:433
msgid "Toolbar"
msgstr "Barra de Ferramentas"

#: includes/fields/class-acf-field-wysiwyg.php:443
msgid "Show Media Upload Buttons?"
msgstr "Mostrar Botões de Upload de Mídia?"

#: includes/fields/class-acf-field-wysiwyg.php:453
msgid "Delay initialization?"
msgstr "Atrasar a inicialização?"

#: includes/fields/class-acf-field-wysiwyg.php:454
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE não será iniciado até que o campo seja clicado"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr "Editar Grupo de Campos"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Validar Email"

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:573 pro/options-page.php:81
msgid "Update"
msgstr "Atualizar"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Post atualizado"

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr "Spam Detectado"

#: includes/input.php:259
msgid "Expand Details"
msgstr "Expandir Detalhes"

#: includes/input.php:260
msgid "Collapse Details"
msgstr "Recolher Detalhes"

#: includes/input.php:261
msgid "Validation successful"
msgstr "Validação realizada com sucesso"

#: includes/input.php:262 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Falha na validação"

#: includes/input.php:263
msgid "1 field requires attention"
msgstr "1 campo requer sua atenção"

#: includes/input.php:264
#, php-format
msgid "%d fields require attention"
msgstr "%d campos requerem sua atenção"

#: includes/input.php:265
msgid "Restricted"
msgstr "Restrito"

#: includes/input.php:266
msgid "Are you sure?"
msgstr "Você tem certeza?"

#: includes/input.php:270
msgid "Cancel"
msgstr "Cancelar"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Post"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Página"

#: includes/locations.php:96
msgid "Forms"
msgstr "Formulários"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Anexo"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Todos %s formatos"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Comentário"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Função do Usuário atual"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Usuário atual"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Logado"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Visualizando a parte pública do site (front-end)"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Visualizando a parte administrativa do site (back-end)"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Item do menu"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Localização do menu"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Menus"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Página Mãe"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Modelo de Página"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Modelo Padrão"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Tipo de Página"

#: includes/locations/class-acf-location-page-type.php:145
msgid "Front Page"
msgstr "Página Inicial"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Posts Page"
msgstr "Página de Posts"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Top Level Page (no parent)"
msgstr "Página de Nível mais Alto (sem mãe)"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Parent Page (has children)"
msgstr "Página Mãe (tem filhas)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Child Page (has parent)"
msgstr "Página Filha (possui mãe)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Categoria de Post"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Formato de Post"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Status do Post"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Taxonomia de Post"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Modelo de Postagem"

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "Termo da Taxonomia"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Formulário do Usuário"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Adicionar / Editar"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Registrar"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Função do Usuário"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widget"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr "Editar"

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr "Atualizar"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "É necessário preencher o campo %s"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Publicar"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Nenhum Grupo de Campos Personalizados encontrado para esta página de opções. "
"<a href=\"%s\">Criar um Grupo de Campos Personalizado</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Erro</b>. Não foi possível conectar ao servidor de atualização"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Atualizações"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Desativar Licença"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Ativar Licença"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Informações da Licença"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Para desbloquear as atualizações, digite sua chave de licença abaixo. Se "
"você não possui uma licença, consulte os <a href=\"%s\" target=\"_blank"
"\">detalhes e preços</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Chave de Licença"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informações de Atualização"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Versão Atual"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Versão mais Recente"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Atualização Disponível"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Atualizar Plugin"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Digite sua chave de licença acima para desbloquear atualizações"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Verificar Novamente"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Aviso de Atualização"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clone"

#: pro/fields/class-acf-field-clone.php:808
msgid "Select one or more fields you wish to clone"
msgstr "Selecione um ou mais campos que deseja clonar"

#: pro/fields/class-acf-field-clone.php:825
msgid "Display"
msgstr "Exibição"

#: pro/fields/class-acf-field-clone.php:826
msgid "Specify the style used to render the clone field"
msgstr "Especifique o estilo utilizado para exibir o campo de clone"

#: pro/fields/class-acf-field-clone.php:831
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grupo (mostra os campos selecionados em um grupo dentro deste campo)"

#: pro/fields/class-acf-field-clone.php:832
msgid "Seamless (replaces this field with selected fields)"
msgstr "Sem bordas (substitui este campo pelos campos selecionados)"

#: pro/fields/class-acf-field-clone.php:853
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Os rótulos serão exibidos como %s"

#: pro/fields/class-acf-field-clone.php:856
msgid "Prefix Field Labels"
msgstr "Prefixo dos Rótulos dos Campos"

#: pro/fields/class-acf-field-clone.php:867
#, php-format
msgid "Values will be saved as %s"
msgstr "Valores serão salvos como %s"

#: pro/fields/class-acf-field-clone.php:870
msgid "Prefix Field Names"
msgstr "Prefixo dos Nomes dos Campos"

#: pro/fields/class-acf-field-clone.php:988
msgid "Unknown field"
msgstr "Campo desconhecido"

#: pro/fields/class-acf-field-clone.php:1027
msgid "Unknown field group"
msgstr "Grupo de campo desconhecido"

#: pro/fields/class-acf-field-clone.php:1031
#, php-format
msgid "All fields from %s field group"
msgstr "Todos os campos do grupo de campos %s"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:174
#: pro/fields/class-acf-field-repeater.php:470
msgid "Add Row"
msgstr "Adicionar Linha"

#: pro/fields/class-acf-field-flexible-content.php:34
msgid "layout"
msgstr "layout"

#: pro/fields/class-acf-field-flexible-content.php:35
msgid "layouts"
msgstr "layouts"

#: pro/fields/class-acf-field-flexible-content.php:36
msgid "remove {layout}?"
msgstr "remover {layout}?"

#: pro/fields/class-acf-field-flexible-content.php:37
msgid "This field requires at least {min} {identifier}"
msgstr "Este campo requer ao menos {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:38
msgid "This field has a limit of {max} {identifier}"
msgstr "Este campo tem um limite de {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:39
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Este campo requer ao menos {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:40
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "A quantidade máxima de {label} foi atingida ({max} {identifier})"

#: pro/fields/class-acf-field-flexible-content.php:41
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponível (máx {max})"

#: pro/fields/class-acf-field-flexible-content.php:42
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} obrigatório (mín {min})"

#: pro/fields/class-acf-field-flexible-content.php:43
msgid "Flexible Content requires at least 1 layout"
msgstr "O campo de Conteúdo Flexível requer pelo menos 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:273
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Clique no botão “%s” abaixo para iniciar a criação do seu layout"

#: pro/fields/class-acf-field-flexible-content.php:406
msgid "Add layout"
msgstr "Adicionar layout"

#: pro/fields/class-acf-field-flexible-content.php:407
msgid "Remove layout"
msgstr "Remover layout"

#: pro/fields/class-acf-field-flexible-content.php:408
#: pro/fields/class-acf-field-repeater.php:298
msgid "Click to toggle"
msgstr "Clique para alternar"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder Layout"
msgstr "Reordenar Layout"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder"
msgstr "Reordenar"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete Layout"
msgstr "Excluir Layout"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate Layout"
msgstr "Duplicar Layout"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New Layout"
msgstr "Adicionar Novo Layout"

#: pro/fields/class-acf-field-flexible-content.php:628
msgid "Min"
msgstr "Mín"

#: pro/fields/class-acf-field-flexible-content.php:641
msgid "Max"
msgstr "Máx"

#: pro/fields/class-acf-field-flexible-content.php:668
#: pro/fields/class-acf-field-repeater.php:466
msgid "Button Label"
msgstr "Rótulo do Botão"

#: pro/fields/class-acf-field-flexible-content.php:677
msgid "Minimum Layouts"
msgstr "Qtde. Mínima de Layouts"

#: pro/fields/class-acf-field-flexible-content.php:686
msgid "Maximum Layouts"
msgstr "Qtde. Máxima de Layouts"

#: pro/fields/class-acf-field-gallery.php:41
msgid "Add Image to Gallery"
msgstr "Adicionar Imagem à Galeria"

#: pro/fields/class-acf-field-gallery.php:45
msgid "Maximum selection reached"
msgstr "A quantidade máxima de seleções foi atingida"

#: pro/fields/class-acf-field-gallery.php:321
msgid "Length"
msgstr "Duração"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Legenda"

#: pro/fields/class-acf-field-gallery.php:373
msgid "Alt Text"
msgstr "Texto Alternativo"

#: pro/fields/class-acf-field-gallery.php:544
msgid "Add to gallery"
msgstr "Adicionar à galeria"

#: pro/fields/class-acf-field-gallery.php:548
msgid "Bulk actions"
msgstr "Ações em massa"

#: pro/fields/class-acf-field-gallery.php:549
msgid "Sort by date uploaded"
msgstr "Ordenar por data de envio"

#: pro/fields/class-acf-field-gallery.php:550
msgid "Sort by date modified"
msgstr "Ordenar por data de modificação"

#: pro/fields/class-acf-field-gallery.php:551
msgid "Sort by title"
msgstr "Ordenar por título"

#: pro/fields/class-acf-field-gallery.php:552
msgid "Reverse current order"
msgstr "Inverter ordem atual"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Close"
msgstr "Fechar"

#: pro/fields/class-acf-field-gallery.php:624
msgid "Minimum Selection"
msgstr "Qtde. Mínima de Seleções"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Maximum Selection"
msgstr "Qtde. Máxima de Seleções"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Insert"
msgstr "Inserir"

#: pro/fields/class-acf-field-gallery.php:643
msgid "Specify where new attachments are added"
msgstr "Especifique onde os novos anexos serão adicionados"

#: pro/fields/class-acf-field-gallery.php:647
msgid "Append to the end"
msgstr "Adicionar no final da galeria"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Prepend to the beginning"
msgstr "Adicionar no início da galeria"

#: pro/fields/class-acf-field-repeater.php:36
msgid "Minimum rows reached ({min} rows)"
msgstr "Quantidade mínima atingida ( {min} linha(s) )"

#: pro/fields/class-acf-field-repeater.php:37
msgid "Maximum rows reached ({max} rows)"
msgstr "Quantidade máxima atingida ( {max} linha(s) )"

#: pro/fields/class-acf-field-repeater.php:343
msgid "Add row"
msgstr "Adicionar linha"

#: pro/fields/class-acf-field-repeater.php:344
msgid "Remove row"
msgstr "Remover linha"

#: pro/fields/class-acf-field-repeater.php:419
msgid "Collapsed"
msgstr "Recolher"

#: pro/fields/class-acf-field-repeater.php:420
msgid "Select a sub field to show when row is collapsed"
msgstr "Selecione um sub campo para exibir quando a linha estiver recolhida"

#: pro/fields/class-acf-field-repeater.php:430
msgid "Minimum Rows"
msgstr "Qtde. Mínima de Linhas"

#: pro/fields/class-acf-field-repeater.php:440
msgid "Maximum Rows"
msgstr "Qtde. Máxima de Linhas"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Não existem Páginas de Opções disponíveis"

#: pro/options-page.php:51
msgid "Options"
msgstr "Opções"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Opções Atualizadas"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Para ativar atualizações, digite sua chave de licença  na página <a "
"href=“%s”>Atualizações</a>. Se você não possui uma licença, consulte os <a "
"href=“%s”>detalhes e preços</a>."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Exportar Grupos de Campos para PHP"

#~ msgid "Download export file"
#~ msgstr "Download do arquivo JSON"

#~ msgid "Generate export code"
#~ msgstr "Gerar código PHP"

#~ msgid "Import"
#~ msgstr "Importar"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "O campo Aba será exibido incorretamente quando adicionado em um layout do "
#~ "tipo Tabela de campos repetidores ou de conteúdos flexíveis"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Utilize o campo “Aba” para agrupar seus campos e organizar melhor sua "
#~ "tela de edição."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Todos os campos que seguirem este campo “Aba” (ou até que outra “Aba” "
#~ "seja definida) ficarão juntos em um grupo que utilizará o rótulo deste "
#~ "campo como título da guia."

#~ msgid "Getting Started"
#~ msgstr "Primeiros Passos"

#~ msgid "Field Types"
#~ msgstr "Tipos de Campos"

#~ msgid "Functions"
#~ msgstr "Funções"

#~ msgid "Actions"
#~ msgstr "Ações"

#~ msgid "Features"
#~ msgstr "Características"

#~ msgid "How to"
#~ msgstr "Como"

#~ msgid "Tutorials"
#~ msgstr "Tutoriais"

#~ msgid "FAQ"
#~ msgstr "Perguntas Frequentes"

#~ msgid "Error"
#~ msgstr "Erro"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "1 campo requer a sua atenção."
#~ msgstr[1] "%d campos requerem sua atenção."

#~ msgid "Disabled"
#~ msgstr "Desabilitado"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Desabilitado <span class=“count”>(%s)</span>"
#~ msgstr[1] "Desabilitados <span class=“count”>(%s)</span>"

#~ msgid "'How to' guides"
#~ msgstr "Guias práticos"

#~ msgid "Created by"
#~ msgstr "Criado por"

#~ msgid "Error loading update"
#~ msgstr "Erro ao carregar atualização"

#~ msgid "See what's new"
#~ msgstr "Veja o que há de novo"

#~ msgid "eg. Show extra content"
#~ msgstr "ex.: Mostrar conteúdo adicional"

#~ msgid "Select"
#~ msgstr "Seleção"

#~ msgctxt "Field label"
#~ msgid "Clone"
#~ msgstr "Clone"

#~ msgctxt "Field instruction"
#~ msgid "Clone"
#~ msgstr "Clone"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Erro de Conexão</b>. Tente novamente"
