!function(t,e){var i={};window.acf=i,i.data={},i.get=function(t){return this.data[t]||null},i.has=function(t){return null!==this.get(t)},i.set=function(t,e){return this.data[t]=e,this};var n=0;i.uniqueId=function(t){var e=++n+"";return t?t+e:e},i.uniqueArray=function(t){function e(t,e,i){return i.indexOf(t)===e}return t.filter(e)};var a="";i.uniqid=function(t,e){var i;void 0===t&&(t="");var n=function(t,e){return e<(t=parseInt(t,10).toString(16)).length?t.slice(t.length-e):e>t.length?Array(e-t.length+1).join("0")+t:t};return a||(a=Math.floor(123456789*Math.random())),a++,i=t,i+=n(parseInt((new Date).getTime()/1e3,10),8),i+=n(a,5),e&&(i+=(10*Math.random()).toFixed(8).toString()),i},i.strReplace=function(t,e,i){return i.split(t).join(e)},i.strCamelCase=function(t){return t=(t=t.replace(/[_-]/g," ")).replace(/(?:^\w|\b\w|\s+)/g,function(t,e){return 0==+t?"":0==e?t.toLowerCase():t.toUpperCase()})},i.strPascalCase=function(t){var e=i.strCamelCase(t);return e.charAt(0).toUpperCase()+e.slice(1)},i.strSlugify=function(t){return i.strReplace("_","-",t.toLowerCase())},i.strSanitize=function(t){var e={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","Æ":"AE","Ç":"C","È":"E","É":"E","Ê":"E","Ë":"E","Ì":"I","Í":"I","Î":"I","Ï":"I","Ð":"D","Ñ":"N","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","Ù":"U","Ú":"U","Û":"U","Ü":"U","Ý":"Y","ß":"s","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","æ":"ae","ç":"c","è":"e","é":"e","ê":"e","ë":"e","ì":"i","í":"i","î":"i","ï":"i","ñ":"n","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","ù":"u","ú":"u","û":"u","ü":"u","ý":"y","ÿ":"y","Ā":"A","ā":"a","Ă":"A","ă":"a","Ą":"A","ą":"a","Ć":"C","ć":"c","Ĉ":"C","ĉ":"c","Ċ":"C","ċ":"c","Č":"C","č":"c","Ď":"D","ď":"d","Đ":"D","đ":"d","Ē":"E","ē":"e","Ĕ":"E","ĕ":"e","Ė":"E","ė":"e","Ę":"E","ę":"e","Ě":"E","ě":"e","Ĝ":"G","ĝ":"g","Ğ":"G","ğ":"g","Ġ":"G","ġ":"g","Ģ":"G","ģ":"g","Ĥ":"H","ĥ":"h","Ħ":"H","ħ":"h","Ĩ":"I","ĩ":"i","Ī":"I","ī":"i","Ĭ":"I","ĭ":"i","Į":"I","į":"i","İ":"I","ı":"i","Ĳ":"IJ","ĳ":"ij","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","Ĺ":"L","ĺ":"l","Ļ":"L","ļ":"l","Ľ":"L","ľ":"l","Ŀ":"L","ŀ":"l","Ł":"l","ł":"l","Ń":"N","ń":"n","Ņ":"N","ņ":"n","Ň":"N","ň":"n","ŉ":"n","Ō":"O","ō":"o","Ŏ":"O","ŏ":"o","Ő":"O","ő":"o","Œ":"OE","œ":"oe","Ŕ":"R","ŕ":"r","Ŗ":"R","ŗ":"r","Ř":"R","ř":"r","Ś":"S","ś":"s","Ŝ":"S","ŝ":"s","Ş":"S","ş":"s","Š":"S","š":"s","Ţ":"T","ţ":"t","Ť":"T","ť":"t","Ŧ":"T","ŧ":"t","Ũ":"U","ũ":"u","Ū":"U","ū":"u","Ŭ":"U","ŭ":"u","Ů":"U","ů":"u","Ű":"U","ű":"u","Ų":"U","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","ź":"z","Ż":"Z","ż":"z","Ž":"Z","ž":"z","ſ":"s","ƒ":"f","Ơ":"O","ơ":"o","Ư":"U","ư":"u","Ǎ":"A","ǎ":"a","Ǐ":"I","ǐ":"i","Ǒ":"O","ǒ":"o","Ǔ":"U","ǔ":"u","Ǖ":"U","ǖ":"u","Ǘ":"U","ǘ":"u","Ǚ":"U","ǚ":"u","Ǜ":"U","ǜ":"u","Ǻ":"A","ǻ":"a","Ǽ":"AE","ǽ":"ae","Ǿ":"O","ǿ":"o"," ":"_","'":"","?":"","/":"","\\":"",".":"",",":"","`":"",">":"","<":"",'"':"","[":"","]":"","|":"","{":"","}":"","(":"",")":""},i=/\W/g,n=function(t){return void 0!==e[t]?e[t]:t};return t=(t=t.replace(i,n)).toLowerCase()},i.strMatch=function(t,e){for(var i=0,n=Math.min(t.length,e.length),a=0;a<n&&t[a]===e[a];a++)i++;return i},i.decode=function(e){return t("<textarea/>").html(e).text()},i.strEscape=function(e){return t("<div>").text(e).html()},i.parseArgs=function(e,i){return"object"!=typeof e&&(e={}),"object"!=typeof i&&(i={}),t.extend({},i,e)},null==window.acfL10n&&(acfL10n={}),i.__=function(t){return acfL10n[t]||t},i._x=function(t,e){return acfL10n[t+"."+e]||acfL10n[t]||t},i._n=function(t,e,n){return 1==n?i.__(t):i.__(e)},i.isArray=function(t){return Array.isArray(t)},i.isObject=function(t){return"object"==typeof t};var r=function(t,e,n){var a=(e=e.replace("[]","[%%index%%]")).match(/([^\[\]])+/g);if(a)for(var r=a.length,o=t,s=0;s<r;s++){var c=String(a[s]);s==r-1?"%%index%%"===c?o.push(n):o[c]=n:("%%index%%"===a[s+1]?i.isArray(o[c])||(o[c]=[]):i.isObject(o[c])||(o[c]={}),o=o[c])}};i.serialize=function(t,e){var n={},a=i.serializeArray(t);void 0!==e&&(a=a.filter(function(t){return 0===t.name.indexOf(e)}).map(function(t){return t.name=t.name.slice(e.length),t}));for(var o=0;o<a.length;o++)r(n,a[o].name,a[o].value);return n},i.serializeArray=function(t){return t.find("select, textarea, input").serializeArray()},i.serializeForAjax=function(t){var e={},n;return i.serializeArray(t).map(function(t){"[]"===t.name.slice(-2)?(e[t.name]=e[t.name]||[],e[t.name].push(t.value)):e[t.name]=t.value}),e},i.addAction=function(t,e,n,a){return i.hooks.addAction.apply(this,arguments),this},i.removeAction=function(t,e){return i.hooks.removeAction.apply(this,arguments),this};var o={};i.doAction=function(t){return o[t]=1,i.hooks.doAction.apply(this,arguments),o[t]=0,this},i.doingAction=function(t){return 1===o[t]},i.didAction=function(t){return void 0!==o[t]},i.currentAction=function(){for(var t in o)if(o[t])return t;return!1},i.addFilter=function(t){return i.hooks.addFilter.apply(this,arguments),this},i.removeFilter=function(t){return i.hooks.removeFilter.apply(this,arguments),this},i.applyFilters=function(t){return i.hooks.applyFilters.apply(this,arguments)},i.arrayArgs=function(t){return Array.prototype.slice.call(t)};try{var s=JSON.parse(localStorage.getItem("acf"))||{}}catch(t){var s={}}var c=function(t){return"this."===t.substr(0,5)&&(t=t.substr(5)+"-"+i.get("post_id")),t};i.getPreference=function(t){return t=c(t),s[t]||null},i.setPreference=function(t,e){t=c(t),null===e?delete s[t]:s[t]=e,localStorage.setItem("acf",JSON.stringify(s))},i.removePreference=function(t){i.setPreference(t,null)},i.remove=function(t){t instanceof jQuery&&(t={target:t}),t=i.parseArgs(t,{target:!1,endHeight:0,complete:function(){}}),i.doAction("remove",t.target),t.target.is("tr")?u(t):l(t)};var l=function(t){var e=t.target,i=e.height(),n=e.width(),a=e.css("margin"),r=e.outerHeight(!0),o=e.attr("style")+"";e.wrap('<div class="acf-temp-remove" style="height:'+r+'px"></div>');var s=e.parent();e.css({height:i,width:n,margin:a,position:"absolute"}),setTimeout(function(){s.css({opacity:0,height:t.endHeight})},50),setTimeout(function(){e.attr("style",o),s.remove(),t.complete()},301)},u=function(e){var i=e.target,n=i.height(),a=i.children().length,r=t('<td class="acf-temp-remove" style="padding:0; height:'+n+'px" colspan="'+a+'"></td>');i.addClass("acf-remove-element"),setTimeout(function(){i.html(r)},251),setTimeout(function(){i.removeClass("acf-remove-element"),r.css({height:e.endHeight})},300),setTimeout(function(){i.remove(),e.complete()},451)};i.duplicate=function(t){t instanceof jQuery&&(t={target:t});var e=0;(t=i.parseArgs(t,{target:!1,search:"",replace:"",before:function(t){},after:function(t,e){},append:function(t,i){t.after(i),e=1}})).target=t.target||t.$el;var n=t.target;t.search=t.search||n.attr("data-id"),t.replace=t.replace||i.uniqid(),t.before(n),i.doAction("before_duplicate",n);var a=n.clone();return i.rename({target:a,search:t.search,replace:t.replace}),a.removeClass("acf-clone"),a.find(".ui-sortable").removeClass("ui-sortable"),t.after(n,a),i.doAction("after_duplicate",n,a),t.append(n,a),i.doAction("append",a),a},i.rename=function(t){t instanceof jQuery&&(t={target:t});var e=(t=i.parseArgs(t,{target:!1,destructive:!1,search:"",replace:""})).target,n=t.search||e.attr("data-id"),a=t.replace||i.uniqid("acf"),r=function(t,e){return e.replace(n,a)};if(t.destructive){var o=e.outerHTML();o=i.strReplace(n,a,o),e.replaceWith(o)}else e.attr("data-id",a),e.find('[id*="'+n+'"]').attr("id",r),e.find('[for*="'+n+'"]').attr("for",r),e.find('[name*="'+n+'"]').attr("name",r);return e},i.prepareForAjax=function(t){return t.nonce=i.get("nonce"),t.post_id=i.get("post_id"),i.has("language")&&(t.lang=i.get("language")),t=i.applyFilters("prepare_for_ajax",t)},i.startButtonLoading=function(t){t.prop("disabled",!0),t.after(' <i class="acf-loading"></i>')},i.stopButtonLoading=function(t){t.prop("disabled",!1),t.next(".acf-loading").remove()},i.showLoading=function(t){t.append('<div class="acf-loading-overlay"><i class="acf-loading"></i></div>')},i.hideLoading=function(t){t.children(".acf-loading-overlay").remove()},i.updateUserSetting=function(e,n){var a={action:"acf/ajax/user_setting",name:e,value:n};t.ajax({url:i.get("ajaxurl"),data:i.prepareForAjax(a),type:"post",dataType:"html"})},i.val=function(t,e,i){var n=t.val();return e!==n&&(t.val(e),t.is("select")&&null===t.val()?(t.val(n),!1):(!0!==i&&t.trigger("change"),!0))},i.show=function(t,e){return e&&i.unlock(t,"hidden",e),!i.isLocked(t,"hidden")&&(!!t.hasClass("acf-hidden")&&(t.removeClass("acf-hidden"),!0))},i.hide=function(t,e){return e&&i.lock(t,"hidden",e),!t.hasClass("acf-hidden")&&(t.addClass("acf-hidden"),!0)},i.isHidden=function(t){return t.hasClass("acf-hidden")},i.isVisible=function(t){return!i.isHidden(t)};var d=function(t,e){return!t.hasClass("acf-disabled")&&(e&&i.unlock(t,"disabled",e),!i.isLocked(t,"disabled")&&(!!t.prop("disabled")&&(t.prop("disabled",!1),!0)))};i.enable=function(e,i){if(e.attr("name"))return d(e,i);var n=!1;return e.find("[name]").each(function(){var e;d(t(this),i)&&(n=!0)}),n};var f=function(t,e){return e&&i.lock(t,"disabled",e),!t.prop("disabled")&&(t.prop("disabled",!0),!0)};i.disable=function(e,i){if(e.attr("name"))return f(e,i);var n=!1;return e.find("[name]").each(function(){var e;f(t(this),i)&&(n=!0)}),n},i.isset=function(t){for(var e=1;e<arguments.length;e++){if(!t||!t.hasOwnProperty(arguments[e]))return!1;t=t[arguments[e]]}return!0},i.isget=function(t){for(var e=1;e<arguments.length;e++){if(!t||!t.hasOwnProperty(arguments[e]))return null;t=t[arguments[e]]}return t},i.getFileInputData=function(t,e){var n=t.val();if(!n)return!1;var a={url:n},r=i.isget(t[0],"files",0);if(r)if(a.size=r.size,a.type=r.type,r.type.indexOf("image")>-1){var o=window.URL||window.webkitURL,s=new Image;s.onload=function(){a.width=this.width,a.height=this.height,e(a)},s.src=o.createObjectURL(r)}else e(a);else e(a)},i.isAjaxSuccess=function(t){return t&&t.success},i.getAjaxMessage=function(t){return i.isget(t,"data","message")},i.getAjaxError=function(t){return i.isget(t,"data","error")},i.renderSelect=function(t,e){var n=t.val(),a=[],r=function(t){var e="";return t.map(function(t){var n=t.text||t.label||"",o=t.id||t.value||"";a.push(o),t.children?e+='<optgroup label="'+i.strEscape(n)+'">'+r(t.children)+"</optgroup>":e+='<option value="'+o+'"'+(t.disabled?' disabled="disabled"':"")+">"+i.strEscape(n)+"</option>"}),e};return t.html(r(e)),a.indexOf(n)>-1&&t.val(n),t.val()};var h=function(t,e){return t.data("acf-lock-"+e)||[]},p=function(t,e,i){t.data("acf-lock-"+e,i)};i.lock=function(t,e,i){var n=h(t,e),a;n.indexOf(i)<0&&(n.push(i),p(t,e,n))},i.unlock=function(t,e,i){var n=h(t,e),a=n.indexOf(i);return a>-1&&(n.splice(a,1),p(t,e,n)),0===n.length},i.isLocked=function(t,e){return h(t,e).length>0},i.isGutenberg=function(){return window.wp&&wp.data&&wp.data.select&&wp.data.select("core/editor")},i.objectToArray=function(t){return Object.keys(t).map(function(e){return t[e]})},t.fn.exists=function(){return t(this).length>0},t.fn.outerHTML=function(){return t(this).get(0).outerHTML},Array.prototype.indexOf||(Array.prototype.indexOf=function(e){return t.inArray(e,this)}),t(document).ready(function(){i.doAction("ready")}),t(window).on("load",function(){i.doAction("load")}),t(window).on("beforeunload",function(){i.doAction("unload")}),t(window).on("resize",function(){i.doAction("resize")}),t(document).on("sortstart",function(t,e){i.doAction("sortstart",e.item,e.placeholder)}),t(document).on("sortstop",function(t,e){i.doAction("sortstop",e.item,e.placeholder)})}(jQuery),function(t,e){"use strict";var i=function(){function t(){return f}function e(t,e,i,n){return"string"==typeof t&&"function"==typeof e&&c("actions",t,e,i=parseInt(i||10,10),n),d}function i(){var t=Array.prototype.slice.call(arguments),e=t.shift();return"string"==typeof e&&u("actions",e,t),d}function n(t,e){return"string"==typeof t&&s("actions",t,e),d}function a(t,e,i,n){return"string"==typeof t&&"function"==typeof e&&c("filters",t,e,i=parseInt(i||10,10),n),d}function r(){var t=Array.prototype.slice.call(arguments),e=t.shift();return"string"==typeof e?u("filters",e,t):d}function o(t,e){return"string"==typeof t&&s("filters",t,e),d}function s(t,e,i,n){if(f[t][e])if(i){var a=f[t][e],r;if(n)for(r=a.length;r--;){var o=a[r];o.callback===i&&o.context===n&&a.splice(r,1)}else for(r=a.length;r--;)a[r].callback===i&&a.splice(r,1)}else f[t][e]=[]}function c(t,e,i,n,a){var r={callback:i,priority:n,context:a},o=f[t][e];o?(o.push(r),o=l(o)):o=[r],f[t][e]=o}function l(t){for(var e,i,n,a=1,r=t.length;a<r;a++){for(e=t[a],i=a;(n=t[i-1])&&n.priority>e.priority;)t[i]=t[i-1],--i;t[i]=e}return t}function u(t,e,i){var n=f[t][e];if(!n)return"filters"===t&&i[0];var a=0,r=n.length;if("filters"===t)for(;a<r;a++)i[0]=n[a].callback.apply(n[a].context,i);else for(;a<r;a++)n[a].callback.apply(n[a].context,i);return"filters"!==t||i[0]}var d={removeFilter:o,applyFilters:r,addFilter:a,removeAction:n,doAction:i,addAction:e,storage:t},f={actions:{},filters:{}};return d};acf.hooks=new i}(window),function(t,e){var i=/^(\S+)\s*(.*)$/,n=function(e){var i=this,n;return n=e&&e.hasOwnProperty("constructor")?e.constructor:function(){return i.apply(this,arguments)},t.extend(n,i),n.prototype=Object.create(i.prototype),t.extend(n.prototype,e),n.prototype.constructor=n,n},a=acf.Model=function(){this.cid=acf.uniqueId("acf"),this.data=t.extend(!0,{},this.data),this.setup.apply(this,arguments),this.$el&&!this.$el.data("acf")&&this.$el.data("acf",this);var e=function(){this.initialize(),this.addEvents(),this.addActions(),this.addFilters()};this.wait&&!acf.didAction(this.wait)?this.addAction(this.wait,e):e.apply(this)};t.extend(a.prototype,{id:"",cid:"",$el:null,data:{},busy:!1,changed:!1,events:{},actions:{},filters:{},eventScope:"",wait:!1,priority:10,get:function(t){return this.data[t]},has:function(t){return null!=this.get(t)},set:function(t,e,i){var n=this.get(t);return n==e?this:(this.data[t]=e,i||(this.changed=!0,this.trigger("changed:"+t,[e,n]),this.trigger("changed",[t,e,n])),this)},inherit:function(e){return e instanceof jQuery&&(e=e.data()),t.extend(this.data,e),this},prop:function(){return this.$el.prop.apply(this.$el,arguments)},setup:function(e){t.extend(this,e)},initialize:function(){},addElements:function(t){if(!(t=t||this.elements||null)||!Object.keys(t).length)return!1;for(var e in t)this.addElement(e,t[e])},addElement:function(t,e){this["$"+t]=this.$(e)},addEvents:function(t){if(!(t=t||this.events||null))return!1;for(var e in t){var n=e.match(i);this.on(n[1],n[2],t[e])}},removeEvents:function(t){if(!(t=t||this.events||null))return!1;for(var e in t){var n=e.match(i);this.off(n[1],n[2],t[e])}},getEventTarget:function(e,i){return e||this.$el||t(document)},validateEvent:function(e){return!this.eventScope||t(e.target).closest(this.eventScope).is(this.$el)},proxyEvent:function(e){return this.proxy(function(i){if(this.validateEvent(i)){var n,a=acf.arrayArgs(arguments).slice(1),r=[i,t(i.currentTarget)].concat(a);e.apply(this,r)}})},on:function(t,e,i,n){var a,r,o,s,c;t instanceof jQuery?n?(a=t,r=e,o=i,s=n):(a=t,r=e,s=i):i?(r=t,o=e,s=i):(r=t,s=e),a=this.getEventTarget(a),"string"==typeof s&&(s=this.proxyEvent(this[s])),r=r+"."+this.cid,c=o?[r,o,s]:[r,s],a.on.apply(a,c)},off:function(t,e,i){var n,a,r,o;t instanceof jQuery?i?(n=t,a=e,r=i):(n=t,a=e):e?(a=t,r=e):a=t,n=this.getEventTarget(n),a=a+"."+this.cid,o=r?[a,r]:[a],n.off.apply(n,o)},trigger:function(t,e,i){var n=this.getEventTarget();return i?n.trigger.apply(n,arguments):n.triggerHandler.apply(n,arguments),this},addActions:function(t){if(!(t=t||this.actions||null))return!1;for(var e in t)this.addAction(e,t[e])},removeActions:function(t){if(!(t=t||this.actions||null))return!1;for(var e in t)this.removeAction(e,t[e])},addAction:function(t,e,i){i=i||this.priority,"string"==typeof e&&(e=this[e]),acf.addAction(t,e,i,this)},removeAction:function(t,e){acf.removeAction(t,this[e])},addFilters:function(t){if(!(t=t||this.filters||null))return!1;for(var e in t)this.addFilter(e,t[e])},addFilter:function(t,e,i){i=i||this.priority,"string"==typeof e&&(e=this[e]),acf.addFilter(t,e,i,this)},removeFilters:function(t){if(!(t=t||this.filters||null))return!1;for(var e in t)this.removeFilter(e,t[e])},removeFilter:function(t,e){acf.removeFilter(t,this[e])},$:function(t){return this.$el.find(t)},remove:function(){this.removeEvents(),this.removeActions(),this.removeFilters(),this.$el.remove()},setTimeout:function(t,e){return setTimeout(this.proxy(t),e)},time:function(){console.time(this.id||this.cid)},timeEnd:function(){console.timeEnd(this.id||this.cid)},show:function(){acf.show(this.$el)},hide:function(){acf.hide(this.$el)},proxy:function(e){return t.proxy(e,this)}}),a.extend=n,acf.models={},acf.getInstance=function(t){return t.data("acf")},acf.getInstances=function(e){var i=[];return e.each(function(){i.push(acf.getInstance(t(this)))}),i}}(jQuery),function(t,e){acf.models.Popup=acf.Model.extend({data:{title:"",content:"",width:0,height:0,loading:!1},events:{'click [data-event="close"]':"onClickClose","click .acf-close-popup":"onClickClose"},setup:function(e){t.extend(this.data,e),this.$el=t(this.tmpl())},initialize:function(){this.render(),this.open()},tmpl:function(){return['<div id="acf-popup">','<div class="acf-popup-box acf-box">','<div class="title"><h3></h3><a href="#" class="acf-icon -cancel grey" data-event="close"></a></div>','<div class="inner"></div>','<div class="loading"><i class="acf-loading"></i></div>',"</div>",'<div class="bg" data-event="close"></div>',"</div>"].join("")},render:function(){var t=this.get("title"),e=this.get("content"),i=this.get("loading"),n=this.get("width"),a=this.get("height");this.title(t),this.content(e),n&&this.$(".acf-popup-box").css("width",n),a&&this.$(".acf-popup-box").css("min-height",a),this.loading(i),acf.doAction("append",this.$el)},update:function(t){this.data=acf.parseArgs(t,this.data),this.render()},title:function(t){this.$(".title:first h3").html(t)},content:function(t){this.$(".inner:first").html(t)},loading:function(t){var e=this.$(".loading:first");t?e.show():e.hide()},open:function(){t("body").append(this.$el)},close:function(){this.remove()},onClickClose:function(t,e){t.preventDefault(),this.close()}}),acf.newPopup=function(t){return new acf.models.Popup(t)}}(jQuery),function(t,e){acf.unload=new acf.Model({wait:"load",active:!0,changed:!1,actions:{validation_failure:"startListening",validation_success:"stopListening"},events:{"change form .acf-field":"startListening","submit form":"stopListening"},enable:function(){this.active=!0},disable:function(){this.active=!1},reset:function(){this.stopListening()},startListening:function(){!this.changed&&this.active&&(this.changed=!0,t(window).on("beforeunload",this.onUnload))},stopListening:function(){this.changed=!1,t(window).off("beforeunload",this.onUnload)},onUnload:function(){return acf.__("The changes you made will be lost if you navigate away from this page")}})}(jQuery),function(t,e){var i=new acf.Model({events:{"click .acf-panel-title":"onClick"},onClick:function(t,e){t.preventDefault(),this.toggle(e.parent())},isOpen:function(t){return t.hasClass("-open")},toggle:function(t){this.isOpen(t)?this.close(t):this.open(t)},open:function(t){t.addClass("-open"),t.find(".acf-panel-title i").attr("class","dashicons dashicons-arrow-down")},close:function(t){t.removeClass("-open"),t.find(".acf-panel-title i").attr("class","dashicons dashicons-arrow-right")}})}(jQuery),function(t,e){var i=acf.Model.extend({data:{text:"",type:"",timeout:0,dismiss:!0,target:!1,close:function(){}},events:{"click .acf-notice-dismiss":"onClickClose"},tmpl:function(){return'<div class="acf-notice"></div>'},setup:function(e){t.extend(this.data,e),this.$el=t(this.tmpl())},initialize:function(){this.render(),this.show()},render:function(){this.type(this.get("type")),this.html("<p>"+this.get("text")+"</p>"),this.get("dismiss")&&(this.$el.append('<a href="#" class="acf-notice-dismiss acf-icon -cancel small"></a>'),this.$el.addClass("-dismiss"));var t=this.get("timeout");t&&this.away(t)},update:function(e){t.extend(this.data,e),this.initialize(),this.removeEvents(),this.addEvents()},show:function(){var t=this.get("target");t&&t.prepend(this.$el)},hide:function(){this.$el.remove()},away:function(t){this.setTimeout(function(){acf.remove(this.$el)},t)},type:function(t){var e=this.get("type");e&&this.$el.removeClass("-"+e),this.$el.addClass("-"+t),"error"==t&&this.$el.addClass("acf-error-message")},html:function(t){this.$el.html(t)},text:function(t){this.$("p").html(t)},onClickClose:function(t,e){t.preventDefault(),this.get("close").apply(this,arguments),this.remove()}});acf.newNotice=function(t){return"object"!=typeof t&&(t={text:t}),new i(t)};var n=new acf.Model({wait:"prepare",priority:1,initialize:function(){var e=t(".acf-admin-notice");e.length&&t("h1:first").after(e)}})}(jQuery),function(t,e){var i=new acf.Model({wait:"prepare",priority:1,initialize:function(){(acf.get("postboxes")||[]).map(acf.newPostbox)}});acf.getPostbox=function(e){return"string"==typeof e&&(e=t("#"+e)),acf.getInstance(e)},acf.getPostboxes=function(){return acf.getInstances(t(".acf-postbox"))},acf.newPostbox=function(t){return new acf.models.Postbox(t)},acf.models.Postbox=acf.Model.extend({data:{id:"",key:"",style:"default",label:"top",edit:""},setup:function(e){e.editLink&&(e.edit=e.editLink),t.extend(this.data,e),this.$el=this.$postbox()},$postbox:function(){return t("#"+this.get("id"))},$hide:function(){return t("#"+this.get("id")+"-hide")},$hideLabel:function(){return this.$hide().parent()},$hndle:function(){return this.$("> .hndle")},$inside:function(){return this.$("> .inside")},isVisible:function(){return this.$el.hasClass("acf-hidden")},initialize:function(){if(this.$el.addClass("acf-postbox"),this.$el.removeClass("hide-if-js"),"block"!==acf.get("editor")){var t=this.get("style");"default"!==t&&this.$el.addClass(t)}this.$inside().addClass("acf-fields").addClass("-"+this.get("label"));var e=this.get("edit");e&&this.$hndle().append('<a href="'+e+'" class="dashicons dashicons-admin-generic acf-hndle-cog acf-js-tooltip" title="'+acf.__("Edit field group")+'"></a>'),this.show()},show:function(){this.$hideLabel().show(),this.$hide().prop("checked",!0),this.$el.show().removeClass("acf-hidden")},enable:function(){acf.enable(this.$el,"postbox")},showEnable:function(){this.show(),this.enable()},hide:function(){this.$hideLabel().hide(),this.$el.hide().addClass("acf-hidden")},disable:function(){acf.disable(this.$el,"postbox")},hideDisable:function(){this.hide(),this.disable()},html:function(t){this.$inside().html(t),acf.doAction("append",this.$el)}})}(jQuery),function(t,e){acf.newTooltip=function(t){return"object"!=typeof t&&(t={text:t}),void 0!==t.confirmRemove?(t.textConfirm=acf.__("Remove"),t.textCancel=acf.__("Cancel"),new n(t)):void 0!==t.confirm?new n(t):new i(t)};var i=acf.Model.extend({data:{text:"",timeout:0,target:null},tmpl:function(){return'<div class="acf-tooltip"></div>'},setup:function(e){t.extend(this.data,e),this.$el=t(this.tmpl())},initialize:function(){this.render(),this.show(),this.position();var e=this.get("timeout");e&&setTimeout(t.proxy(this.fade,this),e)},update:function(e){t.extend(this.data,e),this.initialize()},render:function(){this.html(this.get("text"))},show:function(){t("body").append(this.$el)},hide:function(){this.$el.remove()},fade:function(){this.$el.addClass("acf-fade-up"),this.setTimeout(function(){this.remove()},250)},html:function(t){this.$el.html(t)},position:function(){var e=this.$el,i=this.get("target");if(i){e.removeClass("right left bottom top").css({top:0,left:0});var n=10,a=i.outerWidth(),r=i.outerHeight(),o=i.offset().top,s=i.offset().left,c=e.outerWidth(),l=e.outerHeight(),u=e.offset().top,d=o-l-u,f=s+a/2-c/2;f<10?(e.addClass("right"),f=s+a,d=o+r/2-l/2-u):f+c+10>t(window).width()?(e.addClass("left"),f=s-c,d=o+r/2-l/2-u):d-t(window).scrollTop()<10?(e.addClass("bottom"),d=o+r-u):e.addClass("top"),e.css({top:d,left:f})}}}),n=i.extend({data:{text:"",textConfirm:"",textCancel:"",target:null,targetConfirm:!0,confirm:function(){},cancel:function(){},context:!1},events:{'click [data-event="cancel"]':"onCancel",'click [data-event="confirm"]':"onConfirm"},addEvents:function(){acf.Model.prototype.addEvents.apply(this);var e=t(document),i=this.get("target");this.setTimeout(function(){this.on(e,"click","onCancel")}),this.get("targetConfirm")&&this.on(i,"click","onConfirm")},removeEvents:function(){acf.Model.prototype.removeEvents.apply(this);var e=t(document),i=this.get("target");this.off(e,"click"),this.off(i,"click")},render:function(){var t,e,i,n=[this.get("text")||acf.__("Are you sure?"),'<a href="#" data-event="confirm">'+(this.get("textConfirm")||acf.__("Yes"))+"</a>",'<a href="#" data-event="cancel">'+(this.get("textCancel")||acf.__("No"))+"</a>"].join(" ");this.html(n),this.$el.addClass("-confirm")},onCancel:function(t,e){t.preventDefault(),t.stopImmediatePropagation();var i=this.get("cancel"),n=this.get("context")||this;i.apply(n,arguments),this.remove()},onConfirm:function(t,e){t.preventDefault(),t.stopImmediatePropagation();var i=this.get("confirm"),n=this.get("context")||this;i.apply(n,arguments),this.remove()}});acf.models.Tooltip=i,acf.models.TooltipConfirm=n;var a=new acf.Model({tooltip:!1,events:{"mouseenter .acf-js-tooltip":"showTitle","mouseup .acf-js-tooltip":"hideTitle","mouseleave .acf-js-tooltip":"hideTitle"},showTitle:function(t,e){var i=e.attr("title");i&&(e.attr("title",""),this.tooltip?this.tooltip.update({text:i,target:e}):this.tooltip=acf.newTooltip({text:i,target:e}))},hideTitle:function(t,e){this.tooltip.hide(),e.attr("title",this.tooltip.get("text"))}})}(jQuery),function(t,e){var i=[];acf.Field=acf.Model.extend({type:"",eventScope:".acf-field",wait:"ready",setup:function(t){this.$el=t,this.inherit(t),this.inherit(this.$control())},val:function(t){return void 0!==t?this.setValue(t):this.prop("disabled")?null:this.getValue()},getValue:function(){return this.$input().val()},setValue:function(t){return acf.val(this.$input(),t)},__:function(t){return acf._e(this.type,t)},$control:function(){return!1},$input:function(){return this.$("[name]:first")},$inputWrap:function(){return this.$(".acf-input:first")},$labelWrap:function(){return this.$(".acf-label:first")},getInputName:function(){return this.$input().attr("name")||""},parent:function(){var t=this.parents();return!!t.length&&t[0]},parents:function(){var t=this.$el.parents(".acf-field"),e;return acf.getFields(t)},show:function(t,e){var i=acf.show(this.$el,t);return i&&(this.prop("hidden",!1),acf.doAction("show_field",this,e)),i},hide:function(t,e){var i=acf.hide(this.$el,t);return i&&(this.prop("hidden",!0),acf.doAction("hide_field",this,e)),i},enable:function(t,e){var i=acf.enable(this.$el,t);return i&&(this.prop("disabled",!1),acf.doAction("enable_field",this,e)),i},disable:function(t,e){var i=acf.disable(this.$el,t);return i&&(this.prop("disabled",!0),acf.doAction("disable_field",this,e)),i},showEnable:function(t,e){return this.enable.apply(this,arguments),this.show.apply(this,arguments)},hideDisable:function(t,e){return this.disable.apply(this,arguments),this.hide.apply(this,arguments)},showNotice:function(t){"object"!=typeof t&&(t={text:t}),this.notice&&this.notice.remove(),t.target=this.$inputWrap(),this.notice=acf.newNotice(t)},removeNotice:function(t){this.notice&&(this.notice.away(t||0),this.notice=!1)},showError:function(e){this.$el.addClass("acf-error"),void 0!==e&&this.showNotice({text:e,type:"error",dismiss:!1}),acf.doAction("invalid_field",this),this.$el.one("focus change","input, select, textarea",t.proxy(this.removeError,this))},removeError:function(){this.$el.removeClass("acf-error"),this.removeNotice(250),acf.doAction("valid_field",this)},trigger:function(t,e,i){return"invalidField"==t&&(i=!0),acf.Model.prototype.trigger.apply(this,[t,e,i])}}),acf.newField=function(t){var e=t.data("type"),i=n(e),a,r=new(acf.models[i]||acf.Field)(t);return acf.doAction("new_field",r),r};var n=function(t){return acf.strPascalCase(t||"")+"Field"};acf.registerFieldType=function(t){var e,a=t.prototype.type,r=n(a);acf.models[r]=t,i.push(a)},acf.getFieldType=function(t){var e=n(t);return acf.models[e]||!1},acf.getFieldTypes=function(t){t=acf.parseArgs(t,{category:""});var e=[];return i.map(function(i){var n=acf.getFieldType(i),a=n.prototype;t.category&&a.category!==t.category||e.push(n)}),e}}(jQuery),function(t,e){acf.findFields=function(e){var i=".acf-field",n=!1;return(e=acf.parseArgs(e,{key:"",name:"",type:"",is:"",parent:!1,sibling:!1,limit:!1,visible:!1,suppressFilters:!1})).suppressFilters||(e=acf.applyFilters("find_fields_args",e)),e.key&&(i+='[data-key="'+e.key+'"]'),e.type&&(i+='[data-type="'+e.type+'"]'),e.name&&(i+='[data-name="'+e.name+'"]'),e.is&&(i+=e.is),e.visible&&(i+=":visible"),n=e.parent?e.parent.find(i):e.sibling?e.sibling.siblings(i):t(i),e.suppressFilters||(n=n.not(".acf-clone .acf-field"),n=acf.applyFilters("find_fields",n)),e.limit&&(n=n.slice(0,e.limit)),n},acf.findField=function(t,e){return acf.findFields({key:t,limit:1,parent:e,suppressFilters:!0})},acf.getField=function(t){t instanceof jQuery||(t=acf.findField(t));var e=t.data("acf");return e||(e=acf.newField(t)),e},acf.getFields=function(e){e instanceof jQuery||(e=acf.findFields(e));var i=[];return e.each(function(){var e=acf.getField(t(this));i.push(e)}),i},acf.findClosestField=function(t){return t.closest(".acf-field")},acf.getClosestField=function(t){var e=acf.findClosestField(t);return this.getField(e)};var i=function(t){var e=t,i=t+"_fields",a=t+"_field",r=function(t){var e,n=acf.arrayArgs(arguments).slice(1),a=acf.getFields({parent:t});if(a.length){var r=[i,a].concat(n);acf.doAction.apply(null,r)}},o=function(t){var e,i=acf.arrayArgs(arguments).slice(1);t.map(function(t,e){var n=[a,t].concat(i);acf.doAction.apply(null,n)})};acf.addAction(e,r),acf.addAction(i,o),n(t)},n=function(t){var e=t+"_field",i=t+"Field",n=function(n){var a=acf.arrayArgs(arguments),r=a.slice(1),s;["type","name","key"].map(function(t){var i="/"+t+"="+n.get(t);a=[e+i,n].concat(r),acf.doAction.apply(null,a)}),o.indexOf(t)>-1&&n.trigger(i,r)};acf.addAction(e,n)},a,r=["valid","invalid","enable","disable","new"],o=["remove","unmount","remount","sortstart","sortstop","show","hide","unload","valid","invalid","enable","disable"];["prepare","ready","load","append","remove","unmount","remount","sortstart","sortstop","show","hide","unload"].map(i),r.map(n);var s=new acf.Model({id:"fieldsEventManager",events:{'click .acf-field a[href="#"]':"onClick","change .acf-field":"onChange"},onClick:function(t){t.preventDefault()},onChange:function(){t("#_acf_changed").val(1)}})}(jQuery),function(t,e){var i=0,n=acf.Field.extend({type:"accordion",wait:"",$control:function(){return this.$(".acf-fields:first")},initialize:function(){if(!this.$el.is("td")){if(this.get("endpoint"))return this.remove();var e=this.$el,n=this.$labelWrap(),r=this.$inputWrap(),o=this.$control(),s=r.children(".description");if(s.length&&n.append(s),this.$el.is("tr")){var c=this.$el.closest("table"),l=t('<div class="acf-accordion-title"/>'),u=t('<div class="acf-accordion-content"/>'),d=t('<table class="'+c.attr("class")+'"/>'),f=t("<tbody/>");l.append(n.html()),d.append(f),u.append(d),r.append(l),r.append(u),n.remove(),o.remove(),r.attr("colspan",2),n=l,r=u,o=f}e.addClass("acf-accordion"),n.addClass("acf-accordion-title"),r.addClass("acf-accordion-content"),i++,this.get("multi_expand")&&e.attr("multi-expand",1);var h=acf.getPreference("this.accordions")||[];void 0!==h[i-1]&&this.set("open",h[i-1]),this.get("open")&&(e.addClass("-open"),r.css("display","block")),n.prepend(a.iconHtml({open:this.get("open")}));var p=e.parent();o.addClass(p.hasClass("-left")?"-left":""),o.addClass(p.hasClass("-clear")?"-clear":""),o.append(e.nextUntil(".acf-field-accordion",".acf-field")),o.removeAttr("data-open data-multi_expand data-endpoint")}}});acf.registerFieldType(n);var a=new acf.Model({actions:{unload:"onUnload"},events:{
"click .acf-accordion-title":"onClick","invalidField .acf-accordion":"onInvalidField"},isOpen:function(t){return t.hasClass("-open")},toggle:function(t){this.isOpen(t)?this.close(t):this.open(t)},iconHtml:function(t){var e;return'<i class="acf-accordion-icon dashicons dashicons-'+(t.open?"arrow-down":"arrow-right")+'"></i>'},open:function(e){e.find(".acf-accordion-content:first").slideDown().css("display","block"),e.find(".acf-accordion-icon:first").replaceWith(this.iconHtml({open:!0})),e.addClass("-open"),acf.doAction("show",e),e.attr("multi-expand")||e.siblings(".acf-accordion.-open").each(function(){a.close(t(this))})},close:function(t){t.find(".acf-accordion-content:first").slideUp(),t.find(".acf-accordion-icon:first").replaceWith(this.iconHtml({open:!1})),t.removeClass("-open"),acf.doAction("hide",t)},onClick:function(t,e){t.preventDefault(),this.toggle(e.parent())},onInvalidField:function(t,e){this.busy||(this.busy=!0,this.setTimeout(function(){this.busy=!1},1e3),this.open(e))},onUnload:function(e){var i=[];t(".acf-accordion").each(function(){var e=t(this).hasClass("-open")?1:0;i.push(e)}),i.length&&acf.setPreference("this.accordions",i)}})}(jQuery),function(t,e){var i=acf.Field.extend({type:"button_group",events:{'click input[type="radio"]':"onClick"},$control:function(){return this.$(".acf-button-group")},$input:function(){return this.$("input:checked")},setValue:function(t){this.$('input[value="'+t+'"]').prop("checked",!0).trigger("change")},onClick:function(t,e){var i=e.parent("label"),n=i.hasClass("selected");this.$(".selected").removeClass("selected"),i.addClass("selected"),this.get("allow_null")&&n&&(i.removeClass("selected"),e.prop("checked",!1).trigger("change"))}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"checkbox",events:{"change input":"onChange","click .acf-add-checkbox":"onClickAdd","click .acf-checkbox-toggle":"onClickToggle","click .acf-checkbox-custom":"onClickCustom"},$control:function(){return this.$(".acf-checkbox-list")},$toggle:function(){return this.$(".acf-checkbox-toggle")},$input:function(){return this.$('input[type="hidden"]')},$inputs:function(){return this.$('input[type="checkbox"]').not(".acf-checkbox-toggle")},getValue:function(){var e=[];return this.$(":checked").each(function(){e.push(t(this).val())}),!!e.length&&e},onChange:function(t,e){var i=e.prop("checked"),n=this.$toggle(),a;(i?e.parent().addClass("selected"):e.parent().removeClass("selected"),n.length)&&(0==this.$inputs().not(":checked").length?n.prop("checked",!0):n.prop("checked",!1))},onClickAdd:function(t,e){var i='<li><input class="acf-checkbox-custom" type="checkbox" checked="checked" /><input type="text" name="'+this.getInputName()+'[]" /></li>';e.parent("li").before(i)},onClickToggle:function(t,e){var i=e.prop("checked"),n;this.$inputs().prop("checked",i)},onClickCustom:function(t,e){var i=e.prop("checked"),n=e.next('input[type="text"]');i?n.prop("disabled",!1):(n.prop("disabled",!0),""==n.val()&&e.parent("li").remove())}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"color_picker",wait:"load",$control:function(){return this.$(".acf-color-picker")},$input:function(){return this.$('input[type="hidden"]')},$inputText:function(){return this.$('input[type="text"]')},setValue:function(t){acf.val(this.$input(),t),this.$inputText().iris("color",t)},initialize:function(){var t=this.$input(),e=this.$inputText(),i=function(i){setTimeout(function(){acf.val(t,e.val())},1)},n={defaultColor:!1,palettes:!0,hide:!0,change:i,clear:i},n=acf.applyFilters("color_picker_args",n,this);e.wpColorPicker(n)}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"date_picker",events:{'blur input[type="text"]':"onBlur"},$control:function(){return this.$(".acf-date-picker")},$input:function(){return this.$('input[type="hidden"]')},$inputText:function(){return this.$('input[type="text"]')},initialize:function(){if(this.has("save_format"))return this.initializeCompatibility();var t=this.$input(),e=this.$inputText(),i={dateFormat:this.get("date_format"),altField:t,altFormat:"yymmdd",changeYear:!0,yearRange:"-100:+100",changeMonth:!0,showButtonPanel:!0,firstDay:this.get("first_day")};i=acf.applyFilters("date_picker_args",i,this),acf.newDatePicker(e,i),acf.doAction("date_picker_init",e,i,this)},initializeCompatibility:function(){var t=this.$input(),e=this.$inputText();e.val(t.val());var i={dateFormat:this.get("date_format"),altField:t,altFormat:this.get("save_format"),changeYear:!0,yearRange:"-100:+100",changeMonth:!0,showButtonPanel:!0,firstDay:this.get("first_day")},n=(i=acf.applyFilters("date_picker_args",i,this)).dateFormat;i.dateFormat=this.get("save_format"),acf.newDatePicker(e,i),e.datepicker("option","dateFormat",n),acf.doAction("date_picker_init",e,i,this)},onBlur:function(){this.$inputText().val()||acf.val(this.$input(),"")}});acf.registerFieldType(i);var n=new acf.Model({priority:5,wait:"ready",initialize:function(){var e=acf.get("locale"),i=acf.get("rtl"),n=acf.get("datePickerL10n");return!!n&&(void 0!==t.datepicker&&(n.isRTL=i,t.datepicker.regional[e]=n,void t.datepicker.setDefaults(n)))}});acf.newDatePicker=function(e,i){if(void 0===t.datepicker)return!1;i=i||{},e.datepicker(i),t("body > #ui-datepicker-div").exists()&&t("body > #ui-datepicker-div").wrap('<div class="acf-ui-datepicker" />')}}(jQuery),function(t,e){var i=acf.models.DatePickerField.extend({type:"date_time_picker",$control:function(){return this.$(".acf-date-time-picker")},initialize:function(){var t=this.$input(),e=this.$inputText(),i={dateFormat:this.get("date_format"),timeFormat:this.get("time_format"),altField:t,altFieldTimeOnly:!1,altFormat:"yy-mm-dd",altTimeFormat:"HH:mm:ss",changeYear:!0,yearRange:"-100:+100",changeMonth:!0,showButtonPanel:!0,firstDay:this.get("first_day"),controlType:"select",oneLine:!0};i=acf.applyFilters("date_time_picker_args",i,this),acf.newDateTimePicker(e,i),acf.doAction("date_time_picker_init",e,i,this)}});acf.registerFieldType(i);var n=new acf.Model({priority:5,wait:"ready",initialize:function(){var e=acf.get("locale"),i=acf.get("rtl"),n=acf.get("dateTimePickerL10n");return!!n&&(void 0!==t.timepicker&&(n.isRTL=i,t.timepicker.regional[e]=n,void t.timepicker.setDefaults(n)))}});acf.newDateTimePicker=function(e,i){if(void 0===t.timepicker)return!1;i=i||{},e.datetimepicker(i),t("body > #ui-datepicker-div").exists()&&t("body > #ui-datepicker-div").wrap('<div class="acf-ui-datepicker" />')}}(jQuery),function(t,e){function i(e){if(r)return e();if(acf.isset(window,"google","maps","Geocoder"))return r=new google.maps.Geocoder,e();if(acf.addAction("google_map_api_loaded",e),!a){var i=acf.get("google_map_api");i&&(a=!0,t.ajax({url:i,dataType:"script",cache:!0,success:function(){r=new google.maps.Geocoder,acf.doAction("google_map_api_loaded")}}))}}var n=acf.Field.extend({type:"google_map",map:!1,wait:"load",events:{'click a[data-name="clear"]':"onClickClear",'click a[data-name="locate"]':"onClickLocate",'click a[data-name="search"]':"onClickSearch","keydown .search":"onKeydownSearch","keyup .search":"onKeyupSearch","focus .search":"onFocusSearch","blur .search":"onBlurSearch",showField:"onShow"},$control:function(){return this.$(".acf-google-map")},$input:function(t){return this.$('input[data-name="'+(t||"address")+'"]')},$search:function(){return this.$(".search")},$canvas:function(){return this.$(".canvas")},addClass:function(t){this.$control().addClass(t)},removeClass:function(t){this.$control().removeClass(t)},getValue:function(){var e={lat:"",lng:"",address:""};return this.$('input[type="hidden"]').each(function(){e[t(this).data("name")]=t(this).val()}),e.lat&&e.lng||(e=!1),e},setValue:function(t){for(var e in t=acf.parseArgs(t,{lat:"",lng:"",address:""}))acf.val(this.$input(e),t[e]);t.lat&&t.lng||(t=!1),this.renderVal(t);var i=this.newLatLng(t.lat,t.lng);acf.doAction("google_map_change",i,this.map,this)},renderVal:function(t){t?(this.addClass("-value"),this.setPosition(t.lat,t.lng),this.map.marker.setVisible(!0)):(this.removeClass("-value"),this.map.marker.setVisible(!1)),this.$search().val(t.address)},setPosition:function(t,e){var i=this.newLatLng(t,e);return this.map.marker.setPosition(i),this.map.marker.setVisible(!0),this.center(),this},center:function(){var t=this.map.marker.getPosition(),e=this.get("lat"),i=this.get("lng");t&&(e=t.lat(),i=t.lng());var n=this.newLatLng(e,i);this.map.setCenter(n)},getSearchVal:function(){return this.$search().val()},initialize:function(){i(this.initializeMap.bind(this))},newLatLng:function(t,e){return new google.maps.LatLng(parseFloat(t),parseFloat(e))},initializeMap:function(){var t=this.get("zoom"),e=this.get("lat"),i=this.get("lng"),n={scrollwheel:!1,zoom:parseInt(t),center:this.newLatLng(e,i),mapTypeId:google.maps.MapTypeId.ROADMAP,marker:{draggable:!0,raiseOnDrag:!0},autocomplete:{}};n=acf.applyFilters("google_map_args",n,this);var a=new google.maps.Map(this.$canvas()[0],n),r=acf.parseArgs(n.marker,{draggable:!0,raiseOnDrag:!0,map:a});r=acf.applyFilters("google_map_marker_args",r,this);var o=new google.maps.Marker(r),s=!1;if(acf.isset(google,"maps","places","Autocomplete")){var c=n.autocomplete||{};c=acf.applyFilters("google_map_autocomplete_args",c,this),(s=new google.maps.places.Autocomplete(this.$search()[0],c)).bindTo("bounds",a)}this.addMapEvents(this,a,o,s),a.acf=this,a.marker=o,a.autocomplete=s,this.map=a,acf.doAction("google_map_init",a,o,this);var l=this.getValue();this.renderVal(l)},addMapEvents:function(t,e,i,n){google.maps.event.addListener(e,"click",function(e){var i=e.latLng.lat(),n=e.latLng.lng();t.searchPosition(i,n)}),google.maps.event.addListener(i,"dragend",function(){var e=this.getPosition(),i=e.lat(),n=e.lng();t.searchPosition(i,n)}),n&&google.maps.event.addListener(n,"place_changed",function(){var e=this.getPlace();e.address=t.getSearchVal(),t.setPlace(e)})},searchPosition:function(e,i){var n=this.newLatLng(e,i),a=this.$control();this.setPosition(e,i),a.addClass("-loading");var o=t.proxy(function(t,n){a.removeClass("-loading");var r="";n!=google.maps.GeocoderStatus.OK?console.log("Geocoder failed due to: "+n):t[0]?r=t[0].formatted_address:console.log("No results found"),this.val({lat:e,lng:i,address:r})},this);r.geocode({latLng:n},o)},setPlace:function(t){if(!t)return this;if(t.name&&!t.geometry)return this.searchAddress(t.name),this;var e=t.geometry.location.lat(),i=t.geometry.location.lng(),n=t.address||t.formatted_address;return this.setValue({lat:e,lng:i,address:n}),this},searchAddress:function(e){var i=e.split(",");if(2==i.length){var n=i[0],a=i[1];if(t.isNumeric(n)&&t.isNumeric(a))return this.searchPosition(n,a)}var o=this.$control();o.addClass("-loading");var s=this.proxy(function(t,i){o.removeClass("-loading");var n="",a="";i!=google.maps.GeocoderStatus.OK?console.log("Geocoder failed due to: "+i):t[0]?(n=t[0].geometry.location.lat(),a=t[0].geometry.location.lng()):console.log("No results found"),this.val({lat:n,lng:a,address:e})});r.geocode({address:e},s)},searchLocation:function(){if(!navigator.geolocation)return alert(acf.__("Sorry, this browser does not support geolocation"));var e=this.$control();e.addClass("-loading");var i=t.proxy(function(t,i){e.removeClass("-loading");var n=t.coords.latitude,a=t.coords.longitude;this.searchPosition(n,a)},this),n=function(t){e.removeClass("-loading")};navigator.geolocation.getCurrentPosition(i,n)},onClickClear:function(t,e){this.val(!1)},onClickLocate:function(t,e){this.searchLocation()},onClickSearch:function(t,e){this.searchAddress(this.$search().val())},onFocusSearch:function(t,e){this.removeClass("-value"),this.onKeyupSearch.apply(this,arguments)},onBlurSearch:function(t,e){this.setTimeout(function(){this.removeClass("-search"),e.val()&&this.addClass("-value")},100)},onKeyupSearch:function(t,e){e.val()?this.addClass("-search"):this.removeClass("-search")},onKeydownSearch:function(t,e){13==t.which&&t.preventDefault()},onMousedown:function(){},onShow:function(){if(!this.map)return!1;this.setTimeout(this.center,10)}});acf.registerFieldType(n);var a=!1,r=!1}(jQuery),function(t,e){var i=acf.Field.extend({type:"image",$control:function(){return this.$(".acf-image-uploader")},$input:function(){return this.$('input[type="hidden"]')},events:{'click a[data-name="add"]':"onClickAdd",'click a[data-name="edit"]':"onClickEdit",'click a[data-name="remove"]':"onClickRemove",'change input[type="file"]':"onChange"},initialize:function(){"basic"===this.get("uploader")&&this.$el.closest("form").attr("enctype","multipart/form-data")},validateAttachment:function(t){void 0!==(t=t||{}).id&&(t=t.attributes),t=acf.parseArgs(t,{url:"",alt:"",title:"",caption:"",description:"",width:0,height:0});var e=acf.isget(t,"sizes",this.get("preview_size"),"url");return null!==e&&(t.url=e),t},render:function(t){t=this.validateAttachment(t),this.$("img").attr({src:t.url,alt:t.alt,title:t.title});var e=t.id||"";this.val(e),e?this.$control().addClass("has-value"):this.$control().removeClass("has-value")},append:function(t,e){var i=function(t,e){for(var i=acf.getFields({key:t.get("key"),parent:e.$el}),n=0;n<i.length;n++)if(!i[n].val())return i[n];return!1},n=i(this,e);n||(e.$(".acf-button:last").trigger("click"),n=i(this,e)),n&&n.render(t)},selectAttachment:function(){var e=this.parent(),i=e&&"repeater"===e.get("type"),n=acf.newMediaPopup({mode:"select",type:"image",title:acf.__("Select Image"),field:this.get("key"),multiple:i,library:this.get("library"),allowedTypes:this.get("mime_types"),select:t.proxy(function(t,i){i>0?this.append(t,e):this.render(t)},this)})},editAttachment:function(){var e=this.val();if(e)var i=acf.newMediaPopup({mode:"edit",title:acf.__("Edit Image"),button:acf.__("Update Image"),attachment:e,field:this.get("key"),select:t.proxy(function(t,e){this.render(t)},this)})},removeAttachment:function(){this.render(!1)},onClickAdd:function(t,e){this.selectAttachment()},onClickEdit:function(t,e){this.editAttachment()},onClickRemove:function(t,e){this.removeAttachment()},onChange:function(e,i){var n=this.$input();acf.getFileInputData(i,function(e){n.val(t.param(e))})}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.models.ImageField.extend({type:"file",$control:function(){return this.$(".acf-file-uploader")},$input:function(){return this.$('input[type="hidden"]')},validateAttachment:function(t){return void 0!==(t=t||{}).id&&(t=t.attributes),t=acf.parseArgs(t,{url:"",alt:"",title:"",filename:"",filesizeHumanReadable:"",icon:"/wp-includes/images/media/default.png"})},render:function(t){t=this.validateAttachment(t),this.$("img").attr({src:t.icon,alt:t.alt,title:t.title}),this.$('[data-name="title"]').text(t.title),this.$('[data-name="filename"]').text(t.filename).attr("href",t.url),this.$('[data-name="filesize"]').text(t.filesizeHumanReadable);var e=t.id||"";acf.val(this.$input(),e),e?this.$control().addClass("has-value"):this.$control().removeClass("has-value")},selectAttachment:function(){var e=this.parent(),i=e&&"repeater"===e.get("type"),n=acf.newMediaPopup({mode:"select",title:acf.__("Select File"),field:this.get("key"),multiple:i,library:this.get("library"),allowedTypes:this.get("mime_types"),select:t.proxy(function(t,i){i>0?this.append(t,e):this.render(t)},this)})},editAttachment:function(){var e=this.val();if(!e)return!1;var i=acf.newMediaPopup({mode:"edit",title:acf.__("Edit File"),button:acf.__("Update File"),attachment:e,field:this.get("key"),select:t.proxy(function(t,e){this.render(t)},this)})}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"link",events:{'click a[data-name="add"]':"onClickEdit",'click a[data-name="edit"]':"onClickEdit",'click a[data-name="remove"]':"onClickRemove","change .link-node":"onChange"},$control:function(){return this.$(".acf-link")},$node:function(){return this.$(".link-node")},getValue:function(){var t=this.$node();return!!t.attr("href")&&{title:t.html(),url:t.attr("href"),target:t.attr("target")}},setValue:function(t){t=acf.parseArgs(t,{title:"",url:"",target:""});var e=this.$control(),i=this.$node();e.removeClass("-value -external"),t.url&&e.addClass("-value"),"_blank"===t.target&&e.addClass("-external"),this.$(".link-title").html(t.title),this.$(".link-url").attr("href",t.url).html(t.url),i.html(t.title),i.attr("href",t.url),i.attr("target",t.target),this.$(".input-title").val(t.title),this.$(".input-target").val(t.target),this.$(".input-url").val(t.url).trigger("change")},onClickEdit:function(t,e){acf.wpLink.open(this.$node())},onClickRemove:function(t,e){this.setValue(!1)},onChange:function(t,e){var i=this.getValue();this.setValue(i)}});acf.registerFieldType(i),acf.wpLink=new acf.Model({getNodeValue:function(){var t=this.get("node");return{title:t.html(),url:t.attr("href"),target:t.attr("target")}},setNodeValue:function(t){var e=this.get("node");e.html(t.title),e.attr("href",t.url),e.attr("target",t.target),e.trigger("change")},getInputValue:function(){return{title:t("#wp-link-text").val(),url:t("#wp-link-url").val(),target:t("#wp-link-target").prop("checked")?"_blank":""}},setInputValue:function(e){t("#wp-link-text").val(e.title),t("#wp-link-url").val(e.url),t("#wp-link-target").prop("checked","_blank"===e.target)},open:function(e){this.on("wplink-open","onOpen"),this.on("wplink-close","onClose"),this.set("node",e);var i=t('<textarea id="acf-link-textarea" style="display:none;"></textarea>');t("body").append(i);var n=this.getNodeValue();wpLink.open("acf-link-textarea",n.url,n.title,null)},onOpen:function(){t("#wp-link-wrap").addClass("has-text-field");var e=this.getNodeValue();this.setInputValue(e)},close:function(){wpLink.close()},onClose:function(){if(!this.has("node"))return!1;this.off("wplink-open"),this.off("wplink-close");var e=this.getInputValue();this.setNodeValue(e),t("#acf-link-textarea").remove(),this.set("node",null)}})}(jQuery),function(t,e){var i=acf.Field.extend({type:"oembed",events:{'click [data-name="clear-button"]':"onClickClear","keypress .input-search":"onKeypressSearch","keyup .input-search":"onKeyupSearch","change .input-search":"onChangeSearch"},$control:function(){return this.$(".acf-oembed")},$input:function(){return this.$(".input-value")},$search:function(){return this.$(".input-search")},getValue:function(){return this.$input().val()},getSearchVal:function(){return this.$search().val()},setValue:function(t){t?this.$control().addClass("has-value"):this.$control().removeClass("has-value"),acf.val(this.$input(),t)},showLoading:function(t){acf.showLoading(this.$(".canvas"))},hideLoading:function(){acf.hideLoading(this.$(".canvas"))},maybeSearch:function(){var e=this.val(),i=this.getSearchVal();if(!i)return this.clear();if("http"!=i.substr(0,4)&&(i="http://"+i),i!==e){var n=this.get("timeout");n&&clearTimeout(n);var a=t.proxy(this.search,this,i);this.set("timeout",setTimeout(a,300))}},search:function(e){var i={action:"acf/fields/oembed/search",s:e,field_key:this.get("key")},n;(n=this.get("xhr"))&&n.abort(),this.showLoading();var n=t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(i),type:"post",dataType:"json",context:this,success:function(t){t&&t.html||(t={url:!1,html:""}),this.val(t.url),this.$(".canvas-media").html(t.html)},complete:function(){this.hideLoading()}});this.set("xhr",n)},clear:function(){this.val(""),this.$search().val(""),this.$(".canvas-media").html("")},onClickClear:function(t,e){this.clear()},onKeypressSearch:function(t,e){13==t.which&&(t.preventDefault(),this.maybeSearch())},onKeyupSearch:function(t,e){e.val()&&this.maybeSearch()},onChangeSearch:function(t,e){this.maybeSearch()}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"radio",events:{'click input[type="radio"]':"onClick"},$control:function(){return this.$(".acf-radio-list")},$input:function(){return this.$("input:checked")},$inputText:function(){return this.$('input[type="text"]')},getValue:function(){var t=this.$input().val();return"other"===t&&this.get("other_choice")&&(t=this.$inputText().val()),t},onClick:function(t,e){var i=e.parent("label"),n=i.hasClass("selected"),a=e.val();this.$(".selected").removeClass("selected"),i.addClass("selected"),this.get("allow_null")&&n&&(i.removeClass("selected"),e.prop("checked",!1).trigger("change"),a=!1),this.get("other_choice")&&("other"===a?this.$inputText().prop("disabled",!1):this.$inputText().prop("disabled",!0))}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"range",events:{'input input[type="range"]':"onChange","change input":"onChange"},$input:function(){return this.$('input[type="range"]')},$inputAlt:function(){return this.$('input[type="number"]')},setValue:function(t){this.busy=!0,acf.val(this.$input(),t),acf.val(this.$inputAlt(),t,!0),this.busy=!1},onChange:function(t,e){this.busy||this.setValue(e.val())}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"relationship",events:{"keypress [data-filter]":"onKeypressFilter","change [data-filter]":"onChangeFilter","keyup [data-filter]":"onChangeFilter","click .choices-list .acf-rel-item":"onClickAdd",'click [data-name="remove_item"]':"onClickRemove",mouseover:"onHover"},$control:function(){return this.$(".acf-relationship")},$list:function(t){return this.$("."+t+"-list")},$listItems:function(t){return this.$list(t).find(".acf-rel-item")},$listItem:function(t,e){return this.$list(t).find('.acf-rel-item[data-id="'+e+'"]')},getValue:function(){var e=[];return this.$listItems("values").each(function(){e.push(t(this).data("id"))}),!!e.length&&e},newChoice:function(t){return["<li>",'<span data-id="'+t.id+'" class="acf-rel-item">'+t.text+"</span>","</li>"].join("")},newValue:function(t){return["<li>",'<input type="hidden" name="'+this.getInputName()+'[]" value="'+t.id+'" />','<span data-id="'+t.id+'" class="acf-rel-item">'+t.text,'<a href="#" class="acf-icon -minus small dark" data-name="remove_item"></a>',"</span>","</li>"].join("")},addSortable:function(t){this.$list("values").sortable({items:"li",forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,update:function(){t.$input().trigger("change")}})},initialize:function(){var t=this.proxy(function(t){if(!this.get("loading")&&this.get("more")){var e=this.$list("choices"),i=Math.ceil(e.scrollTop()),n=Math.ceil(e[0].scrollHeight),a=Math.ceil(e.innerHeight()),r=this.get("paged")||1;i+a>=n&&(this.set("paged",r+1),this.fetch())}});this.$list("choices").scrollTop(0).on("scroll",t),this.fetch()},onHover:function(e){t().off(e),this.addSortable(this)},onKeypressFilter:function(t,e){13==t.which&&t.preventDefault()},onChangeFilter:function(t,e){var i=e.val(),n=e.data("filter");this.get(n)!==i&&(this.set(n,i),this.set("paged",1),e.is("select")?this.fetch():this.maybeFetch())},onClickAdd:function(t,e){var i=this.val(),n=parseInt(this.get("max"));if(e.hasClass("disabled"))return!1;if(n>0&&i&&i.length>=n)return this.showNotice({text:acf.__("Maximum values reached ( {max} values )").replace("{max}",n),type:"warning"}),!1;e.addClass("disabled");var a=this.newValue({id:e.data("id"),text:e.html()});this.$list("values").append(a),this.$input().trigger("change")},onClickRemove:function(t,e){t.preventDefault();var i=e.parent(),n=i.parent(),a=i.data("id");n.remove(),this.$listItem("choices",a).removeClass("disabled"),this.$input().trigger("change")},maybeFetch:function(){var t=this.get("timeout");t&&clearTimeout(t),t=this.setTimeout(this.fetch,300),this.set("timeout",t)},getAjaxData:function(){var t=this.$control().data();for(var e in t)t[e]=this.get(e);return t.action="acf/fields/relationship/query",t.field_key=this.get("key"),t=acf.applyFilters("relationship_ajax_data",t,this)},fetch:function(){var e;(e=this.get("xhr"))&&e.abort();var i=this.getAjaxData(),n=this.$list("choices");1==i.paged&&n.html("");var a=t('<li><i class="acf-loading"></i> '+acf.__("Loading")+"</li>");n.append(a),this.set("loading",!0);var r=function(){this.set("loading",!1),a.remove()},o=function(e){if(!e||!e.results||!e.results.length)return this.set("more",!1),void(1==this.get("paged")&&this.$list("choices").append("<li>"+acf.__("No matches found")+"</li>"));this.set("more",e.more);var i=this.walkChoices(e.results),a=t(i),r=this.val();r&&r.length&&r.map(function(t){a.find('.acf-rel-item[data-id="'+t+'"]').addClass("disabled")}),n.append(a);var o=!1,s=!1;n.find(".acf-rel-label").each(function(){var e=t(this),i=e.siblings("ul");if(o&&o.text()==e.text())return s.append(i.children()),void t(this).parent().remove();o=e,s=i})},e=t.ajax({url:acf.get("ajaxurl"),dataType:"json",type:"post",data:acf.prepareForAjax(i),context:this,success:o,complete:r});this.set("xhr",e)},walkChoices:function(e){var i=function(e){var n="";return t.isArray(e)?e.map(function(t){n+=i(t)}):t.isPlainObject(e)&&(void 0!==e.children?(n+='<li><span class="acf-rel-label">'+e.text+'</span><ul class="acf-bl">',n+=i(e.children),n+="</ul></li>"):n+='<li><span class="acf-rel-item" data-id="'+e.id+'">'+e.text+"</span></li>"),n};return i(e)}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"select",select2:!1,wait:"load",events:{removeField:"onRemove"},$input:function(){return this.$("select")},initialize:function(){var t=this.$input();if(this.inherit(t),this.get("ui")){var e=this.get("ajax_action");e||(e="acf/fields/"+this.get("type")+"/query"),this.select2=acf.newSelect2(t,{field:this,ajax:this.get("ajax"),multiple:this.get("multiple"),placeholder:this.get("placeholder"),allowNull:this.get("allow_null"),ajaxAction:e})}},onRemove:function(){this.select2&&this.select2.destroy()}});acf.registerFieldType(i)}(jQuery),function(t,e){var i="tab",n=acf.Field.extend({type:"tab",wait:"",tabs:!1,tab:!1,findFields:function(){return this.$el.nextUntil(".acf-field-tab",".acf-field")},getFields:function(){return acf.getFields(this.findFields())},findTabs:function(){return this.$el.prevAll(".acf-tab-wrap:first")},findTab:function(){return this.$(".acf-tab-button")},initialize:function(){if(this.$el.is("td"))return this.events={},!1;var t=this.findTabs(),e=this.findTab(),i=acf.parseArgs(e.data(),{endpoint:!1,placement:"",before:this.$el});!t.length||i.endpoint?this.tabs=new r(i):this.tabs=t.data("acf"),this.tab=this.tabs.addTab(e,this)},isActive:function(){return this.tab.isActive()},showFields:function(){this.getFields().map(function(t){t.show(this.cid,"tab"),t.hiddenByTab=!1},this)},hideFields:function(){this.getFields().map(function(t){t.hide(this.cid,"tab"),t.hiddenByTab=this.tab},this)},show:function(t){var e=acf.Field.prototype.show.apply(this,arguments);return e&&(this.tab.show(),this.tabs.refresh()),e},hide:function(t){var e=acf.Field.prototype.hide.apply(this,arguments);return e&&(this.tab.hide(),this.isActive()&&this.tabs.reset()),e},enable:function(t){this.getFields().map(function(t){t.enable("tab")})},disable:function(t){this.getFields().map(function(t){t.disable("tab")})}});acf.registerFieldType(n);var a=0,r=acf.Model.extend({tabs:[],active:!1,actions:{refresh:"onRefresh"},data:{before:!1,placement:"top",index:0,initialized:!1},setup:function(e){t.extend(this.data,e),this.tabs=[],this.active=!1;var i=this.get("placement"),n=this.get("before"),r=n.parent();"left"==i&&r.hasClass("acf-fields")&&r.addClass("-sidebar"),n.is("tr")?this.$el=t('<tr class="acf-tab-wrap"><td colspan="2"><ul class="acf-hl acf-tab-group"></ul></td></tr>'):this.$el=t('<div class="acf-tab-wrap -'+i+'"><ul class="acf-hl acf-tab-group"></ul></div>'),n.before(this.$el),this.set("index",a,!0),a++},initializeTabs:function(){var t=this.getVisible().shift(),e,i,n=(acf.getPreference("this.tabs")||[])[this.get("index")];this.tabs[n]&&this.tabs[n].isVisible()&&(t=this.tabs[n]),t?this.selectTab(t):this.closeTabs(),this.set("initialized",!0)},getVisible:function(){return this.tabs.filter(function(t){return t.isVisible()})},getActive:function(){return this.active},setActive:function(t){return this.active=t},hasActive:function(){return!1!==this.active},isActive:function(t){var e=this.getActive();return e&&e.cid===t.cid},closeActive:function(){this.hasActive()&&this.closeTab(this.getActive())},openTab:function(t){this.closeActive(),t.open(),this.setActive(t)},closeTab:function(t){t.close(),this.setActive(!1)},closeTabs:function(){this.tabs.map(this.closeTab,this)},selectTab:function(t){this.tabs.map(function(e){t.cid!==e.cid&&this.closeTab(e)},this),this.openTab(t)},addTab:function(e,i){var n=t("<li></li>");n.append(e),this.$("ul").append(n);var a=new o({$el:n,field:i,group:this});return this.tabs.push(a),a},reset:function(){return this.closeActive(),this.refresh()},refresh:function(){if(this.hasActive())return!1;var t=this.getVisible().shift();return t&&this.openTab(t),t},onRefresh:function(){if("left"===this.get("placement")){var t=this.$el.parent(),e=this.$el.children("ul"),i=t.is("td")?"height":"min-height",n=e.position().top+e.outerHeight(!0)-1;t.css(i,n)}}}),o=acf.Model.extend({group:!1,field:!1,events:{"click a":"onClick"},index:function(){return this.$el.index()},isVisible:function(){return acf.isVisible(this.$el)},isActive:function(){return this.$el.hasClass("active")},open:function(){this.$el.addClass("active"),this.field.showFields()},close:function(){this.$el.removeClass("active"),this.field.hideFields()},onClick:function(t,e){t.preventDefault(),this.toggle()},toggle:function(){this.isActive()||this.group.openTab(this)}}),s=new acf.Model({priority:50,actions:{prepare:"render",append:"render",unload:"onUnload",invalid_field:"onInvalidField"},findTabs:function(){return t(".acf-tab-wrap")},getTabs:function(){return acf.getInstances(this.findTabs())},render:function(t){this.getTabs().map(function(t){t.get("initialized")||t.initializeTabs()})},onInvalidField:function(t){this.busy||t.hiddenByTab&&(t.hiddenByTab.toggle(),this.busy=!0,this.setTimeout(function(){this.busy=!1},100))},onUnload:function(){var t=[];this.getTabs().map(function(e){var i=e.hasActive()?e.getActive().index():0;t.push(i)}),t.length&&acf.setPreference("this.tabs",t)}})}(jQuery),function(t,e){var i=acf.models.SelectField.extend({type:"post_object"});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.models.SelectField.extend({type:"page_link"});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.models.SelectField.extend({type:"user"});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"taxonomy",data:{ftype:"select"},select2:!1,wait:"load",events:{'click a[data-name="add"]':"onClickAdd",'click input[type="radio"]':"onClickRadio"},$control:function(){return this.$(".acf-taxonomy-field")},$input:function(){return this.getRelatedPrototype().$input.apply(this,arguments)},getRelatedType:function(){var t=this.get("ftype");return"multi_select"==t&&(t="select"),t},getRelatedPrototype:function(){return acf.getFieldType(this.getRelatedType()).prototype},getValue:function(){return this.getRelatedPrototype().getValue.apply(this,arguments)},setValue:function(){return this.getRelatedPrototype().setValue.apply(this,arguments)},initialize:function(){this.getRelatedPrototype().initialize.apply(this,arguments)},onRemove:function(){this.select2&&this.select2.destroy()},onClickAdd:function(e,i){var n=this,a=!1,r=!1,o=!1,s=!1,c=!1,l=!1,u=!1,d=function(){a=acf.newPopup({title:i.attr("title"),loading:!0,width:"300px"});var e={action:"acf/fields/taxonomy/add_term",field_key:n.get("key")};t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(e),type:"post",dataType:"html",success:f})},f=function(t){a.loading(!1),a.content(t),r=a.$("form"),o=a.$('input[name="term_name"]'),s=a.$('select[name="term_parent"]'),c=a.$(".acf-submit-button"),o.focus(),a.on("submit","form",h)},h=function(e,i){if(e.preventDefault(),e.stopImmediatePropagation(),""===o.val())return o.focus(),!1;acf.startButtonLoading(c);var a={action:"acf/fields/taxonomy/add_term",field_key:n.get("key"),term_name:o.val(),term_parent:s.length?s.val():0};t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"json",success:p})},p=function(t){acf.stopButtonLoading(c),u&&u.remove(),acf.isAjaxSuccess(t)?(o.val(""),g(t.data),u=acf.newNotice({
type:"success",text:acf.getAjaxMessage(t),target:r,timeout:2e3,dismiss:!1})):u=acf.newNotice({type:"error",text:acf.getAjaxError(t),target:r,timeout:2e3,dismiss:!1}),o.focus()},g=function(e){var i=t('<option value="'+e.term_id+'">'+e.term_label+"</option>"),a;e.term_parent?s.children('option[value="'+e.term_parent+'"]').after(i):s.append(i),acf.getFields({type:"taxonomy"}).map(function(t){t.get("taxonomy")==n.get("taxonomy")&&t.appendTerm(e)}),n.selectTerm(e.term_id)};d()},appendTerm:function(t){"select"==this.getRelatedType()?this.appendTermSelect(t):this.appendTermCheckbox(t)},appendTermSelect:function(t){this.select2.addOption({id:t.term_id,text:t.term_label})},appendTermCheckbox:function(e){var i=this.$("[name]:first").attr("name"),n=this.$("ul:first");"checkbox"==this.getRelatedType()&&(i+="[]");var a=t(['<li data-id="'+e.term_id+'">',"<label>",'<input type="'+this.get("ftype")+'" value="'+e.term_id+'" name="'+i+'" /> ',"<span>"+e.term_name+"</span>","</label>","</li>"].join(""));if(e.term_parent){var r=n.find('li[data-id="'+e.term_parent+'"]');(n=r.children("ul")).exists()||(n=t('<ul class="children acf-bl"></ul>'),r.append(n))}n.append(a)},selectTerm:function(t){var e;"select"==this.getRelatedType()?this.select2.selectOption(t):this.$('input[value="'+t+'"]').prop("checked",!0).trigger("change")},onClickRadio:function(t,e){var i=e.parent("label"),n=i.hasClass("selected");this.$(".selected").removeClass("selected"),i.addClass("selected"),this.get("allow_null")&&n&&(i.removeClass("selected"),e.prop("checked",!1).trigger("change"))}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.models.DatePickerField.extend({type:"time_picker",$control:function(){return this.$(".acf-time-picker")},initialize:function(){var t=this.$input(),e=this.$inputText(),i={timeFormat:this.get("time_format"),altField:t,altFieldTimeOnly:!1,altTimeFormat:"HH:mm:ss",showButtonPanel:!0,controlType:"select",oneLine:!0,closeText:acf.get("dateTimePickerL10n").selectText,timeOnly:!0,onClose:function(t,e,i){var n=e.dpDiv.find(".ui-datepicker-close");!t&&n.is(":hover")&&i._updateDateTime()}};i=acf.applyFilters("time_picker_args",i,this),acf.newTimePicker(e,i),acf.doAction("time_picker_init",e,i,this)}});acf.registerFieldType(i),acf.newTimePicker=function(e,i){if(void 0===t.timepicker)return!1;i=i||{},e.timepicker(i),t("body > #ui-datepicker-div").exists()&&t("body > #ui-datepicker-div").wrap('<div class="acf-ui-datepicker" />')}}(jQuery),function(t,e){var i=acf.Field.extend({type:"true_false",events:{"change .acf-switch-input":"onChange","focus .acf-switch-input":"onFocus","blur .acf-switch-input":"onBlur","keypress .acf-switch-input":"onKeypress"},$input:function(){return this.$('input[type="checkbox"]')},$switch:function(){return this.$(".acf-switch")},getValue:function(){return this.$input().prop("checked")?1:0},initialize:function(){this.render()},render:function(){var t=this.$switch();if(t.length){var e=t.children(".acf-switch-on"),i=t.children(".acf-switch-off"),n=Math.max(e.width(),i.width());n&&(e.css("min-width",n),i.css("min-width",n))}},switchOn:function(){this.$input().prop("checked",!0),this.$switch().addClass("-on")},switchOff:function(){this.$input().prop("checked",!1),this.$switch().removeClass("-on")},onChange:function(t,e){e.prop("checked")?this.switchOn():this.switchOff()},onFocus:function(t,e){this.$switch().addClass("-focus")},onBlur:function(t,e){this.$switch().removeClass("-focus")},onKeypress:function(t,e){return 37===t.keyCode?this.switchOff():39===t.keyCode?this.switchOn():void 0}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"url",events:{'keyup input[type="url"]':"onkeyup"},$control:function(){return this.$(".acf-input-wrap")},$input:function(){return this.$('input[type="url"]')},initialize:function(){this.render()},isValid:function(){var t=this.val();return!!t&&(-1!==t.indexOf("://")||0===t.indexOf("//"))},render:function(){this.isValid()?this.$control().addClass("-valid"):this.$control().removeClass("-valid")},onkeyup:function(t,e){this.render()}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=acf.Field.extend({type:"wysiwyg",wait:"load",events:{"mousedown .acf-editor-wrap.delay":"onMousedown",unmountField:"disableEditor",remountField:"enableEditor",removeField:"disableEditor"},$control:function(){return this.$(".acf-editor-wrap")},$input:function(){return this.$("textarea")},getMode:function(){return this.$control().hasClass("tmce-active")?"visual":"text"},initialize:function(){this.$control().hasClass("delay")||this.initializeEditor()},initializeEditor:function(){var t=this.$control(),e=this.$input(),i={tinymce:!0,quicktags:!0,toolbar:this.get("toolbar"),mode:this.getMode(),field:this},n=e.attr("id"),a=acf.uniqueId("acf-editor-"),r=e.data();acf.rename({target:t,search:n,replace:a,destructive:!0}),this.set("id",a,!0),acf.tinymce.initialize(a,i),this.$input().data(r)},onMousedown:function(t){t.preventDefault();var e=this.$control();e.removeClass("delay"),e.find(".acf-editor-toolbar").remove(),this.initializeEditor()},enableEditor:function(){"visual"==this.getMode()&&acf.tinymce.enable(this.get("id"))},disableEditor:function(){acf.tinymce.destroy(this.get("id"))}});acf.registerFieldType(i)}(jQuery),function(t,e){var i=[];acf.Condition=acf.Model.extend({type:"",operator:"==",label:"",choiceType:"input",fieldTypes:[],data:{conditions:!1,field:!1,rule:{}},events:{change:"change",keyup:"change",enableField:"change",disableField:"change"},setup:function(e){t.extend(this.data,e)},getEventTarget:function(t,e){return t||this.get("field").$el},change:function(t,e){this.get("conditions").change(t)},match:function(t,e){return!1},calculate:function(){return this.match(this.get("rule"),this.get("field"))},choices:function(t){return'<input type="text" />'}}),acf.newCondition=function(t,e){var i=e.get("field"),n=i.getField(t.field);if(!i||!n)return!1;var a={rule:t,target:i,conditions:e,field:n},r=n.get("type"),o=t.operator,s,c,l;return new(acf.getConditionTypes({fieldType:r,operator:o})[0]||acf.Condition)(a)};var n=function(t){return acf.strPascalCase(t||"")+"Condition"};acf.registerConditionType=function(t){var e,a=t.prototype.type,r=n(a);acf.models[r]=t,i.push(a)},acf.getConditionType=function(t){var e=n(t);return acf.models[e]||!1},acf.registerConditionForFieldType=function(t,e){var i=acf.getConditionType(t);i&&i.prototype.fieldTypes.push(e)},acf.getConditionTypes=function(t){t=acf.parseArgs(t,{fieldType:"",operator:""});var e=[];return i.map(function(i){var n=acf.getConditionType(i),a=n.prototype.fieldTypes,r=n.prototype.operator;t.fieldType&&-1===a.indexOf(t.fieldType)||t.operator&&r!==t.operator||e.push(n)}),e}}(jQuery),function(t,e){var i="conditional_logic",n=new acf.Model({id:"conditionsManager",priority:20,actions:{new_field:"onNewField"},onNewField:function(t){t.has("conditions")&&t.getConditions().render()}}),a=function(t,e){var i=acf.getFields({key:e,sibling:t.$el,suppressFilters:!0});return i.length||(i=acf.getFields({key:e,parent:t.$el.parent(),suppressFilters:!0})),!!i.length&&i[0]};acf.Field.prototype.getField=function(t){var e=a(this,t);if(e)return e;for(var i=this.parents(),n=0;n<i.length;n++)if(e=a(i[n],t))return e;return!1},acf.Field.prototype.getConditions=function(){return this.conditions||(this.conditions=new o(this)),this.conditions};var r=!1,o=acf.Model.extend({id:"Conditions",data:{field:!1,timeStamp:!1,groups:[]},setup:function(t){this.data.field=t;var e=t.get("conditions");e instanceof Array?e[0]instanceof Array?e.map(function(t,e){this.addRules(t,e)},this):this.addRules(e):this.addRule(e)},change:function(t){if(this.get("timeStamp")===t.timeStamp)return!1;this.set("timeStamp",t.timeStamp,!0);var e=this.render()},render:function(){return this.calculate()?this.show():this.hide()},show:function(){return this.get("field").showEnable(this.cid,i)},hide:function(){return this.get("field").hideDisable(this.cid,i)},calculate:function(){var t=!1;return this.getGroups().map(function(e){var i;t||e.filter(function(t){return t.calculate()}).length==e.length&&(t=!0)}),t},hasGroups:function(){return null!=this.data.groups},getGroups:function(){return this.data.groups},addGroup:function(){var t=[];return this.data.groups.push(t),t},hasGroup:function(t){return null!=this.data.groups[t]},getGroup:function(t){return this.data.groups[t]},removeGroup:function(t){return this.data.groups[t].delete,this},addRules:function(t,e){t.map(function(t){this.addRule(t,e)},this)},addRule:function(t,e){var i;e=e||0,i=this.hasGroup(e)?this.getGroup(e):this.addGroup();var n=acf.newCondition(t,this);if(!n)return!1;i.push(n)},hasRule:function(){},getRule:function(t,e){return t=t||0,e=e||0,this.data.groups[e][t]},removeRule:function(){}})}(jQuery),function(t,e){var i=acf.__,n=function(t){return t?""+t:""},a=function(t,e){return n(t).toLowerCase()===n(e).toLowerCase()},r=function(t,e){return parseFloat(t)===parseFloat(e)},o=function(t,e){return parseFloat(t)>parseFloat(e)},s=function(t,e){return parseFloat(t)<parseFloat(e)},c=function(t,e){return(e=e.map(function(t){return n(t)})).indexOf(t)>-1},l=function(t,e){return n(t).indexOf(n(e))>-1},u=function(t,e){var i=new RegExp(n(e),"gi");return n(t).match(i)},d=acf.Condition.extend({type:"hasValue",operator:"!=empty",label:i("Has any value"),fieldTypes:["text","textarea","number","range","email","url","password","image","file","wysiwyg","oembed","select","checkbox","radio","button_group","link","post_object","page_link","relationship","taxonomy","user","google_map","date_picker","date_time_picker","time_picker","color_picker"],match:function(t,e){return!!e.val()},choices:function(t){return'<input type="text" disabled="" />'}});acf.registerConditionType(d);var f=d.extend({type:"hasNoValue",operator:"==empty",label:i("Has no value"),match:function(t,e){return!d.prototype.match.apply(this,arguments)}});acf.registerConditionType(f);var h=acf.Condition.extend({type:"equalTo",operator:"==",label:i("Value is equal to"),fieldTypes:["text","textarea","number","range","email","url","password"],match:function(e,i){return t.isNumeric(e.value)?r(e.value,i.val()):a(e.value,i.val())},choices:function(t){return'<input type="text" />'}});acf.registerConditionType(h);var p=h.extend({type:"notEqualTo",operator:"!=",label:i("Value is not equal to"),match:function(t,e){return!h.prototype.match.apply(this,arguments)}});acf.registerConditionType(p);var g=acf.Condition.extend({type:"patternMatch",operator:"==pattern",label:i("Value matches pattern"),fieldTypes:["text","textarea","email","url","password","wysiwyg"],match:function(t,e){return u(e.val(),t.value)},choices:function(t){return'<input type="text" placeholder="[a-z0-9]" />'}});acf.registerConditionType(g);var m=acf.Condition.extend({type:"contains",operator:"==contains",label:i("Value contains"),fieldTypes:["text","textarea","number","email","url","password","wysiwyg","oembed","select"],match:function(t,e){return l(e.val(),t.value)},choices:function(t){return'<input type="text" />'}});acf.registerConditionType(m);var v=h.extend({type:"trueFalseEqualTo",choiceType:"select",fieldTypes:["true_false"],choices:function(t){return[{id:1,text:i("Checked")}]}});acf.registerConditionType(v);var y=p.extend({type:"trueFalseNotEqualTo",choiceType:"select",fieldTypes:["true_false"],choices:function(t){return[{id:1,text:i("Checked")}]}});acf.registerConditionType(y);var b=acf.Condition.extend({type:"selectEqualTo",operator:"==",label:i("Value is equal to"),fieldTypes:["select","checkbox","radio","button_group"],match:function(t,e){var i=e.val();return i instanceof Array?c(t.value,i):a(t.value,i)},choices:function(e){var n=[],a=e.$setting("choices textarea").val().split("\n");return e.$input("allow_null").prop("checked")&&n.push({id:"",text:i("Null")}),a.map(function(e){(e=e.split(":"))[1]=e[1]||e[0],n.push({id:t.trim(e[0]),text:t.trim(e[1])})}),n}});acf.registerConditionType(b);var w=b.extend({type:"selectNotEqualTo",operator:"!=",label:i("Value is not equal to"),match:function(t,e){return!b.prototype.match.apply(this,arguments)}});acf.registerConditionType(w);var x=acf.Condition.extend({type:"greaterThan",operator:">",label:i("Value is greater than"),fieldTypes:["number","range"],match:function(t,e){var i=e.val();return i instanceof Array&&(i=i.length),o(i,t.value)},choices:function(t){return'<input type="number" />'}});acf.registerConditionType(x);var _=x.extend({type:"lessThan",operator:"<",label:i("Value is less than"),match:function(t,e){var i=e.val();return i instanceof Array&&(i=i.length),s(i,t.value)},choices:function(t){return'<input type="number" />'}});acf.registerConditionType(_);var $=x.extend({type:"selectionGreaterThan",label:i("Selection is greater than"),fieldTypes:["checkbox","select","post_object","page_link","relationship","taxonomy","user"]});acf.registerConditionType($);var k=_.extend({type:"selectionLessThan",label:i("Selection is less than"),fieldTypes:["checkbox","select","post_object","page_link","relationship","taxonomy","user"]});acf.registerConditionType(k)}(jQuery),function(t,e){acf.newMediaPopup=function(t){var e=null,t=acf.parseArgs(t,{mode:"select",title:"",button:"",type:"",field:!1,allowedTypes:"",library:"all",multiple:!1,attachment:0,autoOpen:!0,open:function(){},select:function(){},close:function(){}});return e="edit"==t.mode?new acf.models.EditMediaPopup(t):new acf.models.SelectMediaPopup(t),t.autoOpen&&setTimeout(function(){e.open()},1),acf.doAction("new_media_popup",e),e};var i=function(){var e=acf.get("post_id");return t.isNumeric(e)?e:0};acf.getMimeTypes=function(){return this.get("mimeTypes")},acf.getMimeType=function(t){var e=acf.getMimeTypes();if(void 0!==e[t])return e[t];for(var i in e)if(-1!==i.indexOf(t))return e[i];return!1};var n=acf.Model.extend({id:"MediaPopup",data:{},defaults:{},frame:!1,setup:function(e){t.extend(this.data,e)},initialize:function(){var t=this.getFrameOptions();this.addFrameStates(t);var e=wp.media(t);e.acf=this,this.addFrameEvents(e,t),this.frame=e},open:function(){this.frame.open()},close:function(){this.frame.close()},remove:function(){this.frame.detach(),this.frame.remove()},getFrameOptions:function(){var t={title:this.get("title"),multiple:this.get("multiple"),library:{},states:[]};return this.get("type")&&(t.library.type=this.get("type")),"uploadedTo"===this.get("library")&&(t.library.uploadedTo=i()),this.get("attachment")&&(t.library.post__in=[this.get("attachment")]),this.get("button")&&(t.button={text:this.get("button")}),t},addFrameStates:function(t){var e=wp.media.query(t.library);this.get("field")&&acf.isset(e,"mirroring","args")&&(e.mirroring.args._acfuploader=this.get("field")),t.states.push(new wp.media.controller.Library({library:e,multiple:this.get("multiple"),title:this.get("title"),priority:20,filterable:"all",editable:!0,allowLocalEdits:!0})),acf.isset(wp,"media","controller","EditImage")&&t.states.push(new wp.media.controller.EditImage)},addFrameEvents:function(t,e){t.on("open",function(){this.$el.closest(".media-modal").addClass("acf-media-modal -"+this.acf.get("mode"))},t),t.on("content:render:edit-image",function(){var t=this.state().get("image"),e=new wp.media.view.EditImage({model:t,controller:this}).render();this.content.set(e),e.loadEditor()},t),t.on("select",function(){var e=t.state().get("selection");e&&e.each(function(e,i){t.acf.get("select").apply(t.acf,[e,i])})}),t.on("close",function(){setTimeout(function(){t.acf.get("close").apply(t.acf),t.acf.remove()},1)})}});acf.models.SelectMediaPopup=n.extend({id:"SelectMediaPopup",setup:function(t){t.button||(t.button=acf._x("Select","verb")),n.prototype.setup.apply(this,arguments)},addFrameEvents:function(t,e){acf.isset(_wpPluploadSettings,"defaults","multipart_params")&&(_wpPluploadSettings.defaults.multipart_params._acfuploader=this.get("field"),t.on("open",function(){delete _wpPluploadSettings.defaults.multipart_params._acfuploader})),t.on("content:activate:browse",function(){var e=!1;try{e=t.content.get().toolbar}catch(t){return void console.log(t)}t.acf.customizeFilters.apply(t.acf,[e])}),n.prototype.addFrameEvents.apply(this,arguments)},customizeFilters:function(e){var i=e.get("filters"),n;("image"==this.get("type")&&(i.filters.all.text=acf.__("All images"),delete i.filters.audio,delete i.filters.video,delete i.filters.image,t.each(i.filters,function(t,e){e.props.type=e.props.type||"image"})),this.get("allowedTypes"))&&this.get("allowedTypes").split(" ").join("").split(".").join("").split(",").map(function(t){var e=acf.getMimeType(t);if(e){var n={text:e,props:{status:null,type:e,uploadedTo:null,orderby:"date",order:"DESC"},priority:20};i.filters[e]=n}});if("uploadedTo"===this.get("library")){var a=this.frame.options.library.uploadedTo;delete i.filters.unattached,delete i.filters.uploaded,t.each(i.filters,function(t,e){e.text+=" ("+acf.__("Uploaded to this post")+")",e.props.uploadedTo=a})}var r=this.get("field"),o;t.each(i.filters,function(t,e){e.props._acfuploader=r}),e.get("search").model.attributes._acfuploader=r,i.renderFilters&&i.renderFilters()}}),acf.models.EditMediaPopup=n.extend({id:"SelectMediaPopup",setup:function(t){t.button||(t.button=acf._x("Update","verb")),n.prototype.setup.apply(this,arguments)},addFrameEvents:function(t,e){t.on("open",function(){this.$el.closest(".media-modal").addClass("acf-expanded"),"browse"!=this.content.mode()&&this.content.mode("browse");var e,i=this.state().get("selection"),n=wp.media.attachment(t.acf.get("attachment"));i.add(n)},t),n.prototype.addFrameEvents.apply(this,arguments)}});var a=new acf.Model({id:"customizePrototypes",wait:"ready",initialize:function(){if(acf.isset(window,"wp","media","view")){var t=i();t&&acf.isset(wp,"media","view","settings","post")&&(wp.media.view.settings.post.id=t),this.customizeAttachmentsButton(),this.customizeAttachmentsRouter(),this.customizeAttachmentFilters(),this.customizeAttachmentCompat(),this.customizeAttachmentLibrary()}},customizeAttachmentsButton:function(){if(acf.isset(wp,"media","view","Button")){var t=wp.media.view.Button;wp.media.view.Button=t.extend({initialize:function(){var t=_.defaults(this.options,this.defaults);this.model=new Backbone.Model(t),this.listenTo(this.model,"change",this.render)}})}},customizeAttachmentsRouter:function(){if(acf.isset(wp,"media","view","Router")){var e=wp.media.view.Router;wp.media.view.Router=e.extend({addExpand:function(){var e=t(['<a href="#" class="acf-expand-details">','<span class="is-closed"><span class="acf-icon -left small grey"></span>'+acf.__("Expand Details")+"</span>",'<span class="is-open"><span class="acf-icon -right small grey"></span>'+acf.__("Collapse Details")+"</span>","</a>"].join(""));e.on("click",function(e){e.preventDefault();var i=t(this).closest(".media-modal");i.hasClass("acf-expanded")?i.removeClass("acf-expanded"):i.addClass("acf-expanded")}),this.$el.append(e)},initialize:function(){return e.prototype.initialize.apply(this,arguments),this.addExpand(),this}})}},customizeAttachmentFilters:function(){var e;acf.isset(wp,"media","view","AttachmentFilters","All")&&(wp.media.view.AttachmentFilters.All.prototype.renderFilters=function(){this.$el.html(_.chain(this.filters).map(function(e,i){return{el:t("<option></option>").val(i).html(e.text)[0],priority:e.priority||50}},this).sortBy("priority").pluck("el").value())})},customizeAttachmentCompat:function(){if(acf.isset(wp,"media","view","AttachmentCompat")){var e=wp.media.view.AttachmentCompat,i=!1;wp.media.view.AttachmentCompat=e.extend({render:function(){return this.rendered?this:(e.prototype.render.apply(this,arguments),this.$("#acf-form-data").length?(clearTimeout(i),i=setTimeout(t.proxy(function(){this.rendered=!0,acf.doAction("append",this.$el)},this),50),this):this)},save:function(t){var e={};t&&t.preventDefault(),e=acf.serializeForAjax(this.$el),this.controller.trigger("attachment:compat:waiting",["waiting"]),this.model.saveCompat(e).always(_.bind(this.postSave,this))}})}},customizeAttachmentLibrary:function(){if(acf.isset(wp,"media","view","Attachment","Library")){var t=wp.media.view.Attachment.Library;wp.media.view.Attachment.Library=t.extend({render:function(){var e=acf.isget(this,"controller","acf"),i=acf.isget(this,"model","attributes");if(e&&i){i.acf_errors&&this.$el.addClass("acf-disabled");var n=e.get("selected");n&&n.indexOf(i.id)>-1&&this.$el.addClass("acf-selected")}return t.prototype.render.apply(this,arguments)},toggleSelection:function(e){var i=this.collection,n=this.options.selection,a=this.model,r=n.single(),o=this.controller,s=acf.isget(this,"model","attributes","acf_errors"),c=o.$el.find(".media-frame-content .media-sidebar");if(c.children(".acf-selection-error").remove(),c.children().removeClass("acf-hidden"),o&&s){var l=acf.isget(this,"model","attributes","filename");return c.children().addClass("acf-hidden"),c.prepend(['<div class="acf-selection-error">','<span class="selection-error-label">'+acf.__("Restricted")+"</span>",'<span class="selection-error-filename">'+l+"</span>",'<span class="selection-error-message">'+s+"</span>","</div>"].join("")),n.reset(),void n.single(a)}return t.prototype.toggleSelection.apply(this,arguments)}})}}})}(jQuery),function(t,e){acf.screen=new acf.Model({active:!0,xhr:!1,timeout:!1,wait:"load",events:{"change #page_template":"onChange","change #parent_id":"onChange","change #post-formats-select":"onChange","change .categorychecklist":"onChange","change .tagsdiv":"onChange",'change .acf-taxonomy-field[data-save="1"]':"onChange","change #product-type":"onChange"},isPost:function(){return"post"===acf.get("screen")},isUser:function(){return"user"===acf.get("screen")},isTaxonomy:function(){return"taxonomy"===acf.get("screen")},isAttachment:function(){return"attachment"===acf.get("screen")},isNavMenu:function(){return"nav_menu"===acf.get("screen")},isWidget:function(){return"widget"===acf.get("screen")},isComment:function(){return"comment"===acf.get("screen")},getPageTemplate:function(){var e=t("#page_template");return e.length?e.val():null},getPageParent:function(e,i){var i;return(i=t("#parent_id")).length?i.val():null},getPageType:function(t,e){return this.getPageParent()?"child":"parent"},getPostType:function(){return t("#post_type").val()},getPostFormat:function(e,i){var i;if((i=t("#post-formats-select input:checked")).length){var n=i.val();return"0"==n?"standard":n}return null},getPostCoreTerms:function(){var e={},i=acf.serialize(t(".categorydiv, .tagsdiv"));for(var n in i.tax_input&&(e=i.tax_input),i.post_category&&(e.category=i.post_category),e)acf.isArray(e[n])||(e[n]=e[n].split(", "));return e},getPostTerms:function(){var t=this.getPostCoreTerms();for(var e in acf.getFields({type:"taxonomy"}).map(function(e){if(e.get("save")){var i=e.val(),n=e.get("taxonomy");i&&(t[n]=t[n]||[],i=acf.isArray(i)?i:[i],t[n]=t[n].concat(i))}}),null!==(productType=this.getProductType())&&(t.product_type=[productType]),t)t[e]=acf.uniqueArray(t[e]);return t},getProductType:function(){var e=t("#product-type");return e.length?e.val():null},check:function(){if("post"===acf.get("screen")){this.xhr&&this.xhr.abort();var e=acf.parseArgs(this.data,{action:"acf/ajax/check_screen",screen:acf.get("screen"),exists:[]});this.isPost()&&(e.post_id=acf.get("post_id")),null!==(postType=this.getPostType())&&(e.post_type=postType),null!==(pageTemplate=this.getPageTemplate())&&(e.page_template=pageTemplate),null!==(pageParent=this.getPageParent())&&(e.page_parent=pageParent),null!==(pageType=this.getPageType())&&(e.page_type=pageType),null!==(postFormat=this.getPostFormat())&&(e.post_format=postFormat),null!==(postTerms=this.getPostTerms())&&(e.post_terms=postTerms),acf.getPostboxes().map(function(t){e.exists.push(t.get("key"))}),e=acf.applyFilters("check_screen_args",e);var i=function(t){acf.isAjaxSuccess(t)&&("post"==acf.get("screen")?this.renderPostScreen(t.data):"user"==acf.get("screen")&&this.renderUserScreen(t.data)),acf.doAction("check_screen_complete",t.data,e)};this.xhr=t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(e),type:"post",dataType:"json",context:this,success:i})}},onChange:function(t,e){this.setTimeout(this.check,1)},renderPostScreen:function(e){var i=[],n=function(e,i){var n=t._data(e[0]).events;for(var a in n)for(var r=0;r<n[a].length;r++)i.on(a,n[a][r].handler)},a=function(e,i){var n=i.indexOf(e);if(-1==n)return!1;for(var a=n-1;a>=0;a--)if(t("#"+i[a]).length)return t("#"+i[a]).after(t("#"+e));for(var a=n+1;a<i.length;a++)if(t("#"+i[a]).length)return t("#"+i[a]).before(t("#"+e));return!1};e.results.map(function(r,o){var s=acf.getPostbox(r.id);if(!s){var c=t(['<div id="'+r.id+'" class="postbox">','<button type="button" class="handlediv" aria-expanded="false">','<span class="screen-reader-text">Toggle panel: '+r.title+"</span>",'<span class="toggle-indicator" aria-hidden="true"></span>',"</button>",'<h2 class="hndle ui-sortable-handle">',"<span>"+r.title+"</span>","</h2>",'<div class="inside">',r.html,"</div>","</div>"].join(""));if(t("#adv-settings").length){var l=t("#adv-settings .metabox-prefs"),u=t(['<label for="'+r.id+'-hide">','<input class="hide-postbox-tog" name="'+r.id+'-hide" type="checkbox" id="'+r.id+'-hide" value="'+r.id+'" checked="checked">'," "+r.title,"</label>"].join(""));n(l.find("input").first(),u.find("input")),l.append(u)}"side"===r.position?t("#"+r.position+"-sortables").append(c):t("#"+r.position+"-sortables").prepend(c);var d=[];if(e.results.map(function(e){r.position===e.position&&t("#"+r.position+"-sortables #"+e.id).length&&d.push(e.id)}),a(r.id,d),e.sorted)for(var f in e.sorted){var d=e.sorted[f].split(",");if(a(r.id,d))break}var h=t("#submitdiv");t("#submitdiv").length&&(n(h.children(".handlediv"),c.children(".handlediv")),n(h.children(".hndle"),c.children(".hndle"))),s=acf.newPostbox(r),acf.doAction("append",c),acf.doAction("append_postbox",s)}s.showEnable(),acf.doAction("show_postbox",s),i.push(r.id)}),acf.getPostboxes().map(function(t){-1===i.indexOf(t.get("id"))&&(t.hideDisable(),acf.doAction("hide_postbox",t))}),t("#acf-style").html(e.style)},renderUserScreen:function(t){}});var i=new acf.Model({wait:"load",initialize:function(){acf.isGutenberg()&&(wp.data.subscribe(this.proxy(this.onChange)),acf.screen.getPageTemplate=this.getPageTemplate,acf.screen.getPageParent=this.getPageParent,acf.screen.getPostType=this.getPostType,acf.screen.getPostFormat=this.getPostFormat,acf.screen.getPostCoreTerms=this.getPostCoreTerms,acf.unload.disable())},onChange:function(){var t=wp.data.select("core/editor").getPostEdits(),e=["template","parent","format"],i;(wp.data.select("core").getTaxonomies()||[]).map(function(t){e.push(t.rest_base)}),(e=e.filter(this.proxy(function(e){return void 0!==t[e]&&t[e]!==this.get(e)}))).length&&this.triggerChange(t)},triggerChange:function(t){void 0!==t&&(this.data=t),acf.screen.check()},getPageTemplate:function(){return wp.data.select("core/editor").getEditedPostAttribute("template")},getPageParent:function(t,e){return wp.data.select("core/editor").getEditedPostAttribute("parent")},getPostType:function(){return wp.data.select("core/editor").getEditedPostAttribute("type")},getPostFormat:function(t,e){return wp.data.select("core/editor").getEditedPostAttribute("format")},getPostCoreTerms:function(){var t={},e;return(wp.data.select("core").getTaxonomies()||[]).map(function(e){var i=wp.data.select("core/editor").getEditedPostAttribute(e.rest_base);i&&(t[e.slug]=i)}),t}});acf.screen.refreshAvailableMetaBoxesPerLocation=function(){var t=wp.data.select("core/edit-post"),e=wp.data.dispatch("core/edit-post"),i={};t.getActiveMetaBoxLocations().map(function(e){i[e]=t.getMetaBoxesPerLocation(e)});var n=[];for(var a in i)n=n.concat(i[a].map(function(t){return t.id}));acf.getPostboxes().map(function(t){if(-1===n.indexOf(t.get("id"))){var e=t.$el.closest("form").attr("class").replace("metabox-location-","");i[e]=i[e]||[],i[e].push({id:t.get("id"),title:t.get("title")})}}),e.setAvailableMetaBoxesPerLocation(i)}}(jQuery),function(t,e){function i(){return acf.isset(window,"jQuery","fn","select2","amd")?4:!!acf.isset(window,"Select2")&&3}acf.newSelect2=function(t,e){if(e=acf.parseArgs(e,{allowNull:!1,placeholder:"",multiple:!1,field:!1,ajax:!1,ajaxAction:"",ajaxData:function(t){return t},ajaxResults:function(t){return t}}),4==i())var n=new a(t,e);else var n=new r(t,e);return acf.doAction("new_select2",n),n};var n=acf.Model.extend({setup:function(e,i){t.extend(this.data,i),this.$el=e},initialize:function(){},selectOption:function(t){var e=this.getOption(t);e.prop("selected")||e.prop("selected",!0).trigger("change")},unselectOption:function(t){var e=this.getOption(t);e.prop("selected")&&e.prop("selected",!1).trigger("change")},getOption:function(t){return this.$('option[value="'+t+'"]')},addOption:function(e){e=acf.parseArgs(e,{id:"",text:"",selected:!1});var i=this.getOption(e.id);return i.length||((i=t("<option></option>")).html(e.text),i.attr("value",e.id),i.prop("selected",e.selected),this.$el.append(i)),i},getValue:function(){var e=[],i=this.$el.find("option:selected");return i.exists()?((i=i.sort(function(t,e){return+t.getAttribute("data-i")-+e.getAttribute("data-i")})).each(function(){var i=t(this);e.push({$el:i,id:i.attr("value"),text:i.text()})}),e):e},mergeOptions:function(){},getChoices:function(){var e=function(i){var n=[];return i.children().each(function(){var i=t(this);i.is("optgroup")?n.push({text:i.attr("label"),children:e(i)}):n.push({id:i.attr("value"),text:i.text()})}),n};return e(this.$el)},decodeChoices:function(t){var e=function(t){return t.map(function(t){return t.text=acf.decode(t.text),t.children&&(t.children=e(t.children)),t}),t};return e(t)},getAjaxData:function(t){var e={action:this.get("ajaxAction"),s:t.term||"",paged:t.page||1},i=this.get("field");i&&(e.field_key=i.get("key"));var n=this.get("ajaxData");return n&&(e=n.apply(this,[e,t])),e=acf.applyFilters("select2_ajax_data",e,this.data,this.$el,i||!1,this),acf.prepareForAjax(e)},getAjaxResults:function(t,e){(t=acf.parseArgs(t,{results:!1,more:!1})).results&&(t.results=this.decodeChoices(t.results));var i=this.get("ajaxResults");return i&&(t=i.apply(this,[t,e])),t=acf.applyFilters("select2_ajax_results",t,e,this)},processAjaxResults:function(e,i){var e;return(e=this.getAjaxResults(e,i)).more&&(e.pagination={more:!0}),setTimeout(t.proxy(this.mergeOptions,this),1),e},destroy:function(){this.$el.data("select2")&&this.$el.select2("destroy"),this.$el.siblings(".select2-container").remove()}}),a=n.extend({initialize:function(){var e=this.$el,i={width:"100%",allowClear:this.get("allowNull"),placeholder:this.get("placeholder"),multiple:this.get("multiple"),data:[],escapeMarkup:function(t){return t}};i.multiple&&this.getValue().map(function(t){t.$el.detach().appendTo(e)}),e.removeData("ajax"),e.removeAttr("data-ajax"),this.get("ajax")&&(i.ajax={url:acf.get("ajaxurl"),delay:250,dataType:"json",type:"post",cache:!1,data:t.proxy(this.getAjaxData,this),processResults:t.proxy(this.processAjaxResults,this)});var n=this.get("field");i=acf.applyFilters("select2_args",i,e,this.data,n||!1,this),e.select2(i);var a=e.next(".select2-container");if(i.multiple){var r=a.find("ul");r.sortable({stop:function(i){r.find(".select2-selection__choice").each(function(){var i;t(t(this).data("data").element).detach().appendTo(e)}),e.trigger("change")}}),e.on("select2:select",this.proxy(function(t){this.getOption(t.params.data.id).detach().appendTo(this.$el)}))}a.addClass("-acf"),acf.doAction("select2_init",e,i,this.data,n||!1,this)},mergeOptions:function(){var e=!1,i=!1;t('.select2-results__option[role="group"]').each(function(){var n=t(this).children("ul"),a=t(this).children("strong");if(i&&i.text()===a.text())return e.append(n.children()),void t(this).remove();e=n,i=a})}}),r=n.extend({initialize:function(){var e=this.$el,i=this.getValue(),n=this.get("multiple"),a={width:"100%",allowClear:this.get("allowNull"),placeholder:this.get("placeholder"),separator:"||",multiple:this.get("multiple"),data:this.getChoices(),escapeMarkup:function(t){return t},dropdownCss:{"z-index":"999999999"},initSelection:function(t,e){e(n?i:i.shift())}
},r=e.siblings("input");r.length||(r=t('<input type="hidden" />'),e.before(r)),inputValue=i.map(function(t){return t.id}).join("||"),r.val(inputValue),a.multiple&&i.map(function(t){t.$el.detach().appendTo(e)}),a.allowClear&&(a.data=a.data.filter(function(t){return""!==t.id})),e.removeData("ajax"),e.removeAttr("data-ajax"),this.get("ajax")&&(a.ajax={url:acf.get("ajaxurl"),quietMillis:250,dataType:"json",type:"post",cache:!1,data:t.proxy(this.getAjaxData,this),results:t.proxy(this.processAjaxResults,this)});var o=this.get("field");a=acf.applyFilters("select2_args",a,e,this.data,o||!1,this),r.select2(a);var s=r.select2("container"),c=t.proxy(this.getOption,this);if(a.multiple){var l=s.find("ul");l.sortable({stop:function(){l.find(".select2-search-choice").each(function(){var i=t(this).data("select2Data"),n;c(i.id).detach().appendTo(e)}),e.trigger("change")}})}r.on("select2-selecting",function(i){var n=i.choice,a=c(n.id);a.length||(a=t('<option value="'+n.id+'">'+n.text+"</option>")),a.detach().appendTo(e)}),s.addClass("-acf"),acf.doAction("select2_init",e,a,this.data,o||!1,this),r.on("change",function(){var t=r.val();t.indexOf("||")&&(t=t.split("||")),e.val(t).trigger("change")}),e.hide()},mergeOptions:function(){var e=!1,i=!1;t("#select2-drop .select2-result-with-children").each(function(){var n=t(this).children("ul"),a=t(this).children(".select2-result-label");if(i&&i.text()===a.text())return i.append(n.children()),void t(this).remove();e=n,i=a})},getAjaxData:function(t,e){var i={term:t,page:e};return n.prototype.getAjaxData.apply(this,[i])}}),o=new acf.Model({priority:5,wait:"prepare",initialize:function(){var t=acf.get("locale"),e=acf.get("rtl"),n=acf.get("select2L10n"),a=i();return!!n&&(0!==t.indexOf("en")&&void(4==a?this.addTranslations4():3==a&&this.addTranslations3()))},addTranslations4:function(){var t=acf.get("select2L10n"),e=acf.get("locale");e=e.replace("_","-");var i={errorLoading:function(){return t.load_fail},inputTooLong:function(e){var i=e.input.length-e.maximum;return i>1?t.input_too_long_n.replace("%d",i):t.input_too_long_1},inputTooShort:function(e){var i=e.minimum-e.input.length;return i>1?t.input_too_short_n.replace("%d",i):t.input_too_short_1},loadingMore:function(){return t.load_more},maximumSelected:function(e){var i=e.maximum;return i>1?t.selection_too_long_n.replace("%d",i):t.selection_too_long_1},noResults:function(){return t.matches_0},searching:function(){return t.searching}};jQuery.fn.select2.amd.define("select2/i18n/"+e,[],function(){return i})},addTranslations3:function(){var e=acf.get("select2L10n"),i=acf.get("locale");i=i.replace("_","-");var n={formatMatches:function(t){return t>1?e.matches_n.replace("%d",t):e.matches_1},formatNoMatches:function(){return e.matches_0},formatAjaxError:function(){return e.load_fail},formatInputTooShort:function(t,i){var n=i-t.length;return n>1?e.input_too_short_n.replace("%d",n):e.input_too_short_1},formatInputTooLong:function(t,i){var n=t.length-i;return n>1?e.input_too_long_n.replace("%d",n):e.input_too_long_1},formatSelectionTooBig:function(t){return t>1?e.selection_too_long_n.replace("%d",t):e.selection_too_long_1},formatLoadMore:function(){return e.load_more},formatSearching:function(){return e.searching}};t.fn.select2.locales=t.fn.select2.locales||{},t.fn.select2.locales[i]=n,t.extend(t.fn.select2.defaults,n)}})}(jQuery),function(t,e){acf.tinymce={defaults:function(){return"undefined"!=typeof tinyMCEPreInit&&{tinymce:tinyMCEPreInit.mceInit.acf_content,quicktags:tinyMCEPreInit.qtInit.acf_content};var t},initialize:function(t,e){(e=acf.parseArgs(e,{tinymce:!0,quicktags:!0,toolbar:"full",mode:"visual",field:!1})).tinymce&&this.initializeTinymce(t,e),e.quicktags&&this.initializeQuicktags(t,e)},initializeTinymce:function(e,i){var n=t("#"+e),a=this.defaults(),r=acf.get("toolbars"),o=i.field||!1,s=o.$el||!1;if("undefined"==typeof tinymce)return!1;if(!a)return!1;if(tinymce.get(e))return this.enable(e);var c=t.extend({},a.tinymce,i.tinymce);c.id=e,c.selector="#"+e;var l=i.toolbar;if(l&&r&&r[l])for(var u=1;u<=4;u++)c["toolbar"+u]=r[l][u]||"";if(c.setup=function(t){t.on("change",function(e){t.save(),n.trigger("change")}),t.on("mouseup",function(t){var e=new MouseEvent("mouseup");window.dispatchEvent(e)})},c.wp_autoresize_on=!1,c.tadv_noautop||(c.wpautop=!0),c=acf.applyFilters("wysiwyg_tinymce_settings",c,e,o),tinyMCEPreInit.mceInit[e]=c,"visual"==i.mode){var d=tinymce.init(c),f=tinymce.get(e);if(!f)return!1;f.acf=i.field,acf.doAction("wysiwyg_tinymce_init",f,f.id,c,o)}},initializeQuicktags:function(e,i){var n=this.defaults();if("undefined"==typeof quicktags)return!1;if(!n)return!1;var a=t.extend({},n.quicktags,i.quicktags);a.id=e;var r=i.field||!1,o=r.$el||!1;a=acf.applyFilters("wysiwyg_quicktags_settings",a,a.id,r),tinyMCEPreInit.qtInit[e]=a;var s=quicktags(a);if(!s)return!1;this.buildQuicktags(s),acf.doAction("wysiwyg_quicktags_init",s,s.id,a,r)},buildQuicktags:function(t){var e,i,n,a,r,t,o,s,c,l,u=",strong,em,link,block,del,ins,img,ul,ol,li,code,more,close,";for(s in e=t.canvas,i=t.name,n=t.settings,r="",a={},c="",l=t.id,n.buttons&&(c=","+n.buttons+","),edButtons)edButtons[s]&&(o=edButtons[s].id,c&&-1!==u.indexOf(","+o+",")&&-1===c.indexOf(","+o+",")||edButtons[s].instance&&edButtons[s].instance!==l||(a[o]=edButtons[s],edButtons[s].html&&(r+=edButtons[s].html(i+"_"))));c&&-1!==c.indexOf(",dfw,")&&(a.dfw=new QTags.DFWButton,r+=a.dfw.html(i+"_")),"rtl"===document.getElementsByTagName("html")[0].dir&&(a.textdirection=new QTags.TextDirectionButton,r+=a.textdirection.html(i+"_")),t.toolbar.innerHTML=r,t.theButtons=a,"undefined"!=typeof jQuery&&jQuery(document).triggerHandler("quicktags-init",[t])},disable:function(t){this.destroyTinymce(t)},remove:function(t){this.destroyTinymce(t)},destroy:function(t){this.destroyTinymce(t)},destroyTinymce:function(t){if("undefined"==typeof tinymce)return!1;var e=tinymce.get(t);return!!e&&(e.save(),e.destroy(),!0)},enable:function(t){this.enableTinymce(t)},enableTinymce:function(t){return"undefined"!=typeof switchEditors&&(void 0!==tinyMCEPreInit.mceInit[t]&&(switchEditors.go(t,"tmce"),!0))}};var i=new acf.Model({priority:5,actions:{prepare:"onPrepare",ready:"onReady"},onPrepare:function(){var e=t("#acf-hidden-wp-editor");e.exists()&&e.appendTo("body")},onReady:function(){acf.isset(window,"wp","oldEditor")&&(wp.editor.autop=wp.oldEditor.autop,wp.editor.removep=wp.oldEditor.removep),acf.isset(window,"tinymce","on")&&tinymce.on("AddEditor",function(t){var e=t.editor;"acf"===e.id.substr(0,3)&&(e=tinymce.editors.content||e,tinymce.activeEditor=e,wpActiveEditor=e.id)})}})}(jQuery),function(t,e){var i=acf.Model.extend({id:"Validator",data:{errors:[],notice:null,status:""},events:{"changed:status":"onChangeStatus"},addErrors:function(t){t.map(this.addError,this)},addError:function(t){this.data.errors.push(t)},hasErrors:function(){return this.data.errors.length},clearErrors:function(){return this.data.errors=[]},getErrors:function(){return this.data.errors},getFieldErrors:function(){var t=[],e=[];return this.getErrors().map(function(i){if(i.input){var n=e.indexOf(i.input);n>-1?t[n]=i:(t.push(i),e.push(i.input))}}),t},getGlobalErrors:function(){return this.getErrors().filter(function(t){return!t.input})},showErrors:function(){if(this.hasErrors()){var e=this.getFieldErrors(),i=this.getGlobalErrors(),n=0,a=!1;e.map(function(t){var e=this.$('[name="'+t.input+'"]').first();if(e.length||(e=this.$('[name^="'+t.input+'"]').first()),e.length){n++;var i=acf.getClosestField(e);i.showError(t.message),a||(a=i.$el)}},this);var r=acf.__("Validation failed");if(i.map(function(t){r+=". "+t.message}),1==n?r+=". "+acf.__("1 field requires attention"):n>1&&(r+=". "+acf.__("%d fields require attention").replace("%d",n)),this.has("notice"))this.get("notice").update({type:"error",text:r});else{var o=acf.newNotice({type:"error",text:r,target:this.$el});this.set("notice",o)}a||(a=this.get("notice").$el),setTimeout(function(){t("html, body").animate({scrollTop:a.offset().top-t(window).height()/2},500)},10)}},onChangeStatus:function(t,e,i,n){this.$el.removeClass("is-"+n).addClass("is-"+i)},validate:function(e){if(e=acf.parseArgs(e,{event:!1,reset:!1,loading:function(){},complete:function(){},failure:function(){},success:function(t){t.submit()}}),"valid"==this.get("status"))return!0;if("validating"==this.get("status"))return!1;if(!this.$(".acf-field").length)return!0;if(e.event){var i=t.Event(null,e.event);e.success=function(){acf.enableSubmit(t(i.target)).trigger(i)}}acf.doAction("validation_begin",this.$el),acf.lockForm(this.$el),e.loading(this.$el,this),this.set("status","validating");var n=function(t){if(acf.isAjaxSuccess(t)){var e=acf.applyFilters("validation_complete",t.data,this.$el,this);e.valid||this.addErrors(e.errors)}},a=function(){acf.unlockForm(this.$el),this.hasErrors()?(this.set("status","invalid"),acf.doAction("validation_failure",this.$el,this),this.showErrors(),e.failure(this.$el,this)):(this.set("status","valid"),this.has("notice")&&this.get("notice").update({type:"success",text:acf.__("Validation successful"),timeout:1e3}),acf.doAction("validation_success",this.$el,this),acf.doAction("submit",this.$el),e.success(this.$el,this),acf.lockForm(this.$el),e.reset&&this.reset()),e.complete(this.$el,this),this.clearErrors()},r=acf.serialize(this.$el);return r.action="acf/validate_save_post",t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(r),type:"post",dataType:"json",context:this,success:n,complete:a}),!1},setup:function(t){this.$el=t},reset:function(){this.set("errors",[]),this.set("notice",null),this.set("status",""),acf.unlockForm(this.$el)}}),n=function(t){var e=t.data("acf");return e||(e=new i(t)),e};acf.validateForm=function(t){return n(t.form).validate(t)},acf.enableSubmit=function(t){return t.removeClass("disabled")},acf.disableSubmit=function(t){return t.addClass("disabled")},acf.showSpinner=function(t){return t.addClass("is-active"),t.css("display","inline-block"),t},acf.hideSpinner=function(t){return t.removeClass("is-active"),t.css("display","none"),t},acf.lockForm=function(t){var e=a(t),i=e.find('.button, [type="submit"]'),n=e.find(".spinner, .acf-spinner");return acf.hideSpinner(n),acf.disableSubmit(i),acf.showSpinner(n.last()),t},acf.unlockForm=function(t){var e=a(t),i=e.find('.button, [type="submit"]'),n=e.find(".spinner, .acf-spinner");return acf.enableSubmit(i),acf.hideSpinner(n),t};var a=function(t){var e,e,e,e;return(e=t.find("#submitdiv")).length?e:(e=t.find("#submitpost")).length?e:(e=t.find("p.submit").last()).length?e:(e=t.find(".acf-form-submit")).length?e:t};acf.validation=new acf.Model({id:"validation",active:!0,wait:"prepare",actions:{ready:"addInputEvents",append:"addInputEvents"},events:{'click input[type="submit"]':"onClickSubmit",'click button[type="submit"]':"onClickSubmit","click #save-post":"onClickSave","submit form#post":"onSubmitPost","submit form":"onSubmit"},initialize:function(){acf.get("validation")||(this.active=!1,this.actions={},this.events={})},enable:function(){this.active=!0},disable:function(){this.active=!1},reset:function(t){n(t).reset()},addInputEvents:function(e){var i=t(".acf-field [name]",e);i.length&&this.on(i,"invalid","onInvalid")},onInvalid:function(t,e){t.preventDefault();var i=e.closest("form");i.length&&(n(i).addError({input:e.attr("name"),message:t.target.validationMessage}),i.submit())},onClickSubmit:function(t,e){this.set("originalEvent",t)},onClickSave:function(t,e){this.set("ignore",!0)},onClickSubmitGutenberg:function(e,i){var n;acf.validateForm({form:t("#editor"),event:e,reset:!0,failure:function(t,e){var i=e.get("notice").$el;i.appendTo(".components-notice-list"),i.find(".acf-notice-dismiss").removeClass("small")}})||(e.preventDefault(),e.stopImmediatePropagation())},onSubmitPost:function(e,i){"dopreview"===t("input#wp-preview").val()&&(this.set("ignore",!0),acf.unlockForm(i))},onSubmit:function(t,e){if(!this.active||this.get("ignore")||t.isDefaultPrevented())return this.allowSubmit();var i;acf.validateForm({form:e,event:this.get("originalEvent")})||t.preventDefault()},allowSubmit:function(){return this.set("ignore",!1),this.set("originalEvent",!1),!0}})}(jQuery),function(t,e){var i=new acf.Model({priority:90,timeout:0,actions:{new_field:"refresh",show_field:"refresh",hide_field:"refresh",remove_field:"refresh"},refresh:function(){clearTimeout(this.timeout),this.timeout=setTimeout(function(){acf.doAction("refresh")},0)}}),n=new acf.Model({priority:1,actions:{sortstart:"onSortstart",sortstop:"onSortstop"},onSortstart:function(t){acf.doAction("unmount",t)},onSortstop:function(t){acf.doAction("remount",t)}}),a=new acf.Model({actions:{sortstart:"onSortstart"},onSortstart:function(e,i){e.is("tr")&&(i.html('<td style="padding:0;" colspan="'+i.children().length+'"></td>'),e.addClass("acf-sortable-tr-helper"),e.children().each(function(){t(this).width(t(this).width())}),i.height(e.height()+"px"),e.removeClass("acf-sortable-tr-helper"))}}),r=new acf.Model({actions:{after_duplicate:"onAfterDuplicate"},onAfterDuplicate:function(e,i){var n=[];e.find("select").each(function(e){n.push(t(this).val())}),i.find("select").each(function(e){t(this).val(n[e])})}}),o=new acf.Model({id:"tableHelper",priority:20,actions:{refresh:"renderTables"},renderTables:function(e){var i=this;t(".acf-table:visible").each(function(){i.renderTable(t(this))})},renderTable:function(e){var i=e.find("> thead > tr:visible > th[data-key]"),n=e.find("> tbody > tr:visible > td[data-key]");if(!i.length||!n.length)return!1;i.each(function(e){var i=t(this),a=i.data("key"),r=n.filter('[data-key="'+a+'"]'),o=r.filter(".acf-hidden");r.removeClass("acf-empty"),r.length===o.length?acf.hide(i):(acf.show(i),o.addClass("acf-empty"))}),i.css("width","auto"),i=i.not(".acf-hidden");var a=100,r=i.length,o;i.filter("[data-width]").each(function(){var e=t(this).data("width");t(this).css("width",e+"%"),a-=e});var s=i.not("[data-width]");if(s.length){var c=a/s.length;s.css("width",c+"%"),a=0}a>0&&i.last().css("width","auto"),n.filter(".-collapsed-target").each(function(){var e=t(this);e.parent().hasClass("-collapsed")?e.attr("colspan",i.length):e.removeAttr("colspan")})}}),s=new acf.Model({id:"fieldsHelper",priority:30,actions:{refresh:"renderGroups"},renderGroups:function(){var e=this;t(".acf-fields:visible").each(function(){e.renderGroup(t(this))})},renderGroup:function(e){var i=0,n=0,a=t(),r=e.children(".acf-field[data-width]:visible");return!!r.length&&(e.hasClass("-left")?(r.removeAttr("data-width"),r.css("width","auto"),!1):(r.removeClass("-r0 -c0").css({"min-height":0}),r.each(function(e){var r=t(this),o=r.position(),s=Math.ceil(o.top),c=Math.ceil(o.left);a.length&&s>i&&(a.css({"min-height":n+"px"}),o=r.position(),s=Math.ceil(o.top),c=Math.ceil(o.left),i=0,n=0,a=t()),acf.get("rtl")&&(c=Math.ceil(r.parent().width()-(o.left+r.outerWidth()))),0==s?r.addClass("-r0"):0==c&&r.addClass("-c0");var l=Math.ceil(r.outerHeight())+1;n=Math.max(n,l),i=Math.max(i,s),a=a.add(r)}),void(a.length&&a.css({"min-height":n+"px"}))))}})}(jQuery),function(t,e){acf.newCompatibility=function(t,e){return(e=e||{}).__proto__=t.__proto__,t.__proto__=e,t.compatibility=e,e},acf.getCompatibility=function(t){return t.compatibility||null};var i=acf.newCompatibility(acf,{l10n:{},o:{},fields:{},update:acf.set,add_action:acf.addAction,remove_action:acf.removeAction,do_action:acf.doAction,add_filter:acf.addFilter,remove_filter:acf.removeFilter,apply_filters:acf.applyFilters,parse_args:acf.parseArgs,disable_el:acf.disable,disable_form:acf.disable,enable_el:acf.enable,enable_form:acf.enable,update_user_setting:acf.updateUserSetting,prepare_for_ajax:acf.prepareForAjax,is_ajax_success:acf.isAjaxSuccess,remove_el:acf.remove,remove_tr:acf.remove,str_replace:acf.strReplace,render_select:acf.renderSelect,get_uniqid:acf.uniqid,serialize_form:acf.serialize,esc_html:acf.strEscape,str_sanitize:acf.strSanitize});i._e=function(t,e){t=t||"";var i=(e=e||"")?t+"."+e:t,n={"image.select":"Select Image","image.edit":"Edit Image","image.update":"Update Image"};if(n[i])return acf.__(n[i]);var a=this.l10n[t]||"";return e&&(a=a[e]||""),a},i.get_selector=function(e){var i=".acf-field";if(!e)return i;if(t.isPlainObject(e)){if(t.isEmptyObject(e))return i;for(var n in e){e=e[n];break}}return i+="-"+e,i=acf.strReplace("_","-",i),i=acf.strReplace("field-field-","field-",i)},i.get_fields=function(t,e,i){var n={is:t||"",parent:e||!1,suppressFilters:i||!1};return n.is&&(n.is=this.get_selector(n.is)),acf.findFields(n)},i.get_field=function(t,e){var i=this.get_fields.apply(this,arguments);return!!i.length&&i.first()},i.get_closest_field=function(t,e){return t.closest(this.get_selector(e))},i.get_field_wrap=function(t){return t.closest(this.get_selector())},i.get_field_key=function(t){return t.data("key")},i.get_field_type=function(t){return t.data("type")},i.get_data=function(t,e){return acf.parseArgs(t.data(),e)},i.maybe_get=function(t,e,i){void 0===i&&(i=null),keys=String(e).split(".");for(var n=0;n<keys.length;n++){if(!t.hasOwnProperty(keys[n]))return i;t=t[keys[n]]}return t};var n=function(t){return t instanceof acf.Field?t.$el:t},a=function(t){return acf.arrayArgs(t).map(n)},r=function(e){return function(){if(arguments.length)var i=a(arguments);else var i=[t(document)];return e.apply(this,i)}};i.add_action=function(t,e,n,a){var o=t.split(" "),s=o.length;if(s>1){for(var c=0;c<s;c++)t=o[c],i.add_action.apply(this,arguments);return this}var e=r(e);return acf.addAction.apply(this,arguments)},i.add_filter=function(t,e,i,n){var e=r(e);return acf.addFilter.apply(this,arguments)},i.model={actions:{},filters:{},events:{},extend:function(e){var i=t.extend({},this,e);return t.each(i.actions,function(t,e){i._add_action(t,e)}),t.each(i.filters,function(t,e){i._add_filter(t,e)}),t.each(i.events,function(t,e){i._add_event(t,e)}),i},_add_action:function(t,e){var i=this,n=t.split(" "),t=n[0]||"",a=n[1]||10;acf.add_action(t,this[e],a,this)},_add_filter:function(t,e){var i=this,n=t.split(" "),t=n[0]||"",a=n[1]||10;acf.add_filter(t,this[e],a,this)},_add_event:function(e,i){var n=this,a=e.indexOf(" "),r=a>0?e.substr(0,a):e,o=a>0?e.substr(a+1):"",s=function(e){e.$el=t(this),acf.field_group&&(e.$field=e.$el.closest(".acf-field-object")),"function"==typeof n.event&&(e=n.event(e)),n[i].apply(n,arguments)};o?t(document).on(r,o,s):t(document).on(r,s)},get:function(t,e){return e=e||null,void 0!==this[t]&&(e=this[t]),e},set:function(t,e){return this[t]=e,"function"==typeof this["_set_"+t]&&this["_set_"+t].apply(this),this}},i.field=acf.model.extend({type:"",o:{},$field:null,_add_action:function(t,e){var i=this;t=t+"_field/type="+i.type,acf.add_action(t,function(t){i.set("$field",t),i[e].apply(i,arguments)})},_add_filter:function(t,e){var i=this;t=t+"_field/type="+i.type,acf.add_filter(t,function(t){i.set("$field",t),i[e].apply(i,arguments)})},_add_event:function(e,i){var n=this,a=e.substr(0,e.indexOf(" ")),r=e.substr(e.indexOf(" ")+1),o=acf.get_selector(n.type);t(document).on(a,o+" "+r,function(e){var a=t(this),r=acf.get_closest_field(a,n.type);r.length&&(r.is(n.$field)||n.set("$field",r),e.$el=a,e.$field=r,n[i].apply(n,[e]))})},_set_$field:function(){"function"==typeof this.focus&&this.focus()},doFocus:function(t){return this.set("$field",t)}});var o=acf.newCompatibility(acf.validation,{remove_error:function(t){acf.getField(t).removeError()},add_warning:function(t,e){acf.getField(t).showNotice({text:e,type:"warning",timeout:1e3})},fetch:acf.validateForm,enableSubmit:acf.enableSubmit,disableSubmit:acf.disableSubmit,showSpinner:acf.showSpinner,hideSpinner:acf.hideSpinner,unlockForm:acf.unlockForm,lockForm:acf.lockForm});i.tooltip={tooltip:function(t,e){var i;return acf.newTooltip({text:t,target:e}).$el},temp:function(t,e){var i=acf.newTooltip({text:t,target:e,timeout:250})},confirm:function(t,e,i,n,a){var r=acf.newTooltip({confirm:!0,text:i,target:t,confirm:function(){e(!0)},cancel:function(){e(!1)}})},confirm_remove:function(t,e){var i=acf.newTooltip({confirmRemove:!0,target:t,confirm:function(){e(!0)},cancel:function(){e(!1)}})}},i.media=new acf.Model({activeFrame:!1,actions:{new_media_popup:"onNewMediaPopup"},frame:function(){return this.activeFrame},onNewMediaPopup:function(t){this.activeFrame=t.frame},popup:function(t){var e;return t.mime_types&&(t.allowedTypes=t.mime_types),t.id&&(t.attachment=t.id),acf.newMediaPopup(t).frame}}),i.select2={init:function(t,e,i){return e.allow_null&&(e.allowNull=e.allow_null),e.ajax_action&&(e.ajaxAction=e.ajax_action),i&&(e.field=acf.getField(i)),acf.newSelect2(t,e)},destroy:function(t){return acf.getInstance(t).destroy()}},i.postbox={render:function(t){return t.edit_url&&(t.editLink=t.edit_url),t.edit_title&&(t.editTitle=t.edit_title),acf.newPostbox(t)}},acf.newCompatibility(acf.screen,{update:function(){return this.set.apply(this,arguments)},fetch:acf.screen.check}),i.ajax=acf.screen}(jQuery);