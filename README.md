# 垂直轮播组件集成说明

## 概述
这是一个基于Swiper.js的垂直轮播组件，专门为Elementor设计，实现了中间激活页比上下两页大的视觉效果。

## 文件说明

### 1. vertical-swiper.css
- 包含所有轮播样式
- 实现中间激活页放大效果
- 响应式设计支持
- 平滑过渡动画

### 2. vertical-swiper.js
- 轮播功能的JavaScript实现
- 自动检测Elementor环境
- 动态加载Swiper资源
- 支持触摸、鼠标滚轮、键盘控制

### 3. vertical-swiper-demo.html
- 完整的演示页面
- 展示如何集成到现有HTML结构

## 集成步骤

### 在WordPress/Elementor中集成

#### 方法1：通过主题文件集成

1. **上传文件**
   ```
   /wp-content/themes/your-theme/assets/css/vertical-swiper.css
   /wp-content/themes/your-theme/assets/js/vertical-swiper.js
   ```

2. **在functions.php中添加**
   ```php
   function enqueue_vertical_swiper_assets() {
       wp_enqueue_style('vertical-swiper', get_template_directory_uri() . '/assets/css/vertical-swiper.css');
       wp_enqueue_script('vertical-swiper', get_template_directory_uri() . '/assets/js/vertical-swiper.js', array(), '1.0.0', true);
   }
   add_action('wp_enqueue_scripts', 'enqueue_vertical_swiper_assets');
   ```

#### 方法2：通过Elementor自定义代码

1. **在Elementor页面设置中添加CSS**
   - 进入页面编辑器
   - 点击左下角设置图标
   - 选择"高级"选项卡
   - 在"自定义CSS"中粘贴 `vertical-swiper.css` 的内容

2. **添加JavaScript**
   - 在"自定义CSS"下方找到"自定义JavaScript"
   - 粘贴 `vertical-swiper.js` 的内容

#### 方法3：通过插件集成

1. **使用Code Snippets插件**
   - 安装Code Snippets插件
   - 创建新的代码片段
   - 选择"Frontend"类型
   - 添加CSS和JS代码

## 功能特性

### 核心功能
- ✅ 垂直方向轮播
- ✅ 同时显示3页内容
- ✅ 中间激活页放大效果
- ✅ 自动播放（4秒间隔）
- ✅ 无限循环
- ✅ 平滑过渡动画

### 交互控制
- ✅ 鼠标滚轮控制
- ✅ 键盘方向键控制
- ✅ 触摸滑动支持
- ✅ 导航按钮
- ✅ 分页指示器
- ✅ 鼠标悬停暂停

### 响应式支持
- ✅ 桌面端：显示3页
- ✅ 平板端：显示3页，间距调整
- ✅ 手机端：显示2.5页，缩放比例调整

## 自定义配置

### 修改轮播参数
在 `vertical-swiper.js` 中找到Swiper初始化部分，可以修改以下参数：

```javascript
const swiper = new Swiper('.swiper-container', {
    direction: 'vertical',        // 方向：vertical/horizontal
    slidesPerView: 3,            // 同时显示的幻灯片数量
    centeredSlides: true,        // 居中显示
    spaceBetween: 30,           // 幻灯片间距
    loop: true,                 // 无限循环
    autoplay: {
        delay: 4000,            // 自动播放间隔（毫秒）
        disableOnInteraction: false,
    },
    speed: 600,                 // 切换速度（毫秒）
});
```

### 修改视觉效果
在 `vertical-swiper.css` 中可以调整：

```css
.swiper-slide {
    opacity: 0.6;              /* 非激活页透明度 */
    transform: scale(0.8);     /* 非激活页缩放比例 */
}

.swiper-slide-active {
    opacity: 1;                /* 激活页透明度 */
    transform: scale(1);       /* 激活页缩放比例 */
}

.swiper-slide-active img {
    transform: scale(1.1);     /* 激活页图片额外放大 */
}
```

## 故障排除

### 常见问题

1. **轮播不显示**
   - 检查CSS和JS文件是否正确加载
   - 确认HTML结构中包含 `id="niankan-container"`
   - 查看浏览器控制台是否有错误信息

2. **样式不正确**
   - 确认CSS文件在JS文件之前加载
   - 检查是否有其他CSS样式冲突
   - 尝试增加CSS选择器权重

3. **触摸控制无效**
   - 确认设备支持触摸事件
   - 检查是否有其他JavaScript阻止了事件传播

4. **自动播放不工作**
   - 检查浏览器的自动播放策略
   - 确认没有其他代码停止了自动播放

### 调试模式
在浏览器控制台中可以看到以下调试信息：
- "垂直轮播初始化完成" - 表示轮播成功初始化
- "Swiper资源加载完成" - 表示Swiper库加载成功

## 浏览器兼容性
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+
- ✅ iOS Safari 12+
- ✅ Android Chrome 60+

## 性能优化建议
1. 图片使用WebP格式以减少加载时间
2. 启用图片懒加载
3. 压缩CSS和JS文件
4. 使用CDN加速Swiper资源加载

## 技术支持
如果遇到问题，请检查：
1. 浏览器控制台错误信息
2. 网络请求是否成功
3. HTML结构是否完整
4. CSS和JS文件路径是否正确
