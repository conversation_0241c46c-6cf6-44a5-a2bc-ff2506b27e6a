<?php
function elementor_form_validation($record, $ajax_handler)
{

    if ($record->get_form_settings('form_post_id') != '3340') {
        return;
    }

    $code_tmp = $record->get_field([
        'id' => 'field_c3819fb',
    ]);
    $phone_tmp = $record->get_field([
        'id' => 'field_df12547',
    ]);
    $code  = $code_tmp['field_c3819fb']['value'];
	$phone = $phone_tmp['field_df12547']['value'];

    if (empty($code)) {
        $ajax_handler->add_error('field_c3819fb', esc_html__('验证码不能为空', 'textdomain'));
    }

    if (empty($phone)) {
        $ajax_handler->add_error('field_df12547', esc_html__('手机号不能为空', 'textdomain'));
    }
    //验证短信验证码
    global $wpdb;
    $qResult = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}ossdu_sms_record WHERE mobile='" . $phone . "' AND code='" . $code . "' ORDER BY id DESC LIMIT 1", ARRAY_A);
    if (empty($qResult)) {
        $ajax_handler->add_error('field_c3819fb', esc_html__('验证码错误', 'textdomain'));
        return;
    }
    $t = time() - $qResult[0]['add_time'];
    if ($t > 300) {
        //5分钟
        $ajax_handler->add_error('field_c3819fb', esc_html__('验证码已过期', 'textdomain'));
        return;
    }
}
//add_action('elementor_pro/forms/validation', 'elementor_form_validation', 10, 2);

function replace_ele_js1()
{
    $ele_js = ['ace', 'ace-language-tools'];
    foreach ($ele_js as $js) {
        wp_deregister_script($js);
    }
    $base_url = 'https://cdn.staticfile.org/ace/1.2.5';

    wp_register_script('ace', $base_url . '/ace.js', [], '1.2.5', true);

    wp_register_script('ace-language-tools', $base_url . '/ext-language_tools.js', ['ace'], '1.2.5', true);
    wp_register_script('clipboard', 'https://cdn.staticfile.org/clipboard.js/2.0.0/clipboard.min.js', [], '2.0.0', true);
}

function replace_ele_js2()
{
    $ele_js = ['prismjs_core', 'prismjs_loader', 'prismjs_normalize', 'prismjs_line_numbers', 'prismjs_line_highlight', 'prismjs_toolbar', 'prismjs_copy_to_clipboard'];
    foreach ($ele_js as $js) {
        wp_deregister_script($js);
    }
    $base_url = 'https://cdn.staticfile.org/prism/1.23.0';
    wp_register_script('prismjs_core', $base_url . '/components/prism-core.min.js', [], '1.23.0', true);
    wp_register_script('prismjs_loader', $base_url . '/plugins/autoloader/prism-autoloader.min.js', ['prismjs_core'], '1.23.0', true);
    wp_register_script('prismjs_normalize', $base_url . '/plugins/normalize-whitespace/prism-normalize-whitespace.min.js', ['prismjs_core'], '1.23.0', true);
    wp_register_script('prismjs_line_numbers', $base_url . '/plugins/line-numbers/prism-line-numbers.min.js', ['prismjs_normalize'], '1.23.0', true);
    wp_register_script('prismjs_line_highlight', $base_url . '/plugins/line-highlight/prism-line-highlight.min.js', ['prismjs_normalize'], '1.23.0', true);
    wp_register_script('prismjs_toolbar', $base_url . '/plugins/toolbar/prism-toolbar.min.js', ['prismjs_normalize'], '1.23.0', true);
    wp_register_script('prismjs_copy_to_clipboard', $base_url . '/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js', ['prismjs_toolbar'], '1.23.0', true);
}

if (has_action('elementor/editor/before_enqueue_scripts')) {
    add_action('elementor/editor/before_enqueue_scripts', 'replace_ele_js1');
    add_action('elementor/frontend/after_register_scripts', 'replace_ele_js2');
}
