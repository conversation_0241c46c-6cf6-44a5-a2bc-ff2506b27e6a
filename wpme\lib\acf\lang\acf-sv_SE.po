msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2017-06-27 15:28+1000\n"
"PO-Revision-Date: 2019-05-15 09:55+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Swedish\n"
"Language: sv_SE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Poedit 1.8.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:63
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:355 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Fältgrupper"

#: acf.php:356
msgid "Field Group"
msgstr "Fältgrupp"

#: acf.php:357 acf.php:389 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:574
msgid "Add New"
msgstr "Lägg till ny"

#: acf.php:358
msgid "Add New Field Group"
msgstr "Lägg till ny fältgrupp"

#: acf.php:359
msgid "Edit Field Group"
msgstr "Redigera fältgrupp"

#: acf.php:360
msgid "New Field Group"
msgstr "Skapa fältgrupp"

#: acf.php:361
msgid "View Field Group"
msgstr "Visa fältgrupp"

#: acf.php:362
msgid "Search Field Groups"
msgstr "Sök fältgrupp"

#: acf.php:363
msgid "No Field Groups found"
msgstr "Inga fältgrupper hittades"

#: acf.php:364
msgid "No Field Groups found in Trash"
msgstr "Inga fältgrupper hittades i papperskorgen"

#: acf.php:387 includes/admin/admin-field-group.php:182
#: includes/admin/admin-field-group.php:275
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:857
msgid "Fields"
msgstr "Fält"

#: acf.php:388
msgid "Field"
msgstr "Fält"

#: acf.php:390
msgid "Add New Field"
msgstr "Skapa nytt fält"

#: acf.php:391
msgid "Edit Field"
msgstr "Redigera fält"

#: acf.php:392 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Nytt fält"

#: acf.php:393
msgid "View Field"
msgstr "Visa fält"

#: acf.php:394
msgid "Search Fields"
msgstr "Sök fält"

#: acf.php:395
msgid "No Fields found"
msgstr "Inga fält hittades"

#: acf.php:396
msgid "No Fields found in Trash"
msgstr "Inga fält hittades i papperskorgen"

#: acf.php:435 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Inaktiv"

#: acf.php:440
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inaktiv <span class=\"count\">(%s)</span>"
msgstr[1] "Inaktiva <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Fältgrupper uppdaterades."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Fältgrupper raderades."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Fältgrupper publicerades."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Fältgrupper sparades."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Fältgruppen skickades."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Fältgruppen schemalades för."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Utkastet till fältgrupp uppdaterades."

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "Plats"

#: includes/admin/admin-field-group.php:184
msgid "Settings"
msgstr "Inställningar"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "Flytta till papperskorgen. Är du säker?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr "vald"

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr "Det finns inga aktiveringsbara fält"

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "Fältgruppen behöver en titel"

#: includes/admin/admin-field-group.php:273
#: includes/api/api-field-group.php:732
msgid "copy"
msgstr "kopiera"

#: includes/admin/admin-field-group.php:274
#: includes/admin/views/field-group-field-conditional-logic.php:54
#: includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3970
msgid "or"
msgstr "eller"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "Föräldrafält"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr "Syskonfält"

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "Flytta egna fält"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr "Detta fält kan inte flyttas förrän ändringarna har sparats"

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr "Nollvärde"

#: includes/admin/admin-field-group.php:281 includes/input.php:257
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"De ändringar som du gjort kommer att förloras om du navigerar bort från "
"denna sida"

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Strängen  \"field_\" får inte användas i början av ett fältnamn"

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr "Fältnycklar"

#: includes/admin/admin-field-group.php:390
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Aktiv"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "Flytt färdig."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Fältet %s kan nu hittas i fältgruppen %s"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "Stäng fönster"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "Välj målet (destinationen) för detta fält"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "Flytta fält"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktiv <span class=\"count\">(%s)</span>"
msgstr[1] "Aktiva <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Fältgruppen kopierad. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s fältgrupp kopierad."
msgstr[1] "%s fältgrupper kopierade."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Fältgrupp synkroniserad. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s fältgrupp synkroniserad."
msgstr[1] "%s fältgrupper synkroniserade."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Synkronisering tillgänglig"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:370
msgid "Title"
msgstr "Titel"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:397
msgid "Description"
msgstr "Beskrivning"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Status"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Skräddarsy Wordpress med kraftfulla, professionella och intuitiva fält."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:111
msgid "Changelog"
msgstr "Versionshistorik"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Se vad som är nytt i <a href=\"\"%s\">version %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Resurser"

#: includes/admin/admin-field-groups.php:619
#, fuzzy
msgid "Website"
msgstr "Denna webbplats använder premiumtillägg som behöver laddas ner"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Dokumentation"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Support"

#: includes/admin/admin-field-groups.php:623
#, fuzzy
msgid "Pro"
msgstr "Adjö tillägg. Hej PRO"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Tack för att du skapar med <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:668
msgid "Duplicate this item"
msgstr "Duplicera detta objekt"

#: includes/admin/admin-field-groups.php:668
#: includes/admin/admin-field-groups.php:684
#: includes/admin/views/field-group-field.php:49
#: pro/fields/class-acf-field-flexible-content.php:573
msgid "Duplicate"
msgstr "Duplicera"

#: includes/admin/admin-field-groups.php:701
#: includes/fields/class-acf-field-google-map.php:132
#: includes/fields/class-acf-field-relationship.php:737
msgid "Search"
msgstr "Sök"

#: includes/admin/admin-field-groups.php:760
#, php-format
msgid "Select %s"
msgstr "Välj %s"

#: includes/admin/admin-field-groups.php:768
msgid "Synchronise field group"
msgstr "Synkronisera fältgrupp"

#: includes/admin/admin-field-groups.php:768
#: includes/admin/admin-field-groups.php:798
msgid "Sync"
msgstr "Synkronisera"

#: includes/admin/admin-field-groups.php:780
msgid "Apply"
msgstr ""

#: includes/admin/admin-field-groups.php:798
#, fuzzy
msgid "Bulk Actions"
msgstr "Välj åtgärd"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Egna fält"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Uppgradera databas"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "Kontrollera webbplatser & uppgradera"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "Fel vid validering av begäran"

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "Inga uppdateringar tillgängliga."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Tillägg"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Fel</b>. Kunde inte ladda tilläggslistan"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Information"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Vad är nytt"

#: includes/admin/settings-tools.php:50
#: includes/admin/views/settings-tools-export.php:19
#: includes/admin/views/settings-tools.php:31
msgid "Tools"
msgstr "Verktyg"

#: includes/admin/settings-tools.php:147 includes/admin/settings-tools.php:380
msgid "No field groups selected"
msgstr "Inga fältgrupper valda"

#: includes/admin/settings-tools.php:184
#: includes/fields/class-acf-field-file.php:174
msgid "No file selected"
msgstr "Ingen fil vald"

#: includes/admin/settings-tools.php:197
msgid "Error uploading file. Please try again"
msgstr "Fel vid uppladdning av fil. Vänligen försök igen"

#: includes/admin/settings-tools.php:206
msgid "Incorrect file type"
msgstr "Felaktig filtyp"

#: includes/admin/settings-tools.php:223
msgid "Import file empty"
msgstr "Importfilen är tom"

#: includes/admin/settings-tools.php:331
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Importerade 1 fältgrupp"
msgstr[1] "Importerade %s fältgrupp"

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "Visningsvillkor"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "Visa detta fält när"

#: includes/admin/views/field-group-field-conditional-logic.php:103
#: includes/locations.php:243
msgid "is equal to"
msgstr "är"

#: includes/admin/views/field-group-field-conditional-logic.php:104
#: includes/locations.php:244
msgid "is not equal to"
msgstr "inte är"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "och"

#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Lägg till regelgrupp"

#: includes/admin/views/field-group-field.php:41
#: pro/fields/class-acf-field-flexible-content.php:420
#: pro/fields/class-acf-field-repeater.php:358
msgid "Drag to reorder"
msgstr "Dra och släpp för att ändra ordning"

#: includes/admin/views/field-group-field.php:45
#: includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "Redigera fält"

#: includes/admin/views/field-group-field.php:48
#: includes/fields/class-acf-field-image.php:140
#: includes/fields/class-acf-field-link.php:152
#: pro/fields/class-acf-field-gallery.php:357
msgid "Edit"
msgstr "Redigera"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "Duplicera fält"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "Flytta fält till en annan grupp"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "Flytta"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "Radera fält"

#: includes/admin/views/field-group-field.php:51
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Delete"
msgstr "Radera"

#: includes/admin/views/field-group-field.php:67
msgid "Field Label"
msgstr "Fälttitel"

#: includes/admin/views/field-group-field.php:68
msgid "This is the name which will appear on the EDIT page"
msgstr "Detta namn kommer att visas vid redigering"

#: includes/admin/views/field-group-field.php:78
msgid "Field Name"
msgstr "Fältnamn"

#: includes/admin/views/field-group-field.php:79
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Ett enda ord, utan mellanslag. Understreck och bindestreck är tillåtna"

#: includes/admin/views/field-group-field.php:89
msgid "Field Type"
msgstr "Fälttyp"

#: includes/admin/views/field-group-field.php:101
#: includes/fields/class-acf-field-tab.php:102
msgid "Instructions"
msgstr "Instruktioner"

#: includes/admin/views/field-group-field.php:102
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instruktioner för den som redigerar"

#: includes/admin/views/field-group-field.php:111
msgid "Required?"
msgstr "Obligatorisk?"

#: includes/admin/views/field-group-field.php:134
msgid "Wrapper Attributes"
msgstr "Attribut för det omslutande elementet (wrappern)"

#: includes/admin/views/field-group-field.php:140
msgid "width"
msgstr "bredd"

#: includes/admin/views/field-group-field.php:155
msgid "class"
msgstr "class"

#: includes/admin/views/field-group-field.php:168
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:180
msgid "Close Field"
msgstr "Stäng fält"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Ordning"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-checkbox.php:317
#: includes/fields/class-acf-field-radio.php:321
#: includes/fields/class-acf-field-select.php:530
#: pro/fields/class-acf-field-flexible-content.php:599
msgid "Label"
msgstr "Titel"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:970
#: pro/fields/class-acf-field-flexible-content.php:612
msgid "Name"
msgstr "Namn"

#: includes/admin/views/field-group-fields.php:7
#, fuzzy
msgid "Key"
msgstr "Visa fältnyckel:"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Typ"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Inga fält. Klicka på knappen <strong>+ Lägg till fält</strong> för att skapa "
"ditt första fält."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Lägg till fält"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Regler"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr "Reglera var denna fältgrupp ska visas"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Stil"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standard (WP metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Transparent (ingen metabox)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Position"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Hög (efter titel)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normal (efter innehåll)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Sidopanel"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Titel placering"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:116
msgid "Top aligned"
msgstr "Toppjusterad"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:117
msgid "Left aligned"
msgstr "Vänsterjusterad"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Placering av instruktion"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Under titlar"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Under fält"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Ordningsnummer"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Fältgrupper med lägre ordningsnummer kommer synas först"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Visas i fältgruppslistan"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Dölj på skärmen"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Välj</b> objekt för att <b>dölja</b> dem från redigeringsvyn"

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Om flera fältgrupper visas i redigeringsvyn, kommer första gruppens "
"inställningar att användas (den med lägst ordningsnummer)"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Permalänk"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Innehållsredigerare"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Utdrag"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Diskussion"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Kommentarer"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Versioner"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "Permalänk"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Författare"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Sidattribut"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:751
msgid "Featured Image"
msgstr "Utvald bild"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Kategorier"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Taggar"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Skicka trackbacks"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Visa detta fält när"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "Uppgradera sajter"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr "Advanced Custom Fields databasuppgradering"

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Följande sajter behöver en databasuppdatering. Kryssa i de du vill uppdatera "
"och klicka på %s."

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Webbplats"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Webbplatsen kräver en databasuppgradering från %s till %s"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "Webbplatsen är uppdaterad"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Uppgradering av databas slutförd. <a href=\"%s\">Återvänd till nätverkets "
"startpanel</a>"

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Det rekommenderas starkt att du säkerhetskopierar din databas innan du "
"fortsätter. Är du säker på att vill köra uppdateringen nu?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Uppgradering genomförd"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Uppgradera data till version %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:36
msgid "Repeater"
msgstr "Upprepningsfält"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:36
msgid "Flexible Content"
msgstr "Flexibelt innehåll"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:36
msgid "Gallery"
msgstr "Galleri"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:13
msgid "Options Page"
msgstr "Inställningssida"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Uppgradering av databasen krävs"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Tack för du uppdaterade till %s v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Innan du börjar använda de nya fantastiska funktionerna, vänligen uppdatera "
"din databas till den senaste versionen."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Läser in uppgifter för uppgradering..."

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Databasuppgraderingen färdig. <a href=\"%s\">Se vad som är nytt</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Ladda ner & installera"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Installerad"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Välkommen till Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Tack för att du uppdaterar! ACF %s är större och bättre än någonsin "
"tidigare. Vi hoppas att du gillar det."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "En smidigare fältupplevelse"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Förbättrad användarvänlighet"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Vi har inkluderat det populära biblioteket Select2 som har förbättrat både "
"användbarhet och laddningstid för ett antal fälttyper såsom inläggsobjekt, "
"sidlänk, taxonomi och val."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Förbättrad design"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Många fält har genomgått en visuell förbättring för att låta ACF se bättre "
"ut än någonsin! Märkbara förändringar syns på galleriet-, relation- och "
"oEmbed- (nytt) fälten!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Förbättrad data"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Omdesignen av dataarkitekturen har tillåtit underfält att leva självständigt "
"från deras föräldrar. Detta gör att du kan dra och släppa fält in och ut "
"från förälderfälten!"

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Adjö tillägg. Hej PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "Introducerande av ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "Vi ändrar hur premium funktionalitet levereras, på ett spännande sätt!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Samtliga 4 premiumtillägg har kombineras till en ny <a href=\"%s\">Pro "
"version av ACF</a>. Med både personlig- och utvecklarlicens tillgänglig, så "
"är premium funktionalitet billigare och tillgängligare än någonsin!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Kraftfulla funktioner"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO innehåller kraftfulla funktioner som upprepningsfält, flexibelt "
"innehåll, ett vackert gallerifält och möjligheten att skapa extra "
"inställningssidor!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Läs mer om <a href=\"%s\">ACF PRO funktioner</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Enkelt att uppgradera"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"För att göra uppgraderingen enkel, <a href=\"%s\">logga in till ditt konto</"
"a> och få en gratis kopia av ACF PRO!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"Vi skrev även en <a href=\"%s\">uppgraderings guide</a>för svara på "
"eventuella frågor, men om du har en, vänligen kontakta vårt support team via "
"<a href=\"%s\">help desk</a>"

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Under huven"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "Smartare fältinställningar"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "ACF sparar nu sina fältinställningar som individuella postobjekt"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Mer AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr "Fler fält använder AJAX-sök för snabbare laddning"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "Lokal JSON"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "Ny automatisk export till JSON funktion förbättrar snabbheten"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "Bättre versionshantering"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr ""
"Ny auto export till JSON funktion möjliggör versionshantering av "
"fältinställningar"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "Bytte XML till JSON"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "Import / Export använder nu JSON istället för XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Nya formulär"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"Fält kan nu kopplas till kommentarer, widgets och alla användarformulär."

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "Ett nytt fält för inbäddning av innehåll (embed) har lagts till"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Nytt galleri"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "Gallerifältet har genomgått en välbehövlig ansiktslyftning"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Nya inställningar"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"Fältgruppsinställningar har lagts till för placering av titel och "
"instruktioner"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "Bättre front-end formulär"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form() kan nu skapa ett nytt inlägg vid submit"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Bättre validering"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr "Validering av formulär görs nu via PHP + AJAX istället för enbart JS"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Relationsfält"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Ny inställning för relationsfält för 'Filter' (Sök, Inläggstyp, Taxonomi)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Flytta runt fält"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""
"Ny fältgrupp funktionalitet tillåter dig att flytta ett fält mellan grupper "
"& föräldrar"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:36
msgid "Page Link"
msgstr "Sidlänk"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Ny arkivgrupp i page_link fältval"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Bättre inställningssidor"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"Nya funktioner för inställningssidor tillåter skapande av både föräldra- och "
"undersidor"

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Vi tror att du kommer uppskatta förändringarna i %s."

#: includes/admin/views/settings-tools-export.php:23
msgid "Export Field Groups to PHP"
msgstr "Exportera fältgrupper till PHP"

#: includes/admin/views/settings-tools-export.php:27
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Följande kod kan användas för att registrera en lokal version av valda "
"fältgrupp(er). Ett lokal fältgrupp kan ge många fördelar som snabbare "
"laddningstider, versionshantering & dynamiska fält/inställningar. Det är "
"bara att kopiera och klistra in följande kod till ditt temas functions.php "
"fil eller att inkludera det i en extern fil."

#: includes/admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "Välj fältgrupp"

#: includes/admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "Exportera fältgrupper"

#: includes/admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Välj de fältgrupper som du vill exportera och sedan välj din exportmetod. "
"Använd knappen för exportera till en .json fil som du sedan kan importera "
"till en annan ACF installation. Använd generera-knappen för att exportera "
"PHP kod som du kan lägga till i ditt tema."

#: includes/admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "Ladda ner exportfil"

#: includes/admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "Generera exportkod"

#: includes/admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "Importera fältgrupper"

#: includes/admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Välj den Advanced Custom Fields JSON-fil som du vill importera. När du "
"klickar på import knappen så kommer ACF importera fältgrupperna."

#: includes/admin/views/settings-tools.php:77
#: includes/fields/class-acf-field-file.php:46
msgid "Select File"
msgstr "Välj fil"

#: includes/admin/views/settings-tools.php:86
msgid "Import"
msgstr "Importera"

#: includes/api/api-helpers.php:856
msgid "Thumbnail"
msgstr "Tumnagel"

#: includes/api/api-helpers.php:857
msgid "Medium"
msgstr "Mellan"

#: includes/api/api-helpers.php:858
msgid "Large"
msgstr "Stor"

#: includes/api/api-helpers.php:907
msgid "Full Size"
msgstr "Full storlek"

#: includes/api/api-helpers.php:1248 includes/api/api-helpers.php:1837
#: pro/fields/class-acf-field-clone.php:1042
msgid "(no title)"
msgstr "(ingen titel)"

#: includes/api/api-helpers.php:1874
#: includes/fields/class-acf-field-page_link.php:284
#: includes/fields/class-acf-field-post_object.php:283
#: includes/fields/class-acf-field-taxonomy.php:992
msgid "Parent"
msgstr "Förälder"

#: includes/api/api-helpers.php:3891
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Bildens bredd måste vara åtminstone %dpx."

#: includes/api/api-helpers.php:3896
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Bildens bredd får inte överskrida %dpx."

#: includes/api/api-helpers.php:3912
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Bildens höjd måste vara åtminstone %dpx."

#: includes/api/api-helpers.php:3917
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Bildens höjd får inte överskrida %dpx."

#: includes/api/api-helpers.php:3935
#, php-format
msgid "File size must be at least %s."
msgstr "Filstorlek måste vara åtminstone %s."

#: includes/api/api-helpers.php:3940
#, php-format
msgid "File size must must not exceed %s."
msgstr "Filstorlek får inte överskrida %s."

#: includes/api/api-helpers.php:3974
#, php-format
msgid "File type must be %s."
msgstr "Filtyp måste vara %s."

#: includes/fields.php:144
msgid "Basic"
msgstr "Enkel"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Innehåll"

#: includes/fields.php:146
msgid "Choice"
msgstr "Alternativ"

#: includes/fields.php:147
msgid "Relational"
msgstr "Relation"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149 includes/fields/class-acf-field-checkbox.php:286
#: includes/fields/class-acf-field-group.php:485
#: includes/fields/class-acf-field-radio.php:300
#: pro/fields/class-acf-field-clone.php:889
#: pro/fields/class-acf-field-flexible-content.php:569
#: pro/fields/class-acf-field-flexible-content.php:618
#: pro/fields/class-acf-field-repeater.php:514
msgid "Layout"
msgstr "Layout"

#: includes/fields.php:305
msgid "Field type does not exist"
msgstr "Fälttyp existerar inte"

#: includes/fields.php:305
#, fuzzy
msgid "Unknown"
msgstr "Okänd fältgrupp"

#: includes/fields/class-acf-field-checkbox.php:36
#: includes/fields/class-acf-field-taxonomy.php:786
msgid "Checkbox"
msgstr "Kryssruta"

#: includes/fields/class-acf-field-checkbox.php:150
msgid "Toggle All"
msgstr "Markera alla"

#: includes/fields/class-acf-field-checkbox.php:207
msgid "Add new choice"
msgstr "Skapa nytt val"

#: includes/fields/class-acf-field-checkbox.php:246
#: includes/fields/class-acf-field-radio.php:250
#: includes/fields/class-acf-field-select.php:466
msgid "Choices"
msgstr "Alternativ"

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "Enter each choice on a new line."
msgstr "Ange varje alternativ på en ny rad"

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "For more control, you may specify both a value and label like this:"
msgstr "För mer kontroll, kan du specificera både ett värde och etikett såhär:"

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "red : Red"
msgstr "röd : Röd"

#: includes/fields/class-acf-field-checkbox.php:255
msgid "Allow Custom"
msgstr "Tillåt annat val"

#: includes/fields/class-acf-field-checkbox.php:260
msgid "Allow 'custom' values to be added"
msgstr "Tillåter 'annat val' att väljas"

#: includes/fields/class-acf-field-checkbox.php:266
msgid "Save Custom"
msgstr "Spara annat val"

#: includes/fields/class-acf-field-checkbox.php:271
msgid "Save 'custom' values to the field's choices"
msgstr "Spara 'annat val' värdet till fältets val"

#: includes/fields/class-acf-field-checkbox.php:277
#: includes/fields/class-acf-field-color_picker.php:146
#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-radio.php:291
#: includes/fields/class-acf-field-select.php:475
#: includes/fields/class-acf-field-text.php:142
#: includes/fields/class-acf-field-textarea.php:139
#: includes/fields/class-acf-field-true_false.php:150
#: includes/fields/class-acf-field-url.php:114
#: includes/fields/class-acf-field-wysiwyg.php:436
msgid "Default Value"
msgstr "Standardvärde"

#: includes/fields/class-acf-field-checkbox.php:278
#: includes/fields/class-acf-field-select.php:476
msgid "Enter each default value on a new line"
msgstr "Ange varje standardvärde på en ny rad"

#: includes/fields/class-acf-field-checkbox.php:292
#: includes/fields/class-acf-field-radio.php:306
msgid "Vertical"
msgstr "Vertikal"

#: includes/fields/class-acf-field-checkbox.php:293
#: includes/fields/class-acf-field-radio.php:307
msgid "Horizontal"
msgstr "Horisontell"

#: includes/fields/class-acf-field-checkbox.php:300
msgid "Toggle"
msgstr "Slå på/av"

#: includes/fields/class-acf-field-checkbox.php:301
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Visa en extra kryssruta för att markera alla val"

#: includes/fields/class-acf-field-checkbox.php:310
#: includes/fields/class-acf-field-file.php:219
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:180
#: includes/fields/class-acf-field-radio.php:314
#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Return Value"
msgstr "Returvärde"

#: includes/fields/class-acf-field-checkbox.php:311
#: includes/fields/class-acf-field-file.php:220
#: includes/fields/class-acf-field-image.php:207
#: includes/fields/class-acf-field-link.php:181
#: includes/fields/class-acf-field-radio.php:315
msgid "Specify the returned value on front end"
msgstr "Välj vilken typ av värde som ska returneras på front-end"

#: includes/fields/class-acf-field-checkbox.php:316
#: includes/fields/class-acf-field-radio.php:320
#: includes/fields/class-acf-field-select.php:529
msgid "Value"
msgstr "Värde"

#: includes/fields/class-acf-field-checkbox.php:318
#: includes/fields/class-acf-field-radio.php:322
#: includes/fields/class-acf-field-select.php:531
msgid "Both (Array)"
msgstr "Båda"

#: includes/fields/class-acf-field-color_picker.php:36
msgid "Color Picker"
msgstr "Färgväljare"

#: includes/fields/class-acf-field-color_picker.php:83
msgid "Clear"
msgstr "Rensa"

#: includes/fields/class-acf-field-color_picker.php:84
msgid "Default"
msgstr "Standard"

#: includes/fields/class-acf-field-color_picker.php:85
msgid "Select Color"
msgstr "Välj färg"

#: includes/fields/class-acf-field-color_picker.php:86
msgid "Current Color"
msgstr "Nuvarande färg"

#: includes/fields/class-acf-field-date_picker.php:36
msgid "Date Picker"
msgstr "Datumväljare"

#: includes/fields/class-acf-field-date_picker.php:44
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Färdig"

#: includes/fields/class-acf-field-date_picker.php:45
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Idag"

#: includes/fields/class-acf-field-date_picker.php:46
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Nästa"

#: includes/fields/class-acf-field-date_picker.php:47
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Föregående"

#: includes/fields/class-acf-field-date_picker.php:48
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "V"

#: includes/fields/class-acf-field-date_picker.php:223
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-time_picker.php:127
msgid "Display Format"
msgstr "Visa format"

#: includes/fields/class-acf-field-date_picker.php:224
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:128
msgid "The format displayed when editing a post"
msgstr "Formatet som visas när du redigerar ett inlägg"

#: includes/fields/class-acf-field-date_picker.php:232
#: includes/fields/class-acf-field-date_picker.php:263
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:224
#: includes/fields/class-acf-field-time_picker.php:135
#: includes/fields/class-acf-field-time_picker.php:150
#, fuzzy
msgid "Custom:"
msgstr "Advanced Custom Fields"

#: includes/fields/class-acf-field-date_picker.php:242
msgid "Save Format"
msgstr "Spara i format"

#: includes/fields/class-acf-field-date_picker.php:243
msgid "The format used when saving a value"
msgstr "Formatet som används när ett värde sparas"

#: includes/fields/class-acf-field-date_picker.php:253
#: includes/fields/class-acf-field-date_time_picker.php:214
#: includes/fields/class-acf-field-post_object.php:447
#: includes/fields/class-acf-field-relationship.php:778
#: includes/fields/class-acf-field-select.php:524
#: includes/fields/class-acf-field-time_picker.php:142
msgid "Return Format"
msgstr "Returvärde"

#: includes/fields/class-acf-field-date_picker.php:254
#: includes/fields/class-acf-field-date_time_picker.php:215
#: includes/fields/class-acf-field-time_picker.php:143
msgid "The format returned via template functions"
msgstr "Formatet som returneras av mallfunktioner"

#: includes/fields/class-acf-field-date_picker.php:272
#: includes/fields/class-acf-field-date_time_picker.php:231
msgid "Week Starts On"
msgstr "Veckor börjar på"

#: includes/fields/class-acf-field-date_time_picker.php:36
msgid "Date Time Picker"
msgstr "Datum/tidväljare"

#: includes/fields/class-acf-field-date_time_picker.php:44
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Välj tid"

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Tid"

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Timme"

#: includes/fields/class-acf-field-date_time_picker.php:47
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minut"

#: includes/fields/class-acf-field-date_time_picker.php:48
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekund"

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisekund"

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekund"

#: includes/fields/class-acf-field-date_time_picker.php:51
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Tidszon"

#: includes/fields/class-acf-field-date_time_picker.php:52
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Nu"

#: includes/fields/class-acf-field-date_time_picker.php:53
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Klar"

#: includes/fields/class-acf-field-date_time_picker.php:54
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Välj"

#: includes/fields/class-acf-field-date_time_picker.php:56
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:57
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:60
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:61
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:36
msgid "Email"
msgstr "E-post"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-radio.php:292
#: includes/fields/class-acf-field-text.php:143
#: includes/fields/class-acf-field-textarea.php:140
#: includes/fields/class-acf-field-url.php:115
#: includes/fields/class-acf-field-wysiwyg.php:437
msgid "Appears when creating a new post"
msgstr "Visas när ett nytt inlägg skapas"

#: includes/fields/class-acf-field-email.php:142
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:134
#: includes/fields/class-acf-field-text.php:151
#: includes/fields/class-acf-field-textarea.php:148
#: includes/fields/class-acf-field-url.php:123
msgid "Placeholder Text"
msgstr "Platshållartext"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:135
#: includes/fields/class-acf-field-text.php:152
#: includes/fields/class-acf-field-textarea.php:149
#: includes/fields/class-acf-field-url.php:124
msgid "Appears within the input"
msgstr "Visas inuti fältet"

#: includes/fields/class-acf-field-email.php:151
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:143
#: includes/fields/class-acf-field-text.php:160
msgid "Prepend"
msgstr "Lägg till före"

#: includes/fields/class-acf-field-email.php:152
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:144
#: includes/fields/class-acf-field-text.php:161
msgid "Appears before the input"
msgstr "Visas före fältet"

#: includes/fields/class-acf-field-email.php:160
#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-password.php:152
#: includes/fields/class-acf-field-text.php:169
msgid "Append"
msgstr "Lägg till efter"

#: includes/fields/class-acf-field-email.php:161
#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-password.php:153
#: includes/fields/class-acf-field-text.php:170
msgid "Appears after the input"
msgstr "Visas efter fältet"

#: includes/fields/class-acf-field-file.php:36
msgid "File"
msgstr "Fil"

#: includes/fields/class-acf-field-file.php:47
msgid "Edit File"
msgstr "Redigera fil"

#: includes/fields/class-acf-field-file.php:48
msgid "Update File"
msgstr "Uppdatera fil"

#: includes/fields/class-acf-field-file.php:49
#: includes/fields/class-acf-field-image.php:54 includes/media.php:57
#: pro/fields/class-acf-field-gallery.php:55
msgid "Uploaded to this post"
msgstr "Uppladdade till detta inlägg"

#: includes/fields/class-acf-field-file.php:145
msgid "File name"
msgstr "Filnamn"

#: includes/fields/class-acf-field-file.php:149
#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:266
#: includes/fields/class-acf-field-image.php:295
#: pro/fields/class-acf-field-gallery.php:705
#: pro/fields/class-acf-field-gallery.php:734
msgid "File size"
msgstr "Filstorlek"

#: includes/fields/class-acf-field-file.php:174
msgid "Add File"
msgstr "Lägg till fil"

#: includes/fields/class-acf-field-file.php:225
msgid "File Array"
msgstr "Fil array"

#: includes/fields/class-acf-field-file.php:226
msgid "File URL"
msgstr "Filadress"

#: includes/fields/class-acf-field-file.php:227
msgid "File ID"
msgstr "Filens ID"

#: includes/fields/class-acf-field-file.php:234
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:670
msgid "Library"
msgstr "Bibliotek"

#: includes/fields/class-acf-field-file.php:235
#: includes/fields/class-acf-field-image.php:232
#: pro/fields/class-acf-field-gallery.php:671
msgid "Limit the media library choice"
msgstr "Begränsa urvalet i mediabiblioteket"

#: includes/fields/class-acf-field-file.php:240
#: includes/fields/class-acf-field-image.php:237
#: includes/locations/class-acf-location-attachment.php:105
#: includes/locations/class-acf-location-comment.php:83
#: includes/locations/class-acf-location-nav-menu.php:106
#: includes/locations/class-acf-location-taxonomy.php:83
#: includes/locations/class-acf-location-user-form.php:91
#: includes/locations/class-acf-location-user-role.php:108
#: includes/locations/class-acf-location-widget.php:87
#: pro/fields/class-acf-field-gallery.php:676
msgid "All"
msgstr "Alla"

#: includes/fields/class-acf-field-file.php:241
#: includes/fields/class-acf-field-image.php:238
#: pro/fields/class-acf-field-gallery.php:677
msgid "Uploaded to post"
msgstr "Uppladdade till detta inlägg"

#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-image.php:245
#: pro/fields/class-acf-field-gallery.php:684
msgid "Minimum"
msgstr "Minimalt"

#: includes/fields/class-acf-field-file.php:249
#: includes/fields/class-acf-field-file.php:260
msgid "Restrict which files can be uploaded"
msgstr "Begränsa vilka filer som kan laddas upp"

#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:713
msgid "Maximum"
msgstr "Maximalt"

#: includes/fields/class-acf-field-file.php:270
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:742
msgid "Allowed file types"
msgstr "Tillåtna filtyper"

#: includes/fields/class-acf-field-file.php:271
#: includes/fields/class-acf-field-image.php:304
#: pro/fields/class-acf-field-gallery.php:743
msgid "Comma separated list. Leave blank for all types"
msgstr "Kommaseparerad lista. Lämna blankt för alla typer"

#: includes/fields/class-acf-field-google-map.php:36
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-google-map.php:51
msgid "Locating"
msgstr "Söker plats"

#: includes/fields/class-acf-field-google-map.php:52
msgid "Sorry, this browser does not support geolocation"
msgstr "Tyvärr saknar denna webbläsare stöd för platsinformation"

#: includes/fields/class-acf-field-google-map.php:133
msgid "Clear location"
msgstr "Rensa plats"

#: includes/fields/class-acf-field-google-map.php:134
msgid "Find current location"
msgstr "Hitta nuvarande plats"

#: includes/fields/class-acf-field-google-map.php:137
msgid "Search for address..."
msgstr "Sök efter adress..."

#: includes/fields/class-acf-field-google-map.php:167
#: includes/fields/class-acf-field-google-map.php:178
msgid "Center"
msgstr "Centrera"

#: includes/fields/class-acf-field-google-map.php:168
#: includes/fields/class-acf-field-google-map.php:179
msgid "Center the initial map"
msgstr "Kartans initiala centrum"

#: includes/fields/class-acf-field-google-map.php:190
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:191
msgid "Set the initial zoom level"
msgstr "Ange kartans initiala zoom nivå"

#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-image.php:257
#: includes/fields/class-acf-field-image.php:286
#: includes/fields/class-acf-field-oembed.php:297
#: pro/fields/class-acf-field-gallery.php:696
#: pro/fields/class-acf-field-gallery.php:725
msgid "Height"
msgstr "Höjd"

#: includes/fields/class-acf-field-google-map.php:201
msgid "Customise the map height"
msgstr "Ställ in kartans höjd"

#: includes/fields/class-acf-field-group.php:36
#, fuzzy
msgid "Group"
msgstr "Fältgrupp"

#: includes/fields/class-acf-field-group.php:469
#: pro/fields/class-acf-field-repeater.php:453
msgid "Sub Fields"
msgstr "Underfält"

#: includes/fields/class-acf-field-group.php:486
#: pro/fields/class-acf-field-clone.php:890
msgid "Specify the style used to render the selected fields"
msgstr "Specificera stilen för att rendera valda fält"

#: includes/fields/class-acf-field-group.php:491
#: pro/fields/class-acf-field-clone.php:895
#: pro/fields/class-acf-field-flexible-content.php:629
#: pro/fields/class-acf-field-repeater.php:522
msgid "Block"
msgstr "Block"

#: includes/fields/class-acf-field-group.php:492
#: pro/fields/class-acf-field-clone.php:896
#: pro/fields/class-acf-field-flexible-content.php:628
#: pro/fields/class-acf-field-repeater.php:521
msgid "Table"
msgstr "Tabell"

#: includes/fields/class-acf-field-group.php:493
#: pro/fields/class-acf-field-clone.php:897
#: pro/fields/class-acf-field-flexible-content.php:630
#: pro/fields/class-acf-field-repeater.php:523
msgid "Row"
msgstr "Rad"

#: includes/fields/class-acf-field-image.php:36
msgid "Image"
msgstr "Bild"

#: includes/fields/class-acf-field-image.php:51
msgid "Select Image"
msgstr "Välj bild"

#: includes/fields/class-acf-field-image.php:52
#: pro/fields/class-acf-field-gallery.php:53
msgid "Edit Image"
msgstr "Redigera bild"

#: includes/fields/class-acf-field-image.php:53
#: pro/fields/class-acf-field-gallery.php:54
msgid "Update Image"
msgstr "Uppdatera bild"

#: includes/fields/class-acf-field-image.php:55
msgid "All images"
msgstr "Alla bilder"

#: includes/fields/class-acf-field-image.php:142
#: includes/fields/class-acf-field-link.php:153 includes/input.php:267
#: pro/fields/class-acf-field-gallery.php:358
#: pro/fields/class-acf-field-gallery.php:546
msgid "Remove"
msgstr "Radera"

#: includes/fields/class-acf-field-image.php:158
msgid "No image selected"
msgstr "Ingen bild vald"

#: includes/fields/class-acf-field-image.php:158
msgid "Add Image"
msgstr "Lägg till bild"

#: includes/fields/class-acf-field-image.php:212
msgid "Image Array"
msgstr "Bild Array"

#: includes/fields/class-acf-field-image.php:213
msgid "Image URL"
msgstr "Bildadress"

#: includes/fields/class-acf-field-image.php:214
msgid "Image ID"
msgstr "Bildens ID"

#: includes/fields/class-acf-field-image.php:221
msgid "Preview Size"
msgstr "Förhandsvisningens storlek"

#: includes/fields/class-acf-field-image.php:222
msgid "Shown when entering data"
msgstr "Visas vid inmatning av data"

#: includes/fields/class-acf-field-image.php:246
#: includes/fields/class-acf-field-image.php:275
#: pro/fields/class-acf-field-gallery.php:685
#: pro/fields/class-acf-field-gallery.php:714
msgid "Restrict which images can be uploaded"
msgstr "Begränsa vilka bilder som kan laddas upp"

#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:278
#: includes/fields/class-acf-field-oembed.php:286
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Width"
msgstr "bredd"

#: includes/fields/class-acf-field-link.php:36
#, fuzzy
msgid "Link"
msgstr "Sidlänk"

#: includes/fields/class-acf-field-link.php:146
#, fuzzy
msgid "Select Link"
msgstr "Välj fil"

#: includes/fields/class-acf-field-link.php:151
msgid "Opens in a new window/tab"
msgstr ""

#: includes/fields/class-acf-field-link.php:186
#, fuzzy
msgid "Link Array"
msgstr "Fil array"

#: includes/fields/class-acf-field-link.php:187
#, fuzzy
msgid "Link URL"
msgstr "Filadress"

#: includes/fields/class-acf-field-message.php:36
#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-true_false.php:141
msgid "Message"
msgstr "Meddelande"

#: includes/fields/class-acf-field-message.php:124
#: includes/fields/class-acf-field-textarea.php:176
msgid "New Lines"
msgstr "Nya linjer"

#: includes/fields/class-acf-field-message.php:125
#: includes/fields/class-acf-field-textarea.php:177
msgid "Controls how new lines are rendered"
msgstr "Reglerar hur nya linjer renderas"

#: includes/fields/class-acf-field-message.php:129
#: includes/fields/class-acf-field-textarea.php:181
msgid "Automatically add paragraphs"
msgstr "Lägg till styckesindelning automatiskt."

#: includes/fields/class-acf-field-message.php:130
#: includes/fields/class-acf-field-textarea.php:182
msgid "Automatically add &lt;br&gt;"
msgstr "Lägg till automatiskt &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:131
#: includes/fields/class-acf-field-textarea.php:183
msgid "No Formatting"
msgstr "Ingen formattering"

#: includes/fields/class-acf-field-message.php:138
msgid "Escape HTML"
msgstr "Inaktivera HTML-rendering"

#: includes/fields/class-acf-field-message.php:139
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Tillåt HTML kod att visas som synlig text istället för att renderas"

#: includes/fields/class-acf-field-number.php:36
msgid "Number"
msgstr "Nummer"

#: includes/fields/class-acf-field-number.php:181
msgid "Minimum Value"
msgstr "Minsta värde"

#: includes/fields/class-acf-field-number.php:190
msgid "Maximum Value"
msgstr "Högsta värde"

#: includes/fields/class-acf-field-number.php:199
msgid "Step Size"
msgstr "Stegvärde"

#: includes/fields/class-acf-field-number.php:237
msgid "Value must be a number"
msgstr "Värdet måste vara ett nummer"

#: includes/fields/class-acf-field-number.php:255
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Värdet måste vara lika med eller högre än %d"

#: includes/fields/class-acf-field-number.php:263
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Värdet måste vara lika med eller lägre än %d"

#: includes/fields/class-acf-field-oembed.php:36
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:237
msgid "Enter URL"
msgstr "Fyll i URL"

#: includes/fields/class-acf-field-oembed.php:250
#: includes/fields/class-acf-field-taxonomy.php:904
msgid "Error."
msgstr "Fel."

#: includes/fields/class-acf-field-oembed.php:250
msgid "No embed found for the given URL."
msgstr "Ingen embed hittades för angiven URL."

#: includes/fields/class-acf-field-oembed.php:283
#: includes/fields/class-acf-field-oembed.php:294
msgid "Embed Size"
msgstr "Embed storlek"

#: includes/fields/class-acf-field-page_link.php:192
msgid "Archives"
msgstr "Arkiv"

#: includes/fields/class-acf-field-page_link.php:500
#: includes/fields/class-acf-field-post_object.php:399
#: includes/fields/class-acf-field-relationship.php:704
msgid "Filter by Post Type"
msgstr "Filtrera efter inläggstyp"

#: includes/fields/class-acf-field-page_link.php:508
#: includes/fields/class-acf-field-post_object.php:407
#: includes/fields/class-acf-field-relationship.php:712
msgid "All post types"
msgstr "Samtliga posttyper"

#: includes/fields/class-acf-field-page_link.php:514
#: includes/fields/class-acf-field-post_object.php:413
#: includes/fields/class-acf-field-relationship.php:718
msgid "Filter by Taxonomy"
msgstr "Filtrera efter taxonomi"

#: includes/fields/class-acf-field-page_link.php:522
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-relationship.php:726
msgid "All taxonomies"
msgstr "Samtliga taxonomier"

#: includes/fields/class-acf-field-page_link.php:528
#: includes/fields/class-acf-field-post_object.php:427
#: includes/fields/class-acf-field-radio.php:259
#: includes/fields/class-acf-field-select.php:484
#: includes/fields/class-acf-field-taxonomy.php:799
#: includes/fields/class-acf-field-user.php:423
msgid "Allow Null?"
msgstr "Tillått nollvärde?"

#: includes/fields/class-acf-field-page_link.php:538
msgid "Allow Archives URLs"
msgstr "Tillåt urler från arkiv"

#: includes/fields/class-acf-field-page_link.php:548
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-select.php:494
#: includes/fields/class-acf-field-user.php:433
msgid "Select multiple values?"
msgstr "Välj multipla värden?"

#: includes/fields/class-acf-field-password.php:36
msgid "Password"
msgstr "Lösenord"

#: includes/fields/class-acf-field-post_object.php:36
#: includes/fields/class-acf-field-post_object.php:452
#: includes/fields/class-acf-field-relationship.php:783
msgid "Post Object"
msgstr "Inläggsobjekt"

#: includes/fields/class-acf-field-post_object.php:453
#: includes/fields/class-acf-field-relationship.php:784
msgid "Post ID"
msgstr "Inläggets ID"

#: includes/fields/class-acf-field-radio.php:36
msgid "Radio Button"
msgstr "Alternativknapp"

#: includes/fields/class-acf-field-radio.php:269
msgid "Other"
msgstr "Annat"

#: includes/fields/class-acf-field-radio.php:274
msgid "Add 'other' choice to allow for custom values"
msgstr "Lägg till värdet 'annat' för att tillåta egna värden"

#: includes/fields/class-acf-field-radio.php:280
msgid "Save Other"
msgstr "Spara annat"

#: includes/fields/class-acf-field-radio.php:285
msgid "Save 'other' values to the field's choices"
msgstr "Spara 'annat'-värden till fältets alternativ"

#: includes/fields/class-acf-field-relationship.php:36
msgid "Relationship"
msgstr "Relation"

#: includes/fields/class-acf-field-relationship.php:48
msgid "Minimum values reached ( {min} values )"
msgstr "Lägsta tillåtna antal värden nått ( {min} värden )"

#: includes/fields/class-acf-field-relationship.php:49
msgid "Maximum values reached ( {max} values )"
msgstr "Högsta tillåtna antal värden uppnått ( {max} värden )"

#: includes/fields/class-acf-field-relationship.php:50
msgid "Loading"
msgstr "Laddar"

#: includes/fields/class-acf-field-relationship.php:51
msgid "No matches found"
msgstr "Inga träffar"

#: includes/fields/class-acf-field-relationship.php:585
msgid "Search..."
msgstr "Sök..."

#: includes/fields/class-acf-field-relationship.php:594
msgid "Select post type"
msgstr "Välj posttyp"

#: includes/fields/class-acf-field-relationship.php:607
msgid "Select taxonomy"
msgstr "Välj taxonomi"

#: includes/fields/class-acf-field-relationship.php:732
msgid "Filters"
msgstr "Filter"

#: includes/fields/class-acf-field-relationship.php:738
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Inläggstyp"

#: includes/fields/class-acf-field-relationship.php:739
#: includes/fields/class-acf-field-taxonomy.php:36
#: includes/fields/class-acf-field-taxonomy.php:769
msgid "Taxonomy"
msgstr "Taxonomi"

#: includes/fields/class-acf-field-relationship.php:746
msgid "Elements"
msgstr "Element"

#: includes/fields/class-acf-field-relationship.php:747
msgid "Selected elements will be displayed in each result"
msgstr "Valda element visas i varje resultat"

#: includes/fields/class-acf-field-relationship.php:758
msgid "Minimum posts"
msgstr "Minsta antal inlägg"

#: includes/fields/class-acf-field-relationship.php:767
msgid "Maximum posts"
msgstr "Högsta antal inlägg"

#: includes/fields/class-acf-field-relationship.php:871
#: pro/fields/class-acf-field-gallery.php:815
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s kräver minst %s val"
msgstr[1] "%s kräver minst %s val"

#: includes/fields/class-acf-field-select.php:36
#: includes/fields/class-acf-field-taxonomy.php:791
msgctxt "noun"
msgid "Select"
msgstr "Flerväljare"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Ett resultat, tryck enter för att välja det."

#: includes/fields/class-acf-field-select.php:50
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d resultat, använd upp och ned pilarna för att navigera."

#: includes/fields/class-acf-field-select.php:51
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Inget resultat"

#: includes/fields/class-acf-field-select.php:52
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Vänligen skriv in 1 eller fler bokstäver"

#: includes/fields/class-acf-field-select.php:53
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Vänligen skriv in %d eller fler bokstäver"

#: includes/fields/class-acf-field-select.php:54
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Vänligen radera 1 bokstav"

#: includes/fields/class-acf-field-select.php:55
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Vänligen radera %d bokstäver"

#: includes/fields/class-acf-field-select.php:56
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Du kan bara välja 1 resultat"

#: includes/fields/class-acf-field-select.php:57
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Du kan bara välja %d resultat"

#: includes/fields/class-acf-field-select.php:58
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Laddar fler resultat"

#: includes/fields/class-acf-field-select.php:59
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Söker…"

#: includes/fields/class-acf-field-select.php:60
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Laddning misslyckades"

#: includes/fields/class-acf-field-select.php:270 includes/media.php:54
msgctxt "verb"
msgid "Select"
msgstr "Välj"

#: includes/fields/class-acf-field-select.php:504
#: includes/fields/class-acf-field-true_false.php:159
msgid "Stylised UI"
msgstr "Stylat utseende"

#: includes/fields/class-acf-field-select.php:514
msgid "Use AJAX to lazy load choices?"
msgstr "Använda AJAX för att ladda alternativ efter att sidan laddats?"

#: includes/fields/class-acf-field-select.php:525
msgid "Specify the value returned"
msgstr "Specificera värdet att returnera"

#: includes/fields/class-acf-field-separator.php:36
msgid "Separator"
msgstr ""

#: includes/fields/class-acf-field-tab.php:36
msgid "Tab"
msgstr "Flik"

#: includes/fields/class-acf-field-tab.php:96
msgid ""
"The tab field will display incorrectly when added to a Table style repeater "
"field or flexible content field layout"
msgstr ""
"Flik fältet kommer att visas felaktigt om de läggs till ett upprepningsfält "
"med tabellutseende eller ett innehållsfält med flexibel layout"

#: includes/fields/class-acf-field-tab.php:97
msgid ""
"Use \"Tab Fields\" to better organize your edit screen by grouping fields "
"together."
msgstr ""
"Använd \"Flikfält\" för att bättre organisera din redigeringsvy genom att "
"gruppera fälten tillsammans."

#: includes/fields/class-acf-field-tab.php:98
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is "
"defined) will be grouped together using this field's label as the tab "
"heading."
msgstr ""
"Alla fält efter detta \"flikfält\" (eller fram till nästa \"flikfält\") "
"kommer att grupperas tillsammans genom fältets titel som flikrubrik."

#: includes/fields/class-acf-field-tab.php:112
msgid "Placement"
msgstr "Placering"

#: includes/fields/class-acf-field-tab.php:124
msgid "End-point"
msgstr "Slutpunkt"

#: includes/fields/class-acf-field-tab.php:125
msgid "Use this field as an end-point and start a new group of tabs"
msgstr "Använd detta fält som slutpunkt och starta en ny grupp flikar"

#: includes/fields/class-acf-field-taxonomy.php:719
#: includes/fields/class-acf-field-true_false.php:95
#: includes/fields/class-acf-field-true_false.php:184 includes/input.php:266
#: pro/admin/views/html-settings-updates.php:103
msgid "No"
msgstr "Nej"

#: includes/fields/class-acf-field-taxonomy.php:738
msgid "None"
msgstr "Ingen"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Select the taxonomy to be displayed"
msgstr "Välj taxonomi som ska visas"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Appearance"
msgstr "Utseende"

#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Select the appearance of this field"
msgstr "Välj utseende för detta fält"

#: includes/fields/class-acf-field-taxonomy.php:785
msgid "Multiple Values"
msgstr "Multipla värden"

#: includes/fields/class-acf-field-taxonomy.php:787
msgid "Multi Select"
msgstr "Flerval"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "Single Value"
msgstr "Ett enda värde"

#: includes/fields/class-acf-field-taxonomy.php:790
msgid "Radio Buttons"
msgstr "Alternativknappar"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Create Terms"
msgstr "Skapa värden"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Allow new terms to be created whilst editing"
msgstr "Tillåt att nya värden läggs till under redigering"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Save Terms"
msgstr "Spara värden"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Connect selected terms to the post"
msgstr "Koppla valda värden till inlägget"

#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Load Terms"
msgstr "Ladda värden"

#: includes/fields/class-acf-field-taxonomy.php:830
msgid "Load value from posts terms"
msgstr "Ladda värde från ett inläggs värden"

#: includes/fields/class-acf-field-taxonomy.php:844
msgid "Term Object"
msgstr "Term objekt"

#: includes/fields/class-acf-field-taxonomy.php:845
msgid "Term ID"
msgstr "Term ID"

#: includes/fields/class-acf-field-taxonomy.php:904
#, php-format
msgid "User unable to add new %s"
msgstr "Användare kan inte lägga till ny %s"

#: includes/fields/class-acf-field-taxonomy.php:917
#, php-format
msgid "%s already exists"
msgstr "%s finns redan"

#: includes/fields/class-acf-field-taxonomy.php:958
#, php-format
msgid "%s added"
msgstr "%s tillagt"

#: includes/fields/class-acf-field-taxonomy.php:1003
msgid "Add"
msgstr "Lägg till"

#: includes/fields/class-acf-field-text.php:36
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-text.php:178
#: includes/fields/class-acf-field-textarea.php:157
msgid "Character Limit"
msgstr "Maximalt antal tecken"

#: includes/fields/class-acf-field-text.php:179
#: includes/fields/class-acf-field-textarea.php:158
msgid "Leave blank for no limit"
msgstr "Lämna tomt för att ha utan begränsning"

#: includes/fields/class-acf-field-textarea.php:36
msgid "Text Area"
msgstr "Textfält"

#: includes/fields/class-acf-field-textarea.php:166
msgid "Rows"
msgstr "Rader"

#: includes/fields/class-acf-field-textarea.php:167
msgid "Sets the textarea height"
msgstr "Välj textfältets höjd"

#: includes/fields/class-acf-field-time_picker.php:36
msgid "Time Picker"
msgstr "Tidväljare"

#: includes/fields/class-acf-field-true_false.php:36
msgid "True / False"
msgstr "Sant / Falskt"

#: includes/fields/class-acf-field-true_false.php:94
#: includes/fields/class-acf-field-true_false.php:174 includes/input.php:265
#: pro/admin/views/html-settings-updates.php:93
msgid "Yes"
msgstr "Ja"

#: includes/fields/class-acf-field-true_false.php:142
msgid "Displays text alongside the checkbox"
msgstr "Visa text bredvid kryssrutan"

#: includes/fields/class-acf-field-true_false.php:170
msgid "On Text"
msgstr "På text"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when active"
msgstr "Text som visas när valet är aktivt"

#: includes/fields/class-acf-field-true_false.php:180
msgid "Off Text"
msgstr "Av text"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr "Text som visas när valet är inaktivt"

#: includes/fields/class-acf-field-url.php:36
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:165
msgid "Value must be a valid URL"
msgstr "Värdet måste vara en giltig URL"

#: includes/fields/class-acf-field-user.php:36 includes/locations.php:95
msgid "User"
msgstr "Användare"

#: includes/fields/class-acf-field-user.php:408
msgid "Filter by role"
msgstr "Filtrera efter roll"

#: includes/fields/class-acf-field-user.php:416
msgid "All user roles"
msgstr "Alla användarroller"

#: includes/fields/class-acf-field-wysiwyg.php:36
msgid "Wysiwyg Editor"
msgstr "WYSIWYG-editor"

#: includes/fields/class-acf-field-wysiwyg.php:385
msgid "Visual"
msgstr "Visuellt"

#: includes/fields/class-acf-field-wysiwyg.php:386
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Text"

#: includes/fields/class-acf-field-wysiwyg.php:392
msgid "Click to initialize TinyMCE"
msgstr "Klicka för att initialisera tinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:445
msgid "Tabs"
msgstr "Flikar"

#: includes/fields/class-acf-field-wysiwyg.php:450
msgid "Visual & Text"
msgstr "Visuellt & Text"

#: includes/fields/class-acf-field-wysiwyg.php:451
msgid "Visual Only"
msgstr "Endast visuellt"

#: includes/fields/class-acf-field-wysiwyg.php:452
msgid "Text Only"
msgstr "Endast text"

#: includes/fields/class-acf-field-wysiwyg.php:459
msgid "Toolbar"
msgstr "Verktygsfält"

#: includes/fields/class-acf-field-wysiwyg.php:469
msgid "Show Media Upload Buttons?"
msgstr "Visa knappar för uppladdning av media?"

#: includes/fields/class-acf-field-wysiwyg.php:479
msgid "Delay initialization?"
msgstr "Fördröj initialisering?"

#: includes/fields/class-acf-field-wysiwyg.php:480
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE initialiseras inte förrän fältet klickas på"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:304
msgid "Edit field group"
msgstr "Redigera fältgrupp"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Validera E-post"

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:588 pro/options-page.php:81
msgid "Update"
msgstr "Uppdatera"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Inlägg uppdaterat"

#: includes/forms/form-front.php:229
msgid "Spam Detected"
msgstr "Skräppost Upptäckt"

#: includes/input.php:258
msgid "Expand Details"
msgstr "Visa detaljer"

#: includes/input.php:259
msgid "Collapse Details"
msgstr "Dölj detaljer"

#: includes/input.php:260
msgid "Validation successful"
msgstr "Validering lyckades"

#: includes/input.php:261 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Validering misslyckades"

#: includes/input.php:262
msgid "1 field requires attention"
msgstr "1 fält kräver din uppmärksamhet"

#: includes/input.php:263
#, php-format
msgid "%d fields require attention"
msgstr "%d fält kräver din uppmärksamhet"

#: includes/input.php:264
msgid "Restricted"
msgstr "Begränsad"

#: includes/input.php:268
msgid "Cancel"
msgstr ""

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Inlägg"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Sida"

#: includes/locations.php:96
msgid "Forms"
msgstr "Formulär"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Bilaga"

#: includes/locations/class-acf-location-attachment.php:113
#, php-format
msgid "All %s formats"
msgstr ""

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Kommentar"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Inloggad användarroll"

#: includes/locations/class-acf-location-current-user-role.php:114
msgid "Super Admin"
msgstr "Superadministratör"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Inloggad användare"

#: includes/locations/class-acf-location-current-user.php:101
msgid "Logged in"
msgstr "Inloggad"

#: includes/locations/class-acf-location-current-user.php:102
msgid "Viewing front end"
msgstr "Visar framsida"

#: includes/locations/class-acf-location-current-user.php:103
msgid "Viewing back end"
msgstr "Visar baksida"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:113
#, fuzzy
msgid "Menu Locations"
msgstr "Plats"

#: includes/locations/class-acf-location-nav-menu.php:123
msgid "Menus"
msgstr ""

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Sidans förälder"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Sidmall"

#: includes/locations/class-acf-location-page-template.php:102
#: includes/locations/class-acf-location-post-template.php:156
msgid "Default Template"
msgstr "Standardmall"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Sidtyp"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Front Page"
msgstr "Förstasida"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Posts Page"
msgstr "Inläggslistningssida"

#: includes/locations/class-acf-location-page-type.php:151
msgid "Top Level Page (no parent)"
msgstr "Toppsida (Ingen förälder)"

#: includes/locations/class-acf-location-page-type.php:152
msgid "Parent Page (has children)"
msgstr "Föräldersida (har undersidor)"

#: includes/locations/class-acf-location-page-type.php:153
msgid "Child Page (has parent)"
msgstr "Undersida (har föräldersida)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Inläggskategori"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Inläggsformat"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Inläggsstatus"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Inläggstaxonomi"

#: includes/locations/class-acf-location-post-template.php:29
#, fuzzy
msgid "Post Template"
msgstr "Sidmall"

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "Taxonomivärde"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Användarformulär"

#: includes/locations/class-acf-location-user-form.php:92
msgid "Add / Edit"
msgstr "Skapa / Redigera"

#: includes/locations/class-acf-location-user-form.php:93
msgid "Register"
msgstr "Registrera"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Användarroll"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widget"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr "Ändra"

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr "Uppdatera"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s värde är obligatorisk"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:196
msgid "Publish"
msgstr "Publicera"

#: pro/admin/admin-options-page.php:202
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Inga fältgrupper hittades för denna inställningssida. <a href=\"%s\">Skapa "
"en fältgrupp</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Fel</b>. Kunde inte ansluta till uppdateringsservern"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:17
msgid "Updates"
msgstr "Uppdateringar"

#: pro/admin/views/html-settings-updates.php:11
msgid "Deactivate License"
msgstr "Inaktivera licens"

#: pro/admin/views/html-settings-updates.php:11
msgid "Activate License"
msgstr "Aktivera licens"

#: pro/admin/views/html-settings-updates.php:21
msgid "License Information"
msgstr "Licensinformation"

#: pro/admin/views/html-settings-updates.php:24
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"För att aktivera uppdateringar, vänligen fyll i din licensnyckel här "
"nedanför. Om du inte har en licensnyckel, vänligen gå till sidan <a href=\"%s"
"\">detaljer & priser</a>"

#: pro/admin/views/html-settings-updates.php:33
msgid "License Key"
msgstr "Licensnyckel"

#: pro/admin/views/html-settings-updates.php:65
msgid "Update Information"
msgstr "Uppdateringsinformation"

#: pro/admin/views/html-settings-updates.php:72
msgid "Current Version"
msgstr "Nuvarande version"

#: pro/admin/views/html-settings-updates.php:80
msgid "Latest Version"
msgstr "Senaste version"

#: pro/admin/views/html-settings-updates.php:88
msgid "Update Available"
msgstr "Uppdatering tillgänglig"

#: pro/admin/views/html-settings-updates.php:96
msgid "Update Plugin"
msgstr "Uppdatera tillägg"

#: pro/admin/views/html-settings-updates.php:98
msgid "Please enter your license key above to unlock updates"
msgstr ""
"Vänligen fyll i din licensnyckel här ovan för att låsa upp uppdateringar"

#: pro/admin/views/html-settings-updates.php:104
msgid "Check Again"
msgstr "Kontrollera igen"

#: pro/admin/views/html-settings-updates.php:121
msgid "Upgrade Notice"
msgstr "Uppgraderingsnotering"

#: pro/fields/class-acf-field-clone.php:36
msgctxt "noun"
msgid "Clone"
msgstr "Klon"

#: pro/fields/class-acf-field-clone.php:858
msgid "Select one or more fields you wish to clone"
msgstr "Välj ett eller fler fält du vill klona"

#: pro/fields/class-acf-field-clone.php:875
msgid "Display"
msgstr "Visa"

#: pro/fields/class-acf-field-clone.php:876
msgid "Specify the style used to render the clone field"
msgstr "Specificera stilen som ska användas för att skapa klonfältet"

#: pro/fields/class-acf-field-clone.php:881
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grupp (visar valda fält i en grupp i detta fält)"

#: pro/fields/class-acf-field-clone.php:882
msgid "Seamless (replaces this field with selected fields)"
msgstr "Sömlös (ersätter detta fält med valda fält)"

#: pro/fields/class-acf-field-clone.php:903
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Fälttitlar visas som %s"

#: pro/fields/class-acf-field-clone.php:906
msgid "Prefix Field Labels"
msgstr "Prefix fälttitlar"

#: pro/fields/class-acf-field-clone.php:917
#, php-format
msgid "Values will be saved as %s"
msgstr "Värden sparas som %s"

#: pro/fields/class-acf-field-clone.php:920
msgid "Prefix Field Names"
msgstr "Prefix fältnamn"

#: pro/fields/class-acf-field-clone.php:1038
msgid "Unknown field"
msgstr "Okänt fält"

#: pro/fields/class-acf-field-clone.php:1077
msgid "Unknown field group"
msgstr "Okänd fältgrupp"

#: pro/fields/class-acf-field-clone.php:1081
#, php-format
msgid "All fields from %s field group"
msgstr "Alla fält från %s fältgrupp"

#: pro/fields/class-acf-field-flexible-content.php:42
#: pro/fields/class-acf-field-repeater.php:230
#: pro/fields/class-acf-field-repeater.php:534
msgid "Add Row"
msgstr "Lägg till rad"

#: pro/fields/class-acf-field-flexible-content.php:45
msgid "layout"
msgstr "layout"

#: pro/fields/class-acf-field-flexible-content.php:46
msgid "layouts"
msgstr "layouter"

#: pro/fields/class-acf-field-flexible-content.php:47
msgid "remove {layout}?"
msgstr "radera {layout}?"

#: pro/fields/class-acf-field-flexible-content.php:48
msgid "This field requires at least {min} {identifier}"
msgstr "Detta fält kräver minst {min} {identifierare}"

#: pro/fields/class-acf-field-flexible-content.php:49
msgid "This field has a limit of {max} {identifier}"
msgstr "Detta fält har en gräns på {max} {identifierare}"

#: pro/fields/class-acf-field-flexible-content.php:50
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Detta fält kräver minst {min} {etikett} {identifierare}"

#: pro/fields/class-acf-field-flexible-content.php:51
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "Maximal {etikett} gräns nåtts ({max} {identifierare})"

#: pro/fields/class-acf-field-flexible-content.php:52
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{tillgänglig} {etikett} {identifierare} tillgänglig (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:53
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{krävs} {etikett} {identifierare} krävs (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:54
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexibelt innehåll kräver minst 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:288
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Klicka på knappen \"%s\" nedan för att börja skapa din layout"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Lägg till layout"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Remove layout"
msgstr "Radera layout"

#: pro/fields/class-acf-field-flexible-content.php:425
#: pro/fields/class-acf-field-repeater.php:360
msgid "Click to toggle"
msgstr "Klicka för att växla"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Reorder Layout"
msgstr "Ändra layoutens ordning"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Reorder"
msgstr "Ändra ordning"

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Delete Layout"
msgstr "Radera layout"

#: pro/fields/class-acf-field-flexible-content.php:573
msgid "Duplicate Layout"
msgstr "Kopiera layout"

#: pro/fields/class-acf-field-flexible-content.php:574
msgid "Add New Layout"
msgstr "Lägg till ny layout"

#: pro/fields/class-acf-field-flexible-content.php:645
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:658
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:685
#: pro/fields/class-acf-field-repeater.php:530
msgid "Button Label"
msgstr "Knapp etikett"

#: pro/fields/class-acf-field-flexible-content.php:694
msgid "Minimum Layouts"
msgstr "Lägsta tillåtna antal layouter"

#: pro/fields/class-acf-field-flexible-content.php:703
msgid "Maximum Layouts"
msgstr "Högsta tillåtna antal layouter"

#: pro/fields/class-acf-field-gallery.php:52
msgid "Add Image to Gallery"
msgstr "Lägg till en bild till galleri"

#: pro/fields/class-acf-field-gallery.php:56
msgid "Maximum selection reached"
msgstr "Högsta tillåtna antal val uppnått"

#: pro/fields/class-acf-field-gallery.php:336
msgid "Length"
msgstr "Längd"

#: pro/fields/class-acf-field-gallery.php:379
msgid "Caption"
msgstr "Bildtext"

#: pro/fields/class-acf-field-gallery.php:388
msgid "Alt Text"
msgstr "Alt Text"

#: pro/fields/class-acf-field-gallery.php:559
msgid "Add to gallery"
msgstr "Lägg till galleri"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Bulk actions"
msgstr "Välj åtgärd"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Sort by date uploaded"
msgstr "Sortera efter uppladdningsdatum"

#: pro/fields/class-acf-field-gallery.php:565
msgid "Sort by date modified"
msgstr "Sortera efter redigeringsdatum"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Sort by title"
msgstr "Sortera efter titel"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Reverse current order"
msgstr "Omvänd nuvarande ordning"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Close"
msgstr "Stäng"

#: pro/fields/class-acf-field-gallery.php:639
msgid "Minimum Selection"
msgstr "Minsta tillåtna antal val"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Maximum Selection"
msgstr "Högsta tillåtna antal val"

#: pro/fields/class-acf-field-gallery.php:657
msgid "Insert"
msgstr "Infoga"

#: pro/fields/class-acf-field-gallery.php:658
msgid "Specify where new attachments are added"
msgstr "Specifiera var nya bilagor läggs till"

#: pro/fields/class-acf-field-gallery.php:662
msgid "Append to the end"
msgstr "Lägg till i slutet"

#: pro/fields/class-acf-field-gallery.php:663
msgid "Prepend to the beginning"
msgstr "Lägg till början"

#: pro/fields/class-acf-field-repeater.php:47
msgid "Minimum rows reached ({min} rows)"
msgstr "Minsta tillåtna antal rader uppnått ({min} rader)"

#: pro/fields/class-acf-field-repeater.php:48
msgid "Maximum rows reached ({max} rows)"
msgstr "Högsta tillåtna antal rader uppnått ({max} rader)"

#: pro/fields/class-acf-field-repeater.php:405
msgid "Add row"
msgstr "Lägg till rad"

#: pro/fields/class-acf-field-repeater.php:406
msgid "Remove row"
msgstr "Radera rad"

#: pro/fields/class-acf-field-repeater.php:483
msgid "Collapsed"
msgstr "Kollapsa"

#: pro/fields/class-acf-field-repeater.php:484
msgid "Select a sub field to show when row is collapsed"
msgstr "Välj ett underfält att visa när raden är kollapsad"

#: pro/fields/class-acf-field-repeater.php:494
msgid "Minimum Rows"
msgstr "Minsta tillåtna antal rader"

#: pro/fields/class-acf-field-repeater.php:504
msgid "Maximum Rows"
msgstr "Högsta tillåtna antal rader"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Det finns inga inställningssidor"

#: pro/options-page.php:51
msgid "Options"
msgstr "Alternativ"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Inställningar uppdaterade"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"För att aktivera uppdateringar, vänligen fyll i din licensnyckel på sidan <a "
"href=\"%s\">uppdateringar</a>. Om du inte har en licensnyckel, vänligen gå "
"till sidan <a href=\"%s\">detaljer & priser</a>"

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Features"
#~ msgstr "Funktioner"

#~ msgid "How to"
#~ msgstr "Instruktioner"

#~ msgid "Term meta upgrade not possible (termmeta table does not exist)"
#~ msgstr ""
#~ "Term meta uppgradering ej möjligt (termmeta tabellen existerar inte)"

#~ msgid ""
#~ "Error validating ACF PRO license URL (website does not match). Please re-"
#~ "activate your license"
#~ msgstr ""
#~ "Fel vid validering av ACF PRO licens URL (Sajten överensstämmer inte). "
#~ "Vänligen aktivera din licens på nytt"

#~ msgid "Getting Started"
#~ msgstr "Kom igång"

#~ msgid "Field Types"
#~ msgstr "Fälttyper"

#~ msgid "Functions"
#~ msgstr "Funktioner"

#~ msgid "Actions"
#~ msgstr "Actions"

#~ msgid "Tutorials"
#~ msgstr "Handledningar"

#~ msgid "FAQ"
#~ msgstr "Frågor & Svar"

#~ msgid "Error"
#~ msgstr "Fel"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "1 fält kräver din uppmärksamhet"
#~ msgstr[1] "%d fält kräver din uppmärksamhet"
