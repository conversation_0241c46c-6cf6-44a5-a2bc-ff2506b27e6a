<?php
/* 可视化不生效 */
if (_res('action') == 'elementor') {
    return false;
}
function x_shortcode()
{
    add_shortcode('xstart', 'xstart_shortcode');
    add_shortcode('xside-cat', 'xside_cat');
    add_shortcode('xyear_content', 'xyear_content');
    add_shortcode('xcat_lists', 'xcat_lists');
    add_shortcode('singlecat', 'singlecat');
    add_shortcode('singlepost', 'singlepost');
    add_shortcode('header-language', 'header_lang');
    add_shortcode('xform', 'xform');
    add_shortcode('tag-lists', 'tag_lists');
    add_shortcode('xdownload', 'xdownload');
    add_shortcode('post-author', 'post_author');
    add_shortcode('xviews', 'xviews');
    add_shortcode('xmap', 'xmap');
    add_shortcode('post-navigation', 'post_navigation');
    add_shortcode('sx_content', 'sx_content');
    add_shortcode('dc-lists', 'dc_lists');
	add_shortcode('ykt_lists', 'ykt_lists');
}
add_action('init', 'x_shortcode');

function xcat_lists()
{
    if (! is_category()) {
        return ;
    }

    $cat_id = get_queried_object_id();

    $tags = cnt_tags();

    foreach ($tags as $v) {
        $pp = '';
        $posts = new WP_Query([
                'cat' => $cat_id,
                'tag_id' => $v->term_id,
                'posts_per_page' => 6,
            ]);
            
        while ($posts->have_posts()) {
            $posts->the_post();
            if (get_the_post_thumbnail_url()) {
                $pic = get_the_post_thumbnail_url();
            } else {
                $pic = __URL__.'/static/pic/'.rand(1, 8).'.jpg';
            }
            $tgs = get_the_tags(get_the_ID());
            $tg_html='';
            if ($tgs != false) {
                foreach ($tgs as $k => $v) {
                    $tg_html .= '<a href="'.get_term_link($v, 'post_tag').'?from='.$cat_id.'">'.$v->name.'</a>';
                }
            }
            $pp .= '<article>
                        <div class="pic">
                            <a href="'.get_the_permalink().'" class="thumb" style="background: url('.$pic.') center no-repeat; background-size: cover;"><img src="'.$pic.'" alt="'.get_the_title().'"></a>
                            <div class="cat">'.$tg_html.' <i><img src="'.__URL__.'/static/i/biaoqian.png"></i></div>
                        </div>
                        <div class="content"><a class="tit1" href="'.get_the_permalink().'">'.get_the_title().'</a>
                        </div>
                        <div class="desc">'.mb_strimwidth(strip_tags(apply_filters('the_content', get_the_content())), 0, 130, "…").'</div>
                    </article>';
            wp_reset_postdata();
        }
        $banner ='';
        $banner_count = 0;
        $cnt = get_field('catcnt', 'category_'.$cat_id);
        if (sizeof($cnt['tag_'.$v->slug])) {
            $banner .= '<div class="banner">';
            foreach ($cnt['tag_'.$v->slug] as $key => $value) {
                $banner .='<a href="'.$value['link'].'"><img src="'.$value['pic'].'" alt=""></a>';
                $banner_count++;
            }
            $banner .='</div>';
        }
        if ($banner_count>1) {
            $script = '<script>
              jQuery(document).ready(function ($) {
                $("#cat-'.$v->term_id.' .banner").slick({
                  dots: true,
                  autoplay: true,
                  infinite: true,
                  speed: 500,
                  cssEase: "linear"
                })
              });
              </script>';
        } else {
            $script ='';
        }
        $html .= '
            <div class="cat-block" id="cat-'.$v->term_id.'" '.$banner_count.'>
                <div class="hd">
                    <div class="tit" style="background: url('.get_field('icon', 'post_tag_'.$v->term_id).') center right no-repeat">'.$v->name.'</div>
                    <a class="more" href="'.get_term_link($v, 'post_tag').'?from='.$cat_id.'">查看更多</a>
                </div>
                '.$banner .'
                <div class="bd post-lists">
                    <div class="lists">
                        '.($pp ? $pp : '<p>未找到相关的资讯。</p>').'
                    </div>
                </div>
            </div>'.$script;
    }
    return $html;
}

function xstart_shortcode($attrs)
{
    extract(
        shortcode_atts(
            array(
                'type' => '',
                'xtype' => '',
                'parent' => '',
                'show_parent' => 0,
                'tax_id' => '',
                'post_type' => 'post',
                'orderby' => '',
                'cat' => '',
                'number' => '',
                'class' => '',
                'tax' => 'category',
                'depth' => 1,
                'hierarchical' => 1,
                'is_thumb' => 0,
                'id' => 0,
            ),
            $attrs
        )
    );
    $html ="<div class='xstart'>";
    switch ($xtype) {
        case "cat-list":
            $html .= "<div class='$xtype'>";
            $cid = get_query_var('cat');
            if ($parent) {
                $cid = $parent;
            }
            
            if ($cat) {
                $cid = $cat;
            }
            $arg = array(
                'taxonomy' => 'category',
                'child_of' => $cid,
                'parent' => $cid,
                'depth' => $depth,
                'hide_empty' => false,
            );
            
            $cats = get_terms($arg);
            
            /* if (is_category()) {
                $html .="<div class='item".(wp_get_term_taxonomy_parent_id(get_query_var('cat'), 'category') == 0 ? ' active' : '')."'><a class='item-link' href='".get_term_link(cat_root_id(get_query_var('cat')))."'>全部</a></div>";
            } */
            
            // array_multisort(array_column($cats, 'name'), SORT_ASC, $cats);
            $term = get_term($cid);
            if ($show_parent) {
                $html .="<h4 class='parent'><a href='".get_term_link($term)."'>".$term->name."</a></h4>";
            }
            foreach ($cats as $c) {
                if ($is_thumb) {
                    $thumb = "<img src='".get_field('pic', 'category_'.$c->term_id)."' title='".$c->name."'/>";
                }
                if ($depth>1 && !is_wp_error(get_term_children($c->term_id, $tax))) {
                    $child ="<ul class='child'>";
                    $child_arr = get_terms([
                        'taxonomy' => 'category',
                        'child_of' => $c->term_id,
                        'parent' => $c->term_id,
                        'depth' => 1,
                        'hide_empty' => false,
                    ]);
                    foreach ($child_arr as $k => $v) {
                        if (get_query_var('cat') == $v->term_id) {
                            $on = " active";
                        } else {
                            $on = '';
                        }
                        $child .="<li class='item $on'><a href='".get_term_link($v->term_id)."'>".$v->name."</a></li>";
                    }
                    $child .="</ul>";
                }
                if (get_query_var('cat') == $c->term_id) {
                    $on = " active";
                } else {
                    $on = '';
                }
                $html .="<div class='item".$on."'><a class='item-link' href='".get_term_link($c->term_id)."'>$thumb".$c->name."</a>$child</div>";
            }
            $html .= "</div>";
            break;
        case "product_slider":
            global $post;
            $html .= "<div class='$xtype'>";
            $video = get_field('video');
            $gallery = get_field('gallery');

            if ($video['link'] || sizeof($gallery)) {
                $num = 0;
                if ($video['link']) {
                    $video_html = '<li><video src="'.$video['link'].'" autoplay loop muted playsinline uk-cover></video></li>';
                    $video_thumb = '<li uk-slideshow-item="'.$num.'"><a href="#"><img src="'.$video['thumb'].'"></a></li>';
                    $num++;
                }
                if (count($gallery)) {
                    foreach ($gallery as $k => $v) {
                        $gallery_html .='<li><img src="'.$v.'" alt="" uk-cover></li>';
                        $gallery_thumb .='<li uk-slideshow-item="'.$num.'"><a href="#"><img src="'.$v.'"></a></li>';
                        $num++;
                    }
                }
                $html .= '<div class="uk-position-relative uk-visible-toggle uk-light" uk-slideshow="animation: push;min-height:500">
                <ul class="uk-slideshow-items"> '.$video_html.$gallery_html.' </ul>
                <ul class="uk-thumbnav"> '.$video_thumb.$gallery_thumb.' </ul>
                <a class="uk-position-center-left uk-position-small uk-hidden-hover" href="#" uk-slidenav-previous uk-slideshow-item="previous"></a>
                <a class="uk-position-center-right uk-position-small uk-hidden-hover" href="#" uk-slidenav-next uk-slideshow-item="next"></a></div>';
            } else {
                $html = '<img src="'.get_the_post_thumbnail_url().'">';
            }
            $html .= "</div>";
            break;
        case "product_xinghao":
            global $post;
            $xh = get_field('setting');
            $html .= "<div class='$xtype'>".$xh['number']."</div>";
            break;
        case "category":
            global $post;
            $cat = wp_get_post_categories(get_the_ID());
            $term =get_term($cat[0]);
            return $term->name;
            break;
        case "x_pagination":
            global $cat,$paged;
            $posts2 = new WP_Query(array(
                'cat' => $cat,
                'post_type' => 'product',
                'posts_per_page' => -1,
            ));
            
            $html .= x_pagination(5, (ceil)($posts2->post_count / get_option('posts_per_page')));
            break;
        default:
        // code...
            break;
    }
    
    $html .="</div>";
    return $html;
}

function singlecat($attrs)
{
    extract(
        shortcode_atts(
            array(
                'field' => '',
            ),
            $attrs
        )
    );
    global $post;
    $cat = get_the_category($post->ID);
    if ($field == 'title') {
        return $cat['0']->name;
    } elseif ($field == 'link') {
        return get_term_link($cat['0']);
    }
}

function singlepost($attrs)
{
    extract(
        shortcode_atts(
            array(
                'type' => '',
            ),
            $attrs
        )
    );
    global $post;
    switch ($type) {
        case 'desc':
            /* 摘要 */
            return mb_strimwidth(strip_tags(apply_filters('the_content', $post->post_content)), 0, 145, "…");
            break;
        
        default:
            # code...
            break;
    }
}

function header_lang($attrs)
{
    extract(
        shortcode_atts(
            array(
                'type' => '',
            ),
            $attrs
        )
    );
    $clang = 'cn';

    return $html ="<div id='header-lang' class='{$type}'>
                <a href='#'>
                    <span>".($clang != 'cn' ? 'EN' :'中文')."</span>
                    <img src='".__URL__."/static/i/yuyan-diqiu.png'>
                </a>
            </div><div id='lang-switcher'>".do_shortcode('[gtranslate]')."</div>
            ";
}

function xform($attrs)
{
    extract(
        shortcode_atts(
            array(
                'field' => '',
            ),
            $attrs
        )
    );
    if ($field == 'time') {
        $year = date('Y');
        for ($i=$year; $i > $year - 6; $i--) {
            $op .= "<a data-value='{$i}' href='#'>{$i}年</a>";
        }
        $now_op = _res('update_time') == '' ? '<a data-value="" href="#">更新时间 <span></span></a>' : '<a data-value="'._res('update_time').'" href="#">'._res('update_time').' <span></span></a>';
        $html = '
        <div class="select-block">
            <div class="hd">'.$now_op.'</div>
            <div class="bd"><div class="wrap">'.$op.'</div></div>
        </div>';
    }
    if ($field == 'type') {
        $tag = cnt_tags();
        
        foreach ($tag as $k => $v) {
            $op .= "<a data-value='{$v->term_id}' href='".get_term_link($v)."?from=".get_queried_object_id()."'>{$v->name}</a>";
        }
        $now_op = _res('update_time') == '' ? '<a data-value="" href="#">行业类别 <span></span></a>' : '<a data-value="'._res('type').'" href="#">'._res('update_time').' <span></span></a>';
        $html = '
        <div class="select-block">
            <div class="hd">'.$now_op.'</div>
            <div class="bd"><div class="wrap">'.$op.'</div></div>
        </div>';
    }

    if ($field == 'cat') {
        if (_res('action') == 'elementor') {
            return false;
        }
        $rcat = get_term(_res('from'));
        $tag = get_queried_object();
        $tag_link = get_term_link($tag);

        if (!is_wp_error($rcat)) {
            $on = '<a data-value="'.$rcat->term_id.'" href="#">'.$rcat->name.' <span></span></a>';
        } else {
            $on = '<a data-value="" href="'.$tag_link.'">所有分类 <span></span></a>';
        }

        $side = new Timber\Menu('sidemenu');
        $side_menu = $side->items();
        $ll = '<a href="'.$tag_link.'">所有分类</a>';
        foreach ($side_menu as $k => $m) {
            if ($m->level == 0) {
                $ll .= '<a data-value="'.$m->object_id.'" href="'.$tag_link.'?from='.$m->object_id.'">'.$m->name.'</a>';
            }
        }
        $html = '
        <div class="select-block">
            <div class="hd">'.$on.'</div>
            <div class="bd"><div class="wrap">'.$ll.'</div></div>
        </div>';
    }

    if ($field == 'date') {
        $on = '<a data-value="" href="#">'.__('请选择月份', 'wpme').' <span></span></a>';
        $year = _res('y') ?:date('Y') ;
        $posts = get_cat_date_posts(6, 48, $year);
        foreach ($posts as $m => $v) {
            $res .= '<a data-value="'.$m.'" href="#m'.$m.'">'.$year.'年'.$m.'月</a>';
        }
        $html = '
        <div class="select-block bymenth">
            <div class="hd">'.$on.'</div>
            <div class="bd"><div class="wrap">'.$res.'</div></div>
        </div>';
    }
    return $html;
}

function tag_lists()
{
    if (_res('action') == 'elementor') {
        return false;
    }
    $cat = get_queried_object();

    $paged = (get_query_var('page')) ? get_query_var('page') : 1;

    $args = [
        'tag_id' => get_queried_object_id(),
        'posts_per_page' => 24,
        'paged' => $paged,
    ];

    if (_res('from')) {
        $args['cat'] = _res('from');
        $term = get_term(_res('from'));
        $cat_html = '<a href="'.get_term_link($term->term_id, 'category').'" class="cat">'.$term->name.' <i><img src="'.__URL__.'/static/i/biaoqian.png"></i></a>';
    }

    $posts = new WP_Query($args);
    
    while ($posts->have_posts()) {
        $posts->the_post();
        if (get_the_post_thumbnail_url()) {
            $pic = get_the_post_thumbnail_url();
        } else {
            $pic = __URL__.'/static/pic/'.rand(1, 8).'.jpg';
        }

        $tgs = get_the_tags(get_the_ID());
        $tg_html='';
        if ($tgs != false) {
            foreach ($tgs as $k => $v) {
                $tg_html .= '<a href="'.get_term_link($v, 'post_tag').'?from='._res('from').'">'.$v->name.'</a>';
            }
        }
        $pp .= '<article>
                <div class="pic">
                    <a href="'.get_the_permalink().'" class="thumb" style="background: url('.$pic.') center no-repeat; background-size: cover;"><img src="'.get_the_post_thumbnail_url().'" alt="'.get_the_title().'"></a>
                    <div class="cat">'.$tg_html.' <i><img src="'.__URL__.'/static/i/biaoqian.png"></i></div>
                </div>
                <div class="content"><a class="tit1" href="'.get_the_permalink().'">'.get_the_title().'</a> </div>
                <div class="desc">'.mb_strimwidth(strip_tags(apply_filters('the_content', get_the_content())), 0, 120, "…").'</div>
            </article>';
    }
    wp_reset_query();
    wp_reset_postdata();
    if (is_object($term)) {
        $catcnt = get_field('catcnt', 'category_'.$term->term_id);
        if ($paged == 1 && $catcnt['sticky_post']) {
            $top_post = $catcnt['sticky_post'];
            $post_cat = get_term(post_parent_id($top_post->ID, 'category'));
    
            $post_top_img = $catcnt['sticky_post_bg'] ? $catcnt['sticky_post_bg'] : get_the_post_thumbnail_url($top_post->ID);
    
            $tgs = get_the_tags($top_post->ID);
            $tg_html='';
            if ($tgs != false) {
                foreach ($tgs as $k => $v) {
                    $tg_html .= '<a href="'.get_term_link($v, 'post_tag').'?from='._res('from').'">'.$v->name.'</a>';
                }
            }
            $top_html = '<div class="post-top">
                <article>
                    <div class="pic">
                        <a href="'.get_permalink($top_post).'" class="thumb"><img src="'.$post_top_img.'" alt="'.$top_post->post_title.'"></a>
                        <div class="cat">'.$tg_html.' <i><img src="'.__URL__.'/static/i/biaoqian.png"></i></div>
                    </div>
                    <div class="content">
                        <a class="tit1" href="'.get_permalink($top_post).'">'.$top_post->post_title.'</a>
                        <div class="desc">'.mb_strimwidth(strip_tags(apply_filters('the_content', $top_post->post_content)), 0, 120, "…").'</div>
                    </div>
                </article>
            </div>';
        }
    }

    if ($posts->max_num_pages >1) {
        $pagination = x_pagination(5, $posts->max_num_pages);
    }
    $html = $top_html.'<div class="post-lists"><div class="lists">'.$pp.'</div></div><div class="pagination">'.$pagination.'</div>';
    return $html;
}

function dc_lists()
{
    if (_res('action') == 'elementor') {
        return false;
    }
    $cat = get_queried_object();

    $paged = (get_query_var('page')) ? get_query_var('page') : 1;

    $args = [
        'tag_id' => get_queried_object_id(),
        'posts_per_page' => 24,
        'paged' => $paged,
    ];

    if (_res('from')) {
        $args['cat'] = _res('from');
        $term = get_term(_res('from'));
        $cat_html = '<a href="'.get_term_link($term->term_id, 'category').'" class="cat">'.$term->name.' <i><img src="'.__URL__.'/static/i/biaoqian-blue.png"></i></a>';
    }

    $posts = new WP_Query($args);
    
    while ($posts->have_posts()) {
        $posts->the_post();
        if (get_the_post_thumbnail_url()) {
            $pic = get_the_post_thumbnail_url();
        } else {
            $pic = __URL__.'/static/pic/'.rand(1, 8).'.jpg';
        }

        $tgs = get_the_tags(get_the_ID());
        $tg_html='';
        if ($tgs != false) {
            foreach ($tgs as $k => $v) {
                $tg_html .= '<a href="'.get_term_link($v, 'post_tag').'?from='._res('from').'">'.$v->name.'</a>';
            }
        }
        $pp .= '<article>
                <div class="pic">
                    <a href="'.get_the_permalink().'" class="thumb" style="background: url('.$pic.') center no-repeat; background-size: cover;"><img src="'.get_the_post_thumbnail_url().'" alt="'.get_the_title().'"></a>
                </div>
                <div class="content"><a class="tit1" href="'.get_the_permalink().'">'.get_the_title().'</a> 
                <div class="cat">'.$tg_html.' <i><img src="'.__URL__.'/static/i/biaoqian-blue.png"></i></div>
                </div>
                <div class="desc">'.mb_strimwidth(strip_tags(apply_filters('the_content', get_the_content())), 0, 120, "…").'</div>
            </article>';
    }
    wp_reset_query();
    wp_reset_postdata();
//     if (is_object($term)) {
//         $catcnt = get_field('catcnt', 'category_'.$term->term_id);
//         if ($paged == 1 && $catcnt['sticky_post']) {
//             $top_post = $catcnt['sticky_post'];
//             $post_cat = get_term(post_parent_id($top_post->ID, 'category'));
    
//             $post_top_img = $catcnt['sticky_post_bg'] ? $catcnt['sticky_post_bg'] : get_the_post_thumbnail_url($top_post->ID);
    
//             $tgs = get_the_tags($top_post->ID);
//             $tg_html='';
//             if ($tgs != false) {
//                 foreach ($tgs as $k => $v) {
//                     $tg_html .= '<a href="'.get_term_link($v, 'post_tag').'?from='._res('from').'">'.$v->name.'</a>';
//                 }
//             }
//             $top_html = '<div class="post-top">
//                 <article>
//                     <div class="pic">
//                         <a href="'.get_permalink($top_post).'" class="thumb"><img src="'.$post_top_img.'" alt="'.$top_post->post_title.'"></a>
//                         <div class="cat">'.$tg_html.' <i><img src="'.__URL__.'/static/i/biaoqian.png"></i></div>
//                     </div>
//                     <div class="content">
//                         <a class="tit1" href="'.get_permalink($top_post).'">'.$top_post->post_title.'</a>
//                         <div class="desc">'.mb_strimwidth(strip_tags(apply_filters('the_content', $top_post->post_content)), 0, 120, "…").'</div>
//                     </div>
//                 </article>
//             </div>';
//         }
//     }

    if ($posts->max_num_pages >1) {
        $pagination = x_pagination(5, $posts->max_num_pages);
    }
//     $html = $top_html.'<div class="post-lists"><div class="lists">'.$pp.'</div></div><div id="dc" class="pagination">'.$pagination.'</div>';
	$html = '<div class="post-lists"><div class="lists">'.$pp.'</div></div><div id="dc" class="pagination">'.$pagination.'</div>';
    return $html;
}

function xyear_content($attrs)
{
    extract(
        shortcode_atts(
            array(
                'type' => ''
            ),
            $attrs
        )
    );
    $term= get_queried_object();
    $year = _res('y') ?:date('Y') ;

    if ($type == 'side') {
        $lists = get_cat_date_posts(6, $term->term_id);
        foreach ($lists as $y => $v) {
            $ls .= "<li class='menu-item menu-item-type-taxonomy menu-item-object-category ".($y == $year ? "current-menu-item" : '')."'><a href='".get_term_link($term)."?y={$y}' >{$y}年</a></li>";
        }
        $html = "<div id='side-menu' class='year-menu'><ul class='menu'>{$ls}</ul></div>";
    }

    if ($type == 'lists') {
        if (_res('action') == 'elementor') {
            return false;
        }
        $lists = get_cat_date_posts(6, $term->term_id, $year);
        $html ='';
        $kk = 0;
        
        foreach ($lists as $m => $posts) {
            $pp='';
            foreach ($posts as $p) {
                if ($kk<3) {
                    $is_new = '<span>最新</span>';
                } else {
                    $is_new = '';
                }
                $pp .= '<article>
                            <div class="content"><a class="tit1" href="'.get_permalink($p).'">'.$p->post_title.'</a> '.$is_new.'</div>
                            <div class="desc">'.mb_strimwidth(strip_tags(apply_filters('the_content', $p->post_content)), 0, 280, "...").'<a class="more" href="'.get_permalink($p).'">[详情]</a></div>
                        </article>';
                $kk++;
            }
            
            $count = count($posts);
            $html .='<div class="byyear-lists" id="m'.$m.'">
                <div class="hd">
                    <div class="tit">'.$m.'月<span>已发布文章'.$count.'篇</span></div>
                </div>
                <div class="bd post-lists">
                    <div class="lists">
                        '.($pp ? $pp : '<p>未找到相关的资讯。</p>').'
                    </div>
                </div>
            </div>
            ';
        }
    }

    return $html;
}

function sx_content($attrs)
{
    extract(
        shortcode_atts(
            array(
                'type' => ''
            ),
            $attrs
        )
    );
    $term= get_queried_object();
    $year = _res('y') ?:date('Y') ;

    if ($type == 'side') {
        $lists = get_cat_date_posts(6, $term->term_id,'','post_tag');
        foreach ($lists as $y => $v) {
            $ls .= "<li class='menu-item menu-item-type-taxonomy menu-item-object-category ".($y == $year ? "current-menu-item" : '')."'><a href='".get_term_link($term)."?y={$y}' >{$y}年</a></li>";
        }
        $html = "<div id='side-menu' class='year-menu'><ul class='menu'>{$ls}</ul></div>";
    }

    if ($type == 'lists') {
        if (_res('action') == 'elementor') {
            return false;
        }
        $lists = get_cat_date_posts(6, $term->term_id, $year, 'post_tag');
        $html ='';
        $kk = 0;     
        foreach ($lists as $m => $posts) {
            $pp='';
            foreach ($posts as $p) {
            if ($kk<3) {
                    $is_new = '<span>最新</span>';
                } else {
                    $is_new = '';
                }
                $pp .= '<article>
                <div class="content"><a class="tit1" href="'.get_permalink($p).'">'.$p->post_title.'</a> '.$is_new.'</div>
                <div class="article-item">
                           <div class="article-lf">
                         <div class="desc">'.mb_strimwidth(strip_tags(apply_filters('the_content', $p->post_content)), 0, 280, "...").'<a class="more" href="'.get_permalink($p).'"></a></div>
                     </div>
                     <div class="article-rg">
                     <div class="img-box">
                     <div class="article-rg-image">
                             <img src="'.get_field('sx_qr',$p->ID).'" />
                         </div>
                         <div class="note">扫码收听喜马拉雅</div>
                     </div>
                     </div>
                     </div>
                        </article>';
                $kk++;
            }
            
            $count = count($posts);
            $html .='<div class="byyear-lists" id="m'.$m.'">
                <div class="hd">
                    <div class="tit">'.$m.'月</div>
                </div>
                <div class="bd post-lists">
                    <div class="lists">
                        '.($pp ? $pp : '<p>未找到相关的资讯。</p>').'
                    </div>
                </div>
            </div>
            ';
        }
    }

    return $html;
}
function ykt_lists()
{
    if (_res('action') == 'elementor') {
        return false;
    }
    $cat = get_queried_object();

    $paged = (get_query_var('page')) ? get_query_var('page') : 1;

    $args = [
        'tag_id' => get_queried_object_id(),
        'posts_per_page' => 24,
        'paged' => $paged,
    ];
	
	$cat_html  = '<div class="bannerdesc"><img src="https://www.u-igroup.com/wp-content/uploads/2022/11/2022121551.jpg" class="attachment-large size-large" alt="" loading="lazy" srcset="https://www.u-igroup.com/wp-content/uploads/2022/11/2022121551.jpg 1036w, https://www.u-igroup.com/wp-conten
/uploads/2022/11/2022121551-768x252.jpg 768w" sizes="(max-width: 1036px) 100vw, 1036px" width="1036" height="340"></div>';
	
    if (_res('from')) {
        $args['cat'] = _res('from');
        $term = get_term(_res('from'));
        $cat_html  = '<div class="bannerdesc">'.($term->description ? $term->description : '<img src="https://www.u-igroup.com/wp-content/uploads/2022/11/2022121551.jpg" class="attachment-large size-large" alt="" loading="lazy" srcset="https://www.u-igroup.com/wp-content/uploads/2022/11/2022121551.jpg 1036w, https://www.u-igroup.com/wp-content/uploads/2022/11/2022121551-768x252.jpg 768w" sizes="(max-width: 1036px) 100vw, 1036px" width="1036" height="340">').'</div>';
    }
	
		$posts = new WP_Query($args);
     while ($posts->have_posts()) {
        $posts->the_post();
        if (get_the_post_thumbnail_url()) {
            $pic = get_the_post_thumbnail_url();
        } else {
            $pic = __URL__.'/static/pic/'.rand(1, 8).'.jpg';
        }

        $tgs = get_the_tags(get_the_ID());
        $tg_html='';
        if ($tgs != false) {
            foreach ($tgs as $k => $v) {
                $tg_html .= '<a href="'.get_term_link($v, 'post_tag').'?from='._res('from').'">'.$v->name.'</a>';
            }
        }
        $pp .= '<article>
                <div class="article-item">
                           <div class="article-lf">
						   <div class="content"><div class="tit1">'.get_the_title().'</div> </div>
                         <div class="desc">'.mb_strimwidth(strip_tags(apply_filters('the_content', get_the_content())), 0, 280, "...").'</div>
						 <div class="ykt_note">
					     <div class="datetime"><span class="name">发布日期：</span>'.get_the_date('Y.m.d').'</div>
						 <div class="qr_note"><span style="color:#AE9572;">注：</span>手机扫描二维码前往小鹅通进行课程观看，个人信息采集请参照“小鹅通个人信息采集隐私条规”。</div>
					 </div>
                     </div>
                     <div class="article-rg">
                     <div class="img-box">
                     <div class="article-rg-image">
                             <img src="'.get_field('video_qr',get_the_ID()).'" />
                         </div>
                         <div class="note">扫码观看课程</div>
                     </div>
                     </div>
                     </div>
            </article>';
    }
    wp_reset_query();
    wp_reset_postdata();


    if ($posts->max_num_pages >1) {
        $pagination = x_pagination(5, $posts->max_num_pages);
    }
	$html = $cat_html.'<div class="byyear-lists ykt-lists"><div class="ykt post-lists"><div class="lists">'.($pp ? $pp : '<p>未找到相关的资讯。</p>').'</div></div><div id="dc" class="pagination">'.$pagination.'</div></div>';
	
    return $html;
}


function xdownload($attrs)
{
    extract(
        shortcode_atts(
            array(
                'type' => ''
            ),
            $attrs
        )
    );
    global $post;
    $file = get_field('file');
    if (!$file) {
        return false;
    }

    $html = "<div class='download-btn'><a href='javascript:;' class='btn' data-id='{$post->ID}'>"._x('研报下载', 'wpme')." <img src='".__URL__."/static/i/icon-xiazai.png' alt=></a><span>{$file['subtype']}</span></div>";
    return $html;
}

function post_author($attrs)
{
    extract(
        shortcode_atts(
            array(
                'field' => ''
            ),
            $attrs
        )
    );
    global $post;
    $author = get_field('author');
    if (!$author) {
        return false;
    }

    $field_html = $authors ='';
    
    foreach ($author as $v) {
        $authors .="
            <div class='author-box'>
                <div class='img'><span><img src='".get_field('pic', 'user_'.$v->ID)."'></span></div>
                <div class='txt'>
                    <h3>".get_field('name', 'user_'.$v->ID)."</h3>
                    <div class='job'>".get_field('job', 'user_'.$v->ID)."</div>
                    <div><img src='".__URL__."/static/i/icon-location.png'></div>
                    <div class='location'>".get_field('address', 'user_'.$v->ID)."</div>
                    <div class='tel'>".get_field('tel', 'user_'.$v->ID)."</div>
                </div>
            </div>";
        if ($field == 'intro') {
            $field_html = get_field('intro', 'user_'.$v->ID);
        }
    }

    if ($field == 'intro') {
        return $field_html;
    } else {
        return "<div class='authors'>$authors</div>";
    }
}

function xviews()
{
    return post_views(0);
}

function post_navigation()
{
    $prev = get_post_pagination();
    $next = get_post_pagination('next');
    if ($prev) {
        if (get_the_post_thumbnail_url($prev)) {
            $pic = get_the_post_thumbnail_url($prev);
        } else {
            $pic = __URL__.'/static/pic/'.rand(1, 8).'.jpg';
        }
        $left = '<a href="'.get_permalink($prev).'">
            <div class="pic"><img src="'.$pic.'" alt=""></div>
            <div class="tit">'.mb_strimwidth(strip_tags(apply_filters('the_title', $prev->post_title)), 0, 36, "...").'</div>
            <span class="navi">< 上一篇</span>
        </a>';
    } else {
        $left = '<p>暂无</p>';
    }
    if ($next) {
        if (get_the_post_thumbnail_url($next)) {
            $pic = get_the_post_thumbnail_url($next);
        } else {
            $pic = __URL__.'/static/pic/'.rand(1, 8).'.jpg';
        }
        $right = '<a href="'.get_permalink($next).'">
            <div class="tit">'.mb_strimwidth(strip_tags(apply_filters('the_title', $next->post_title)), 0, 36, "...").'</div>
            <div class="pic"><img src="'.$pic.'" alt=""></div>
            <span class="navi">下一篇 ></span>
        </a>';
    } else {
        $right = '<p>暂无</p>';
    }
    $html = '<div class="pgnav">
        <div class="left">'.$left.'</div>
        <div class="right">'.$right.'</div>
    </div>';
    return $html;
}

function xmap($attrs)
{
    extract(
        shortcode_atts(
            array(
                'id' => ''
            ),
            $attrs
        )
    );
    $maps = gop('biaozhu');
    if ($id == 'cn') {
        $bg = __URL__ . '/static/i/map-cn.png';
        $poins_list = $maps['map_cn'];
    } else {
        $bg = __URL__ . '/static/i/map-gb.jpg';
        $poins_list = $maps['map_gb'];
    }
    foreach ($poins_list as $k => $v) {
        $pos .= '<div class="box '.$v['sign'].'" style="left:'.$v['x'].'px;bottom:'.$v['y'].'px;">
        <div class="cnt">
            <div class="hd">'.$v['name'].'</div>
            <div class="bd">'.$v['address'].'</div>
        </div>
        <div class="point"></div>
    </div>';
    }

    $html = '<div class="map-block">
        <div class="img"><img src="'.$bg.'" alt=""></div>
        <div class="map-list">'.$pos.'</div>
    </div>';

    
    return $html;
}