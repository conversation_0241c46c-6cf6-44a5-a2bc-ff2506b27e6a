<?php

include_once __PROOT__ . '/inc/Redis.php';
include_once __PROOT__ . '/inc/main.php';
include_once __PROOT__ . '/inc/admin.php';
// include_once __PROOT__ . '/inc/seo.php';
include_once __ROOT__ . '/config/acf-config.php';

class StarterSite
{
    /** Add timber support. */
    public function __construct()
    {
        add_action('after_setup_theme', array($this, 'theme_supports'));
        add_action('init', array($this, 'register_post_types'));
        add_action('init', array($this, 'register_taxonomies'));
        add_action('init', array($this, 'register_post_status'));
        add_action('wp_before_admin_bar_render', array($this, 'custom_toolbar'), 999);
        // add_filter('timber/twig', array($this, 'add_to_twig'));
        // parent::__construct();
    }
    public function theme_supports()
    {
        $file = __ROOT__ . '/config/theme_supports.php';
        if (file_exists($file)) {
            include_once $file;
        } else {
            include_once __PROOT__ . '/config/theme_supports.php';
        }
    }

    /** This is where you can register custom post types. */
    public function register_post_types()
    {
        $file = __ROOT__ . '/config/post_types.php';
        if (file_exists($file)) {
            include_once $file;
        } else {
            include_once __PROOT__ . '/config/post_types.php';
        }
    }

    /** This is where you can register custom taxonomies. */
    public function register_taxonomies()
    {
        $file = __ROOT__ . '/config/taxonomies.php';
        if (file_exists($file)) {
            include_once $file;
        } else {
            include_once __PROOT__ . '/config/taxonomies.php';
        }
    }

    public function register_post_status()
    {
        $file = __ROOT__ . '/config/post_status.php';
        if (file_exists($file)) {
            include_once $file;
        } else {
            include_once __PROOT__ . '/config/post_status.php';
        }
    }

    // Add Toolbar Menus
    public function custom_toolbar()
    {
        $file = __ROOT__ . '/config/toolbars.php';
        if (file_exists($file)) {
            include_once $file;
        } else {
            include_once __PROOT__ . '/config/toolbars.php';
        }
    }
}

new StarterSite();
