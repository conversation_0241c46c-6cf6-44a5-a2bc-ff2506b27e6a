{"name": "altorouter/altorouter", "description": "A lightning fast router for PHP", "keywords": ["router", "routing", "lightweight"], "homepage": "https://github.com/dannyvankooten/AltoRouter", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://dannyvankooten.com/"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/koenpunt"}, {"name": "niahoo", "homepage": "https://github.com/niahoo"}], "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "4.5.*", "codeclimate/php-test-reporter": "dev-master"}, "autoload": {"classmap": ["AltoRouter.php"]}, "scripts": {"test": "vendor/bin/phpunit"}}