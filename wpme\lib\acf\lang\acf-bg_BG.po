msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2017-06-27 15:36+1000\n"
"PO-Revision-Date: 2018-02-06 10:03+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: bg_BG\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.1\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:63
msgid "Advanced Custom Fields"
msgstr "Модерни потребителски полета"

#: acf.php:355 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Групи полета"

#: acf.php:356
msgid "Field Group"
msgstr "Групa полета"

#: acf.php:357 acf.php:389 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:574
msgid "Add New"
msgstr "Създаване"

#: acf.php:358
msgid "Add New Field Group"
msgstr "Създаване на нова група полета"

#: acf.php:359
msgid "Edit Field Group"
msgstr "Редактиране на група полета"

#: acf.php:360
msgid "New Field Group"
msgstr "Нова група полета"

#: acf.php:361
msgid "View Field Group"
msgstr "Преглед на група полета"

#: acf.php:362
msgid "Search Field Groups"
msgstr "Търсене на групи полета"

#: acf.php:363
msgid "No Field Groups found"
msgstr "Няма открити групи полета"

#: acf.php:364
msgid "No Field Groups found in Trash"
msgstr "Няма открити групи полета в кошчето"

#: acf.php:387 includes/admin/admin-field-group.php:182
#: includes/admin/admin-field-group.php:275
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:857
msgid "Fields"
msgstr "Полета"

#: acf.php:388
msgid "Field"
msgstr "Поле"

#: acf.php:390
msgid "Add New Field"
msgstr "Добавяне на ново поле"

#: acf.php:391
msgid "Edit Field"
msgstr "Редактиране на поле"

#: acf.php:392 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Ново поле"

#: acf.php:393
msgid "View Field"
msgstr "Преглед на поле"

#: acf.php:394
msgid "Search Fields"
msgstr "Търсене на полета"

#: acf.php:395
msgid "No Fields found"
msgstr "Няма открити полета"

#: acf.php:396
msgid "No Fields found in Trash"
msgstr "Няма открити полета в кошчето"

#: acf.php:435 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
#, fuzzy
msgid "Inactive"
msgstr "Активно"

#: acf.php:440
#, fuzzy, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Активно <span class=\"count\">(%s)</span>"
msgstr[1] "Активни <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Групата полета бе обновена."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Групата полета бе изтрита."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Групата полета бе публикувана."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Групата полета бе запазена."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Групата полета бе изпратена."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Групата полета бе планирана."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Черновата на групата полета бе обновена."

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "Местоположение"

#: includes/admin/admin-field-group.php:184
msgid "Settings"
msgstr "Настройки"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "Преместване в кошчето. Сигурни ли сте?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr "избрано"

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr "Няма налични полета за превключване"

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "Заглавието на групата полета е задължително"

#: includes/admin/admin-field-group.php:273
#: includes/api/api-field-group.php:732
msgid "copy"
msgstr "копиране"

#: includes/admin/admin-field-group.php:274
#: includes/admin/views/field-group-field-conditional-logic.php:54
#: includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3970
msgid "or"
msgstr "или"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "Родителски полета"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr "Съседни полета"

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "Преместване на поле"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr "Това поле не може да бъде преместено докато не го запазите."

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr "Нищо"

#: includes/admin/admin-field-group.php:281 includes/input.php:257
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Промените, които сте направили, ще бъдат загубени ако излезете от тази "
"страница"

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Низът \"field_\" не може да бъде използван в началото на името на поле"

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr "Ключове на полетата"

#: includes/admin/admin-field-group.php:390
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Активно"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "Преместването бе завършено."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Полето %s сега може да бъде открито в групата полета %s"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "Затваряне на прозореца"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "Моля, изберете дестинация за това поле"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "Преместване на поле"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Активно <span class=\"count\">(%s)</span>"
msgstr[1] "Активни <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Групата полета %s бе дублирана."

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s група полета беше дублирана."
msgstr[1] "%s групи полета бяха дублирани."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Групата полета %s бе синхронизирана."

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s група полета беше синхронизирана."
msgstr[1] "%s групи полета бяха синхронизирани."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Налична е синхронизация"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:370
msgid "Title"
msgstr "Заглавие"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:397
msgid "Description"
msgstr "Описание"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Статус"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr "Персонализирайте WordPress с мощни, професионални и интуитивни полета."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:111
msgid "Changelog"
msgstr "Дневник с промени"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr ""

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Ресурси"

#: includes/admin/admin-field-groups.php:619
#, fuzzy
msgid "Website"
msgstr "Следните разширения бяха намерени като активирани на този уебсайт."

#: includes/admin/admin-field-groups.php:620
#, fuzzy
msgid "Documentation"
msgstr "Местоположение"

#: includes/admin/admin-field-groups.php:621
#, fuzzy
msgid "Support"
msgstr "Импортиране"

#: includes/admin/admin-field-groups.php:623
#, fuzzy
msgid "Pro"
msgstr "Сбогом на добавките. Здравей, PRO"

#: includes/admin/admin-field-groups.php:628
#, fuzzy, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Благодарим ви за обновяването към %s v%s!"

#: includes/admin/admin-field-groups.php:668
msgid "Duplicate this item"
msgstr "Дублиране на този елемент"

#: includes/admin/admin-field-groups.php:668
#: includes/admin/admin-field-groups.php:684
#: includes/admin/views/field-group-field.php:49
#: pro/fields/class-acf-field-flexible-content.php:573
msgid "Duplicate"
msgstr "Дублиране"

#: includes/admin/admin-field-groups.php:701
#: includes/fields/class-acf-field-google-map.php:132
#: includes/fields/class-acf-field-relationship.php:737
msgid "Search"
msgstr "Търсене"

#: includes/admin/admin-field-groups.php:760
#, php-format
msgid "Select %s"
msgstr "Избор на %s"

#: includes/admin/admin-field-groups.php:768
msgid "Synchronise field group"
msgstr "Синхронизиране на групата полета"

#: includes/admin/admin-field-groups.php:768
#: includes/admin/admin-field-groups.php:798
msgid "Sync"
msgstr "Синхронизация"

#: includes/admin/admin-field-groups.php:780
msgid "Apply"
msgstr ""

#: includes/admin/admin-field-groups.php:798
#, fuzzy
msgid "Bulk Actions"
msgstr "Групови действия"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Потребителски полета"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Обновяване на базата данни"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "Преглед на сайтове и обновяване"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr ""

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "Няма налични актуализации."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Добавки"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Грешка</b>. Списъкът с добавки не може да бъде зареден"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Информация"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Какво ново"

#: includes/admin/settings-tools.php:50
#: includes/admin/views/settings-tools-export.php:19
#: includes/admin/views/settings-tools.php:31
msgid "Tools"
msgstr "Инструменти"

#: includes/admin/settings-tools.php:147 includes/admin/settings-tools.php:380
msgid "No field groups selected"
msgstr "Няма избрани групи полета"

#: includes/admin/settings-tools.php:184
#: includes/fields/class-acf-field-file.php:174
msgid "No file selected"
msgstr "Няма избран файл"

#: includes/admin/settings-tools.php:197
msgid "Error uploading file. Please try again"
msgstr "Грешка при качване на файл. Моля, опитайте отново"

#: includes/admin/settings-tools.php:206
msgid "Incorrect file type"
msgstr "Грешен тип файл"

#: includes/admin/settings-tools.php:223
msgid "Import file empty"
msgstr "Файлът за импортиране е празен"

#: includes/admin/settings-tools.php:331
#, fuzzy, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Импортиране на групи полета"
msgstr[1] "Импортиране на групи полета"

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "Условна логика"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "Показване на това поле ако"

#: includes/admin/views/field-group-field-conditional-logic.php:103
#: includes/locations.php:243
msgid "is equal to"
msgstr "е равно на"

#: includes/admin/views/field-group-field-conditional-logic.php:104
#: includes/locations.php:244
msgid "is not equal to"
msgstr "не е равно на"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "и"

#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Добавяне на група правила"

#: includes/admin/views/field-group-field.php:41
#: pro/fields/class-acf-field-flexible-content.php:420
#: pro/fields/class-acf-field-repeater.php:358
msgid "Drag to reorder"
msgstr "Плъзнете, за да пренаредите"

#: includes/admin/views/field-group-field.php:45
#: includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "Редактиране на поле"

#: includes/admin/views/field-group-field.php:48
#: includes/fields/class-acf-field-image.php:140
#: includes/fields/class-acf-field-link.php:152
#: pro/fields/class-acf-field-gallery.php:357
msgid "Edit"
msgstr "Редактиране"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "Дублиране на поле"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "Преместване на поле в друга група"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "Преместване"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "Изтриване на поле"

#: includes/admin/views/field-group-field.php:51
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Delete"
msgstr "Изтриване"

#: includes/admin/views/field-group-field.php:67
msgid "Field Label"
msgstr "Етикет на полето"

#: includes/admin/views/field-group-field.php:68
msgid "This is the name which will appear on the EDIT page"
msgstr "Това е името, което ще се покаже на страницата за редакция"

#: includes/admin/views/field-group-field.php:78
msgid "Field Name"
msgstr "Име на полето"

#: includes/admin/views/field-group-field.php:79
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Една дума, без интервали. Долни черти и тирета са позволени"

#: includes/admin/views/field-group-field.php:89
msgid "Field Type"
msgstr "Тип на полето"

#: includes/admin/views/field-group-field.php:101
#: includes/fields/class-acf-field-tab.php:102
msgid "Instructions"
msgstr "Инструкции"

#: includes/admin/views/field-group-field.php:102
msgid "Instructions for authors. Shown when submitting data"
msgstr "Инструкции за автори. Показват се когато се изпращат данни"

#: includes/admin/views/field-group-field.php:111
msgid "Required?"
msgstr "Задължително?"

#: includes/admin/views/field-group-field.php:134
msgid "Wrapper Attributes"
msgstr "Атрибути"

#: includes/admin/views/field-group-field.php:140
msgid "width"
msgstr "широчина"

#: includes/admin/views/field-group-field.php:155
msgid "class"
msgstr "клас"

#: includes/admin/views/field-group-field.php:168
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:180
msgid "Close Field"
msgstr "Затваряне на полето"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Ред"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-checkbox.php:317
#: includes/fields/class-acf-field-radio.php:321
#: includes/fields/class-acf-field-select.php:530
#: pro/fields/class-acf-field-flexible-content.php:599
msgid "Label"
msgstr "Етикет"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:970
#: pro/fields/class-acf-field-flexible-content.php:612
msgid "Name"
msgstr "Име"

#: includes/admin/views/field-group-fields.php:7
#, fuzzy
msgid "Key"
msgstr "Ключ на полето"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Тип"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Няма полета. Натиснете бутона <strong>+ Добавяне на поле</strong> за да "
"създадете първото си поле."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Добавяне на поле"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Правила"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Създаване на група правила, определящи кои екрани за редактиране ще "
"използват тези модерни потребителски полета"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Стил"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Стандартен (WordPress кутия)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Без WordPress кутия"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Позиция"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Високо (след заглавието)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Нормално (след съдържанието)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Отстрани"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Позиция на етикета"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:116
msgid "Top aligned"
msgstr "Отгоре"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:117
msgid "Left aligned"
msgstr "Отляво"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Позиция на инструкциите"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Под етикетите"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Под полетата"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Пореден №"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Групите полета с по-малък пореден номер ще бъдат показани първи"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Показани в списъка с групи полета"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Скриване от екрана"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Изберете</b> елементи, които да <b>скриете</b> от екрана."

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Ако множество групи полета са показани на екрана, опциите на първата група "
"полета ще бъдат използвани (тази с най-малкия пореден номер)"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Постоянна връзка"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Редактор на съдържание"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Откъс"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Дискусия"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Коментари"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Ревизии"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "Кратко име"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Автор"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Формат"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Атрибути на страницата"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:751
msgid "Featured Image"
msgstr "Главна снимка"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Категории"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Етикети"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Изпращане на проследяващи връзки"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Показване на тази група полета ако"

#: includes/admin/views/install-network.php:4
#, fuzzy
msgid "Upgrade Sites"
msgstr "Забележки за обновяването"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr "Модерни потребителски полета - Обновяване на базата данни"

#: includes/admin/views/install-network.php:11
#, fuzzy, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Следните сайтове имат нужда от обновяване на базата данни. Изберете тези, "
"които искате да обновите и натиснете на \"Обновяване на базата данни\"."

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Сайт"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Сайтът изисква обновяване на базата данни от %s до %s"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "Сайтът няма нужда от обновяване"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Обновяването на базата данни бе завършено. <a href=\"%s\">Връщане към "
"мрежовото табло</a>"

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Силно Ви препоръчваме да архивирате вашата база данни преди да продължите. "
"Сигурни ли сте, че искате да продължите с обновяването?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Обновяването завърши"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Обновяване на данните до версия %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:36
msgid "Repeater"
msgstr "Повторител"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:36
msgid "Flexible Content"
msgstr "Гъвкаво съдържание"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:36
msgid "Gallery"
msgstr "Галерия"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:13
msgid "Options Page"
msgstr "Страница с опции"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Изисква се обновяване на базата данни"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Благодарим ви за обновяването към %s v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Преди да започнете да използвате новите страхотни функции, моля обновете "
"базата данни до последната версия."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Прочитане на задачите за обновяване..."

#: includes/admin/views/install.php:11
#, fuzzy, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Обновяването на базата данни бе завършено. <a href=\"%s\">Връщане към "
"мрежовото табло</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Сваляне и инсталиране"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Инсталирано"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Добре дошли в Модерни потребителски полета"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Благодарим, че обновихте! Модерни потребителски полета %s сега е по-голям и "
"по-добър от всякога. Надяваме се че ще Ви хареса."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "По-удобна работа с потребителски полета"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Подобрена ползваемост"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Включването на популярната библиотека Select2 подобри използването и "
"скоростта на множество полета, включително обект-публикация, връзка към "
"страница, таксономия и поле за избор."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Подобрен дизайн"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Много от полетата претърпяха визуални подобрения и сега изглеждат по-добре "
"от всякога! Забележими промени могат да се видят по галерията, полето за "
"връзка и oEmbed полето!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Подобрени данни"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Подобряването на архитектурата на данните позволи вложените полета да "
"съществуват независимо от своите родители. Това позволява да ги местите "
"извън родителите си!"

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Сбогом на добавките. Здравей, PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "Представяме Ви Модерни потребителски полета PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""
"Променяме начина по който Ви предоставяме платената функционалност по "
"вълнуващ начин!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Всички 4 платени добавки бяха обединени в една нова <a href=\"%s\">PRO "
"версия</a>. С наличните личен лиценз и този за разработчици, платената "
"функционалност е по-достъпна от всякога!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Мощни функции"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"PRO версията съдържа мощни функции като повторяеми полета, гъвкави "
"оформления на съдържанието, красиво поле за галерия и възможността да "
"създавате допълнителни страници с опции в администрацията."

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Научете повече за <a href=\"%s\">PRO функциите</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Лесно обновяване"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"За да направите обновяването лесно, <a href=\"%s\">влезте в профила си</a> и "
"вземете вашето безплатно PRO копие!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"Също така написахме <a href=\"%s\">съветник по обновяването</a> за да "
"отговорим на всякакви въпроси, но ако имате някакви други въпроси, моля "
"свържете се с нашия отдел <a href=\"%s\">Поддръжка</a>"

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Под капака"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "По-умни настройки на полетата"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "Вече записваме настройките на полетата като индивидуални публикации"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Повече AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr ""
"Още повече полета използват AJAX-базирано търсене, за да ускорят зареждането "
"на страниците"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "Локален JSON"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "Новия автоматичен експорт към JSON увеличава скоростта"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "По-добър контрол на версиите"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr ""
"Новия автоматичен експорт към JSON позволява настройките на полетата да "
"бъдат под контрол на версиите"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "Заменихме XML с JSON"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "Импортирането и експортирането вече използват JSON вместо XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Нови формуляри"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"Полетата вече могат да бъдат закачени към коментари, джаджи и "
"потребителските формуляри!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "Ново поле за вграждане на съдържание бе добавено"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Нова галерия"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "Полето за галерия претърпя сериозни визуални подобрения"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Нови настройки"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"Бяха добавени настройки на групите полета за поставяне на етикет и инструкции"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "По-добри форми в сайта"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form() вече може да създава нови публикации при изпращане"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "По-добра валидация"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr "Валидацията на формулярите вече се прави с PHP + AJAX вместо само с JS"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Поле за връзка"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Нови настройки на полето за връзка за 'Филтри' (търсене, тип публикация, "
"таксономия)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Местене на полета"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""
"Новата функционалност на групите полета Ви позволява да местите полета "
"измежду групите и родителите"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:36
msgid "Page Link"
msgstr "Връзка към страница"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Нова група архиви в page_link полето"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "По-добри страници с опции"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"Новите функции за страници с опции позволяват създаването както на "
"родителски страници, така и на страници-деца."

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Смятаме, че ще харесате промените в %s."

#: includes/admin/views/settings-tools-export.php:23
msgid "Export Field Groups to PHP"
msgstr "Експортиране на групите полета към PHP"

#: includes/admin/views/settings-tools-export.php:27
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Следния код може да се използва, за да регистрирате локална версия на "
"избраните групи полета. Локалната група полета може да помогне с по-бързо "
"зареждане, контрол на версиите и динамични настройки. Просто копирайте и "
"сложете кода във файла functions.php на темата си или го сложете във външен "
"файл."

#: includes/admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "Избор на групи полета"

#: includes/admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "Експортиране на групи полета"

#: includes/admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Изберете групите полета които искате да експортирате и после изберете "
"желания метод. Използвайте бутона за сваляне за да създадете .json файл, "
"които можете да импортирате в друга инсталация. Използвайте бутона за "
"генериране за да експортирате към PHP код, които можете да поставите в "
"темата си."

#: includes/admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "Сваляне на експортирания файл"

#: includes/admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "Генериране на код"

#: includes/admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "Импортиране на групи полета"

#: includes/admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Изберете JSON файла, който искате да импортирате. Когато натиснете бутона за "
"импортиране, групите полета ще бъдат импортирани."

#: includes/admin/views/settings-tools.php:77
#: includes/fields/class-acf-field-file.php:46
msgid "Select File"
msgstr "Избор на файл"

#: includes/admin/views/settings-tools.php:86
msgid "Import"
msgstr "Импортиране"

#: includes/api/api-helpers.php:856
msgid "Thumbnail"
msgstr "Картинка"

#: includes/api/api-helpers.php:857
msgid "Medium"
msgstr "Средна"

#: includes/api/api-helpers.php:858
msgid "Large"
msgstr "Голяма"

#: includes/api/api-helpers.php:907
msgid "Full Size"
msgstr "Пълен размер"

#: includes/api/api-helpers.php:1248 includes/api/api-helpers.php:1837
#: pro/fields/class-acf-field-clone.php:1042
msgid "(no title)"
msgstr "(без заглавие)"

#: includes/api/api-helpers.php:1874
#: includes/fields/class-acf-field-page_link.php:284
#: includes/fields/class-acf-field-post_object.php:283
#: includes/fields/class-acf-field-taxonomy.php:992
#, fuzzy
msgid "Parent"
msgstr "Горно ниво страница (родител)"

#: includes/api/api-helpers.php:3891
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Ширината на изображението трябва да бъде поне %d пиксела."

#: includes/api/api-helpers.php:3896
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Ширината на изображението не трябва да надвишава %d пиксела."

#: includes/api/api-helpers.php:3912
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Височината на изображението трябва да бъде поне %d пиксела."

#: includes/api/api-helpers.php:3917
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Височината на изображението не трябва да надвишава %d пиксела."

#: includes/api/api-helpers.php:3935
#, php-format
msgid "File size must be at least %s."
msgstr "Размерът на файла трябва да бъде поне %s."

#: includes/api/api-helpers.php:3940
#, php-format
msgid "File size must must not exceed %s."
msgstr "Размерът на файла трябва да не надвишава %s."

#: includes/api/api-helpers.php:3974
#, php-format
msgid "File type must be %s."
msgstr "Типът на файла трябва да бъде %s."

#: includes/fields.php:144
msgid "Basic"
msgstr "Основен"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Съдържание"

#: includes/fields.php:146
msgid "Choice"
msgstr "Избор"

#: includes/fields.php:147
msgid "Relational"
msgstr "Релационен"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149 includes/fields/class-acf-field-checkbox.php:286
#: includes/fields/class-acf-field-group.php:485
#: includes/fields/class-acf-field-radio.php:300
#: pro/fields/class-acf-field-clone.php:889
#: pro/fields/class-acf-field-flexible-content.php:569
#: pro/fields/class-acf-field-flexible-content.php:618
#: pro/fields/class-acf-field-repeater.php:514
msgid "Layout"
msgstr "Шаблон"

#: includes/fields.php:305
msgid "Field type does not exist"
msgstr "Типът поле не съществува"

#: includes/fields.php:305
msgid "Unknown"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:36
#: includes/fields/class-acf-field-taxonomy.php:786
msgid "Checkbox"
msgstr "Отметка"

#: includes/fields/class-acf-field-checkbox.php:150
msgid "Toggle All"
msgstr "Превключване на всички"

#: includes/fields/class-acf-field-checkbox.php:207
#, fuzzy
msgid "Add new choice"
msgstr "Добавяне на ново поле"

#: includes/fields/class-acf-field-checkbox.php:246
#: includes/fields/class-acf-field-radio.php:250
#: includes/fields/class-acf-field-select.php:466
msgid "Choices"
msgstr "Опции"

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "Enter each choice on a new line."
msgstr "Въведете всяка опция на нов ред."

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "For more control, you may specify both a value and label like this:"
msgstr "За повече контрол, можете да уточните и стойност и етикет, например:"

#: includes/fields/class-acf-field-checkbox.php:247
#: includes/fields/class-acf-field-radio.php:251
#: includes/fields/class-acf-field-select.php:467
msgid "red : Red"
msgstr "red : Red"

#: includes/fields/class-acf-field-checkbox.php:255
#, fuzzy
msgid "Allow Custom"
msgstr "Позволяване на празна стойност?"

#: includes/fields/class-acf-field-checkbox.php:260
msgid "Allow 'custom' values to be added"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:266
#, fuzzy
msgid "Save Custom"
msgstr "Преместване на поле"

#: includes/fields/class-acf-field-checkbox.php:271
#, fuzzy
msgid "Save 'custom' values to the field's choices"
msgstr "Запазване на стойностите 'друго' към опциите на полето"

#: includes/fields/class-acf-field-checkbox.php:277
#: includes/fields/class-acf-field-color_picker.php:146
#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-radio.php:291
#: includes/fields/class-acf-field-select.php:475
#: includes/fields/class-acf-field-text.php:142
#: includes/fields/class-acf-field-textarea.php:139
#: includes/fields/class-acf-field-true_false.php:150
#: includes/fields/class-acf-field-url.php:114
#: includes/fields/class-acf-field-wysiwyg.php:436
msgid "Default Value"
msgstr "Стойност по подразбиране"

#: includes/fields/class-acf-field-checkbox.php:278
#: includes/fields/class-acf-field-select.php:476
msgid "Enter each default value on a new line"
msgstr "Въведете всяка стойност по подразбиране на нов ред"

#: includes/fields/class-acf-field-checkbox.php:292
#: includes/fields/class-acf-field-radio.php:306
msgid "Vertical"
msgstr "Вертикален"

#: includes/fields/class-acf-field-checkbox.php:293
#: includes/fields/class-acf-field-radio.php:307
msgid "Horizontal"
msgstr "Хоризонтален"

#: includes/fields/class-acf-field-checkbox.php:300
msgid "Toggle"
msgstr "Превключване"

#: includes/fields/class-acf-field-checkbox.php:301
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Прибавете допълнителна отметка за да превключите всички опции"

#: includes/fields/class-acf-field-checkbox.php:310
#: includes/fields/class-acf-field-file.php:219
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:180
#: includes/fields/class-acf-field-radio.php:314
#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Return Value"
msgstr "Върната стойност"

#: includes/fields/class-acf-field-checkbox.php:311
#: includes/fields/class-acf-field-file.php:220
#: includes/fields/class-acf-field-image.php:207
#: includes/fields/class-acf-field-link.php:181
#: includes/fields/class-acf-field-radio.php:315
msgid "Specify the returned value on front end"
msgstr "Уточнява върнатата стойност в сайта"

#: includes/fields/class-acf-field-checkbox.php:316
#: includes/fields/class-acf-field-radio.php:320
#: includes/fields/class-acf-field-select.php:529
#, fuzzy
msgid "Value"
msgstr "%s стойност е задължителна"

#: includes/fields/class-acf-field-checkbox.php:318
#: includes/fields/class-acf-field-radio.php:322
#: includes/fields/class-acf-field-select.php:531
msgid "Both (Array)"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:36
msgid "Color Picker"
msgstr "Избор на цвят"

#: includes/fields/class-acf-field-color_picker.php:83
msgid "Clear"
msgstr "Изчистване"

#: includes/fields/class-acf-field-color_picker.php:84
msgid "Default"
msgstr "По подразбиране"

#: includes/fields/class-acf-field-color_picker.php:85
msgid "Select Color"
msgstr "Избор на цвят"

#: includes/fields/class-acf-field-color_picker.php:86
msgid "Current Color"
msgstr "Текущ цвят"

#: includes/fields/class-acf-field-date_picker.php:36
msgid "Date Picker"
msgstr "Избор на дата"

#: includes/fields/class-acf-field-date_picker.php:44
#, fuzzy
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Готово"

#: includes/fields/class-acf-field-date_picker.php:45
#, fuzzy
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Днес"

#: includes/fields/class-acf-field-date_picker.php:46
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:47
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:48
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:223
#: includes/fields/class-acf-field-date_time_picker.php:197
#: includes/fields/class-acf-field-time_picker.php:127
msgid "Display Format"
msgstr "Формат на показване"

#: includes/fields/class-acf-field-date_picker.php:224
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-time_picker.php:128
msgid "The format displayed when editing a post"
msgstr "Форматът, показан при редакция на публикация"

#: includes/fields/class-acf-field-date_picker.php:232
#: includes/fields/class-acf-field-date_picker.php:263
#: includes/fields/class-acf-field-date_time_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:224
#: includes/fields/class-acf-field-time_picker.php:135
#: includes/fields/class-acf-field-time_picker.php:150
#, fuzzy
msgid "Custom:"
msgstr "Модерни потребителски полета"

#: includes/fields/class-acf-field-date_picker.php:242
#, fuzzy
msgid "Save Format"
msgstr "Запази формата"

#: includes/fields/class-acf-field-date_picker.php:243
#, fuzzy
msgid "The format used when saving a value"
msgstr "Форматът, показан при редакция на публикация"

#: includes/fields/class-acf-field-date_picker.php:253
#: includes/fields/class-acf-field-date_time_picker.php:214
#: includes/fields/class-acf-field-post_object.php:447
#: includes/fields/class-acf-field-relationship.php:778
#: includes/fields/class-acf-field-select.php:524
#: includes/fields/class-acf-field-time_picker.php:142
msgid "Return Format"
msgstr "Формат на върнатите данни"

#: includes/fields/class-acf-field-date_picker.php:254
#: includes/fields/class-acf-field-date_time_picker.php:215
#: includes/fields/class-acf-field-time_picker.php:143
msgid "The format returned via template functions"
msgstr "Форматът, който се връща от шаблонните функции"

#: includes/fields/class-acf-field-date_picker.php:272
#: includes/fields/class-acf-field-date_time_picker.php:231
msgid "Week Starts On"
msgstr "Седмицата започва с"

#: includes/fields/class-acf-field-date_time_picker.php:36
#, fuzzy
msgid "Date Time Picker"
msgstr "Избор на дата и час"

#: includes/fields/class-acf-field-date_time_picker.php:44
#, fuzzy
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Затваряне на полето"

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:47
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:48
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:51
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:52
#, fuzzy
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Импортирането и експортирането вече използват JSON вместо XML"

#: includes/fields/class-acf-field-date_time_picker.php:53
#, fuzzy
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Готово"

#: includes/fields/class-acf-field-date_time_picker.php:54
#, fuzzy
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Избор"

#: includes/fields/class-acf-field-date_time_picker.php:56
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:57
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:60
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:61
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr ""

#: includes/fields/class-acf-field-email.php:36
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-email.php:134
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-radio.php:292
#: includes/fields/class-acf-field-text.php:143
#: includes/fields/class-acf-field-textarea.php:140
#: includes/fields/class-acf-field-url.php:115
#: includes/fields/class-acf-field-wysiwyg.php:437
msgid "Appears when creating a new post"
msgstr "Появява се при създаване на нова публикация"

#: includes/fields/class-acf-field-email.php:142
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:134
#: includes/fields/class-acf-field-text.php:151
#: includes/fields/class-acf-field-textarea.php:148
#: includes/fields/class-acf-field-url.php:123
msgid "Placeholder Text"
msgstr "Текст при липса на стойност"

#: includes/fields/class-acf-field-email.php:143
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:135
#: includes/fields/class-acf-field-text.php:152
#: includes/fields/class-acf-field-textarea.php:149
#: includes/fields/class-acf-field-url.php:124
msgid "Appears within the input"
msgstr "Показва се в полето при липса на стойност"

#: includes/fields/class-acf-field-email.php:151
#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-password.php:143
#: includes/fields/class-acf-field-text.php:160
msgid "Prepend"
msgstr "Поставяне в началото"

#: includes/fields/class-acf-field-email.php:152
#: includes/fields/class-acf-field-number.php:164
#: includes/fields/class-acf-field-password.php:144
#: includes/fields/class-acf-field-text.php:161
msgid "Appears before the input"
msgstr "Показва се преди полето"

#: includes/fields/class-acf-field-email.php:160
#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-password.php:152
#: includes/fields/class-acf-field-text.php:169
msgid "Append"
msgstr "Поставяне в края"

#: includes/fields/class-acf-field-email.php:161
#: includes/fields/class-acf-field-number.php:173
#: includes/fields/class-acf-field-password.php:153
#: includes/fields/class-acf-field-text.php:170
msgid "Appears after the input"
msgstr "Показва се след полето"

#: includes/fields/class-acf-field-file.php:36
msgid "File"
msgstr "Файл"

#: includes/fields/class-acf-field-file.php:47
msgid "Edit File"
msgstr "Редактиране на файл"

#: includes/fields/class-acf-field-file.php:48
msgid "Update File"
msgstr "Актуализация на файла"

#: includes/fields/class-acf-field-file.php:49
#: includes/fields/class-acf-field-image.php:54 includes/media.php:57
#: pro/fields/class-acf-field-gallery.php:55
msgid "Uploaded to this post"
msgstr "Прикачени към тази публикация"

#: includes/fields/class-acf-field-file.php:145
#, fuzzy
msgid "File name"
msgstr "Име на файла"

#: includes/fields/class-acf-field-file.php:149
#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-file.php:263
#: includes/fields/class-acf-field-image.php:266
#: includes/fields/class-acf-field-image.php:295
#: pro/fields/class-acf-field-gallery.php:705
#: pro/fields/class-acf-field-gallery.php:734
msgid "File size"
msgstr "Размер на файла"

#: includes/fields/class-acf-field-file.php:174
msgid "Add File"
msgstr "Добавяне на файл"

#: includes/fields/class-acf-field-file.php:225
msgid "File Array"
msgstr "Масив от файлове"

#: includes/fields/class-acf-field-file.php:226
msgid "File URL"
msgstr "URL на файла"

#: includes/fields/class-acf-field-file.php:227
msgid "File ID"
msgstr "ID на файла"

#: includes/fields/class-acf-field-file.php:234
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:670
msgid "Library"
msgstr "Библиотека"

#: includes/fields/class-acf-field-file.php:235
#: includes/fields/class-acf-field-image.php:232
#: pro/fields/class-acf-field-gallery.php:671
msgid "Limit the media library choice"
msgstr "Ограничаване на избора на файлове"

#: includes/fields/class-acf-field-file.php:240
#: includes/fields/class-acf-field-image.php:237
#: includes/locations/class-acf-location-attachment.php:105
#: includes/locations/class-acf-location-comment.php:83
#: includes/locations/class-acf-location-nav-menu.php:106
#: includes/locations/class-acf-location-taxonomy.php:83
#: includes/locations/class-acf-location-user-form.php:91
#: includes/locations/class-acf-location-user-role.php:108
#: includes/locations/class-acf-location-widget.php:87
#: pro/fields/class-acf-field-gallery.php:676
msgid "All"
msgstr "Всички"

#: includes/fields/class-acf-field-file.php:241
#: includes/fields/class-acf-field-image.php:238
#: pro/fields/class-acf-field-gallery.php:677
msgid "Uploaded to post"
msgstr "Прикачени към публикация"

#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-image.php:245
#: pro/fields/class-acf-field-gallery.php:684
msgid "Minimum"
msgstr "Минимум"

#: includes/fields/class-acf-field-file.php:249
#: includes/fields/class-acf-field-file.php:260
msgid "Restrict which files can be uploaded"
msgstr "Ограничаване какви файлове могат да бъдат качени"

#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:713
msgid "Maximum"
msgstr "Максимум"

#: includes/fields/class-acf-field-file.php:270
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:742
msgid "Allowed file types"
msgstr "Позволени файлови типове"

#: includes/fields/class-acf-field-file.php:271
#: includes/fields/class-acf-field-image.php:304
#: pro/fields/class-acf-field-gallery.php:743
msgid "Comma separated list. Leave blank for all types"
msgstr "Списък, разделен със запетаи. Оставете празно за всички типове"

#: includes/fields/class-acf-field-google-map.php:36
msgid "Google Map"
msgstr "Google карта"

#: includes/fields/class-acf-field-google-map.php:51
msgid "Locating"
msgstr "Намиране"

#: includes/fields/class-acf-field-google-map.php:52
msgid "Sorry, this browser does not support geolocation"
msgstr "За съжаление този браузър не поддържа геолокация"

#: includes/fields/class-acf-field-google-map.php:133
msgid "Clear location"
msgstr "Изчистване на местоположение"

#: includes/fields/class-acf-field-google-map.php:134
msgid "Find current location"
msgstr "Намерете текущото местоположение"

#: includes/fields/class-acf-field-google-map.php:137
msgid "Search for address..."
msgstr "Търсене на адрес..."

#: includes/fields/class-acf-field-google-map.php:167
#: includes/fields/class-acf-field-google-map.php:178
msgid "Center"
msgstr "Центриране"

#: includes/fields/class-acf-field-google-map.php:168
#: includes/fields/class-acf-field-google-map.php:179
msgid "Center the initial map"
msgstr "Центриране на първоначалната карта"

#: includes/fields/class-acf-field-google-map.php:190
msgid "Zoom"
msgstr "Увеличаване"

#: includes/fields/class-acf-field-google-map.php:191
msgid "Set the initial zoom level"
msgstr "Задаване на ниво на първоначалното увеличение"

#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-image.php:257
#: includes/fields/class-acf-field-image.php:286
#: includes/fields/class-acf-field-oembed.php:297
#: pro/fields/class-acf-field-gallery.php:696
#: pro/fields/class-acf-field-gallery.php:725
msgid "Height"
msgstr "Височина"

#: includes/fields/class-acf-field-google-map.php:201
msgid "Customise the map height"
msgstr "Персонализиране на височината на картата"

#: includes/fields/class-acf-field-group.php:36
#, fuzzy
msgid "Group"
msgstr "Създай нова група от полета"

#: includes/fields/class-acf-field-group.php:469
#: pro/fields/class-acf-field-repeater.php:453
msgid "Sub Fields"
msgstr "Вложени полета"

#: includes/fields/class-acf-field-group.php:486
#: pro/fields/class-acf-field-clone.php:890
msgid "Specify the style used to render the selected fields"
msgstr ""

#: includes/fields/class-acf-field-group.php:491
#: pro/fields/class-acf-field-clone.php:895
#: pro/fields/class-acf-field-flexible-content.php:629
#: pro/fields/class-acf-field-repeater.php:522
msgid "Block"
msgstr "Блок"

#: includes/fields/class-acf-field-group.php:492
#: pro/fields/class-acf-field-clone.php:896
#: pro/fields/class-acf-field-flexible-content.php:628
#: pro/fields/class-acf-field-repeater.php:521
msgid "Table"
msgstr "Таблица"

#: includes/fields/class-acf-field-group.php:493
#: pro/fields/class-acf-field-clone.php:897
#: pro/fields/class-acf-field-flexible-content.php:630
#: pro/fields/class-acf-field-repeater.php:523
msgid "Row"
msgstr "Ред"

#: includes/fields/class-acf-field-image.php:36
msgid "Image"
msgstr "Изображение"

#: includes/fields/class-acf-field-image.php:51
msgid "Select Image"
msgstr "Избор на изображение"

#: includes/fields/class-acf-field-image.php:52
#: pro/fields/class-acf-field-gallery.php:53
msgid "Edit Image"
msgstr "Редактиране на изображение"

#: includes/fields/class-acf-field-image.php:53
#: pro/fields/class-acf-field-gallery.php:54
msgid "Update Image"
msgstr "Актуализация на изображението"

#: includes/fields/class-acf-field-image.php:55
msgid "All images"
msgstr "Всички изображения"

#: includes/fields/class-acf-field-image.php:142
#: includes/fields/class-acf-field-link.php:153 includes/input.php:267
#: pro/fields/class-acf-field-gallery.php:358
#: pro/fields/class-acf-field-gallery.php:546
msgid "Remove"
msgstr "Премахване"

#: includes/fields/class-acf-field-image.php:158
msgid "No image selected"
msgstr "Няма избрано изображение"

#: includes/fields/class-acf-field-image.php:158
msgid "Add Image"
msgstr "Добавяне на изображение"

#: includes/fields/class-acf-field-image.php:212
msgid "Image Array"
msgstr "Масив от изображения"

#: includes/fields/class-acf-field-image.php:213
msgid "Image URL"
msgstr "URL на изображението"

#: includes/fields/class-acf-field-image.php:214
msgid "Image ID"
msgstr "ID на изображението"

#: includes/fields/class-acf-field-image.php:221
msgid "Preview Size"
msgstr "Размер на визуализация"

#: includes/fields/class-acf-field-image.php:222
msgid "Shown when entering data"
msgstr "Показва се при въвеждане на данни"

#: includes/fields/class-acf-field-image.php:246
#: includes/fields/class-acf-field-image.php:275
#: pro/fields/class-acf-field-gallery.php:685
#: pro/fields/class-acf-field-gallery.php:714
msgid "Restrict which images can be uploaded"
msgstr "Ограничаване какви изображения могат да бъдат качени"

#: includes/fields/class-acf-field-image.php:249
#: includes/fields/class-acf-field-image.php:278
#: includes/fields/class-acf-field-oembed.php:286
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Width"
msgstr "Ширина"

#: includes/fields/class-acf-field-link.php:36
#, fuzzy
msgid "Link"
msgstr "Връзка към страница"

#: includes/fields/class-acf-field-link.php:146
#, fuzzy
msgid "Select Link"
msgstr "Избор на файл"

#: includes/fields/class-acf-field-link.php:151
msgid "Opens in a new window/tab"
msgstr ""

#: includes/fields/class-acf-field-link.php:186
#, fuzzy
msgid "Link Array"
msgstr "Масив от файлове"

#: includes/fields/class-acf-field-link.php:187
#, fuzzy
msgid "Link URL"
msgstr "URL на файла"

#: includes/fields/class-acf-field-message.php:36
#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-true_false.php:141
msgid "Message"
msgstr "Съобщение"

#: includes/fields/class-acf-field-message.php:124
#: includes/fields/class-acf-field-textarea.php:176
msgid "New Lines"
msgstr "Нови редове"

#: includes/fields/class-acf-field-message.php:125
#: includes/fields/class-acf-field-textarea.php:177
msgid "Controls how new lines are rendered"
msgstr "Контролира как се извеждат новите редове"

#: includes/fields/class-acf-field-message.php:129
#: includes/fields/class-acf-field-textarea.php:181
msgid "Automatically add paragraphs"
msgstr "Автоматично добавяне на параграфи"

#: includes/fields/class-acf-field-message.php:130
#: includes/fields/class-acf-field-textarea.php:182
msgid "Automatically add &lt;br&gt;"
msgstr "Автоматично добавяне на &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:131
#: includes/fields/class-acf-field-textarea.php:183
msgid "No Formatting"
msgstr "Без форматиране"

#: includes/fields/class-acf-field-message.php:138
msgid "Escape HTML"
msgstr "Изчистване на HTML"

#: includes/fields/class-acf-field-message.php:139
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Позволяване на HTML-а да се показва като видим текст"

#: includes/fields/class-acf-field-number.php:36
msgid "Number"
msgstr "Число"

#: includes/fields/class-acf-field-number.php:181
msgid "Minimum Value"
msgstr "Минимална стойност"

#: includes/fields/class-acf-field-number.php:190
msgid "Maximum Value"
msgstr "Максимална стойност"

#: includes/fields/class-acf-field-number.php:199
msgid "Step Size"
msgstr "Размер на стъпката"

#: includes/fields/class-acf-field-number.php:237
msgid "Value must be a number"
msgstr "Стойността трябва да е число"

#: includes/fields/class-acf-field-number.php:255
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Стойността трябва да е равна на или по-голяма от %d"

#: includes/fields/class-acf-field-number.php:263
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Стойността трябва да е равна на или по-малка от %d"

#: includes/fields/class-acf-field-oembed.php:36
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:237
msgid "Enter URL"
msgstr "Въведете URL адрес"

#: includes/fields/class-acf-field-oembed.php:250
#: includes/fields/class-acf-field-taxonomy.php:904
msgid "Error."
msgstr "Грешка."

#: includes/fields/class-acf-field-oembed.php:250
msgid "No embed found for the given URL."
msgstr "Няма открито вграждане за посочения URL адрес."

#: includes/fields/class-acf-field-oembed.php:283
#: includes/fields/class-acf-field-oembed.php:294
msgid "Embed Size"
msgstr "Размери за вграждане"

#: includes/fields/class-acf-field-page_link.php:192
msgid "Archives"
msgstr "Архиви"

#: includes/fields/class-acf-field-page_link.php:500
#: includes/fields/class-acf-field-post_object.php:399
#: includes/fields/class-acf-field-relationship.php:704
msgid "Filter by Post Type"
msgstr "Филтриране по тип публикация"

#: includes/fields/class-acf-field-page_link.php:508
#: includes/fields/class-acf-field-post_object.php:407
#: includes/fields/class-acf-field-relationship.php:712
msgid "All post types"
msgstr "Всички типове публикации"

#: includes/fields/class-acf-field-page_link.php:514
#: includes/fields/class-acf-field-post_object.php:413
#: includes/fields/class-acf-field-relationship.php:718
msgid "Filter by Taxonomy"
msgstr "Филтриране по таксономия"

#: includes/fields/class-acf-field-page_link.php:522
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-relationship.php:726
msgid "All taxonomies"
msgstr "Всички таксономии"

#: includes/fields/class-acf-field-page_link.php:528
#: includes/fields/class-acf-field-post_object.php:427
#: includes/fields/class-acf-field-radio.php:259
#: includes/fields/class-acf-field-select.php:484
#: includes/fields/class-acf-field-taxonomy.php:799
#: includes/fields/class-acf-field-user.php:423
msgid "Allow Null?"
msgstr "Позволяване на празна стойност?"

#: includes/fields/class-acf-field-page_link.php:538
msgid "Allow Archives URLs"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:548
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-select.php:494
#: includes/fields/class-acf-field-user.php:433
msgid "Select multiple values?"
msgstr "Избиране на няколко стойности?"

#: includes/fields/class-acf-field-password.php:36
msgid "Password"
msgstr "Парола"

#: includes/fields/class-acf-field-post_object.php:36
#: includes/fields/class-acf-field-post_object.php:452
#: includes/fields/class-acf-field-relationship.php:783
msgid "Post Object"
msgstr "Обект-публикация"

#: includes/fields/class-acf-field-post_object.php:453
#: includes/fields/class-acf-field-relationship.php:784
msgid "Post ID"
msgstr "ID на публикация"

#: includes/fields/class-acf-field-radio.php:36
msgid "Radio Button"
msgstr "Радио бутон"

#: includes/fields/class-acf-field-radio.php:269
msgid "Other"
msgstr "Друго"

#: includes/fields/class-acf-field-radio.php:274
msgid "Add 'other' choice to allow for custom values"
msgstr "Добавяне на избор 'друго' като възможност за потребителските стойности"

#: includes/fields/class-acf-field-radio.php:280
msgid "Save Other"
msgstr "Запазване"

#: includes/fields/class-acf-field-radio.php:285
msgid "Save 'other' values to the field's choices"
msgstr "Запазване на стойностите 'друго' към опциите на полето"

#: includes/fields/class-acf-field-relationship.php:36
msgid "Relationship"
msgstr "Връзка"

#: includes/fields/class-acf-field-relationship.php:48
msgid "Minimum values reached ( {min} values )"
msgstr "Минималния брой стойности бе достигнат ( {min} стойности )"

#: includes/fields/class-acf-field-relationship.php:49
msgid "Maximum values reached ( {max} values )"
msgstr "Максималния брой стойности бе достигнат ( {min} стойности )"

#: includes/fields/class-acf-field-relationship.php:50
msgid "Loading"
msgstr "Зареждане"

#: includes/fields/class-acf-field-relationship.php:51
msgid "No matches found"
msgstr "Няма намерени съвпадения"

#: includes/fields/class-acf-field-relationship.php:585
msgid "Search..."
msgstr "Търсене…"

#: includes/fields/class-acf-field-relationship.php:594
msgid "Select post type"
msgstr "Изберете тип на публикацията"

#: includes/fields/class-acf-field-relationship.php:607
msgid "Select taxonomy"
msgstr "Изберете таксономия"

#: includes/fields/class-acf-field-relationship.php:732
msgid "Filters"
msgstr "Филтри"

#: includes/fields/class-acf-field-relationship.php:738
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Вид на публикация"

#: includes/fields/class-acf-field-relationship.php:739
#: includes/fields/class-acf-field-taxonomy.php:36
#: includes/fields/class-acf-field-taxonomy.php:769
msgid "Taxonomy"
msgstr "Таксономия"

#: includes/fields/class-acf-field-relationship.php:746
msgid "Elements"
msgstr "Елементи"

#: includes/fields/class-acf-field-relationship.php:747
msgid "Selected elements will be displayed in each result"
msgstr "Избраните елементи ще бъдат показани във всеки резултат"

#: includes/fields/class-acf-field-relationship.php:758
msgid "Minimum posts"
msgstr "Минимален брой публикации"

#: includes/fields/class-acf-field-relationship.php:767
msgid "Maximum posts"
msgstr "Максимален брой публикации"

#: includes/fields/class-acf-field-relationship.php:871
#: pro/fields/class-acf-field-gallery.php:815
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s изисква поне %s избор"
msgstr[1] "%s изисква поне %s избора"

#: includes/fields/class-acf-field-select.php:36
#: includes/fields/class-acf-field-taxonomy.php:791
#, fuzzy
msgctxt "noun"
msgid "Select"
msgstr "Избор"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr ""

#: includes/fields/class-acf-field-select.php:50
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""

#: includes/fields/class-acf-field-select.php:51
#, fuzzy
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Няма намерени съвпадения"

#: includes/fields/class-acf-field-select.php:52
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:53
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:54
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr ""

#: includes/fields/class-acf-field-select.php:55
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:56
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr ""

#: includes/fields/class-acf-field-select.php:57
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr ""

#: includes/fields/class-acf-field-select.php:58
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr ""

#: includes/fields/class-acf-field-select.php:59
#, fuzzy
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Търсене на полета"

#: includes/fields/class-acf-field-select.php:60
#, fuzzy
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Провалена валидация"

#: includes/fields/class-acf-field-select.php:270 includes/media.php:54
#, fuzzy
msgctxt "verb"
msgid "Select"
msgstr "Избор"

#: includes/fields/class-acf-field-select.php:504
#: includes/fields/class-acf-field-true_false.php:159
msgid "Stylised UI"
msgstr "Стилизиран интерфейс"

#: includes/fields/class-acf-field-select.php:514
msgid "Use AJAX to lazy load choices?"
msgstr "Използване на AJAX за зареждане на опциите?"

#: includes/fields/class-acf-field-select.php:525
#, fuzzy
msgid "Specify the value returned"
msgstr "Уточнява върнатата стойност в сайта"

#: includes/fields/class-acf-field-separator.php:36
msgid "Separator"
msgstr ""

#: includes/fields/class-acf-field-tab.php:36
msgid "Tab"
msgstr "Раздел"

#: includes/fields/class-acf-field-tab.php:96
msgid ""
"The tab field will display incorrectly when added to a Table style repeater "
"field or flexible content field layout"
msgstr ""
"Полето за раздел ще се покаже грешно когато се добави към поле-повторител с "
"табличен стил, или поле за гъвкаво съдържание"

#: includes/fields/class-acf-field-tab.php:97
msgid ""
"Use \"Tab Fields\" to better organize your edit screen by grouping fields "
"together."
msgstr ""
"Използвайте \"Полета Раздел\" за да организирате по-добре екраните за "
"редактиране чрез групиране на полетата."

#: includes/fields/class-acf-field-tab.php:98
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is "
"defined) will be grouped together using this field's label as the tab "
"heading."
msgstr ""
"Всички полета след това \"раздел поле\" (или до следващото такова) ще бъдат "
"групирани заедно в този раздел."

#: includes/fields/class-acf-field-tab.php:112
msgid "Placement"
msgstr "Положение"

#: includes/fields/class-acf-field-tab.php:124
msgid "End-point"
msgstr "Крайна точка"

#: includes/fields/class-acf-field-tab.php:125
msgid "Use this field as an end-point and start a new group of tabs"
msgstr ""
"Използване на това поле като крайна точка и започване на нова група раздели"

#: includes/fields/class-acf-field-taxonomy.php:719
#: includes/fields/class-acf-field-true_false.php:95
#: includes/fields/class-acf-field-true_false.php:184 includes/input.php:266
#: pro/admin/views/html-settings-updates.php:103
msgid "No"
msgstr "Не"

#: includes/fields/class-acf-field-taxonomy.php:738
msgid "None"
msgstr "Никакъв"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Select the taxonomy to be displayed"
msgstr "Избор на таксономия"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Appearance"
msgstr "Външен вид"

#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Select the appearance of this field"
msgstr "Избор на външния вид на това поле"

#: includes/fields/class-acf-field-taxonomy.php:785
msgid "Multiple Values"
msgstr "Множество стойности"

#: includes/fields/class-acf-field-taxonomy.php:787
msgid "Multi Select"
msgstr "Множество избрани стойности"

#: includes/fields/class-acf-field-taxonomy.php:789
msgid "Single Value"
msgstr "Единична стойност"

#: includes/fields/class-acf-field-taxonomy.php:790
msgid "Radio Buttons"
msgstr "Радио бутони"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Create Terms"
msgstr "Създаване на термини"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Allow new terms to be created whilst editing"
msgstr "Позволяване нови термини да се създават при редактиране"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Save Terms"
msgstr "Запазване на термини"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Connect selected terms to the post"
msgstr "Свързване на избраните термини към тази публикация"

#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Load Terms"
msgstr "Зареждане на термини"

#: includes/fields/class-acf-field-taxonomy.php:830
msgid "Load value from posts terms"
msgstr "Зареждане на стойност от термините на публикациите"

#: includes/fields/class-acf-field-taxonomy.php:844
msgid "Term Object"
msgstr "Обект-термин"

#: includes/fields/class-acf-field-taxonomy.php:845
msgid "Term ID"
msgstr "ID на термин"

#: includes/fields/class-acf-field-taxonomy.php:904
#, php-format
msgid "User unable to add new %s"
msgstr "Потребителят не може да добави %s"

#: includes/fields/class-acf-field-taxonomy.php:917
#, php-format
msgid "%s already exists"
msgstr "%s вече съществува"

#: includes/fields/class-acf-field-taxonomy.php:958
#, php-format
msgid "%s added"
msgstr "успешно добавяне на %s"

#: includes/fields/class-acf-field-taxonomy.php:1003
msgid "Add"
msgstr "Добавяне"

#: includes/fields/class-acf-field-text.php:36
msgid "Text"
msgstr "Текст"

#: includes/fields/class-acf-field-text.php:178
#: includes/fields/class-acf-field-textarea.php:157
msgid "Character Limit"
msgstr "Максимален брой символи"

#: includes/fields/class-acf-field-text.php:179
#: includes/fields/class-acf-field-textarea.php:158
msgid "Leave blank for no limit"
msgstr "Оставете празно за да премахнете ограничението"

#: includes/fields/class-acf-field-textarea.php:36
msgid "Text Area"
msgstr "Текстова област"

#: includes/fields/class-acf-field-textarea.php:166
msgid "Rows"
msgstr "Редове"

#: includes/fields/class-acf-field-textarea.php:167
msgid "Sets the textarea height"
msgstr "Задава височината на текстовото поле"

#: includes/fields/class-acf-field-time_picker.php:36
#, fuzzy
msgid "Time Picker"
msgstr "Избор на дата и час"

#: includes/fields/class-acf-field-true_false.php:36
msgid "True / False"
msgstr "Вярно / невярно"

#: includes/fields/class-acf-field-true_false.php:94
#: includes/fields/class-acf-field-true_false.php:174 includes/input.php:265
#: pro/admin/views/html-settings-updates.php:93
msgid "Yes"
msgstr "Да"

#: includes/fields/class-acf-field-true_false.php:142
msgid "Displays text alongside the checkbox"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:170
#, fuzzy
msgid "On Text"
msgstr "Текст"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when active"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:180
#, fuzzy
msgid "Off Text"
msgstr "Текст"

#: includes/fields/class-acf-field-true_false.php:181
msgid "Text shown when inactive"
msgstr ""

#: includes/fields/class-acf-field-url.php:36
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:165
msgid "Value must be a valid URL"
msgstr "Стойността трябва да е валиден URL"

#: includes/fields/class-acf-field-user.php:36 includes/locations.php:95
msgid "User"
msgstr "Потребител"

#: includes/fields/class-acf-field-user.php:408
msgid "Filter by role"
msgstr "Филтриране по роля"

#: includes/fields/class-acf-field-user.php:416
msgid "All user roles"
msgstr "Всички потребителски роли"

#: includes/fields/class-acf-field-wysiwyg.php:36
msgid "Wysiwyg Editor"
msgstr "Редактор на съдържание"

#: includes/fields/class-acf-field-wysiwyg.php:385
msgid "Visual"
msgstr "Визуален"

#: includes/fields/class-acf-field-wysiwyg.php:386
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Текстов"

#: includes/fields/class-acf-field-wysiwyg.php:392
msgid "Click to initialize TinyMCE"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:445
msgid "Tabs"
msgstr "Раздели"

#: includes/fields/class-acf-field-wysiwyg.php:450
msgid "Visual & Text"
msgstr "Визуален и текстов"

#: includes/fields/class-acf-field-wysiwyg.php:451
msgid "Visual Only"
msgstr "Само визуален"

#: includes/fields/class-acf-field-wysiwyg.php:452
msgid "Text Only"
msgstr "Само текстов"

#: includes/fields/class-acf-field-wysiwyg.php:459
msgid "Toolbar"
msgstr "Лента с инструменти"

#: includes/fields/class-acf-field-wysiwyg.php:469
msgid "Show Media Upload Buttons?"
msgstr "Показване на бутоните за качване на файлове?"

#: includes/fields/class-acf-field-wysiwyg.php:479
msgid "Delay initialization?"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:480
msgid "TinyMCE will not be initalized until field is clicked"
msgstr ""

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:304
msgid "Edit field group"
msgstr "Редактиране на група полета"

#: includes/forms/form-front.php:55
#, fuzzy
msgid "Validate Email"
msgstr "Провалена валидация"

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:588 pro/options-page.php:81
msgid "Update"
msgstr "Обновяване"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Публикацията бе актуализирана"

#: includes/forms/form-front.php:229
msgid "Spam Detected"
msgstr "Открит спам"

#: includes/input.php:258
msgid "Expand Details"
msgstr "Разпъване на детайлите"

#: includes/input.php:259
msgid "Collapse Details"
msgstr "Свиване на детайлите"

#: includes/input.php:260
msgid "Validation successful"
msgstr "Успешна валидация"

#: includes/input.php:261 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Провалена валидация"

#: includes/input.php:262
msgid "1 field requires attention"
msgstr "1 поле изисква внимание"

#: includes/input.php:263
#, php-format
msgid "%d fields require attention"
msgstr "%d полета изискват внимание"

#: includes/input.php:264
msgid "Restricted"
msgstr "Ограничен"

#: includes/input.php:268
msgid "Cancel"
msgstr ""

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Публикация"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Страница"

#: includes/locations.php:96
msgid "Forms"
msgstr "Формуляри"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Файл"

#: includes/locations/class-acf-location-attachment.php:113
#, php-format
msgid "All %s formats"
msgstr ""

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Коментар"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Роля на текущия потребител"

#: includes/locations/class-acf-location-current-user-role.php:114
msgid "Super Admin"
msgstr "Супер администратор"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Текущ потребител"

#: includes/locations/class-acf-location-current-user.php:101
msgid "Logged in"
msgstr "Влезли сте"

#: includes/locations/class-acf-location-current-user.php:102
msgid "Viewing front end"
msgstr "Преглеждане на сайта"

#: includes/locations/class-acf-location-current-user.php:103
msgid "Viewing back end"
msgstr "Преглеждане на администрацията"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr ""

#: includes/locations/class-acf-location-nav-menu.php:113
#, fuzzy
msgid "Menu Locations"
msgstr "Местоположение"

#: includes/locations/class-acf-location-nav-menu.php:123
msgid "Menus"
msgstr ""

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Страница родител"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Шаблон на страница"

#: includes/locations/class-acf-location-page-template.php:102
#: includes/locations/class-acf-location-post-template.php:156
msgid "Default Template"
msgstr "Шаблон по подразбиране"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Тип страница"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Front Page"
msgstr "Първа страница"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Posts Page"
msgstr "Страница с публикации"

#: includes/locations/class-acf-location-page-type.php:151
msgid "Top Level Page (no parent)"
msgstr "Горно ниво страница (родител)"

#: includes/locations/class-acf-location-page-type.php:152
msgid "Parent Page (has children)"
msgstr "Родителска страница (има деца)"

#: includes/locations/class-acf-location-page-type.php:153
msgid "Child Page (has parent)"
msgstr "Дете страница (има родител)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Категория на публикация"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Формат на публикация"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Статус на публикация"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Таксономия на публикация"

#: includes/locations/class-acf-location-post-template.php:29
#, fuzzy
msgid "Post Template"
msgstr "Шаблон на страница"

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "Термин"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Потребителски формуляр"

#: includes/locations/class-acf-location-user-form.php:92
msgid "Add / Edit"
msgstr "Добавяне / редактиране"

#: includes/locations/class-acf-location-user-form.php:93
msgid "Register"
msgstr "Регистрация"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Потребителска роля"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Джаджa"

#: includes/media.php:55
#, fuzzy
msgctxt "verb"
msgid "Edit"
msgstr "Редактиране"

#: includes/media.php:56
#, fuzzy
msgctxt "verb"
msgid "Update"
msgstr "Обновяване"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s стойност е задължителна"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Модерни потребителски полета PRO"

#: pro/admin/admin-options-page.php:196
msgid "Publish"
msgstr "Публикуване"

#: pro/admin/admin-options-page.php:202
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Няма намерени групи полета за тази страница с опции. <a href=\"%s"
"\">Създаване на група полета</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Грешка</b>. Неуспешно свързване със сървъра"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:17
msgid "Updates"
msgstr "Актуализации"

#: pro/admin/views/html-settings-updates.php:11
msgid "Deactivate License"
msgstr "Деактивиране на лиценз"

#: pro/admin/views/html-settings-updates.php:11
msgid "Activate License"
msgstr "Активиране на лиценз"

#: pro/admin/views/html-settings-updates.php:21
#, fuzzy
msgid "License Information"
msgstr "Информация за обновяването"

#: pro/admin/views/html-settings-updates.php:24
#, fuzzy, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"За да включите обновяванията, моля въведете вашия лицензионен ключ на "
"страницата <a href=\"%s\">Актуализации</a>. Ако нямате лицензионен ключ, "
"моля посетете <a href=\"%s\">детайли и цени</a>"

#: pro/admin/views/html-settings-updates.php:33
msgid "License Key"
msgstr "Лицензионен ключ"

#: pro/admin/views/html-settings-updates.php:65
msgid "Update Information"
msgstr "Информация за обновяването"

#: pro/admin/views/html-settings-updates.php:72
msgid "Current Version"
msgstr "Текуща версия"

#: pro/admin/views/html-settings-updates.php:80
msgid "Latest Version"
msgstr "Последна версия"

#: pro/admin/views/html-settings-updates.php:88
msgid "Update Available"
msgstr "Налице е обновяване"

#: pro/admin/views/html-settings-updates.php:96
msgid "Update Plugin"
msgstr "Обновяване"

#: pro/admin/views/html-settings-updates.php:98
msgid "Please enter your license key above to unlock updates"
msgstr "Моля въведете вашия лицензионен ключ за да отключите обновяванията"

#: pro/admin/views/html-settings-updates.php:104
msgid "Check Again"
msgstr "Проверка"

#: pro/admin/views/html-settings-updates.php:121
msgid "Upgrade Notice"
msgstr "Забележки за обновяването"

#: pro/fields/class-acf-field-clone.php:36
msgctxt "noun"
msgid "Clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:858
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:875
#, fuzzy
msgid "Display"
msgstr "Формат на показване"

#: pro/fields/class-acf-field-clone.php:876
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:881
msgid "Group (displays selected fields in a group within this field)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:882
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:903
#, fuzzy, php-format
msgid "Labels will be displayed as %s"
msgstr "Избраните елементи ще бъдат показани във всеки резултат"

#: pro/fields/class-acf-field-clone.php:906
#, fuzzy
msgid "Prefix Field Labels"
msgstr "Етикет на полето"

#: pro/fields/class-acf-field-clone.php:917
#, php-format
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:920
#, fuzzy
msgid "Prefix Field Names"
msgstr "Име на полето"

#: pro/fields/class-acf-field-clone.php:1038
#, fuzzy
msgid "Unknown field"
msgstr "Под полетата"

#: pro/fields/class-acf-field-clone.php:1077
#, fuzzy
msgid "Unknown field group"
msgstr "Синхронизиране на групата полета"

#: pro/fields/class-acf-field-clone.php:1081
#, php-format
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:42
#: pro/fields/class-acf-field-repeater.php:230
#: pro/fields/class-acf-field-repeater.php:534
msgid "Add Row"
msgstr "Добавяне на ред"

#: pro/fields/class-acf-field-flexible-content.php:45
msgid "layout"
msgstr "шаблон"

#: pro/fields/class-acf-field-flexible-content.php:46
msgid "layouts"
msgstr "шаблони"

#: pro/fields/class-acf-field-flexible-content.php:47
msgid "remove {layout}?"
msgstr "премахване?"

#: pro/fields/class-acf-field-flexible-content.php:48
msgid "This field requires at least {min} {identifier}"
msgstr "Това поле изисква поне {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:49
msgid "This field has a limit of {max} {identifier}"
msgstr "Това поле има лимит от {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:50
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Това поле изисква поне {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:51
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "Максималния лимит на {label} бе достигнат ({max} {identifier})"

#: pro/fields/class-acf-field-flexible-content.php:52
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} налични  (максимум  {max})"

#: pro/fields/class-acf-field-flexible-content.php:53
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} задължителни (минимум {min})"

#: pro/fields/class-acf-field-flexible-content.php:54
msgid "Flexible Content requires at least 1 layout"
msgstr "Полето за гъвкаво съдържание изисква поне 1 шаблон полета"

#: pro/fields/class-acf-field-flexible-content.php:288
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Натиснете бутона \"%s\" за да започнете да създавате вашия шаблон"

#: pro/fields/class-acf-field-flexible-content.php:423
msgid "Add layout"
msgstr "Създаване на шаблон"

#: pro/fields/class-acf-field-flexible-content.php:424
msgid "Remove layout"
msgstr "Премахване на шаблон"

#: pro/fields/class-acf-field-flexible-content.php:425
#: pro/fields/class-acf-field-repeater.php:360
msgid "Click to toggle"
msgstr "Кликнете за да превключите"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Reorder Layout"
msgstr "Пренареждане на шаблон"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Reorder"
msgstr "Пренареждане"

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Delete Layout"
msgstr "Изтриване на шаблон"

#: pro/fields/class-acf-field-flexible-content.php:573
msgid "Duplicate Layout"
msgstr "Дублиране на шаблон"

#: pro/fields/class-acf-field-flexible-content.php:574
msgid "Add New Layout"
msgstr "Добавяне на нов шаблон"

#: pro/fields/class-acf-field-flexible-content.php:645
msgid "Min"
msgstr "Минимум"

#: pro/fields/class-acf-field-flexible-content.php:658
msgid "Max"
msgstr "Максимум"

#: pro/fields/class-acf-field-flexible-content.php:685
#: pro/fields/class-acf-field-repeater.php:530
msgid "Button Label"
msgstr "Етикет на бутона"

#: pro/fields/class-acf-field-flexible-content.php:694
msgid "Minimum Layouts"
msgstr "Минимален брой шаблони"

#: pro/fields/class-acf-field-flexible-content.php:703
msgid "Maximum Layouts"
msgstr "Максимален брой шаблони"

#: pro/fields/class-acf-field-gallery.php:52
msgid "Add Image to Gallery"
msgstr "Добавяне на изображение към галерия"

#: pro/fields/class-acf-field-gallery.php:56
msgid "Maximum selection reached"
msgstr "Максималния брой избори бе достигнат"

#: pro/fields/class-acf-field-gallery.php:336
msgid "Length"
msgstr "Размер"

#: pro/fields/class-acf-field-gallery.php:379
#, fuzzy
msgid "Caption"
msgstr "Опции"

#: pro/fields/class-acf-field-gallery.php:388
#, fuzzy
msgid "Alt Text"
msgstr "Текст"

#: pro/fields/class-acf-field-gallery.php:559
msgid "Add to gallery"
msgstr "Добавяне към галерия"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Bulk actions"
msgstr "Групови действия"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Sort by date uploaded"
msgstr "Сортиране по дата на качване"

#: pro/fields/class-acf-field-gallery.php:565
msgid "Sort by date modified"
msgstr "Сортиране по дата на последна промяна"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Sort by title"
msgstr "Сортиране по заглавие"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Reverse current order"
msgstr "Обръщане на текущия ред"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Close"
msgstr "Затваряне"

#: pro/fields/class-acf-field-gallery.php:639
msgid "Minimum Selection"
msgstr "Минимална селекция"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Maximum Selection"
msgstr "Максимална селекция"

#: pro/fields/class-acf-field-gallery.php:657
msgid "Insert"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:658
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:662
#, fuzzy
msgid "Append to the end"
msgstr "Показва се след полето"

#: pro/fields/class-acf-field-gallery.php:663
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:47
msgid "Minimum rows reached ({min} rows)"
msgstr "Минималния брой редове бе достигнат ({min} реда)"

#: pro/fields/class-acf-field-repeater.php:48
msgid "Maximum rows reached ({max} rows)"
msgstr "Максималния брой редове бе достигнат ({max} реда)"

#: pro/fields/class-acf-field-repeater.php:405
msgid "Add row"
msgstr "Добавяне на ред"

#: pro/fields/class-acf-field-repeater.php:406
msgid "Remove row"
msgstr "Премахване на ред"

#: pro/fields/class-acf-field-repeater.php:483
msgid "Collapsed"
msgstr "Свит"

#: pro/fields/class-acf-field-repeater.php:484
msgid "Select a sub field to show when row is collapsed"
msgstr "Изберете вложено поле, което да се показва когато реда е свит"

#: pro/fields/class-acf-field-repeater.php:494
msgid "Minimum Rows"
msgstr "Минимален брой редове"

#: pro/fields/class-acf-field-repeater.php:504
msgid "Maximum Rows"
msgstr "Максимален брой редове"

#: pro/locations/class-acf-location-options-page.php:70
msgid "No options pages exist"
msgstr "Няма създадени страници с опции"

#: pro/options-page.php:51
msgid "Options"
msgstr "Опции"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Опциите бяха актуализирани"

#: pro/updates.php:97
#, fuzzy, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"За да включите обновяванията, моля въведете вашия лицензионен ключ на "
"страницата <a href=\"%s\">Актуализации</a>. Ако нямате лицензионен ключ, "
"моля посетете <a href=\"%s\">детайли и цени</a>"

#. Plugin URI of the plugin/theme
#, fuzzy
msgid "https://www.advancedcustomfields.com/"
msgstr "http://www.advancedcustomfields.com/"

#. Author of the plugin/theme
#, fuzzy
msgid "Elliot Condon"
msgstr "Елиът Кондън"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Disabled"
#~ msgstr "Изключено"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Изключено <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Изключени <span class=\"count\">(%s)</span>"

#~ msgid "See what's new in"
#~ msgstr "Вижте какво е новото в"

#~ msgid "version"
#~ msgstr "версия"

#~ msgid "Getting Started"
#~ msgstr "Как да започнете"

#~ msgid "Field Types"
#~ msgstr "Типове полета"

#~ msgid "Functions"
#~ msgstr "Функции"

#~ msgid "Actions"
#~ msgstr "Действия"

#~ msgid "'How to' guides"
#~ msgstr "Ръководства"

#~ msgid "Tutorials"
#~ msgstr "Уроци"

#~ msgid "Created by"
#~ msgstr "Създадено от"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr ""
#~ "<b>Успех</b>. Инструментът за импортиране добави %s групи полета: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Внимание</b>. Инструментът за импортиране откри, че %s групи полета "
#~ "вече съществуват и бяха игнорирани: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Обновяване"

#~ msgid "Upgrade"
#~ msgstr "Обновяване"

#~ msgid "Error"
#~ msgstr "Грешка"

#~ msgid "Upgrading data to"
#~ msgstr "Обновяване на данните към"

#~ msgid "See what's new"
#~ msgstr "Вижте какво е новото"

#~ msgid "Show a different month"
#~ msgstr "Показване на различен месец"

#~ msgid "Return format"
#~ msgstr "Формат при връщане"

#~ msgid "uploaded to this post"
#~ msgstr "прикачен към тази публикация"

#~ msgid "File Size"
#~ msgstr "Размер на файла"

#~ msgid "No File selected"
#~ msgstr "Няма избран файл"

#~ msgid "eg. Show extra content"
#~ msgstr "напр. Покажи допълнително съдържание"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Грешка при свързване</b>. Моля, опитайте отново"

#~ msgid "Save Options"
#~ msgstr "Запазване на опциите"

#~ msgid "License"
#~ msgstr "Лиценз"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "За да отключите обновяванията, моля въведете вашия лицензен код в "
#~ "съответното поле. Ако нямате такъв, моля вижте"

#~ msgid "details & pricing"
#~ msgstr "детайли и цени"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Модерни потребителски полета PRO"
