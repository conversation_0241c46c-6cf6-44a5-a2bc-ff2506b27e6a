msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2015-08-11 23:33+0200\n"
"PO-Revision-Date: 2018-02-06 10:06+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: shogo kato <<EMAIL>>\n"
"Language: ja_JP\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.1\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;esc_attr_e;esc_attr_x:1,2c;"
"esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:63
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:205 admin/admin.php:61
msgid "Field Groups"
msgstr "フィールドグループ"

#: acf.php:206
msgid "Field Group"
msgstr "フィールドグループ"

#: acf.php:207 acf.php:239 admin/admin.php:62 pro/fields/flexible-content.php:517
msgid "Add New"
msgstr "新規追加"

#: acf.php:208
msgid "Add New Field Group"
msgstr "フィールドグループを新規追加"

#: acf.php:209
msgid "Edit Field Group"
msgstr "フィールドグループを編集"

#: acf.php:210
msgid "New Field Group"
msgstr "新規フィールドグループ"

#: acf.php:211
msgid "View Field Group"
msgstr "フィールドグループを表示"

#: acf.php:212
msgid "Search Field Groups"
msgstr "フィールドグループを検索"

#: acf.php:213
msgid "No Field Groups found"
msgstr "フィールドグループが見つかりませんでした"

#: acf.php:214
msgid "No Field Groups found in Trash"
msgstr "ゴミ箱の中にフィールドグループは見つかりませんでした"

#: acf.php:237 admin/field-group.php:182 admin/field-group.php:213 admin/field-groups.php:519
msgid "Fields"
msgstr "フィールド"

#: acf.php:238
msgid "Field"
msgstr "フィールド"

#: acf.php:240
msgid "Add New Field"
msgstr "新規フィールドを追加"

#: acf.php:241
msgid "Edit Field"
msgstr "フィールドを編集"

#: acf.php:242 admin/views/field-group-fields.php:18 admin/views/settings-info.php:111
msgid "New Field"
msgstr "新規フィールド"

#: acf.php:243
msgid "View Field"
msgstr "フィールドを表示"

#: acf.php:244
msgid "Search Fields"
msgstr "フィールドを検索"

#: acf.php:245
msgid "No Fields found"
msgstr "フィールドが見つかりませんでした"

#: acf.php:246
msgid "No Fields found in Trash"
msgstr "ゴミ箱の中にフィールドは見つかりませんでした"

#: acf.php:268 admin/field-group.php:283 admin/field-groups.php:583
#: admin/views/field-group-options.php:18
msgid "Disabled"
msgstr ""

#: acf.php:273
#, php-format
msgid "Disabled <span class=\"count\">(%s)</span>"
msgid_plural "Disabled <span class=\"count\">(%s)</span>"
msgstr[0] ""

#: admin/admin.php:57 admin/views/field-group-options.php:120
msgid "Custom Fields"
msgstr "カスタムフィールド"

#: admin/field-group.php:68 admin/field-group.php:69 admin/field-group.php:71
msgid "Field group updated."
msgstr "フィールドグループを更新しました"

#: admin/field-group.php:70
msgid "Field group deleted."
msgstr "フィールドグループを削除しました"

#: admin/field-group.php:73
msgid "Field group published."
msgstr "フィールドグループを公開しました"

#: admin/field-group.php:74
msgid "Field group saved."
msgstr "フィールドグループを保存しました"

#: admin/field-group.php:75
msgid "Field group submitted."
msgstr "フィールドグループを送信しました"

#: admin/field-group.php:76
msgid "Field group scheduled for."
msgstr "フィールドグループを公開予約しました"

#: admin/field-group.php:77
msgid "Field group draft updated."
msgstr "フィールドグループの下書きを更新しました"

#: admin/field-group.php:176
msgid "Move to trash. Are you sure?"
msgstr "ゴミ箱に移動します。よろしいですか？"

#: admin/field-group.php:177
msgid "checked"
msgstr "チェック済み"

#: admin/field-group.php:178
msgid "No toggle fields available"
msgstr "利用できるトグルフィールドがありません"

#: admin/field-group.php:179
msgid "Field group title is required"
msgstr "フィールドグループのタイトルは必須です"

#: admin/field-group.php:180 api/api-field-group.php:607
msgid "copy"
msgstr "複製"

#: admin/field-group.php:181 admin/views/field-group-field-conditional-logic.php:67
#: admin/views/field-group-field-conditional-logic.php:162 admin/views/field-group-locations.php:23
#: admin/views/field-group-locations.php:131 api/api-helpers.php:3262
msgid "or"
msgstr "または"

#: admin/field-group.php:183
msgid "Parent fields"
msgstr "親フィールド"

#: admin/field-group.php:184
msgid "Sibling fields"
msgstr "兄弟フィールド"

#: admin/field-group.php:185
msgid "Move Custom Field"
msgstr "カスタムフィールドを移動"

#: admin/field-group.php:186
msgid "This field cannot be moved until its changes have been saved"
msgstr "このフィールドは変更が保存されるまで移動することはできません"

#: admin/field-group.php:187
msgid "Null"
msgstr "空"

#: admin/field-group.php:188 core/input.php:128
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "このページから移動した場合、変更は失われます"

#: admin/field-group.php:189
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "\"field_\" はフィールド名の先頭に使うことはできません"

#: admin/field-group.php:214
msgid "Location"
msgstr "位置"

#: admin/field-group.php:215
msgid "Settings"
msgstr ""

#: admin/field-group.php:253
msgid "Field Keys"
msgstr ""

#: admin/field-group.php:283 admin/views/field-group-options.php:17
msgid "Active"
msgstr ""

#: admin/field-group.php:744
msgid "Front Page"
msgstr "フロントページ"

#: admin/field-group.php:745
msgid "Posts Page"
msgstr "投稿ページ"

#: admin/field-group.php:746
msgid "Top Level Page (no parent)"
msgstr "最上位のページ（親ページがない）"

#: admin/field-group.php:747
msgid "Parent Page (has children)"
msgstr "親ページ（子ページがある場合）"

#: admin/field-group.php:748
msgid "Child Page (has parent)"
msgstr "子ページ（親ページがある場合）"

#: admin/field-group.php:764
msgid "Default Template"
msgstr "デフォルトテンプレート"

#: admin/field-group.php:786
msgid "Logged in"
msgstr "ログイン済み"

#: admin/field-group.php:787
msgid "Viewing front end"
msgstr "フロントエンドで表示"

#: admin/field-group.php:788
msgid "Viewing back end"
msgstr "バックエンドで表示"

#: admin/field-group.php:807
msgid "Super Admin"
msgstr "ネットワーク管理者"

#: admin/field-group.php:818 admin/field-group.php:826 admin/field-group.php:840
#: admin/field-group.php:847 admin/field-group.php:862 admin/field-group.php:872 fields/file.php:235
#: fields/image.php:226 pro/fields/gallery.php:653
msgid "All"
msgstr "全て"

#: admin/field-group.php:827
msgid "Add / Edit"
msgstr "追加 / 編集"

#: admin/field-group.php:828
msgid "Register"
msgstr "登録"

#: admin/field-group.php:1059
msgid "Move Complete."
msgstr "移動が完了しました。"

#: admin/field-group.php:1060
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "この %s フィールドは今 %s フィールドグループにあります"

#: admin/field-group.php:1062
msgid "Close Window"
msgstr "ウィンドウを閉じる"

#: admin/field-group.php:1097
msgid "Please select the destination for this field"
msgstr "このフィールドの移動先を選択してください"

#: admin/field-group.php:1104
msgid "Move Field"
msgstr "フィールドを移動"

#: admin/field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] ""

#: admin/field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "フィールドグループを複製しました。 %s"

#: admin/field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s個 のフィールドグループを複製しました"

#: admin/field-groups.php:228
#, php-format
msgid "Field group synchronised. %s"
msgstr "フィールドグループを同期しました。%s"

#: admin/field-groups.php:232
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s個 のフィールドグループを同期しました"

#: admin/field-groups.php:403 admin/field-groups.php:573
msgid "Sync available"
msgstr "利用可能な同期"

#: admin/field-groups.php:516
msgid "Title"
msgstr "タイトル"

#: admin/field-groups.php:517 admin/views/field-group-options.php:98 admin/views/update-network.php:20
#: admin/views/update-network.php:28
msgid "Description"
msgstr ""

#: admin/field-groups.php:518 admin/views/field-group-options.php:10
msgid "Status"
msgstr ""

#: admin/field-groups.php:616 admin/settings-info.php:76 pro/admin/views/settings-updates.php:111
msgid "Changelog"
msgstr "更新履歴"

#: admin/field-groups.php:617
msgid "See what's new in"
msgstr "新着情報を見る"

#: admin/field-groups.php:617
msgid "version"
msgstr "バージョン"

#: admin/field-groups.php:619
msgid "Resources"
msgstr "リソース"

#: admin/field-groups.php:621
msgid "Getting Started"
msgstr "はじめに"

#: admin/field-groups.php:622 pro/admin/settings-updates.php:73 pro/admin/views/settings-updates.php:17
msgid "Updates"
msgstr "アップデート"

#: admin/field-groups.php:623
msgid "Field Types"
msgstr "フィールドタイプ"

#: admin/field-groups.php:624
msgid "Functions"
msgstr "ファンクション"

#: admin/field-groups.php:625
msgid "Actions"
msgstr "アクション"

#: admin/field-groups.php:626 fields/relationship.php:718
msgid "Filters"
msgstr "フィルター"

#: admin/field-groups.php:627
msgid "'How to' guides"
msgstr "使い方ガイド"

#: admin/field-groups.php:628
msgid "Tutorials"
msgstr "チュートリアル"

#: admin/field-groups.php:633
msgid "Created by"
msgstr "作成"

#: admin/field-groups.php:673
msgid "Duplicate this item"
msgstr "この項目を複製"

#: admin/field-groups.php:673 admin/field-groups.php:685 admin/views/field-group-field.php:58
#: pro/fields/flexible-content.php:516
msgid "Duplicate"
msgstr "複製"

#: admin/field-groups.php:724
#, php-format
msgid "Select %s"
msgstr "%s を選択"

#: admin/field-groups.php:730
msgid "Synchronise field group"
msgstr "フィールドグループを同期する"

#: admin/field-groups.php:730 admin/field-groups.php:750
msgid "Sync"
msgstr "同期する"

#: admin/settings-addons.php:51 admin/views/settings-addons.php:9
msgid "Add-ons"
msgstr "アドオン"

#: admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>エラー</b> アドオンのリストを読み込めませんでした"

#: admin/settings-info.php:50
msgid "Info"
msgstr "お知らせ"

#: admin/settings-info.php:75
msgid "What's New"
msgstr "新着情報"

#: admin/settings-tools.php:54 admin/views/settings-tools-export.php:9 admin/views/settings-tools.php:31
msgid "Tools"
msgstr ""

#: admin/settings-tools.php:151 admin/settings-tools.php:365
msgid "No field groups selected"
msgstr "フィールドグループが選択されていません"

#: admin/settings-tools.php:188
msgid "No file selected"
msgstr "ファイルが選択されていません"

#: admin/settings-tools.php:201
msgid "Error uploading file. Please try again"
msgstr "ファイルのアップロードに失敗しました。もう一度試してください。"

#: admin/settings-tools.php:210
msgid "Incorrect file type"
msgstr "不正なファイルタイプ"

#: admin/settings-tools.php:227
msgid "Import file empty"
msgstr "インポートファイルが空です"

#: admin/settings-tools.php:323
#, php-format
msgid "<b>Success</b>. Import tool added %s field groups: %s"
msgstr "<b>成功</b> インポートツールは %s個 のフィールドグループを追加しました：%s"

#: admin/settings-tools.php:332
#, php-format
msgid "<b>Warning</b>. Import tool detected %s field groups already exist and have been ignored: %s"
msgstr ""
"<b>警告</b> インポートツールは %s個 のフィールドグループが既に存在しているのを検出したため無視しました："
"%s"

#: admin/update.php:113
msgid "Upgrade ACF"
msgstr ""

#: admin/update.php:143
msgid "Review sites & upgrade"
msgstr ""

#: admin/update.php:298
msgid "Upgrade"
msgstr "アップグレード"

#: admin/update.php:328
msgid "Upgrade Database"
msgstr ""

#: admin/views/field-group-field-conditional-logic.php:29
msgid "Conditional Logic"
msgstr "条件判定"

#: admin/views/field-group-field-conditional-logic.php:40 admin/views/field-group-field.php:137
#: fields/checkbox.php:246 fields/message.php:117 fields/page_link.php:568 fields/page_link.php:582
#: fields/post_object.php:434 fields/post_object.php:448 fields/select.php:411 fields/select.php:425
#: fields/select.php:439 fields/select.php:453 fields/tab.php:172 fields/taxonomy.php:770
#: fields/taxonomy.php:784 fields/taxonomy.php:798 fields/taxonomy.php:812 fields/user.php:457
#: fields/user.php:471 fields/wysiwyg.php:384 pro/admin/views/settings-updates.php:93
msgid "Yes"
msgstr "はい"

#: admin/views/field-group-field-conditional-logic.php:41 admin/views/field-group-field.php:138
#: fields/checkbox.php:247 fields/message.php:118 fields/page_link.php:569 fields/page_link.php:583
#: fields/post_object.php:435 fields/post_object.php:449 fields/select.php:412 fields/select.php:426
#: fields/select.php:440 fields/select.php:454 fields/tab.php:173 fields/taxonomy.php:685
#: fields/taxonomy.php:771 fields/taxonomy.php:785 fields/taxonomy.php:799 fields/taxonomy.php:813
#: fields/user.php:458 fields/user.php:472 fields/wysiwyg.php:385
#: pro/admin/views/settings-updates.php:103
msgid "No"
msgstr "いいえ"

#: admin/views/field-group-field-conditional-logic.php:65
msgid "Show this field if"
msgstr "このフィールドグループの表示条件"

#: admin/views/field-group-field-conditional-logic.php:111 admin/views/field-group-locations.php:88
msgid "is equal to"
msgstr "等しい"

#: admin/views/field-group-field-conditional-logic.php:112 admin/views/field-group-locations.php:89
msgid "is not equal to"
msgstr "等しくない"

#: admin/views/field-group-field-conditional-logic.php:149 admin/views/field-group-locations.php:118
msgid "and"
msgstr "and"

#: admin/views/field-group-field-conditional-logic.php:164 admin/views/field-group-locations.php:133
msgid "Add rule group"
msgstr "ルールを追加"

#: admin/views/field-group-field.php:54 admin/views/field-group-field.php:57
msgid "Edit field"
msgstr "フィールドを編集"

#: admin/views/field-group-field.php:57 pro/fields/gallery.php:355
msgid "Edit"
msgstr "編集"

#: admin/views/field-group-field.php:58
msgid "Duplicate field"
msgstr "フィールドを複製"

#: admin/views/field-group-field.php:59
msgid "Move field to another group"
msgstr "別のグループにフィールドを移動する"

#: admin/views/field-group-field.php:59
msgid "Move"
msgstr "移動"

#: admin/views/field-group-field.php:60
msgid "Delete field"
msgstr "フィールドを削除"

#: admin/views/field-group-field.php:60 pro/fields/flexible-content.php:515
msgid "Delete"
msgstr "削除"

#: admin/views/field-group-field.php:68 fields/oembed.php:212 fields/taxonomy.php:886
msgid "Error"
msgstr "エラー"

#: fields/oembed.php:220 fields/taxonomy.php:900
msgid "Error."
msgstr "エラー."

#: admin/views/field-group-field.php:68
msgid "Field type does not exist"
msgstr "フィールドタイプが存在しません"

#: admin/views/field-group-field.php:81
msgid "Field Label"
msgstr "フィールドラベル"

#: admin/views/field-group-field.php:82
msgid "This is the name which will appear on the EDIT page"
msgstr "編集ページで表示される名前です"

#: admin/views/field-group-field.php:93
msgid "Field Name"
msgstr "フィールド名"

#: admin/views/field-group-field.php:94
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "スペースは不可、アンダースコアとダッシュは使用可能。"

#: admin/views/field-group-field.php:105
msgid "Field Type"
msgstr "フィールドタイプ"

#: admin/views/field-group-field.php:118 fields/tab.php:143
msgid "Instructions"
msgstr "説明"

#: admin/views/field-group-field.php:119
msgid "Instructions for authors. Shown when submitting data"
msgstr "投稿者向けの説明。編集時に表示されます"

#: admin/views/field-group-field.php:130
msgid "Required?"
msgstr "必須か？"

#: admin/views/field-group-field.php:158
msgid "Wrapper Attributes"
msgstr "ラッパーの属性"

#: admin/views/field-group-field.php:164
msgid "width"
msgstr "width"

#: admin/views/field-group-field.php:178
msgid "class"
msgstr "class"

#: admin/views/field-group-field.php:191
msgid "id"
msgstr "id"

#: admin/views/field-group-field.php:203
msgid "Close Field"
msgstr "フィールドを閉じる"

#: admin/views/field-group-fields.php:29
msgid "Order"
msgstr "順序"

#: admin/views/field-group-fields.php:30 pro/fields/flexible-content.php:541
msgid "Label"
msgstr "ラベル"

#: admin/views/field-group-fields.php:31 pro/fields/flexible-content.php:554
msgid "Name"
msgstr "名前"

#: admin/views/field-group-fields.php:32
msgid "Type"
msgstr "タイプ"

#: admin/views/field-group-fields.php:44
msgid "No fields. Click the <strong>+ Add Field</strong> button to create your first field."
msgstr ""
"フィールドはありません。<strong>+ 新規追加</strong>ボタンをクリックして最初のフィールドを作成してくださ"
"い"

#: admin/views/field-group-fields.php:51
msgid "Drag and drop to reorder"
msgstr "ドラッグアンドドロップで並べ替える"

#: admin/views/field-group-fields.php:54
msgid "+ Add Field"
msgstr "+ フィールドを追加"

#: admin/views/field-group-locations.php:5
msgid "Rules"
msgstr "ルール"

#: admin/views/field-group-locations.php:6
msgid "Create a set of rules to determine which edit screens will use these advanced custom fields"
msgstr "どの編集画面でカスタムフィールドを表示するかを決定するルールを作成します。"

#: admin/views/field-group-locations.php:21
msgid "Show this field group if"
msgstr "このフィールドグループを表示する条件"

#: admin/views/field-group-locations.php:41 admin/views/field-group-locations.php:47
msgid "Post"
msgstr "投稿"

#: admin/views/field-group-locations.php:42 fields/relationship.php:724
msgid "Post Type"
msgstr "投稿タイプ"

#: admin/views/field-group-locations.php:43
msgid "Post Status"
msgstr "投稿ステータス"

#: admin/views/field-group-locations.php:44
msgid "Post Format"
msgstr "投稿フォーマット"

#: admin/views/field-group-locations.php:45
msgid "Post Category"
msgstr "投稿カテゴリー"

#: admin/views/field-group-locations.php:46
msgid "Post Taxonomy"
msgstr "投稿タクソノミー"

#: admin/views/field-group-locations.php:49 admin/views/field-group-locations.php:53
msgid "Page"
msgstr "ページ"

#: admin/views/field-group-locations.php:50
msgid "Page Template"
msgstr "ページテンプレート"

#: admin/views/field-group-locations.php:51
msgid "Page Type"
msgstr "ページタイプ"

#: admin/views/field-group-locations.php:52
msgid "Page Parent"
msgstr "親ページ"

#: admin/views/field-group-locations.php:55 fields/user.php:36
msgid "User"
msgstr "ユーザー"

#: admin/views/field-group-locations.php:56
msgid "Current User"
msgstr "現在のユーザー"

#: admin/views/field-group-locations.php:57
msgid "Current User Role"
msgstr "現在の権限グループ"

#: admin/views/field-group-locations.php:58
msgid "User Form"
msgstr "ユーザーフォーム"

#: admin/views/field-group-locations.php:59
msgid "User Role"
msgstr "権限グループ"

#: admin/views/field-group-locations.php:61 pro/admin/options-page.php:48
msgid "Forms"
msgstr "フォーム"

#: admin/views/field-group-locations.php:62
msgid "Attachment"
msgstr "メディア"

#: admin/views/field-group-locations.php:63
msgid "Taxonomy Term"
msgstr "タクソノミーターム"

#: admin/views/field-group-locations.php:64
msgid "Comment"
msgstr "コメント"

#: admin/views/field-group-locations.php:65
msgid "Widget"
msgstr "ウィジェット"

#: admin/views/field-group-options.php:25
msgid "Style"
msgstr "スタイル"

#: admin/views/field-group-options.php:32
msgid "Standard (WP metabox)"
msgstr "標準（WPメタボックス）"

#: admin/views/field-group-options.php:33
msgid "Seamless (no metabox)"
msgstr "シームレス（メタボックスなし）"

#: admin/views/field-group-options.php:40
msgid "Position"
msgstr "位置"

#: admin/views/field-group-options.php:47
msgid "High (after title)"
msgstr "高（タイトルの後）"

#: admin/views/field-group-options.php:48
msgid "Normal (after content)"
msgstr "通常（コンテンツエディタの後）"

#: admin/views/field-group-options.php:49
msgid "Side"
msgstr "サイド"

#: admin/views/field-group-options.php:57
msgid "Label placement"
msgstr "ラベルの配置"

#: admin/views/field-group-options.php:64 fields/tab.php:159
msgid "Top aligned"
msgstr "上揃え"

#: admin/views/field-group-options.php:65 fields/tab.php:160
msgid "Left aligned"
msgstr "左揃え"

#: admin/views/field-group-options.php:72
msgid "Instruction placement"
msgstr "説明の配置"

#: admin/views/field-group-options.php:79
msgid "Below labels"
msgstr "ラベルの下"

#: admin/views/field-group-options.php:80
msgid "Below fields"
msgstr "フィールドの下"

#: admin/views/field-group-options.php:87
msgid "Order No."
msgstr "順番"

#: admin/views/field-group-options.php:88
msgid "Field groups with a lower order will appear first"
msgstr ""

#: admin/views/field-group-options.php:99
msgid "Shown in field group list"
msgstr ""

#: admin/views/field-group-options.php:109
msgid "Hide on screen"
msgstr "画面に非表示"

#: admin/views/field-group-options.php:110
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "編集画面で<b>表示しない</b>アイテムを<b>選択</b>"

#: admin/views/field-group-options.php:110
msgid ""
"If multiple field groups appear on an edit screen, the first field group's options will be used (the "
"one with the lowest order number)"
msgstr ""

#: admin/views/field-group-options.php:117
msgid "Permalink"
msgstr "パーマリンク"

#: admin/views/field-group-options.php:118
msgid "Content Editor"
msgstr "コンテンツエディタ"

#: admin/views/field-group-options.php:119
msgid "Excerpt"
msgstr "抜粋"

#: admin/views/field-group-options.php:121
msgid "Discussion"
msgstr "ディスカッション"

#: admin/views/field-group-options.php:122
msgid "Comments"
msgstr "コメント"

#: admin/views/field-group-options.php:123
msgid "Revisions"
msgstr "リビジョン"

#: admin/views/field-group-options.php:124
msgid "Slug"
msgstr "スラッグ"

#: admin/views/field-group-options.php:125
msgid "Author"
msgstr "作成者"

#: admin/views/field-group-options.php:126
msgid "Format"
msgstr "フォーマット"

#: admin/views/field-group-options.php:127
msgid "Page Attributes"
msgstr "ページ属性"

#: admin/views/field-group-options.php:128 fields/relationship.php:737
msgid "Featured Image"
msgstr "アイキャッチ画像"

#: admin/views/field-group-options.php:129
msgid "Categories"
msgstr "カテゴリー"

#: admin/views/field-group-options.php:130
msgid "Tags"
msgstr "タグ"

#: admin/views/field-group-options.php:131
msgid "Send Trackbacks"
msgstr "トラックバック"

#: admin/views/settings-addons.php:23
msgid "Download & Install"
msgstr "ダウンロードしてインストール"

#: admin/views/settings-addons.php:42
msgid "Installed"
msgstr "インストール済み"

#: admin/views/settings-info.php:9
msgid "Welcome to Advanced Custom Fields"
msgstr "ようこそ Advanced Custom Fields"

#: admin/views/settings-info.php:10
#, php-format
msgid "Thank you for updating! ACF %s is bigger and better than ever before. We hope you like it."
msgstr ""
"アップグレードありがとうございます！ACF %s は規模、質ともに向上しています。気に入ってもらえたら幸いで"
"す。"

#: admin/views/settings-info.php:23
msgid "A smoother custom field experience"
msgstr "もっとも快適なカスタムフィールド体験"

#: admin/views/settings-info.php:28
msgid "Improved Usability"
msgstr "改良されたユーザビリティ"

#: admin/views/settings-info.php:29
msgid ""
"Including the popular Select2 library has improved both usability and speed across a number of field "
"types including post object, page link, taxonomy and select."
msgstr ""
"内蔵した人気のSelect2ライブラリによって、投稿オブジェクトやページリンク、タクソノミーなど多くのフィール"
"ドタイプにおける選択のユーザビリティと速度の両方を改善しました。"

#: admin/views/settings-info.php:33
msgid "Improved Design"
msgstr "改良されたデザイン"

#: admin/views/settings-info.php:34
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than ever! Noticeable changes are "
"seen on the gallery, relationship and oEmbed (new) fields!"
msgstr ""
"ACFがより良くなるよう、多くのフィールドのデザインを一新しました！目立った変化は、ギャラリーフィールドや"
"関連フィールド、（新しい）oEmbedフィールドでわかるでしょう！"

#: admin/views/settings-info.php:38
msgid "Improved Data"
msgstr "改良されたデータ"

#: admin/views/settings-info.php:39
msgid ""
"Redesigning the data architecture has allowed sub fields to live independently from their parents. This "
"allows you to drag and drop fields in and out of parent fields!"
msgstr ""
"データ構造を再設計したことでサブフィールドは親フィールドから独立して存在できるようになりました。これに"
"よって親フィールドの内外にフィールドをドラッグアンドドロップできるます。"

#: admin/views/settings-info.php:45
msgid "Goodbye Add-ons. Hello PRO"
msgstr "さようならアドオン、こんにちはPRO"

#: admin/views/settings-info.php:50
msgid "Introducing ACF PRO"
msgstr "ACF PRO紹介"

#: admin/views/settings-info.php:51
msgid "We're changing the way premium functionality is delivered in an exciting way!"
msgstr "我々はエキサイティングな方法で有料機能を提供することにしました！"

#: admin/views/settings-info.php:52
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro version of ACF</a>. With both "
"personal and developer licenses available, premium functionality is more affordable and accessible than "
"ever before!"
msgstr ""
"4つのアドオンを<a href=\"%s\">ACFのPROバージョン</a>として組み合わせました。個人または開発者ライセンスに"
"よって、以前よりお手頃な価格で有料機能を利用できます！"

#: admin/views/settings-info.php:56
msgid "Powerful Features"
msgstr "パワフルな機能"

#: admin/views/settings-info.php:57
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content layouts, a beautiful "
"gallery field and the ability to create extra admin options pages!"
msgstr ""
"ACF PROには、繰り返し可能なデータ、柔軟なコンテンツレイアウト、美しいギャラリーフィールド、オプション"
"ページを作成するなど、パワフルな機能が含まれています！"

#: admin/views/settings-info.php:58
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "もっと<a href=\"%s\">ACF PRO の機能</a>を見る。"

#: admin/views/settings-info.php:62
msgid "Easy Upgrading"
msgstr "簡単なアップグレード"

#: admin/views/settings-info.php:63
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> and claim a free copy of "
"ACF PRO!"
msgstr ""
"簡単なアップグレードのために、<a href=\"%s\">ストアアカウントにログイン</a>してACF PROの無料コピーを申請"
"してください。"

#: admin/views/settings-info.php:64
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, but if you do have one, "
"please contact our support team via the <a href=\"%s\">help desk</a>"
msgstr ""
"我々は多くの質問に応えるために<a href=\"%s\">アップグレードガイド</a>を用意していますが、もし質問がある"
"場合は<a href=\"%s\">ヘルプデスク</a>からサポートチームに連絡をしてください。"

#: admin/views/settings-info.php:72
msgid "Under the Hood"
msgstr "その内部では"

#: admin/views/settings-info.php:77
msgid "Smarter field settings"
msgstr "よりスマートなフィールド設定"

#: admin/views/settings-info.php:78
msgid "ACF now saves its field settings as individual post objects"
msgstr "ACFはそれぞれのフィールドを独立した投稿オブジェクトとして保存するようになりました。"

#: admin/views/settings-info.php:82
msgid "More AJAX"
msgstr "いっそうAJAXに"

#: admin/views/settings-info.php:83
msgid "More fields use AJAX powered search to speed up page loading"
msgstr "ページの読み込み速度を高速化するために、より多くのフィールドがAJAXを利用するようになりました。"

#: admin/views/settings-info.php:87
msgid "Local JSON"
msgstr "ローカルJSON"

#: admin/views/settings-info.php:88
msgid "New auto export to JSON feature improves speed"
msgstr "新しいJSON形式の自動エクスポート機能の速度を改善。"

#: admin/views/settings-info.php:94
msgid "Better version control"
msgstr "より良いバージョンコントロール"

#: admin/views/settings-info.php:95
msgid "New auto export to JSON feature allows field settings to be version controlled"
msgstr "新しいJSON形式の自動エクスポート機能は、フィールド設定のバージョンコントロールを可能にします。"

#: admin/views/settings-info.php:99
msgid "Swapped XML for JSON"
msgstr "XMLからJSONへ"

#: admin/views/settings-info.php:100
msgid "Import / Export now uses JSON in favour of XML"
msgstr "インポート / エクスポートにXML形式より優れているJSON形式が使えます。"

#: admin/views/settings-info.php:104
msgid "New Forms"
msgstr "新しいフォーム"

#: admin/views/settings-info.php:105
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr "コメントとウィジェット、全てのユーザーのフォームにフィールドを追加できます。"

#: admin/views/settings-info.php:112
msgid "A new field for embedding content has been added"
msgstr "新しいフィールドに「oEmbed（埋め込みコンテンツ）」を追加しています。"

#: admin/views/settings-info.php:116
msgid "New Gallery"
msgstr "新しいギャラリー"

#: admin/views/settings-info.php:117
msgid "The gallery field has undergone a much needed facelift"
msgstr "ギャラリーフィールドは多くのマイナーチェンジをしています。"

#: admin/views/settings-info.php:121
msgid "New Settings"
msgstr "新しい設定"

#: admin/views/settings-info.php:122
msgid "Field group settings have been added for label placement and instruction placement"
msgstr "フィールドグループの設定に「ラベルの配置」と「説明の配置」を追加しています。"

#: admin/views/settings-info.php:128
msgid "Better Front End Forms"
msgstr "より良いフロントエンドフォーム"

#: admin/views/settings-info.php:129
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form()は新しい投稿をフロントエンドから作成できるようになりました。"

#: admin/views/settings-info.php:133
msgid "Better Validation"
msgstr "より良いバリデーション"

#: admin/views/settings-info.php:134
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr "フォームバリデーションは、JSのみより優れているPHP + AJAXで行われます。"

#: admin/views/settings-info.php:138
msgid "Relationship Field"
msgstr "関連フィールド"

#: admin/views/settings-info.php:139
msgid "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr "関連フィールドの新しい設定「フィルター」（検索、投稿タイプ、タクソノミー）。"

#: admin/views/settings-info.php:145
msgid "Moving Fields"
msgstr "フィールド移動"

#: admin/views/settings-info.php:146
msgid "New field group functionality allows you to move a field between groups & parents"
msgstr ""
"新しいフィールドグループでは、フィールドが親フィールドやフィールドグループ間を移動することができます。"

#: admin/views/settings-info.php:150 fields/page_link.php:36
msgid "Page Link"
msgstr "ページリンク"

#: admin/views/settings-info.php:151
msgid "New archives group in page_link field selection"
msgstr "新しいページリンクの選択肢に「アーカイブグループ」を追加しています。"

#: admin/views/settings-info.php:155
msgid "Better Options Pages"
msgstr "より良いオプションページ"

#: admin/views/settings-info.php:156
msgid "New functions for options page allow creation of both parent and child menu pages"
msgstr "オプションページの新しい機能として、親と子の両方のメニューページを作ることができます。"

#: admin/views/settings-info.php:165
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "%s の変更は、きっと気に入っていただけるでしょう。"

#: admin/views/settings-tools-export.php:13
msgid "Export Field Groups to PHP"
msgstr "フィールドグループを PHP形式 でエクスポートする"

#: admin/views/settings-tools-export.php:17
msgid ""
"The following code can be used to register a local version of the selected field group(s). A local "
"field group can provide many benefits such as faster load times, version control & dynamic fields/"
"settings. Simply copy and paste the following code to your theme's functions.php file or include it "
"within an external file."
msgstr ""
"以下のコードは選択したフィールドグループのローカルバージョンとして登録に使えます。ローカルフィールドグ"
"ループは読み込み時間の短縮やバージョンコントロール、動的なフィールド/設定など多くの利点があります。以下"
"のコードをテーマのfunctions.phpや外部ファイルにコピー＆ペーストしてください。"

#: admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "フィールドグループを選択"

#: admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "フィールドグループをエクスポート"

#: admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export method. Use the download "
"button to export to a .json file which you can then import to another ACF installation. Use the "
"generate button to export to PHP code which you can place in your theme."
msgstr ""
"エクスポートしたいフィールドグループとエクスポート方法を選んでください。ダウンロードボタンでは別のACFを"
"インストールした環境でインポートできるJSONファイルがエクスポートされます。生成ボタンではテーマ内で利用で"
"きるPHPコードが生成されます。"

#: admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "エクスポートファイルをダウンロード"

#: admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "エクスポートコードを生成"

#: admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "フィールドグループをインポート"

#: admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When you click the import button "
"below, ACF will import the field groups."
msgstr ""
"インポートしたいACFのJSONファイルを選択してください。下のインポートボタンをクリックすると、ACFはフィール"
"ドグループをインポートします。"

#: admin/views/settings-tools.php:77 fields/file.php:46
msgid "Select File"
msgstr "ファイルを選択する"

#: admin/views/settings-tools.php:86
msgid "Import"
msgstr "インポート"

#: admin/views/update-network.php:8 admin/views/update.php:8
msgid "Advanced Custom Fields Database Upgrade"
msgstr ""

#: admin/views/update-network.php:10
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update and then click “Upgrade "
"Database”."
msgstr ""

#: admin/views/update-network.php:19 admin/views/update-network.php:27
msgid "Site"
msgstr ""

#: admin/views/update-network.php:47
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr ""

#: admin/views/update-network.php:49
msgid "Site is up to date"
msgstr ""

#: admin/views/update-network.php:62 admin/views/update.php:16
msgid "Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""

#: admin/views/update-network.php:101 admin/views/update-notice.php:35
msgid ""
"It is strongly recommended that you backup your database before proceeding. Are you sure you wish to "
"run the updater now?"
msgstr "処理前にデータベースのバックアップを強く推奨します。アップデーターを実行してもよろしいですか？"

#: admin/views/update-network.php:157
msgid "Upgrade complete"
msgstr ""

#: admin/views/update-network.php:161
msgid "Upgrading data to"
msgstr ""

#: admin/views/update-notice.php:23
msgid "Database Upgrade Required"
msgstr "データベースのアップグレードが必要です"

#: admin/views/update-notice.php:25
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "%s v%sへのアップグレードありがとうございます"

#: admin/views/update-notice.php:25
msgid ""
"Before you start using the new awesome features, please update your database to the newest version."
msgstr "素晴らしい新機能を利用する前にデータベースを最新バージョンに更新してください。"

#: admin/views/update.php:12
msgid "Reading upgrade tasks..."
msgstr "アップグレードタスクを読み込んでいます..."

#: admin/views/update.php:14
#, php-format
msgid "Upgrading data to version %s"
msgstr "バージョン %s へデータアップグレード中"

#: admin/views/update.php:16
msgid "See what's new"
msgstr "新着情報を見る"

#: admin/views/update.php:110
msgid "No updates available."
msgstr ""

#: api/api-helpers.php:821
msgid "Thumbnail"
msgstr "サムネイル"

#: api/api-helpers.php:822
msgid "Medium"
msgstr "中"

#: api/api-helpers.php:823
msgid "Large"
msgstr "大"

#: api/api-helpers.php:871
msgid "Full Size"
msgstr "フルサイズ"

#: api/api-helpers.php:1581
msgid "(no title)"
msgstr "（無題）"

#: api/api-helpers.php:3183
#, php-format
msgid "Image width must be at least %dpx."
msgstr "画像の幅は少なくとも %dpx 必要です。"

#: api/api-helpers.php:3188
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "画像の幅は %dpx を超えてはいけません。"

#: api/api-helpers.php:3204
#, php-format
msgid "Image height must be at least %dpx."
msgstr "画像の高さは少なくとも %dpx 必要です。"

#: api/api-helpers.php:3209
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "画像の高さは %dpx を超えてはいけません。"

#: api/api-helpers.php:3227
#, php-format
msgid "File size must be at least %s."
msgstr "ファイルサイズは少なくとも %s 必要です。"

#: api/api-helpers.php:3232
#, php-format
msgid "File size must must not exceed %s."
msgstr "ファイルサイズは %s を超えてはいけません。"

#: api/api-helpers.php:3266
#, php-format
msgid "File type must be %s."
msgstr "ファイルタイプは %s でなければいけません。"

#: api/api-template.php:1289 pro/fields/gallery.php:564
msgid "Update"
msgstr "更新"

#: api/api-template.php:1290
msgid "Post updated"
msgstr "投稿更新済み"

#: core/field.php:131
msgid "Basic"
msgstr "基本"

#: core/field.php:132
msgid "Content"
msgstr "コンテンツ"

#: core/field.php:133
msgid "Choice"
msgstr "選択肢"

#: core/field.php:134
msgid "Relational"
msgstr "関連"

#: core/field.php:135
msgid "jQuery"
msgstr "jQuery"

#: core/field.php:136 fields/checkbox.php:226 fields/radio.php:231 pro/fields/flexible-content.php:512
#: pro/fields/repeater.php:392
msgid "Layout"
msgstr "レイアウト"

#: core/input.php:129
msgid "Expand Details"
msgstr "詳細を広げる"

#: core/input.php:130
msgid "Collapse Details"
msgstr "詳細を縮める"

#: core/input.php:131
msgid "Validation successful"
msgstr "検証に成功"

#: core/input.php:132
msgid "Validation failed"
msgstr "検証に失敗"

#: core/input.php:133
msgid "1 field requires attention"
msgstr ""

#: core/input.php:134
#, php-format
msgid "%d fields require attention"
msgstr ""

#: core/input.php:135
msgid "Restricted"
msgstr ""

#: core/input.php:533
#, php-format
msgid "%s value is required"
msgstr "%s の値は必須です"

#: fields/checkbox.php:36 fields/taxonomy.php:752
msgid "Checkbox"
msgstr "チェックボックス"

#: fields/checkbox.php:144
msgid "Toggle All"
msgstr "全て 選択 / 解除"

#: fields/checkbox.php:208 fields/radio.php:193 fields/select.php:388
msgid "Choices"
msgstr "選択肢"

#: fields/checkbox.php:209 fields/radio.php:194 fields/select.php:389
msgid "Enter each choice on a new line."
msgstr "選択肢を改行で区切って入力してください"

#: fields/checkbox.php:209 fields/radio.php:194 fields/select.php:389
msgid "For more control, you may specify both a value and label like this:"
msgstr "下記のように記述すると、値とラベルの両方を制御することができます。"

#: fields/checkbox.php:209 fields/radio.php:194 fields/select.php:389
msgid "red : Red"
msgstr "red : 赤"

#: fields/checkbox.php:217 fields/color_picker.php:158 fields/email.php:124 fields/number.php:150
#: fields/radio.php:222 fields/select.php:397 fields/text.php:148 fields/textarea.php:145
#: fields/true_false.php:115 fields/url.php:117 fields/wysiwyg.php:345
msgid "Default Value"
msgstr "デフォルト値"

#: fields/checkbox.php:218 fields/select.php:398
msgid "Enter each default value on a new line"
msgstr "デフォルト値を入力する"

#: fields/checkbox.php:232 fields/radio.php:237
msgid "Vertical"
msgstr "垂直"

#: fields/checkbox.php:233 fields/radio.php:238
msgid "Horizontal"
msgstr "水平"

#: fields/checkbox.php:240
msgid "Toggle"
msgstr ""

#: fields/checkbox.php:241
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""

#: fields/color_picker.php:36
msgid "Color Picker"
msgstr "カラーピッカー"

#: fields/color_picker.php:94
msgid "Clear"
msgstr "クリア"

#: fields/color_picker.php:95
msgid "Default"
msgstr "デフォルト"

#: fields/color_picker.php:96
msgid "Select Color"
msgstr "色を選択"

#: fields/date_picker.php:36
msgid "Date Picker"
msgstr "デイトピッカー"

#: fields/date_picker.php:72
msgid "Done"
msgstr "完了"

#: fields/date_picker.php:73
msgid "Today"
msgstr "本日"

#: fields/date_picker.php:76
msgid "Show a different month"
msgstr "別の月を表示する"

#: fields/date_picker.php:149
msgid "Display Format"
msgstr "表示フォーマット"

#: fields/date_picker.php:150
msgid "The format displayed when editing a post"
msgstr "投稿編集中に表示されるフォーマット"

#: fields/date_picker.php:164
msgid "Return format"
msgstr "返り値"

#: fields/date_picker.php:165
msgid "The format returned via template functions"
msgstr "テンプレート関数で返されるフォーマット"

#: fields/date_picker.php:180
msgid "Week Starts On"
msgstr "週の始まり"

#: fields/email.php:36
msgid "Email"
msgstr "メール"

#: fields/email.php:125 fields/number.php:151 fields/radio.php:223 fields/text.php:149
#: fields/textarea.php:146 fields/url.php:118 fields/wysiwyg.php:346
msgid "Appears when creating a new post"
msgstr "新規投稿を作成時に表示されます"

#: fields/email.php:133 fields/number.php:159 fields/password.php:137 fields/text.php:157
#: fields/textarea.php:154 fields/url.php:126
msgid "Placeholder Text"
msgstr "プレースホルダーのテキスト"

#: fields/email.php:134 fields/number.php:160 fields/password.php:138 fields/text.php:158
#: fields/textarea.php:155 fields/url.php:127
msgid "Appears within the input"
msgstr "入力欄に表示されます"

#: fields/email.php:142 fields/number.php:168 fields/password.php:146 fields/text.php:166
msgid "Prepend"
msgstr "先頭に追加"

#: fields/email.php:143 fields/number.php:169 fields/password.php:147 fields/text.php:167
msgid "Appears before the input"
msgstr "入力欄の先頭に表示されます"

#: fields/email.php:151 fields/number.php:177 fields/password.php:155 fields/text.php:175
msgid "Append"
msgstr "末尾に追加"

#: fields/email.php:152 fields/number.php:178 fields/password.php:156 fields/text.php:176
msgid "Appears after the input"
msgstr "入力欄の末尾に表示されます"

#: fields/file.php:36
msgid "File"
msgstr "ファイル"

#: fields/file.php:47
msgid "Edit File"
msgstr "ファイルを編集する"

#: fields/file.php:48
msgid "Update File"
msgstr "ファイルを更新する"

#: fields/file.php:49 pro/fields/gallery.php:55
msgid "uploaded to this post"
msgstr "この投稿にアップロードされる"

#: fields/file.php:142
msgid "File Name"
msgstr "ファイルネーム"

#: fields/file.php:146
msgid "File Size"
msgstr "ファイルサイズ"

#: fields/file.php:169
msgid "No File selected"
msgstr "ファイルが選択されていません"

#: fields/file.php:169
msgid "Add File"
msgstr "ファイルを追加する"

#: fields/file.php:214 fields/image.php:195 fields/taxonomy.php:821
msgid "Return Value"
msgstr "返り値"

#: fields/file.php:215 fields/image.php:196
msgid "Specify the returned value on front end"
msgstr "フロントエンドへの返り値を指定してください"

#: fields/file.php:220
msgid "File Array"
msgstr "ファイル 配列"

#: fields/file.php:221
msgid "File URL"
msgstr "ファイル URL"

#: fields/file.php:222
msgid "File ID"
msgstr "ファイル ID"

#: fields/file.php:229 fields/image.php:220 pro/fields/gallery.php:647
msgid "Library"
msgstr "ライブラリ"

#: fields/file.php:230 fields/image.php:221 pro/fields/gallery.php:648
msgid "Limit the media library choice"
msgstr "制限するメディアライブラリを選択"

#: fields/file.php:236 fields/image.php:227 pro/fields/gallery.php:654
msgid "Uploaded to post"
msgstr "投稿にアップロードされる"

#: fields/file.php:243 fields/image.php:234 pro/fields/gallery.php:661
msgid "Minimum"
msgstr "最小"

#: fields/file.php:244 fields/file.php:255
msgid "Restrict which files can be uploaded"
msgstr "アップロード可能なファイルを制限"

#: fields/file.php:247 fields/file.php:258 fields/image.php:257 fields/image.php:290
#: pro/fields/gallery.php:684 pro/fields/gallery.php:717
msgid "File size"
msgstr "ファイルサイズ"

#: fields/file.php:254 fields/image.php:267 pro/fields/gallery.php:694
msgid "Maximum"
msgstr "最大"

#: fields/file.php:265 fields/image.php:300 pro/fields/gallery.php:727
msgid "Allowed file types"
msgstr "許可するファイルタイプ"

#: fields/file.php:266 fields/image.php:301 pro/fields/gallery.php:728
msgid "Comma separated list. Leave blank for all types"
msgstr "カンマ区切りのリストで入力。全てのタイプを許可する場合は空白のままで"

#: fields/google-map.php:36
msgid "Google Map"
msgstr "Googleマップ"

#: fields/google-map.php:51
msgid "Locating"
msgstr "場所"

#: fields/google-map.php:52
msgid "Sorry, this browser does not support geolocation"
msgstr "ごめんなさい、このブラウザーはgeolocationに対応していません"

#: fields/google-map.php:135
msgid "Clear location"
msgstr "位置情報をクリア"

#: fields/google-map.php:140
msgid "Find current location"
msgstr "現在の位置情報を検索"

#: fields/google-map.php:141
msgid "Search for address..."
msgstr "住所で検索..."

#: fields/google-map.php:173 fields/google-map.php:184
msgid "Center"
msgstr "センター"

#: fields/google-map.php:174 fields/google-map.php:185
msgid "Center the initial map"
msgstr "マップ初期状態のセンター"

#: fields/google-map.php:198
msgid "Zoom"
msgstr "ズーム"

#: fields/google-map.php:199
msgid "Set the initial zoom level"
msgstr "マップ初期状態のズームレベル"

#: fields/google-map.php:208 fields/image.php:246 fields/image.php:279 fields/oembed.php:262
#: pro/fields/gallery.php:673 pro/fields/gallery.php:706
msgid "Height"
msgstr "高さ"

#: fields/google-map.php:209
msgid "Customise the map height"
msgstr "マップの高さを調整"

#: fields/image.php:36
msgid "Image"
msgstr "画像"

#: fields/image.php:51
msgid "Select Image"
msgstr "画像を選択する"

#: fields/image.php:52 pro/fields/gallery.php:53
msgid "Edit Image"
msgstr "画像を編集する"

#: fields/image.php:53 pro/fields/gallery.php:54
msgid "Update Image"
msgstr "画像を更新する"

#: fields/image.php:54
msgid "Uploaded to this post"
msgstr "この投稿にアップロード済み"

#: fields/image.php:55
msgid "All images"
msgstr "全ての画像"

#: fields/image.php:147
msgid "No image selected"
msgstr "画像が選択されていません"

#: fields/image.php:147
msgid "Add Image"
msgstr "画像を追加する"

#: fields/image.php:201
msgid "Image Array"
msgstr "画像 配列"

#: fields/image.php:202
msgid "Image URL"
msgstr "画像 URL"

#: fields/image.php:203
msgid "Image ID"
msgstr "画像 ID"

#: fields/image.php:210 pro/fields/gallery.php:637
msgid "Preview Size"
msgstr "プレビューサイズ"

#: fields/image.php:211 pro/fields/gallery.php:638
msgid "Shown when entering data"
msgstr "投稿編集中に表示されます"

#: fields/image.php:235 fields/image.php:268 pro/fields/gallery.php:662 pro/fields/gallery.php:695
msgid "Restrict which images can be uploaded"
msgstr "アップロード可能な画像を制限"

#: fields/image.php:238 fields/image.php:271 fields/oembed.php:251 pro/fields/gallery.php:665
#: pro/fields/gallery.php:698
msgid "Width"
msgstr "幅"

#: fields/message.php:36 fields/message.php:103 fields/true_false.php:106
msgid "Message"
msgstr "メッセージ"

#: fields/message.php:104
msgid "Please note that all text will first be passed through the wp function "
msgstr "すべてのテキストが最初にWordPressの関数を通過しますのでご注意ください"

#: fields/message.php:112
msgid "Escape HTML"
msgstr "HTMLをエスケープ"

#: fields/message.php:113
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "HTMLマークアップのコードとして表示を許可"

#: fields/number.php:36
msgid "Number"
msgstr "数値"

#: fields/number.php:186
msgid "Minimum Value"
msgstr "最小値"

#: fields/number.php:195
msgid "Maximum Value"
msgstr "最大値"

#: fields/number.php:204
msgid "Step Size"
msgstr "ステップサイズ"

#: fields/number.php:242
msgid "Value must be a number"
msgstr "値は数値でなければいけません"

#: fields/number.php:260
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "数値は %d 以上でなければいけません"

#: fields/number.php:268
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "数値は %d 以下でなければいけません"

#: fields/oembed.php:36
msgid "oEmbed"
msgstr "oEmbed"

#: fields/oembed.php:199
msgid "Enter URL"
msgstr "URLを入力"

#: fields/oembed.php:212
msgid "No embed found for the given URL."
msgstr "指定されたURLには埋め込む内容がありません."

#: fields/oembed.php:248 fields/oembed.php:259
msgid "Embed Size"
msgstr "埋め込みサイズ"

#: fields/page_link.php:206
msgid "Archives"
msgstr "アーカイブ"

#: fields/page_link.php:535 fields/post_object.php:401 fields/relationship.php:690
msgid "Filter by Post Type"
msgstr "投稿タイプで絞り込み"

#: fields/page_link.php:543 fields/post_object.php:409 fields/relationship.php:698
msgid "All post types"
msgstr "全ての投稿タイプ"

#: fields/page_link.php:549 fields/post_object.php:415 fields/relationship.php:704
msgid "Filter by Taxonomy"
msgstr "タクソノミーで絞り込み"

#: fields/page_link.php:557 fields/post_object.php:423 fields/relationship.php:712
msgid "All taxonomies"
msgstr "全てのタクソノミー"

#: fields/page_link.php:563 fields/post_object.php:429 fields/select.php:406 fields/taxonomy.php:765
#: fields/user.php:452
msgid "Allow Null?"
msgstr "空の値を許可するか？"

#: fields/page_link.php:577 fields/post_object.php:443 fields/select.php:420 fields/user.php:466
msgid "Select multiple values?"
msgstr "複数の値を選択できるか？"

#: fields/password.php:36
msgid "Password"
msgstr "パスワード"

#: fields/post_object.php:36 fields/post_object.php:462 fields/relationship.php:769
msgid "Post Object"
msgstr "投稿オブジェクト"

#: fields/post_object.php:457 fields/relationship.php:764
msgid "Return Format"
msgstr "返り値のフォーマット"

#: fields/post_object.php:463 fields/relationship.php:770
msgid "Post ID"
msgstr "投稿 ID"

#: fields/radio.php:36
msgid "Radio Button"
msgstr "ラジオボタン"

#: fields/radio.php:202
msgid "Other"
msgstr "その他"

#: fields/radio.php:206
msgid "Add 'other' choice to allow for custom values"
msgstr "選択肢「その他」を追加する"

#: fields/radio.php:212
msgid "Save Other"
msgstr "その他を保存"

#: fields/radio.php:216
msgid "Save 'other' values to the field's choices"
msgstr "「その他」の値を選択肢に追加する"

#: fields/relationship.php:36
msgid "Relationship"
msgstr "関連"

#: fields/relationship.php:48
msgid "Minimum values reached ( {min} values )"
msgstr ""

#: fields/relationship.php:49
msgid "Maximum values reached ( {max} values )"
msgstr "最大値( {max} ) に達しました"

#: fields/relationship.php:50
msgid "Loading"
msgstr "読み込み中"

#: fields/relationship.php:51
msgid "No matches found"
msgstr "一致する項目がありません"

#: fields/relationship.php:571
msgid "Search..."
msgstr "検索..."

#: fields/relationship.php:580
msgid "Select post type"
msgstr "投稿タイプを選択"

#: fields/relationship.php:593
msgid "Select taxonomy"
msgstr "タクソノミーを選択"

#: fields/relationship.php:723
msgid "Search"
msgstr "検索"

#: fields/relationship.php:725 fields/taxonomy.php:36 fields/taxonomy.php:735
msgid "Taxonomy"
msgstr "タクソノミー"

#: fields/relationship.php:732
msgid "Elements"
msgstr "要素"

#: fields/relationship.php:733
msgid "Selected elements will be displayed in each result"
msgstr "選択した要素が表示されます。"

#: fields/relationship.php:744
msgid "Minimum posts"
msgstr ""

#: fields/relationship.php:753
msgid "Maximum posts"
msgstr "最大投稿数"

#: fields/select.php:36 fields/select.php:174 fields/taxonomy.php:757
msgid "Select"
msgstr "セレクトボックス"

#: fields/select.php:434
msgid "Stylised UI"
msgstr "スタイリッシュなUI"

#: fields/select.php:448
msgid "Use AJAX to lazy load choices?"
msgstr "選択肢をAJAXで遅延ロードするか？"

#: fields/tab.php:36
msgid "Tab"
msgstr "タブ"

#: fields/tab.php:128
msgid "Warning"
msgstr "注意"

#: fields/tab.php:133
msgid ""
"The tab field will display incorrectly when added to a Table style repeater field or flexible content "
"field layout"
msgstr ""
"このタブは、テーブルスタイルの繰り返しフィールドか柔軟コンテンツフィールドが追加された場合、正しく表示さ"
"れません"

#: fields/tab.php:146
msgid "Use \"Tab Fields\" to better organize your edit screen by grouping fields together."
msgstr "\"タブ\" を使うとフィールドのグループ化によって編集画面をより整理できます。"

#: fields/tab.php:148
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is defined) will be grouped "
"together using this field's label as the tab heading."
msgstr ""
"この\"タブ\" の後に続く（または別の \"タブ\" が定義されるまでの）全てのフィールドは、このフィールドのラ"
"ベルがタブの見出しとなりグループ化されます。"

#: fields/tab.php:155
msgid "Placement"
msgstr "タブの配置"

#: fields/tab.php:167
msgid "End-point"
msgstr ""

#: fields/tab.php:168
msgid "Use this field as an end-point and start a new group of tabs"
msgstr ""

#: fields/taxonomy.php:565
#, php-format
msgid "Add new %s "
msgstr ""

#: fields/taxonomy.php:704
msgid "None"
msgstr "無"

#: fields/taxonomy.php:736
msgid "Select the taxonomy to be displayed"
msgstr ""

#: fields/taxonomy.php:745
msgid "Appearance"
msgstr ""

#: fields/taxonomy.php:746
msgid "Select the appearance of this field"
msgstr ""

#: fields/taxonomy.php:751
msgid "Multiple Values"
msgstr "複数値"

#: fields/taxonomy.php:753
msgid "Multi Select"
msgstr "複数選択"

#: fields/taxonomy.php:755
msgid "Single Value"
msgstr "単一値"

#: fields/taxonomy.php:756
msgid "Radio Buttons"
msgstr "ラジオボタン"

#: fields/taxonomy.php:779
msgid "Create Terms"
msgstr ""

#: fields/taxonomy.php:780
msgid "Allow new terms to be created whilst editing"
msgstr ""

#: fields/taxonomy.php:793
msgid "Save Terms"
msgstr ""

#: fields/taxonomy.php:794
msgid "Connect selected terms to the post"
msgstr ""

#: fields/taxonomy.php:807
msgid "Load Terms"
msgstr ""

#: fields/taxonomy.php:808
msgid "Load value from posts terms"
msgstr ""

#: fields/taxonomy.php:826
msgid "Term Object"
msgstr "タームオブジェクト"

#: fields/taxonomy.php:827
msgid "Term ID"
msgstr "ターム ID"

#: fields/taxonomy.php:886
#, php-format
msgid "User unable to add new %s"
msgstr ""

#: fields/taxonomy.php:899
#, php-format
msgid "%s already exists"
msgstr ""

#: fields/taxonomy.php:940
#, php-format
msgid "%s added"
msgstr ""

#: fields/taxonomy.php:985
msgid "Add"
msgstr ""

#: fields/text.php:36
msgid "Text"
msgstr "テキスト"

#: fields/text.php:184 fields/textarea.php:163
msgid "Character Limit"
msgstr "制限文字数"

#: fields/text.php:185 fields/textarea.php:164
msgid "Leave blank for no limit"
msgstr "制限しない場合は空白のままで"

#: fields/textarea.php:36
msgid "Text Area"
msgstr "テキストエリア"

#: fields/textarea.php:172
msgid "Rows"
msgstr "行数"

#: fields/textarea.php:173
msgid "Sets the textarea height"
msgstr "テキストエリアの高さを指定"

#: fields/textarea.php:182
msgid "New Lines"
msgstr "改行"

#: fields/textarea.php:183
msgid "Controls how new lines are rendered"
msgstr "改行をどのように表示するか制御"

#: fields/textarea.php:187
msgid "Automatically add paragraphs"
msgstr "自動的に段落に変換"

#: fields/textarea.php:188
msgid "Automatically add &lt;br&gt;"
msgstr "自動的に&lt;br&gt;に変換"

#: fields/textarea.php:189
msgid "No Formatting"
msgstr "なにもしない"

#: fields/true_false.php:36
msgid "True / False"
msgstr "真 / 偽"

#: fields/true_false.php:107
msgid "eg. Show extra content"
msgstr "例：追加コンテンツを表示する"

#: fields/url.php:36
msgid "Url"
msgstr "URL"

#: fields/url.php:160
msgid "Value must be a valid URL"
msgstr "値はURL形式でなければいけません"

#: fields/user.php:437
msgid "Filter by role"
msgstr "ロールでフィルタする"

#: fields/user.php:445
msgid "All user roles"
msgstr "全ての権限グループ"

#: fields/wysiwyg.php:37
msgid "Wysiwyg Editor"
msgstr "Wysiwyg エディタ"

#: fields/wysiwyg.php:297
msgid "Visual"
msgstr "ビジュアル"

#: fields/wysiwyg.php:298
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "テキスト"

#: fields/wysiwyg.php:354
msgid "Tabs"
msgstr "タブ"

#: fields/wysiwyg.php:359
msgid "Visual & Text"
msgstr "ビジュアル＆テキスト"

#: fields/wysiwyg.php:360
msgid "Visual Only"
msgstr "ビジュアルのみ"

#: fields/wysiwyg.php:361
msgid "Text Only"
msgstr "テキストのみ"

#: fields/wysiwyg.php:368
msgid "Toolbar"
msgstr "ツールバー"

#: fields/wysiwyg.php:378
msgid "Show Media Upload Buttons?"
msgstr "メディアアップロードボタンを表示するか？"

#: forms/post.php:297 pro/admin/options-page.php:373
msgid "Edit field group"
msgstr "フィールドグループを編集"

#: pro/acf-pro.php:24
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/acf-pro.php:175
msgid "Flexible Content requires at least 1 layout"
msgstr "柔軟コンテンツは少なくとも1個のレイアウトが必要です"

#: pro/admin/options-page.php:48
msgid "Options Page"
msgstr "オプションページ"

#: pro/admin/options-page.php:83
msgid "No options pages exist"
msgstr "オプションページはありません"

#: pro/admin/options-page.php:298
msgid "Options Updated"
msgstr "オプションを更新しました"

#: pro/admin/options-page.php:304
msgid "No Custom Field Groups found for this options page. <a href=\"%s\">Create a Custom Field Group</a>"
msgstr ""
"このオプションページにカスタムフィールドグループがありません. <a href=\"%s\">カスタムフィールドグループ"
"を作成</a>"

#: pro/admin/settings-updates.php:137
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>エラー</b> 更新サーバーに接続できません"

#: pro/admin/settings-updates.php:267 pro/admin/settings-updates.php:338
msgid "<b>Connection Error</b>. Sorry, please try again"
msgstr "<b>接続エラー</b> すみません、もう一度試してみてください"

#: pro/admin/views/options-page.php:48
msgid "Publish"
msgstr "公開"

#: pro/admin/views/options-page.php:54
msgid "Save Options"
msgstr "オプションを保存"

#: pro/admin/views/settings-updates.php:11
msgid "Deactivate License"
msgstr "ライセンスのアクティベートを解除"

#: pro/admin/views/settings-updates.php:11
msgid "Activate License"
msgstr "ライセンスをアクティベート"

#: pro/admin/views/settings-updates.php:21
msgid "License"
msgstr "ライセンス"

#: pro/admin/views/settings-updates.php:24
msgid ""
"To unlock updates, please enter your license key below. If you don't have a licence key, please see"
msgstr ""
"アップデートのロックを解除するには、以下にライセンスキーを入力してください。ライセンスキーを持っていない"
"場合は、こちらを参照してください。"

#: pro/admin/views/settings-updates.php:24
msgid "details & pricing"
msgstr "価格と詳細"

#: pro/admin/views/settings-updates.php:33
msgid "License Key"
msgstr "ライセンスキー"

#: pro/admin/views/settings-updates.php:65
msgid "Update Information"
msgstr "アップデート情報"

#: pro/admin/views/settings-updates.php:72
msgid "Current Version"
msgstr "現在のバージョン"

#: pro/admin/views/settings-updates.php:80
msgid "Latest Version"
msgstr "最新のバージョン"

#: pro/admin/views/settings-updates.php:88
msgid "Update Available"
msgstr "利用可能なアップデート"

#: pro/admin/views/settings-updates.php:96
msgid "Update Plugin"
msgstr "プラグインをアップデート"

#: pro/admin/views/settings-updates.php:98
msgid "Please enter your license key above to unlock updates"
msgstr "アップデートのロックを解除するためにライセンスキーを入力してください"

#: pro/admin/views/settings-updates.php:104
msgid "Check Again"
msgstr "再確認"

#: pro/admin/views/settings-updates.php:121
msgid "Upgrade Notice"
msgstr "アップグレード通知"

#: pro/api/api-options-page.php:22 pro/api/api-options-page.php:23
msgid "Options"
msgstr "オプション"

#: pro/core/updates.php:186
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s\">Updates</a> page. If you don't "
"have a licence key, please see <a href=\"%s\">details & pricing</a>"
msgstr ""
"アップデートを有効にするには、<a href=\"%s\">アップデート</a>ページにライセンスキーを入力してください。"
"ライセンスキーを持っていない場合は、こちらを<a href=\"%s\">詳細と価格</a>参照してください。"

#: pro/fields/flexible-content.php:36
msgid "Flexible Content"
msgstr "柔軟コンテンツ"

#: pro/fields/flexible-content.php:42 pro/fields/repeater.php:43
msgid "Add Row"
msgstr "行を追加"

#: pro/fields/flexible-content.php:45
msgid "layout"
msgstr "レイアウト"

#: pro/fields/flexible-content.php:46
msgid "layouts"
msgstr "レイアウト"

#: pro/fields/flexible-content.php:47
msgid "remove {layout}?"
msgstr "{layout} を削除しますか？"

#: pro/fields/flexible-content.php:48
msgid "This field requires at least {min} {identifier}"
msgstr "このフィールドは{identifier}が最低{min}個は必要です"

#: pro/fields/flexible-content.php:49
msgid "This field has a limit of {max} {identifier}"
msgstr "このフィールドは{identifier}が最高{max}個までです"

#: pro/fields/flexible-content.php:50
msgid "This field requires at least {min} {label} {identifier}"
msgstr "{identifier}に{label}は最低{min}個必要です"

#: pro/fields/flexible-content.php:51
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "{label}は最大数に達しました（{max} {identifier}）"

#: pro/fields/flexible-content.php:52
msgid "{available} {label} {identifier} available (max {max})"
msgstr "あと{available}個 {identifier}には {label} を利用できます（最大 {max}個）"

#: pro/fields/flexible-content.php:53
msgid "{required} {label} {identifier} required (min {min})"
msgstr "あと{required}個 {identifier}には {label} を利用する必要があります（最小 {max}個）"

#: pro/fields/flexible-content.php:211
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "下の \"%s\" ボタンをクリックしてレイアウトの作成を始めてください"

#: pro/fields/flexible-content.php:369
msgid "Add layout"
msgstr "レイアウトを追加"

#: pro/fields/flexible-content.php:372
msgid "Remove layout"
msgstr "レイアウトを削除"

#: pro/fields/flexible-content.php:514
msgid "Reorder Layout"
msgstr "レイアウトを並べ替え"

#: pro/fields/flexible-content.php:514
msgid "Reorder"
msgstr "並べ替え"

#: pro/fields/flexible-content.php:515
msgid "Delete Layout"
msgstr "レイアウトを削除"

#: pro/fields/flexible-content.php:516
msgid "Duplicate Layout"
msgstr "レイアウトを複製"

#: pro/fields/flexible-content.php:517
msgid "Add New Layout"
msgstr "新しいレイアウトを追加"

#: pro/fields/flexible-content.php:561
msgid "Display"
msgstr "表示"

#: pro/fields/flexible-content.php:572 pro/fields/repeater.php:399
msgid "Table"
msgstr "表"

#: pro/fields/flexible-content.php:573 pro/fields/repeater.php:400
msgid "Block"
msgstr "ブロック"

#: pro/fields/flexible-content.php:574 pro/fields/repeater.php:401
msgid "Row"
msgstr "行"

#: pro/fields/flexible-content.php:589
msgid "Min"
msgstr "最小数"

#: pro/fields/flexible-content.php:602
msgid "Max"
msgstr "最大数"

#: pro/fields/flexible-content.php:630 pro/fields/repeater.php:408
msgid "Button Label"
msgstr "ボタンのラベル"

#: pro/fields/flexible-content.php:639
msgid "Minimum Layouts"
msgstr "レイアウトの最小数"

#: pro/fields/flexible-content.php:648
msgid "Maximum Layouts"
msgstr "レイアウトの最大数"

#: pro/fields/gallery.php:36
msgid "Gallery"
msgstr "ギャラリー"

#: pro/fields/gallery.php:52
msgid "Add Image to Gallery"
msgstr "ギャラリーに画像を追加"

#: pro/fields/gallery.php:56
msgid "Maximum selection reached"
msgstr "選択の最大数に到達しました"

#: pro/fields/gallery.php:335
msgid "Length"
msgstr "長さ"

#: pro/fields/gallery.php:355
msgid "Remove"
msgstr "取り除く"

#: pro/fields/gallery.php:535
msgid "Add to gallery"
msgstr "ギャラリーを追加"

#: pro/fields/gallery.php:539
msgid "Bulk actions"
msgstr "一括操作"

#: pro/fields/gallery.php:540
msgid "Sort by date uploaded"
msgstr "アップロード日で並べ替え"

#: pro/fields/gallery.php:541
msgid "Sort by date modified"
msgstr "変更日で並び替え"

#: pro/fields/gallery.php:542
msgid "Sort by title"
msgstr "タイトルで並び替え"

#: pro/fields/gallery.php:543
msgid "Reverse current order"
msgstr "並び順を逆にする"

#: pro/fields/gallery.php:561
msgid "Close"
msgstr "閉じる"

#: pro/fields/gallery.php:619
msgid "Minimum Selection"
msgstr "最小選択数"

#: pro/fields/gallery.php:628
msgid "Maximum Selection"
msgstr "最大選択数"

#: pro/fields/gallery.php:809
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s は少なくとも %s個 選択してください"

#: pro/fields/repeater.php:36
msgid "Repeater"
msgstr "繰り返しフィールド"

#: pro/fields/repeater.php:46
msgid "Minimum rows reached ({min} rows)"
msgstr "最小行数に達しました（{min} 行）"

#: pro/fields/repeater.php:47
msgid "Maximum rows reached ({max} rows)"
msgstr "最大行数に達しました（{max} 行）"

#: pro/fields/repeater.php:259
msgid "Drag to reorder"
msgstr "ドラッグして並び替え"

#: pro/fields/repeater.php:301
msgid "Add row"
msgstr "行を追加"

#: pro/fields/repeater.php:302
msgid "Remove row"
msgstr "行を削除"

#: pro/fields/repeater.php:350
msgid "Sub Fields"
msgstr "サブフィールド"

#: pro/fields/repeater.php:372
msgid "Minimum Rows"
msgstr "最小行数"

#: pro/fields/repeater.php:382
msgid "Maximum Rows"
msgstr "最大行数"

#. Plugin Name of the plugin/theme
msgid "Advanced Custom Fields Pro"
msgstr ""

#. Plugin URI of the plugin/theme
msgid "http://www.advancedcustomfields.com/"
msgstr ""

#. Description of the plugin/theme
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""

#. Author of the plugin/theme
msgid "elliot condon"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr ""

#~ msgid "Hide / Show All"
#~ msgstr "全て 非表示 / 表示"

#~ msgid "Show Field Keys"
#~ msgstr "フィールドキーを表示"

#~ msgid "Pending Review"
#~ msgstr "レビュー待ち"

#~ msgid "Draft"
#~ msgstr "下書き"

#~ msgid "Future"
#~ msgstr "予約投稿"

#~ msgid "Private"
#~ msgstr "非公開"

#~ msgid "Revision"
#~ msgstr "リビジョン"

#~ msgid "Trash"
#~ msgstr "ゴミ箱"

#~ msgid "Import / Export"
#~ msgstr "インポート / エクスポート"

#~ msgid "Field groups are created in order <br />from lowest to highest"
#~ msgstr "フィールドグループは、順番が小さいほうから大きいほうへ作成されます"

#~ msgid ""
#~ "If multiple field groups appear on an edit screen, the first field group's options will be used. "
#~ "(the one with the lowest order number)"
#~ msgstr ""
#~ "編集画面に複数のフィールドグループが表示される場合、最初の（=順番の最も小さい）フィールドグループのオ"
#~ "プションが使用されます。"

#~ msgid "ACF PRO Required"
#~ msgstr "ACF PROが必要です"

#~ msgid ""
#~ "We have detected an issue which requires your attention: This website makes use of premium add-ons "
#~ "(%s) which are no longer compatible with ACF."
#~ msgstr ""
#~ "あなたに注意すべき問題があります：有料アドオン（%s）を利用したこのウェブサイトにACFはもはや対応してい"
#~ "ません。"

#~ msgid "Don't panic, you can simply roll back the plugin and continue using ACF as you know it!"
#~ msgstr ""
#~ "慌てないでください、プラグインをロールバックすることで今までどおりACFを使用し続けることができます！"

#~ msgid "Roll back to ACF v%s"
#~ msgstr "ACF v%sにロールバックする"

#~ msgid "Learn why ACF PRO is required for my site"
#~ msgstr "なぜ自分のサイトにACF PROが必要なのか学ぶ"

#~ msgid "Update Database"
#~ msgstr "データベースを更新"

#~ msgid "Data Upgrade"
#~ msgstr "データアップグレード"

#~ msgid "Data upgraded successfully."
#~ msgstr "データアップグレード成功"

#~ msgid "Data is at the latest version."
#~ msgstr "データは最新バージョンです"

#~ msgid "1 required field below is empty"
#~ msgid_plural "%s required fields below are empty"
#~ msgstr[0] "下記の %s個 の必須フィールドが空です"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "ターム情報の読込／保存"

#~ msgid "Load value based on the post's terms and update the post's terms on save"
#~ msgstr "投稿ターム情報を読み込み、保存時に反映させる"

#~ msgid "Top Level Page (parent of 0)"
#~ msgstr "一番上の階層（親ページがない）"

#~ msgid "Logged in User Type"
#~ msgstr "ログインしているユーザーのタイプ"

#~ msgid "Field&nbsp;Groups"
#~ msgstr "フィールドグループ"

#~ msgid "Custom field updated."
#~ msgstr "カスタムフィールドを更新しました"

#~ msgid "Custom field deleted."
#~ msgstr "カスタムフィールドを削除しました"

#~ msgid "Field group restored to revision from %s"
#~ msgstr "リビジョン %s からフィールドグループを復元しました"

#~ msgid "Full"
#~ msgstr "フルサイズ"

#~ msgid "No ACF groups selected"
#~ msgstr "ACF グループが選択されていません"

#~ msgid "Repeater Field"
#~ msgstr "繰り返しフィールド"

#~ msgid "Create infinite rows of repeatable data with this versatile interface!"
#~ msgstr "繰り返し挿入可能なフォームを、すてきなインターフェースで作成します。"

#~ msgid "Gallery Field"
#~ msgstr "ギャラリーフィールド"

#~ msgid "Create image galleries in a simple and intuitive interface!"
#~ msgstr "画像ギャラリーを、シンプルで直感的なインターフェースで作成します。"

#~ msgid "Create global data to use throughout your website!"
#~ msgstr "ウェブサイト全体で使用できるグローバルデータを作成します。"

#~ msgid "Flexible Content Field"
#~ msgstr "柔軟コンテンツフィールド"

#~ msgid "Create unique designs with a flexible content layout manager!"
#~ msgstr "柔軟なコンテンツレイアウト管理により、すてきなデザインを作成します。"

#~ msgid "Gravity Forms Field"
#~ msgstr "Gravity Forms フィールド"

#~ msgid "Creates a select field populated with Gravity Forms!"
#~ msgstr "Creates a select field populated with Gravity Forms!"

#, fuzzy
#~ msgid "Date & Time Picker"
#~ msgstr "デイトピッカー"

#~ msgid "jQuery date & time picker"
#~ msgstr "jQuery デイトタイムピッカー"

#, fuzzy
#~ msgid "Location Field"
#~ msgstr "位置"

#~ msgid "Find addresses and coordinates of a desired location"
#~ msgstr "Find addresses and coordinates of a desired location"

#, fuzzy
#~ msgid "Contact Form 7 Field"
#~ msgstr "カスタムフィールド"

#~ msgid "Assign one or more contact form 7 forms to a post"
#~ msgstr "Assign one or more contact form 7 forms to a post"

#, fuzzy
#~ msgid "Advanced Custom Fields Add-Ons"
#~ msgstr "Advanced Custom Fields"

#~ msgid ""
#~ "The following Add-ons are available to increase the functionality of the Advanced Custom Fields "
#~ "plugin."
#~ msgstr "Advanced Custom Fields プラグインに機能を追加するアドオンが利用できます。"

#~ msgid ""
#~ "Each Add-on can be installed as a separate plugin (receives updates) or included in your theme (does "
#~ "not receive updates)."
#~ msgstr ""
#~ "それぞれのアドオンは、個別のプラグインとしてインストールする(管理画面で更新できる)か、テーマに含める"
#~ "(管理画面で更新できない)かしてください。"

#~ msgid "Purchase & Install"
#~ msgstr "購入してインストールする"

#~ msgid "Download"
#~ msgstr "ダウンロードする"

#, fuzzy
#~ msgid "Export"
#~ msgstr "XML をエクスポートする"

#, fuzzy
#~ msgid "Select the field groups to be exported"
#~ msgstr "一覧からフィールドグループを選択し、\"XML をエクスポートする\" をクリックしてください"

#, fuzzy
#~ msgid "Export to XML"
#~ msgstr "XML をエクスポートする"

#, fuzzy
#~ msgid "Export to PHP"
#~ msgstr "フィールドグループを PHP 形式でエクスポートする"

#~ msgid "ACF will create a .xml export file which is compatible with the native WP import plugin."
#~ msgstr ""
#~ "ACF は .xml 形式のエクスポートファイルを作成します。WP のインポートプラグインと互換性があります。"

#~ msgid ""
#~ "Imported field groups <b>will</b> appear in the list of editable field groups. This is useful for "
#~ "migrating fields groups between Wp websites."
#~ msgstr ""
#~ "インポートしたフィールドグループは、編集可能なフィールドグループの一覧に表示されます。WP ウェブサイト"
#~ "間でフィールドグループを移行するのに役立ちます。"

#~ msgid "Select field group(s) from the list and click \"Export XML\""
#~ msgstr "一覧からフィールドグループを選択し、\"XML をエクスポートする\" をクリックしてください"

#~ msgid "Save the .xml file when prompted"
#~ msgstr "指示に従って .xml ファイルを保存してください"

#~ msgid "Navigate to Tools &raquo; Import and select WordPress"
#~ msgstr "ツール &raquo; インポートと進み、WordPress を選択してください"

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "(インストールを促された場合は) WP インポートプラグインをインストールしてください"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "エクスポートした .xml ファイルをアップロードし、インポートする"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "ユーザーを選択するが、Import Attachments を選択しない"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "これで OK です。WordPress をお楽しみください"

#~ msgid "ACF will create the PHP code to include in your theme."
#~ msgstr "ACF は、テーマに含める PHP コードを作成します"

#~ msgid ""
#~ "Registered field groups <b>will not</b> appear in the list of editable field groups. This is useful "
#~ "for including fields in themes."
#~ msgstr ""
#~ "登録したフィールドグループは、編集可能なフィールドグループの一覧に<b>表示されません</b>。テーマに"
#~ "フィールドを含めるときに役立ちます。"

#~ msgid ""
#~ "Please note that if you export and register field groups within the same WP, you will see duplicate "
#~ "fields on your edit screens. To fix this, please move the original field group to the trash or "
#~ "remove the code from your functions.php file."
#~ msgstr ""
#~ "同一の WP でフィールドグループをエクスポートして登録する場合は、編集画面で重複フィールドになることに"
#~ "注意してください。これを修正するには、元のフィールドグループをゴミ箱へ移動するか、functions.php ファ"
#~ "イルからこのコードを除去してください。"

#~ msgid "Select field group(s) from the list and click \"Create PHP\""
#~ msgstr "一覧からフィールドグループを選択し、\"PHP 形式のデータを作成する\" をクリックしてください。"

#~ msgid "Copy the PHP code generated"
#~ msgstr "生成された PHP コードをコピーし、"

#~ msgid "Paste into your functions.php file"
#~ msgstr "functions.php に貼り付けてください"

#~ msgid "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr "アドオンを有効化するには、最初の何行かのコードを編集して使用してください"

#~ msgid "Notes"
#~ msgstr "注意"

#~ msgid "Include in theme"
#~ msgstr "テーマに含める"

#~ msgid ""
#~ "The Advanced Custom Fields plugin can be included within a theme. To do so, move the ACF plugin "
#~ "inside your theme and add the following code to your functions.php file:"
#~ msgstr ""
#~ "Advanced Custom Fields プラグインは、テーマに含めることができます。プラグインをテーマ内に移動し、"
#~ "functions.php に下記コードを追加してください。"

#~ msgid ""
#~ "To remove all visual interfaces from the ACF plugin, you can use a constant to enable lite mode. Add "
#~ "the following code to your functions.php file <b>before</b> the include_once code:"
#~ msgstr ""
#~ "Advanced Custom Fields プラグインのビジュアルインターフェースを取り除くには、定数を利用して「ライト"
#~ "モード」を有効にすることができます。functions.php の include_once よりも<b>前</b>に下記のコードを追加"
#~ "してください。"

#, fuzzy
#~ msgid "Back to export"
#~ msgstr "設定に戻る"

#~ msgid ""
#~ "/**\n"
#~ " *  Install Add-ons\n"
#~ " *  \n"
#~ " *  The following code will include all 4 premium Add-Ons in your theme.\n"
#~ " *  Please do not attempt to include a file which does not exist. This will produce an error.\n"
#~ " *  \n"
#~ " *  All fields must be included during the 'acf/register_fields' action.\n"
#~ " *  Other types of Add-ons (like the options page) can be included outside of this action.\n"
#~ " *  \n"
#~ " *  The following code assumes you have a folder 'add-ons' inside your theme.\n"
#~ " *\n"
#~ " *  IMPORTANT\n"
#~ " *  Add-ons may be included in a premium theme as outlined in the terms and conditions.\n"
#~ " *  However, they are NOT to be included in a premium / free plugin.\n"
#~ " *  For more information, please read http://www.advancedcustomfields.com/terms-conditions/\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " *  Install Add-ons\n"
#~ " *  \n"
#~ " *  The following code will include all 4 premium Add-Ons in your theme.\n"
#~ " *  Please do not attempt to include a file which does not exist. This will produce an error.\n"
#~ " *  \n"
#~ " *  All fields must be included during the 'acf/register_fields' action.\n"
#~ " *  Other types of Add-ons (like the options page) can be included outside of this action.\n"
#~ " *  \n"
#~ " *  The following code assumes you have a folder 'add-ons' inside your theme.\n"
#~ " *\n"
#~ " *  IMPORTANT\n"
#~ " *  Add-ons may be included in a premium theme as outlined in the terms and conditions.\n"
#~ " *  However, they are NOT to be included in a premium / free plugin.\n"
#~ " *  For more information, please read http://www.advancedcustomfields.com/terms-conditions/\n"
#~ " */"

#, fuzzy
#~ msgid ""
#~ "/**\n"
#~ " *  Register Field Groups\n"
#~ " *\n"
#~ " *  The register_field_group function accepts 1 array which holds the relevant data to register a "
#~ "field group\n"
#~ " *  You may edit the array as you see fit. However, this may result in errors if the array is not "
#~ "compatible with ACF\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * フィールドグループを登録する\n"
#~ " * register_field_group 関数は、フィールドグループを登録するのに関係するデータを持っている一つの配列"
#~ "を受け付けます。\n"
#~ " * 配列を好きなように編集することができます。しかし、配列が ACF と互換性の無い場合、エラーになってし"
#~ "まいます。\n"
#~ " * このコードは、functions.php ファイルを読み込む度に実行する必要があります。\n"
#~ " */"

#~ msgid "No field groups were selected"
#~ msgstr "フィールドグループが選択されていません"

#, fuzzy
#~ msgid "Show Field Key:"
#~ msgstr "フィールドキー"

#~ msgid "Vote"
#~ msgstr "投票"

#~ msgid "Follow"
#~ msgstr "フォロー"

#~ msgid "Thank you for updating to the latest version!"
#~ msgstr "最新版への更新ありがとうございます。"

#~ msgid "is more polished and enjoyable than ever before. We hope you like it."
#~ msgstr "は以前よりも洗練され、より良くなりました。気に入ってもらえると嬉しいです。"

#~ msgid "What’s New"
#~ msgstr "更新情報"

#, fuzzy
#~ msgid "Download Add-ons"
#~ msgstr "アドオンを探す"

#~ msgid "Activation codes have grown into plugins!"
#~ msgstr "アクティベーションコードから、プラグインに変更されました。"

#~ msgid ""
#~ "Add-ons are now activated by downloading and installing individual plugins. Although these plugins "
#~ "will not be hosted on the wordpress.org repository, each Add-on will continue to receive updates in "
#~ "the usual way."
#~ msgstr ""
#~ "アドオンは、個別のプラグインをダウンロードしてインストールしてください。wordpress.org リポジトリには"
#~ "ありませんが、管理画面でこれらのアドオンの更新を行う事が出来ます。"

#~ msgid "All previous Add-ons have been successfully installed"
#~ msgstr "今まで使用していたアドオンがインストールされました。"

#~ msgid "This website uses premium Add-ons which need to be downloaded"
#~ msgstr ""
#~ "このウェブサイトではプレミアムアドオンが使用されており、アドオンをダウンロードする必要があります。"

#, fuzzy
#~ msgid "Download your activated Add-ons"
#~ msgstr "アドオンを有効化する"

#~ msgid "This website does not use premium Add-ons and will not be affected by this change."
#~ msgstr "このウェブサイトではプレミアムアドオンを使用しておらず、この変更に影響されません。"

#~ msgid "Easier Development"
#~ msgstr "開発を容易に"

#, fuzzy
#~ msgid "New Field Types"
#~ msgstr "フィールドタイプ"

#, fuzzy
#~ msgid "Taxonomy Field"
#~ msgstr "タクソノミー"

#, fuzzy
#~ msgid "User Field"
#~ msgstr "フィールドを閉じる"

#, fuzzy
#~ msgid "Email Field"
#~ msgstr "ギャラリーフィールド"

#, fuzzy
#~ msgid "Password Field"
#~ msgstr "新規フィールド"

#, fuzzy
#~ msgid "Custom Field Types"
#~ msgstr "カスタムフィールド"

#~ msgid ""
#~ "Creating your own field type has never been easier! Unfortunately, version 3 field types are not "
#~ "compatible with version 4."
#~ msgstr ""
#~ "独自のフィールドタイプが簡単に作成できます。残念ですが、バージョン 3 とバージョン 4 には互換性があり"
#~ "ません。"

#~ msgid "Migrating your field types is easy, please"
#~ msgstr "フィールドタイプをマイグレーションするのは簡単です。"

#~ msgid "follow this tutorial"
#~ msgstr "このチュートリアルに従ってください。"

#~ msgid "to learn more."
#~ msgstr "詳細を見る"

#~ msgid "Actions &amp; Filters"
#~ msgstr "アクションとフィルター"

#~ msgid ""
#~ "All actions & filters have received a major facelift to make customizing ACF even easier! Please"
#~ msgstr "カスタマイズを簡単にするため、すべてのアクションとフィルターを改装しました。"

#, fuzzy
#~ msgid "read this guide"
#~ msgstr "このフィールドを編集する"

#~ msgid "to find the updated naming convention."
#~ msgstr "新しい命名規則をごらんください。"

#~ msgid "Preview draft is now working!"
#~ msgstr "プレビューが有効になりました。"

#~ msgid "This bug has been squashed along with many other little critters!"
#~ msgstr "このバグを修正しました。"

#~ msgid "See the full changelog"
#~ msgstr "全ての更新履歴を見る"

#~ msgid "Important"
#~ msgstr "重要"

#~ msgid "Database Changes"
#~ msgstr "データベース更新"

#~ msgid ""
#~ "Absolutely <strong>no</strong> changes have been made to the database between versions 3 and 4. This "
#~ "means you can roll back to version 3 without any issues."
#~ msgstr ""
#~ "バージョン 3 と 4 でデータベースの更新はありません。問題が発生した場合、バージョン 3 へのロールバック"
#~ "を行うことができます。"

#~ msgid "Potential Issues"
#~ msgstr "潜在的な問題"

#~ msgid ""
#~ "Do to the sizable changes surounding Add-ons, field types and action/filters, your website may not "
#~ "operate correctly. It is important that you read the full"
#~ msgstr ""
#~ "アドオン、フィールドタイプ、アクション／フィルターに関する変更のため、ウェブサイトが正常に動作しない"
#~ "可能性があります。"

#~ msgid "Migrating from v3 to v4"
#~ msgstr "バージョン 3 から 4 への移行をごらんください。"

#~ msgid "guide to view the full list of changes."
#~ msgstr "変更の一覧を見ることができます。"

#~ msgid "Really Important!"
#~ msgstr "非常に重要"

#~ msgid ""
#~ "If you updated the ACF plugin without prior knowledge of such changes, please roll back to the latest"
#~ msgstr "予備知識無しに更新してしまった場合は、"

#~ msgid "version 3"
#~ msgstr "バージョン 3 "

#~ msgid "of this plugin."
#~ msgstr "にロールバックしてください。"

#~ msgid "Thank You"
#~ msgstr "ありがとうございます"

#~ msgid ""
#~ "A <strong>BIG</strong> thank you to everyone who has helped test the version 4 beta and for all the "
#~ "support I have received."
#~ msgstr ""
#~ "バージョン 4 ベータのテストに協力してくださった皆さん、サポートしてくださった皆さんに感謝します。"

#~ msgid "Without you all, this release would not have been possible!"
#~ msgstr "皆さんの助けが無ければ、リリースすることはできなかったでしょう。"

#, fuzzy
#~ msgid "Changelog for"
#~ msgstr "更新履歴"

#~ msgid "Learn more"
#~ msgstr "詳細を見る"

#~ msgid "Overview"
#~ msgstr "概要"

#~ msgid ""
#~ "Previously, all Add-ons were unlocked via an activation code (purchased from the ACF Add-ons store). "
#~ "New to v4, all Add-ons act as separate plugins which need to be individually downloaded, installed "
#~ "and updated."
#~ msgstr ""
#~ "今までは、アドオンはアクティベーションコードでロック解除していました。バージョン 4 では、アドオンは個"
#~ "別のプラグインとしてダウンロードしてインストールする必要があります。"

#~ msgid "This page will assist you in downloading and installing each available Add-on."
#~ msgstr "このページは、アドオンのダウンロードやインストールを手助けします。"

#, fuzzy
#~ msgid "Available Add-ons"
#~ msgstr "アドオンを有効化する"

#~ msgid "The following Add-ons have been detected as activated on this website."
#~ msgstr "以下のアドオンがこのウェブサイトで有効になっています。"

#~ msgid "Activation Code"
#~ msgstr "アクティベーションコード"

#, fuzzy
#~ msgid "Installation"
#~ msgstr "説明"

#~ msgid "For each Add-on available, please perform the following:"
#~ msgstr "それぞれのアドオンについて、下記を実行してください。"

#~ msgid "Download the Add-on plugin (.zip file) to your desktop"
#~ msgstr "アドオン(.zip ファイル)をダウンロードする"

#~ msgid "Navigate to"
#~ msgstr "管理画面で"

#~ msgid "Plugins > Add New > Upload"
#~ msgstr "プラグイン > 新規追加 > アップロード"

#~ msgid "Use the uploader to browse, select and install your Add-on (.zip file)"
#~ msgstr "アドオンのファイルを選択してインストールする"

#~ msgid "Once the plugin has been uploaded and installed, click the 'Activate Plugin' link"
#~ msgstr "アップロードできたら、有効化をクリックする"

#~ msgid "The Add-on is now installed and activated!"
#~ msgstr "アドオンがインストールされ、有効化されました。"

#~ msgid "Awesome. Let's get to work"
#~ msgstr "素晴らしい。作業に戻ります。"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "検証に失敗しました。下記のフィールドの少なくとも一つが必須です。"

#, fuzzy
#~ msgid "What's new"
#~ msgstr "新着情報で見る"

#~ msgid "credits"
#~ msgstr "クレジット"

#~ msgid "Modifying field group options 'show on page'"
#~ msgstr "フィールドグループオプション「ページで表示する」を変更"

#~ msgid "Modifying field option 'taxonomy'"
#~ msgstr "フィールドオプション「タクソノミー」を変更"

#~ msgid "Moving user custom fields from wp_options to wp_usermeta'"
#~ msgstr "ユーザーのカスタムフィールドを wp_options から wp_usermeta に変更する"

#~ msgid "blue : Blue"
#~ msgstr "blue : 青"

#~ msgid "eg: #ffffff"
#~ msgstr "例: #ffffff"

#~ msgid "Save format"
#~ msgstr "フォーマットを保存する"

#~ msgid "This format will determin the value saved to the database and returned via the API"
#~ msgstr "このフォーマットは、値をデータベースに保存し、API で返す形式を決定します"

#~ msgid "\"yymmdd\" is the most versatile save format. Read more about"
#~ msgstr "最も良く用いられるフォーマットは \"yymmdd\" です。詳細は"

#~ msgid "jQuery date formats"
#~ msgstr "jQuery 日付フォーマット"

#~ msgid "This format will be seen by the user when entering a value"
#~ msgstr "ユーザーが値を入力するときのフォーマット"

#~ msgid "\"dd/mm/yy\" or \"mm/dd/yy\" are the most used Display Formats. Read more about"
#~ msgstr "よく使用されるのは、\"dd/mm/yy\" や \"mm/dd/yy\" です。詳細は"

#~ msgid "Dummy"
#~ msgstr "ダミー"

#~ msgid "No File Selected"
#~ msgstr "ファイルが選択されていません"

#~ msgid "File Object"
#~ msgstr "ファイルオブジェクト"

#~ msgid "File Updated."
#~ msgstr "ファイルを更新しました"

#~ msgid "Media attachment updated."
#~ msgstr "メディアアタッチメントを更新しました"

#~ msgid "No files selected"
#~ msgstr "ファイルが選択されていません"

#~ msgid "Add Selected Files"
#~ msgstr "選択されたファイルを追加する"

#~ msgid "Image Object"
#~ msgstr "画像オブジェクト"

#~ msgid "Image Updated."
#~ msgstr "画像を更新しました"

#~ msgid "No images selected"
#~ msgstr "画像が選択されていません"

#, fuzzy
#~ msgid "Add Selected Images"
#~ msgstr "選択した画像を追加する"

#~ msgid "Text &amp; HTML entered here will appear inline with the fields"
#~ msgstr "ここに記述したテキストと HTML がインラインで表示されます。"

#~ msgid "Specifies the minimum value allowed"
#~ msgstr "最小値を指定します。"

#~ msgid "Specifies the maximim value allowed"
#~ msgstr "最大値を指定します。"

#~ msgid "Step"
#~ msgstr "Step"

#~ msgid "Specifies the legal number intervals"
#~ msgstr "入力値の間隔を指定します。"

#~ msgid "Filter from Taxonomy"
#~ msgstr "タクソノミーでフィルタする"

#~ msgid "Enter your choices one per line"
#~ msgstr "選択肢を一行ずつ入力してください"

#~ msgid "Red"
#~ msgstr "赤"

#~ msgid "Blue"
#~ msgstr "青"

#~ msgid "Filter by post type"
#~ msgstr "投稿タイプでフィルタする"

#, fuzzy
#~ msgid "Post Type Select"
#~ msgstr "投稿タイプ"

#, fuzzy
#~ msgid "Post Title"
#~ msgstr "投稿タイプ"

#~ msgid ""
#~ "All fields proceeding this \"tab field\" (or until another \"tab field\"  is defined) will appear "
#~ "grouped on the edit screen."
#~ msgstr "タブフィールドでフィールドを区切り、グループ化して表示します。"

#~ msgid "You can use multiple tabs to break up your fields into sections."
#~ msgstr "複数のタブを使用することができます。"

#~ msgid "Formatting"
#~ msgstr "フォーマット"

#~ msgid "Define how to render html tags"
#~ msgstr "html タグの表示を決定する"

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "html タグ/新しい行の表示を決定する"

#~ msgid "auto &lt;br /&gt;"
#~ msgstr "自動 &lt;br /&gt;"

#~ msgid "Field Order"
#~ msgstr "フィールド順序"

#~ msgid "Field Key"
#~ msgstr "フィールドキー"

#~ msgid "Edit this Field"
#~ msgstr "このフィールドを編集する"

#~ msgid "Read documentation for this field"
#~ msgstr "このフィールドのドキュメントを読む"

#~ msgid "Docs"
#~ msgstr "ドキュメント"

#~ msgid "Duplicate this Field"
#~ msgstr "このフィールドを複製する"

#~ msgid "Delete this Field"
#~ msgstr "このフィールドを削除する"

#~ msgid "Field Instructions"
#~ msgstr "フィールド記入のヒント"

#~ msgid "Show this field when"
#~ msgstr "表示する条件"

#~ msgid "all"
#~ msgstr "全て"

#~ msgid "any"
#~ msgstr "任意"

#~ msgid "these rules are met"
#~ msgstr "これらの条件を満たす"

#, fuzzy
#~ msgid "Taxonomy Term (Add / Edit)"
#~ msgstr "タクソノミー(追加/編集)"

#~ msgid "User (Add / Edit)"
#~ msgstr "ユーザー(追加/編集)"

#, fuzzy
#~ msgid "Media Attachment (Edit)"
#~ msgstr "メディアアタッチメントを更新しました"

#~ msgid "Normal"
#~ msgstr "Normal"

#~ msgid "No Metabox"
#~ msgstr "メタボックス無"

#~ msgid "Standard Metabox"
#~ msgstr "標準メタボックス"
