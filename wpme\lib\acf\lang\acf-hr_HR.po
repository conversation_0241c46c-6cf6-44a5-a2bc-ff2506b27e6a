msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2018-02-06 10:08+1000\n"
"PO-Revision-Date: 2018-05-05 14:02+0700\n"
"Last-Translator: \n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: hr_HR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.7\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPath-1: acf-pro-hr/acf.pot\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:67
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:369 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Grupe polja"

#: acf.php:370
msgid "Field Group"
msgstr "Grupa polja"

#: acf.php:371 acf.php:403 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:559
msgid "Add New"
msgstr "Dodaj"

#: acf.php:372
msgid "Add New Field Group"
msgstr "Dodaj novo polje"

#: acf.php:373
msgid "Edit Field Group"
msgstr "Uredi polje"

#: acf.php:374
msgid "New Field Group"
msgstr "Novo polje"

#: acf.php:375
msgid "View Field Group"
msgstr "Pregledaj polje"

#: acf.php:376
msgid "Search Field Groups"
msgstr "Pretraži polja"

#: acf.php:377
msgid "No Field Groups found"
msgstr "Niste dodali nijedno polje"

#: acf.php:378
msgid "No Field Groups found in Trash"
msgstr "Nije pronađena nijedna stranica"

#: acf.php:401 includes/admin/admin-field-group.php:182
#: includes/admin/admin-field-group.php:275
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Polja"

#: acf.php:402
msgid "Field"
msgstr "Polje"

#: acf.php:404
msgid "Add New Field"
msgstr "Dodaj polje"

#: acf.php:405
msgid "Edit Field"
msgstr "Uredi polje"

#: acf.php:406 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Dodaj polje"

#: acf.php:407
msgid "View Field"
msgstr "Pregledaj polje"

#: acf.php:408
msgid "Search Fields"
msgstr "Pretraži polja"

#: acf.php:409
msgid "No Fields found"
msgstr "Nije pronađeno nijedno polje"

#: acf.php:410
msgid "No Fields found in Trash"
msgstr "Nije pronađeno nijedno polje u smeću"

#: acf.php:449 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Neaktivno"

#: acf.php:454
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Neaktivno <span class=“count”>(%s)</span>"
msgstr[1] "Neaktivnih: <span class=“count”>(%s)</span>"
msgstr[2] "Neaktivnih: <span class=“count”>(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Skup polja ažuriran."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Skup polja izbrisan."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Skup polja objavljen."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Skup polja spremljen."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Skup polja je spremljen."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Skup polja je označen za."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Skica ažurirana."

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "Lokacija"

#: includes/admin/admin-field-group.php:184
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Postavke"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "Premjesti u smeće?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr "odabrano"

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr "Nema polja koji omoguću korisniku odabir"

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "Naziv polja je obavezna"

#: includes/admin/admin-field-group.php:273
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "kopiraj"

#: includes/admin/admin-field-group.php:274
#: includes/admin/views/field-group-field-conditional-logic.php:54
#: includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:4048
msgid "or"
msgstr "ili"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "Matično polje"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr "Slična polja"

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "Premjesti polje"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr "Potrebno je spremiti izmjene prije nego možete premjestiti polje"

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr "Null"

#: includes/admin/admin-field-group.php:281 includes/input.php:258
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Izmjene koje ste napravili bit će izgubljene ukoliko napustite ovu stranicu"

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Polje ne može započinjati sa “field_”, odabrite drugi naziv"

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr "Oznaka polja"

#: includes/admin/admin-field-group.php:390
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Aktivan"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "Premještanje dovršeno."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr ""
"Polje %s od sada možete naći na drugoj lokacaiji, kao dio %s skupa polja"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "Zatvori prozor"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "Odaberite lokaciju za ovo polje"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "Premjesti polje"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktivno <span class=“count”>(%s)</span>"
msgstr[1] "Aktivno <span class=“count”>(%s)</span>"
msgstr[2] "Aktivno <span class=“count”>(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Skup polja %s dupliciran"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Polja duplicirana (%s)."
msgstr[1] "Polja duplicirana (%s)."
msgstr[2] "Polja duplicirana (%s)."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Skup polja sinkroniziran. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Polja sinkronizirana (%s)."
msgstr[1] "Polja sinkronizirana (%s)."
msgstr[2] "Polja sinkronizirana (%s)."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Sinkronizacija dostupna"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:355
msgid "Title"
msgstr "Naziv"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:382
msgid "Description"
msgstr "Opis"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Status"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Prilagodite WordPress sa moćnim, profesionalnim i intuitivnim dodatnim "
"poljima."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Popis izmjena"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Pogledaj što je novo u <a href=\"%s\">%s verziji</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Materijali"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Web mjesto"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Dokumentacija"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Podrška"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Hvala što koristite <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:667
msgid "Duplicate this item"
msgstr "Dupliciraj"

#: includes/admin/admin-field-groups.php:667
#: includes/admin/admin-field-groups.php:683
#: includes/admin/views/field-group-field.php:49
#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Duplicate"
msgstr "Dupliciraj"

#: includes/admin/admin-field-groups.php:700
#: includes/fields/class-acf-field-google-map.php:112
#: includes/fields/class-acf-field-relationship.php:656
msgid "Search"
msgstr "Pretraži"

#: includes/admin/admin-field-groups.php:759
#, php-format
msgid "Select %s"
msgstr "Odaberi %s"

#: includes/admin/admin-field-groups.php:767
msgid "Synchronise field group"
msgstr "Sinkroniziraj skup polja"

#: includes/admin/admin-field-groups.php:767
#: includes/admin/admin-field-groups.php:797
msgid "Sync"
msgstr "Sinkroniziraj"

#: includes/admin/admin-field-groups.php:779
msgid "Apply"
msgstr "Prijavi"

#: includes/admin/admin-field-groups.php:797
msgid "Bulk Actions"
msgstr "Skupne akcije"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Alati"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Dodatna polja"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Nadogradi bazu podataka"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "Pregledaj stranice i nadogradi"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "Greška prilikom verifikacije"

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "Nema novih nadogradnji."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Dodaci"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Greška</b>. Greška prilikom učitavanja dodataka"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Info"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Što je novo"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Izvezi skup polja"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Generiraj PHP kod"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Niste odabrali polje"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Odaberite skup polja"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Odaberite polja koja želite izvesti i zatim odaberite željeni format. Klikom "
"na gumb “preuzimanje”, preuzmite .json datoteku sa poljima koju zatim možete "
"uvesti u drugu ACF instalaciju.\n"
"Klikom na “generiraj” gumb, izvezite PHP kod koji možete uključiti u "
"WordPress temu."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Datoteka za izvoz"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Navedeni kod možete koristiti kako bi registrirali lokalnu verziju odabranih "
"polja ili skupine polja. Lokalna polje pružaju dodatne mogućnosti kao što je "
"brže očitavanje, verzioniranje i dinamičke postavke polja. Jednostavno "
"kopirajte navedeni kod u functions.php datoteku u vašoj temi ili uključite "
"ih kao vanjsku datoteku."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Kopiraj u međuspremnik"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Uvoz skupa polja"

#: includes/admin/tools/class-acf-admin-tool-import.php:61
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Odaberite ACF JSON datoteku koju želite uvesti. Nakon što kliknete ‘Uvezi’ "
"gumb, ACF će uvesti sva polja iz odabrane datoteke."

#: includes/admin/tools/class-acf-admin-tool-import.php:66
#: includes/fields/class-acf-field-file.php:35
msgid "Select File"
msgstr "Odaberite datoteku"

#: includes/admin/tools/class-acf-admin-tool-import.php:76
msgid "Import File"
msgstr "Datoteka za uvoz"

#: includes/admin/tools/class-acf-admin-tool-import.php:100
#: includes/fields/class-acf-field-file.php:159
msgid "No file selected"
msgstr "Niste odabrali datoteku"

#: includes/admin/tools/class-acf-admin-tool-import.php:113
msgid "Error uploading file. Please try again"
msgstr "Greška prilikom prijenosa datoteke, molimo pokušaj ponovno"

#: includes/admin/tools/class-acf-admin-tool-import.php:122
msgid "Incorrect file type"
msgstr "Nedozvoljeni format datoteke"

#: includes/admin/tools/class-acf-admin-tool-import.php:139
msgid "Import file empty"
msgstr "Odabrana datoteka za uvoz ne sadrži"

#: includes/admin/tools/class-acf-admin-tool-import.php:247
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "Uvjet za prikaz"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "Prikaži polje ako"

#: includes/admin/views/field-group-field-conditional-logic.php:103
#: includes/locations.php:247
msgid "is equal to"
msgstr "je jednako"

#: includes/admin/views/field-group-field-conditional-logic.php:104
#: includes/locations.php:248
msgid "is not equal to"
msgstr "je drukčije"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "i"

#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Dodaj skup pravila"

#: includes/admin/views/field-group-field.php:41
#: pro/fields/class-acf-field-flexible-content.php:403
#: pro/fields/class-acf-field-repeater.php:296
msgid "Drag to reorder"
msgstr "Presloži polja povlačenjem"

#: includes/admin/views/field-group-field.php:45
#: includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "Uredi polje"

#: includes/admin/views/field-group-field.php:48
#: includes/fields/class-acf-field-file.php:141
#: includes/fields/class-acf-field-image.php:122
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:342
msgid "Edit"
msgstr "Uredi"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "Dupliciraj polje"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "Premjeti polje u drugu skupinu"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "Premjesti"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "Obriši polje"

#: includes/admin/views/field-group-field.php:51
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Delete"
msgstr "Obriši"

#: includes/admin/views/field-group-field.php:68
msgid "Field Label"
msgstr "Naziv polja"

#: includes/admin/views/field-group-field.php:69
msgid "This is the name which will appear on the EDIT page"
msgstr "Naziv koji se prikazuje prilikom uređivanja stranice"

#: includes/admin/views/field-group-field.php:78
msgid "Field Name"
msgstr "Naziv polja"

#: includes/admin/views/field-group-field.php:79
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Jedna riječ, bez razmaka. Povlaka i donja crta su dozvoljeni"

#: includes/admin/views/field-group-field.php:88
msgid "Field Type"
msgstr "Tip polja"

#: includes/admin/views/field-group-field.php:99
msgid "Instructions"
msgstr "Upute"

#: includes/admin/views/field-group-field.php:100
msgid "Instructions for authors. Shown when submitting data"
msgstr "Upute priliko uređivanja. Vidljivo prilikom spremanja podataka"

#: includes/admin/views/field-group-field.php:109
msgid "Required?"
msgstr "Obavezno?"

#: includes/admin/views/field-group-field.php:132
msgid "Wrapper Attributes"
msgstr "Značajke prethodnog elementa"

#: includes/admin/views/field-group-field.php:138
msgid "width"
msgstr "širina"

#: includes/admin/views/field-group-field.php:153
msgid "class"
msgstr "klasa"

#: includes/admin/views/field-group-field.php:166
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:178
msgid "Close Field"
msgstr "Zatvori polje"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Redni broj"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:415
#: includes/fields/class-acf-field-radio.php:306
#: includes/fields/class-acf-field-select.php:432
#: pro/fields/class-acf-field-flexible-content.php:584
msgid "Label"
msgstr "Oznaka"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:964
#: pro/fields/class-acf-field-flexible-content.php:597
msgid "Name"
msgstr "Naziv"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Ključ"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tip"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Nema polja. Kliknite gumb <strong>+ Dodaj polje</strong> da bi kreirali "
"polje."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "Dodaj polje"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Pravila"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr "Odaberite pravila koja određuju koji prikaz će koristiti ACF polja"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Stil"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Zadano (WP metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Bez"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Pozicija"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Visoko (nakon naslova)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normalno (nakon saržaja)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Desni stupac"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Pozicija oznake"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Poravnato sa vrhom"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Lijevo poravnato"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Pozicija uputa"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Ispod oznake"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Iznad oznake"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Redni broj."

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Skup polja sa nižim brojem će biti više pozicioniran"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Vidljivo u popisu"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Sakrij"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Odaberite</b> koje grupe želite <b>sakriti</b> prilikom uređivanja."

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Ukoliko je više skupova polja prikazano na istom ekranu, postavke prvog "
"skupa polja će biti korištene (postavke polja sa nižim brojem u redosljedu)"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Stalna veza"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Uređivač sadržaja"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Izvadak"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Rasprava"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Komentari"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Revizija"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Atributi stranice"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:670
msgid "Featured Image"
msgstr "Istaknuta slika"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Kategorije"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Oznake"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Pošalji povratnu vezu"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Prikaži ovaj skup polja ako"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "Ažuriraj stranice"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr "Nadogradnja baze ACF"

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Ažuriranje baze podatak dovršeno. Provjerite koje web stranice u svojoj "
"mreži želite nadograditi i zatim kliknite %s."

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Web stranica"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr ""
"Za web stranicu je potrebna nadogradnja baze podataka iz %s na verziju %s"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "Nema novih ažuriranja za web stranica"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Baza podataka je nadograđena. <a href=“%s”>Kliknite ovdje za povratak na "
"administraciju WordPress mreže</a>"

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Prije nego nastavite preporučamo da napravite sigurnosnu kopiju baze "
"podataka. Jeste li sigurni da želite nastaviti ažuriranje?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Nadogradnja završena"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Nadogradnja na verziju %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Ponavljajuće polje"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Fleksibilno polje"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galerija"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Postavke"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Potrebno je nadograditi bazu podataka"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Hvala što ste nadogradili %s na v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Prije nego što počnete koristiti nove mogućnosti, molimo ažurirajte bazu "
"podataka na posljednju verziju."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""
"Molimo provjerite da su svi premium dodaci (%s) ažurirani na najnoviju "
"verziju."

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Učitavam podatke za nadogradnju…"

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Nadogradnja baze je dovršena. <a href=\"%s\">Pogledajte što je novo</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Preuzimam datoteke"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Instalirano"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Advanced Custom Fields vam želi dobrodošlicu"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Ažuriranje dovršeno, hvala! ACF %s je veći i bolji nego ikad prije. Nadamo "
"se da će vam se svidjet."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "Bolje korisničko iskustvo korištenja prilagođenih polja"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Poboljšana uporabljivost"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Uključivanje popularne biblioteke Select2 poboljšano je korisničko iskustvo "
"i brzina na velikom broju polja."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Unaprijeđen dizajn"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Mnoga polja su vizualno osvježena te time ACF sada izgleda bolje nego ikad "
"prije!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Unaprijeđeno upravljanje podacima"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Nova arhitektura polja omogućuje pod poljima da budu korištena zasebno bez "
"obzira kojem skupu polja pripadaju. Ovo vam omogućuje premještanje polja iz "
"jednog skupa u drugi!"

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Doviđenja dodaci, upoznajte PRO verziju"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "Predstavljamo ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""
"Mijanjamo način funkcioniranja premium dodataka, od sada mnogo jednostavnije!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Sva 4 premium dodakta od sada su ukomponiranu u novu <a href=“%s”>Pro "
"verziju ACF</a>. Sa novim osobnom i razvojnom opcijom licenciranja, premium "
"funkcionalnost je dosupnija i povoljnija nego prije!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Super mogućnosti"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO uključuje napredne funkcionalnosti kao ponavljajuća polja, modularni "
"raspored, galerija slika i mogućnost dodavanja novih stranica u postavkama "
"administracije!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Pročitajte više o <a href=“%s”>mogućnostima ACF PRO</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Jednostavno ažuriranje"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"Kako bi pojednostavili ažuriranje, <a href=“%s”>prijavite se s vašim "
"računom</a> i osigurajte besplatnu verziju ACF PRO!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"Provjeriti <a href=“%s”>upute za ažuriranje</a> ako imate dodatnih pitanja, "
"ili kontaktirajte našu <a href=“%s”>tim za podršku</a>"

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Ispod haube"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "Pametnije postavke"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "ACF od sada sprema postavke polja kao objekt"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Više AJAX-a"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr ""
"Više polja koristi asinkrono pretraživanje kako bi učitavanje stranice bilo "
"brže"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "Učitavanje polja iz JSON datoteke"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "Nova mogućnost automatskog izvoza u JSON obliku"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "Bolje upravljanje verzijama"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr "Nova opcija izvoza u JSON omogućuje verziranje"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "JSON umjesto XML"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "Uvoz / Izvoz sada koristi JSON umjesto XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Nove forme"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"Od sada je moguće dodati polja na sve stranice, uključujući komentare, "
"stranice za uređivanje korisnika i widgete!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "Novo polje za ugnježdeni sadržaj"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Nova galerija"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "Polje Galerija je dobilo novi izgled"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Nove postavke"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"Postavke svakog polja uključuju dodatna polja, polje za opis i polje za "
"upute namjenjene korisniku"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "Bolji prikaz formi na web stranici"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr ""
"acf_form() funkcija od sada omogućuje dodavanje nove objave prilikom "
"spremanja"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Bolja verifikacija polja"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr ""
"Verifikacija polja se sada obavlja asinkrono (PHP + AJAX) umjesto "
"dosadašnjeg načina (Javascript)"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Polje za povezivanje objekta"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Novo postavke polja Veza za filter (pretraga, tip objekta, taksonomija)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Premještanje polja"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr "Nova funkcionalnost polja omogućuje premještanje polja i skupa polja"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "URL stranice"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Nova skupina ‘arhiva’ prilikom odabira polja page_link"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Bolja upravljanje stranica sa postavkama"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"Nova funkcionalnost kod dodavanja stranica za postavke omogućuju dodavanje "
"izvornih i pod stranica izbornika"

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Mislimo da će vam se svidjeti promjene u %s."

#: includes/api/api-helpers.php:947
msgid "Thumbnail"
msgstr "Sličica"

#: includes/api/api-helpers.php:948
msgid "Medium"
msgstr "Srednja"

#: includes/api/api-helpers.php:949
msgid "Large"
msgstr "Velika"

#: includes/api/api-helpers.php:998
msgid "Full Size"
msgstr "Puna veličina"

#: includes/api/api-helpers.php:1339 includes/api/api-helpers.php:1912
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(bez naziva)"

#: includes/api/api-helpers.php:3969
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Širina slike mora biti najmanje %dpx."

#: includes/api/api-helpers.php:3974
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Širina slike ne smije biti veća od %dpx."

#: includes/api/api-helpers.php:3990
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Visina slike mora biti najmanje %dpx."

#: includes/api/api-helpers.php:3995
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Visina slike ne smije biti veća od %dpx."

#: includes/api/api-helpers.php:4013
#, php-format
msgid "File size must be at least %s."
msgstr "Veličina datoteke mora biti najmanje %s."

#: includes/api/api-helpers.php:4018
#, php-format
msgid "File size must must not exceed %s."
msgstr "Datoteke ne smije biti veća od %s."

#: includes/api/api-helpers.php:4052
#, php-format
msgid "File type must be %s."
msgstr "Tip datoteke mora biti %s."

#: includes/fields.php:144
msgid "Basic"
msgstr "Osnovno"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Sadržaj"

#: includes/fields.php:146
msgid "Choice"
msgstr "Odabir"

#: includes/fields.php:147
msgid "Relational"
msgstr "Relacijski"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:384
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:285
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:554
#: pro/fields/class-acf-field-flexible-content.php:603
#: pro/fields/class-acf-field-repeater.php:450
msgid "Layout"
msgstr "Format"

#: includes/fields.php:326
msgid "Field type does not exist"
msgstr "Tip polja ne postoji"

#: includes/fields.php:326
msgid "Unknown"
msgstr "Nepoznato polje"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Multi prošireno"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Otvori"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Prikaži accordion polje kao otvoreno prilikom učitavanja."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Mulit-proširenje"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Omogući prikaz ovog accordion polja bez zatvaranje ostalih."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Prijelomna točka"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Preciziraj prijelomnu točku za prethoda polja accordion. Ovo će omogućiti "
"novi skup polja nakon prijelomne točke."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Skup dugmadi"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "Mogući odabiri"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "Svaki odabir je potrebno dodati kao novi red."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr "Za bolju kontrolu unesite oboje, vrijednost i naziv, kao npr:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "crvena : Crvena"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:412
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-taxonomy.php:793
#: includes/fields/class-acf-field-user.php:408
msgid "Allow Null?"
msgstr "Dozvoli null vrijednost?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:276
#: includes/fields/class-acf-field-range.php:148
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:410
msgid "Default Value"
msgstr "Zadana vrijednost"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:277
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:411
msgid "Appears when creating a new post"
msgstr "Prikazuje se prilikom kreiranje nove objave"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:391
#: includes/fields/class-acf-field-radio.php:292
msgid "Horizontal"
msgstr "Horizontalno"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:390
#: includes/fields/class-acf-field-radio.php:291
msgid "Vertical"
msgstr "Vertikalno"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:408
#: includes/fields/class-acf-field-file.php:204
#: includes/fields/class-acf-field-image.php:188
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:299
#: includes/fields/class-acf-field-taxonomy.php:833
msgid "Return Value"
msgstr "Vrati vrijednost"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:409
#: includes/fields/class-acf-field-file.php:205
#: includes/fields/class-acf-field-image.php:189
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:300
msgid "Specify the returned value on front end"
msgstr "Vrijednost koja će biti vraćena na pristupnom dijelu"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-radio.php:305
#: includes/fields/class-acf-field-select.php:431
msgid "Value"
msgstr "Vrijednost"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:416
#: includes/fields/class-acf-field-radio.php:307
#: includes/fields/class-acf-field-select.php:433
msgid "Both (Array)"
msgstr "Oboje (podatkovni niz)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Checkbox"
msgstr "Skup dugmadi"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Sakrij sve"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Dodaj odabir"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Obogući dodatne"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Omogući ‘dodatne’ vrijednosti"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Spremi"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Spremi ‘dodatne’ vrijednosti i prikaži ih omogući njihov odabir"

#: includes/fields/class-acf-field-checkbox.php:376
#: includes/fields/class-acf-field-select.php:378
msgid "Enter each default value on a new line"
msgstr "Unesite svaku novu vrijednost u zasebnu liniju"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Toggle"
msgstr "Prikaži/Sakrij"

#: includes/fields/class-acf-field-checkbox.php:399
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Dodaj okvir za izbor koji omogućje odabir svih opcija"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Odabir boje"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Ukloni"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Zadano"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Odaberite boju"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Trenutna boja"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Odabir datuma"

#: includes/fields/class-acf-field-date_picker.php:33
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Završeno"

#: includes/fields/class-acf-field-date_picker.php:34
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Danas"

#: includes/fields/class-acf-field-date_picker.php:35
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Slijedeći"

#: includes/fields/class-acf-field-date_picker.php:36
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Prethodni"

#: includes/fields/class-acf-field-date_picker.php:37
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Tjedan"

#: includes/fields/class-acf-field-date_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:181
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Format prikaza"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Format za prikaz prilikom administracije"

#: includes/fields/class-acf-field-date_picker.php:216
#: includes/fields/class-acf-field-date_picker.php:247
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-date_time_picker.php:208
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Prilagođeno:"

#: includes/fields/class-acf-field-date_picker.php:226
msgid "Save Format"
msgstr "Spremi format"

#: includes/fields/class-acf-field-date_picker.php:227
msgid "The format used when saving a value"
msgstr "Format koji će biti spremljen"

#: includes/fields/class-acf-field-date_picker.php:237
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-relationship.php:697
#: includes/fields/class-acf-field-select.php:426
#: includes/fields/class-acf-field-time_picker.php:124
msgid "Return Format"
msgstr "Format za prikaz na web stranici"

#: includes/fields/class-acf-field-date_picker.php:238
#: includes/fields/class-acf-field-date_time_picker.php:199
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "Format koji vraća funkcija"

#: includes/fields/class-acf-field-date_picker.php:256
#: includes/fields/class-acf-field-date_time_picker.php:215
msgid "Week Starts On"
msgstr "Tjedan počinje"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Odabir datuma i sata"

#: includes/fields/class-acf-field-date_time_picker.php:33
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Odaberi vrijeme"

#: includes/fields/class-acf-field-date_time_picker.php:34
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Vrijeme"

#: includes/fields/class-acf-field-date_time_picker.php:35
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Sat"

#: includes/fields/class-acf-field-date_time_picker.php:36
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuta"

#: includes/fields/class-acf-field-date_time_picker.php:37
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekunda"

#: includes/fields/class-acf-field-date_time_picker.php:38
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisekunda"

#: includes/fields/class-acf-field-date_time_picker.php:39
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunda"

#: includes/fields/class-acf-field-date_time_picker.php:40
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Vremenska zona"

#: includes/fields/class-acf-field-date_time_picker.php:41
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Trenutno vrijeme"

#: includes/fields/class-acf-field-date_time_picker.php:42
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Završeno"

#: includes/fields/class-acf-field-date_time_picker.php:43
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Odaberi"

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "Prije podne"

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "Prije podne"

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "Poslije podne"

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "Poslije podne"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Zadana vrijednost"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Prikazuje se unutar polja"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:187
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Umetni ispred"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Prijazuje se ispred polja"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:196
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Umetni na kraj"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Prikazuje se iza polja"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Datoteka"

#: includes/fields/class-acf-field-file.php:36
msgid "Edit File"
msgstr "Uredi datoteku"

#: includes/fields/class-acf-field-file.php:37
msgid "Update File"
msgstr "Ažuriraj datoteku"

#: includes/fields/class-acf-field-file.php:38
#: includes/fields/class-acf-field-image.php:43 includes/media.php:57
#: pro/fields/class-acf-field-gallery.php:44
msgid "Uploaded to this post"
msgstr "Postavljeno uz ovu objavu"

#: includes/fields/class-acf-field-file.php:130
msgid "File name"
msgstr "Naziv datoteke"

#: includes/fields/class-acf-field-file.php:134
#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: pro/fields/class-acf-field-gallery.php:690
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Veličina datoteke"

#: includes/fields/class-acf-field-file.php:143
#: includes/fields/class-acf-field-image.php:124
#: includes/fields/class-acf-field-link.php:140 includes/input.php:269
#: pro/fields/class-acf-field-gallery.php:343
#: pro/fields/class-acf-field-gallery.php:531
msgid "Remove"
msgstr "Ukloni"

#: includes/fields/class-acf-field-file.php:159
msgid "Add File"
msgstr "Dodaj datoteku"

#: includes/fields/class-acf-field-file.php:210
msgid "File Array"
msgstr "Vrijednost kao niz"

#: includes/fields/class-acf-field-file.php:211
msgid "File URL"
msgstr "Putanja datoteke"

#: includes/fields/class-acf-field-file.php:212
msgid "File ID"
msgstr "Vrijednost kao ID"

#: includes/fields/class-acf-field-file.php:219
#: includes/fields/class-acf-field-image.php:213
#: pro/fields/class-acf-field-gallery.php:655
msgid "Library"
msgstr "Zbirka"

#: includes/fields/class-acf-field-file.php:220
#: includes/fields/class-acf-field-image.php:214
#: pro/fields/class-acf-field-gallery.php:656
msgid "Limit the media library choice"
msgstr "Ograniči odabir iz zbirke"

#: includes/fields/class-acf-field-file.php:225
#: includes/fields/class-acf-field-image.php:219
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:661
msgid "All"
msgstr "Sve"

#: includes/fields/class-acf-field-file.php:226
#: includes/fields/class-acf-field-image.php:220
#: pro/fields/class-acf-field-gallery.php:662
msgid "Uploaded to post"
msgstr "Dodani uz trenutnu objavu"

#: includes/fields/class-acf-field-file.php:233
#: includes/fields/class-acf-field-image.php:227
#: pro/fields/class-acf-field-gallery.php:669
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:234
#: includes/fields/class-acf-field-file.php:245
msgid "Restrict which files can be uploaded"
msgstr "Ograniči tip datoteka koji se smije uvesti"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:256
#: pro/fields/class-acf-field-gallery.php:698
msgid "Maximum"
msgstr "Maksimum"

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:285
#: pro/fields/class-acf-field-gallery.php:727
msgid "Allowed file types"
msgstr "Dozvoljeni tipovi datoteka"

#: includes/fields/class-acf-field-file.php:256
#: includes/fields/class-acf-field-image.php:286
#: pro/fields/class-acf-field-gallery.php:728
msgid "Comma separated list. Leave blank for all types"
msgstr ""
"Dodaj kao niz odvojen zarezom, npr: .txt, .jpg, ... Ukoliko je prazno, sve "
"datoteke su dozvoljene"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google mapa"

#: includes/fields/class-acf-field-google-map.php:40
msgid "Locating"
msgstr "Lociranje u tijeku"

#: includes/fields/class-acf-field-google-map.php:41
msgid "Sorry, this browser does not support geolocation"
msgstr "Nažalost, ovaj preglednik ne podržava geo lociranje"

#: includes/fields/class-acf-field-google-map.php:113
msgid "Clear location"
msgstr "Ukloni lokaciju"

#: includes/fields/class-acf-field-google-map.php:114
msgid "Find current location"
msgstr "Pronađi trenutnu lokaciju"

#: includes/fields/class-acf-field-google-map.php:117
msgid "Search for address..."
msgstr "Pretraži po adresi..."

#: includes/fields/class-acf-field-google-map.php:147
#: includes/fields/class-acf-field-google-map.php:158
msgid "Center"
msgstr "Centriraj"

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-google-map.php:159
msgid "Center the initial map"
msgstr "Centriraj prilikom učitavanja"

#: includes/fields/class-acf-field-google-map.php:170
msgid "Zoom"
msgstr "Uvećaj"

#: includes/fields/class-acf-field-google-map.php:171
msgid "Set the initial zoom level"
msgstr "Postavi zadanu vrijednost uvećanja"

#: includes/fields/class-acf-field-google-map.php:180
#: includes/fields/class-acf-field-image.php:239
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-oembed.php:281
#: pro/fields/class-acf-field-gallery.php:681
#: pro/fields/class-acf-field-gallery.php:710
msgid "Height"
msgstr "Visina"

#: includes/fields/class-acf-field-google-map.php:181
msgid "Customise the map height"
msgstr "Uredi visinu mape"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Skup polja"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:389
msgid "Sub Fields"
msgstr "Pod polja"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Odaberite način prikaza odabranih polja"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:614
#: pro/fields/class-acf-field-repeater.php:458
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:457
msgid "Table"
msgstr "Tablica"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:615
#: pro/fields/class-acf-field-repeater.php:459
msgid "Row"
msgstr "Red"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Slika"

#: includes/fields/class-acf-field-image.php:40
msgid "Select Image"
msgstr "Odaberi sliku"

#: includes/fields/class-acf-field-image.php:41
#: pro/fields/class-acf-field-gallery.php:42
msgid "Edit Image"
msgstr "Uredi sliku"

#: includes/fields/class-acf-field-image.php:42
#: pro/fields/class-acf-field-gallery.php:43
msgid "Update Image"
msgstr "Ažuriraj sliku"

#: includes/fields/class-acf-field-image.php:44
msgid "All images"
msgstr "Sve slike"

#: includes/fields/class-acf-field-image.php:140
msgid "No image selected"
msgstr "Nema odabranih slika"

#: includes/fields/class-acf-field-image.php:140
msgid "Add Image"
msgstr "Dodaj sliku"

#: includes/fields/class-acf-field-image.php:194
msgid "Image Array"
msgstr "Podaci kao niz"

#: includes/fields/class-acf-field-image.php:195
msgid "Image URL"
msgstr "Putanja slike"

#: includes/fields/class-acf-field-image.php:196
msgid "Image ID"
msgstr "ID slike"

#: includes/fields/class-acf-field-image.php:203
msgid "Preview Size"
msgstr "Veličina prikaza prilikom uređivanja stranice"

#: includes/fields/class-acf-field-image.php:204
msgid "Shown when entering data"
msgstr "Prikazuje se prilikom unosa podataka"

#: includes/fields/class-acf-field-image.php:228
#: includes/fields/class-acf-field-image.php:257
#: pro/fields/class-acf-field-gallery.php:670
#: pro/fields/class-acf-field-gallery.php:699
msgid "Restrict which images can be uploaded"
msgstr "Ograniči koje slike mogu biti dodane"

#: includes/fields/class-acf-field-image.php:231
#: includes/fields/class-acf-field-image.php:260
#: includes/fields/class-acf-field-oembed.php:270
#: pro/fields/class-acf-field-gallery.php:673
#: pro/fields/class-acf-field-gallery.php:702
msgid "Width"
msgstr "Širina"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Poveznica"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Odaberite poveznicu"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Otvori u novom prozoru/kartici"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Vrijednost kao niz"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "Putanja poveznice"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Poruka"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Broj linija"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Određuje način prikaza novih linija"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Dodaj paragraf"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Dodaj novi red - &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Bez obrade"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Onemogući HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Prikažite HTML kodove kao tekst umjesto iscrtavanja"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Broj"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:157
msgid "Minimum Value"
msgstr "Minimum"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:167
msgid "Maximum Value"
msgstr "Maksimum"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:177
msgid "Step Size"
msgstr "Korak"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Vrijednost mora biti broj"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Unešena vrijednost mora biti jednaka ili viša od %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Unešena vrijednost mora biti jednaka ili niža od %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "Poveznica"

#: includes/fields/class-acf-field-oembed.php:234
#: includes/fields/class-acf-field-taxonomy.php:898
msgid "Error."
msgstr "Greška."

#: includes/fields/class-acf-field-oembed.php:234
msgid "No embed found for the given URL."
msgstr "Nije pronađen nijedan umetak za unesenu adresu."

#: includes/fields/class-acf-field-oembed.php:267
#: includes/fields/class-acf-field-oembed.php:278
msgid "Embed Size"
msgstr "Dimenzija umetka"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Arhiva"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:268
#: includes/fields/class-acf-field-taxonomy.php:986
msgid "Parent"
msgstr "Matični"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:384
#: includes/fields/class-acf-field-relationship.php:623
msgid "Filter by Post Type"
msgstr "Filtriraj po tipu posta"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:392
#: includes/fields/class-acf-field-relationship.php:631
msgid "All post types"
msgstr "Svi tipovi"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:398
#: includes/fields/class-acf-field-relationship.php:637
msgid "Filter by Taxonomy"
msgstr "Filtriraj prema taksonomiji"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:406
#: includes/fields/class-acf-field-relationship.php:645
msgid "All taxonomies"
msgstr "Sve taksonomije"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Omogući odabir arhive tipova"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:422
#: includes/fields/class-acf-field-select.php:396
#: includes/fields/class-acf-field-user.php:418
msgid "Select multiple values?"
msgstr "Dozvoli odabir više vrijednosti?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Lozinka"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:702
msgid "Post Object"
msgstr "Objekt"

#: includes/fields/class-acf-field-post_object.php:438
#: includes/fields/class-acf-field-relationship.php:703
msgid "Post ID"
msgstr "ID objave"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Radiogumb"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Drugo"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Dodaj odabir ’ostalo’ za slobodan unost"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Spremi ostale"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Spremi ostale vrijednosti i omogući njihov odabir"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Raspon"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Veza"

#: includes/fields/class-acf-field-relationship.php:37
msgid "Minimum values reached ( {min} values )"
msgstr "Minimalna vrijednost je {min}"

#: includes/fields/class-acf-field-relationship.php:38
msgid "Maximum values reached ( {max} values )"
msgstr "Već ste dodali najviše dozvoljenih vrijednosti (najviše: {max})"

#: includes/fields/class-acf-field-relationship.php:39
msgid "Loading"
msgstr "Učitavanje"

#: includes/fields/class-acf-field-relationship.php:40
msgid "No matches found"
msgstr "Nema rezultata"

#: includes/fields/class-acf-field-relationship.php:423
msgid "Select post type"
msgstr "Odaberi tip posta"

#: includes/fields/class-acf-field-relationship.php:449
msgid "Select taxonomy"
msgstr "Odebarite taksonomiju"

#: includes/fields/class-acf-field-relationship.php:539
msgid "Search..."
msgstr "Pretraga…"

#: includes/fields/class-acf-field-relationship.php:651
msgid "Filters"
msgstr "Filteri"

#: includes/fields/class-acf-field-relationship.php:657
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Tip objave"

#: includes/fields/class-acf-field-relationship.php:658
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:763
msgid "Taxonomy"
msgstr "Taksonomija"

#: includes/fields/class-acf-field-relationship.php:665
msgid "Elements"
msgstr "Elementi"

#: includes/fields/class-acf-field-relationship.php:666
msgid "Selected elements will be displayed in each result"
msgstr "Odabrani elementi bit će prikazani u svakom rezultatu"

#: includes/fields/class-acf-field-relationship.php:677
msgid "Minimum posts"
msgstr "Minimalno"

#: includes/fields/class-acf-field-relationship.php:686
msgid "Maximum posts"
msgstr "Maksimalno"

#: includes/fields/class-acf-field-relationship.php:790
#: pro/fields/class-acf-field-gallery.php:800
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:785
msgctxt "noun"
msgid "Select"
msgstr "Odaberi"

#: includes/fields/class-acf-field-select.php:38
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Jedan rezultat dostupan, pritisnite enter za odabir."

#: includes/fields/class-acf-field-select.php:39
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d rezultata dostupno, za pomicanje koristite strelice gore/dole."

#: includes/fields/class-acf-field-select.php:40
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nema rezultata"

#: includes/fields/class-acf-field-select.php:41
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Molimo unesite 1 ili više znakova"

#: includes/fields/class-acf-field-select.php:42
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Molimo unesite najmanje %d ili više znakova"

#: includes/fields/class-acf-field-select.php:43
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Molimo obrišite 1 znak"

#: includes/fields/class-acf-field-select.php:44
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Molimo obrišite višak znakova - %d znak(ova) je višak"

#: includes/fields/class-acf-field-select.php:45
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Moguće je odabrati samo jednu opciju"

#: includes/fields/class-acf-field-select.php:46
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Odabir opcija je ograničen na najviše %d"

#: includes/fields/class-acf-field-select.php:47
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Učitavam rezultate&hellip;"

#: includes/fields/class-acf-field-select.php:48
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Pretražujem&hellip;"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Neuspješno učitavanje"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:54
msgctxt "verb"
msgid "Select"
msgstr "Odaberi"

#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Stilizirano sučelje"

#: includes/fields/class-acf-field-select.php:416
msgid "Use AJAX to lazy load choices?"
msgstr "Asinkrono učitaj dostupne odabire?"

#: includes/fields/class-acf-field-select.php:427
msgid "Specify the value returned"
msgstr "Preciziraj vrijednost za povrat"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Razdjelnik"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Kartica"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Pozicija"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Preciziraj prijelomnu točku za prethodne kartice. Ovo će omogućiti novi skup "
"kartica nakon prijelomne točke."

#: includes/fields/class-acf-field-taxonomy.php:713
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Nema %s"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "None"
msgstr "Bez odabira"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Select the taxonomy to be displayed"
msgstr "Odaberite taksonomiju za prikaz"

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Appearance"
msgstr "Prikaz"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Select the appearance of this field"
msgstr "Odaberite izgled polja"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Multiple Values"
msgstr "Omogući odabir više vrijednosti"

#: includes/fields/class-acf-field-taxonomy.php:781
msgid "Multi Select"
msgstr "Više odabira"

#: includes/fields/class-acf-field-taxonomy.php:783
msgid "Single Value"
msgstr "Jedan odabir"

#: includes/fields/class-acf-field-taxonomy.php:784
msgid "Radio Buttons"
msgstr "Radiogumbi"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "Create Terms"
msgstr "Kreiraj pojmove"

#: includes/fields/class-acf-field-taxonomy.php:804
msgid "Allow new terms to be created whilst editing"
msgstr "Omogući kreiranje pojmova prilikom uređivanja"

#: includes/fields/class-acf-field-taxonomy.php:813
msgid "Save Terms"
msgstr "Spremi pojmove"

#: includes/fields/class-acf-field-taxonomy.php:814
msgid "Connect selected terms to the post"
msgstr "Spoji odabrane pojmove sa objavom"

#: includes/fields/class-acf-field-taxonomy.php:823
msgid "Load Terms"
msgstr "Učitaj pojmove"

#: includes/fields/class-acf-field-taxonomy.php:824
msgid "Load value from posts terms"
msgstr "Učitaj pojmove iz objave"

#: includes/fields/class-acf-field-taxonomy.php:838
msgid "Term Object"
msgstr "Vrijednost pojma kao objekt"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Term ID"
msgstr "Vrijednost kao: ID pojma"

#: includes/fields/class-acf-field-taxonomy.php:898
#, php-format
msgid "User unable to add new %s"
msgstr "Korisnik nije u mogućnosti dodati %s"

#: includes/fields/class-acf-field-taxonomy.php:911
#, php-format
msgid "%s already exists"
msgstr "%s već postoji"

#: includes/fields/class-acf-field-taxonomy.php:952
#, php-format
msgid "%s added"
msgstr "Dodano: %s"

#: includes/fields/class-acf-field-taxonomy.php:997
msgid "Add"
msgstr "Dodaj"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Ograniči broj znakova"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Ostavite prazno za neograničeno"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Tekst polje"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Broj redova"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Podesi visinu tekstualnog polja"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Odabri vremena (sat i minute)"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "True / False"

#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159 includes/input.php:267
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Da"

#: includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:169 includes/input.php:268
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Ne"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Prikazuje tekst uz odabirni okvir"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Tekst za aktivno stanje"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Tekst prikazan dok je polje aktivno"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Off Text"
msgstr "Tekst za neaktivno stanje"

#: includes/fields/class-acf-field-true_false.php:166
msgid "Text shown when inactive"
msgstr "Tekst prikazan dok je polje neaktivno"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Poveznica"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Vrijednost molja biti valjana"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Korisnik"

#: includes/fields/class-acf-field-user.php:393
msgid "Filter by role"
msgstr "Filtar prema ulozi"

#: includes/fields/class-acf-field-user.php:401
msgid "All user roles"
msgstr "Sve uloge"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Vizualno uređivanje"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual"
msgstr "Vizualno"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Tekst polje"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Click to initialize TinyMCE"
msgstr "Aktiviraj vizualno uređivanje na klik"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Tabs"
msgstr "Kartice"

#: includes/fields/class-acf-field-wysiwyg.php:424
msgid "Visual & Text"
msgstr "Vizualno i tekstualno"

#: includes/fields/class-acf-field-wysiwyg.php:425
msgid "Visual Only"
msgstr "Samo vizualni"

#: includes/fields/class-acf-field-wysiwyg.php:426
msgid "Text Only"
msgstr "Samo tekstualno"

#: includes/fields/class-acf-field-wysiwyg.php:433
msgid "Toolbar"
msgstr "Alatna traka"

#: includes/fields/class-acf-field-wysiwyg.php:443
msgid "Show Media Upload Buttons?"
msgstr "Prikaži gumb za odabir datoteka?"

#: includes/fields/class-acf-field-wysiwyg.php:453
msgid "Delay initialization?"
msgstr "Odgodi učitavanje?"

#: includes/fields/class-acf-field-wysiwyg.php:454
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE neće biti učitan dok korisnik ne klikne na polje"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr "Uredi skup polja"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Verificiraj email"

#: includes/forms/form-front.php:103 pro/fields/class-acf-field-gallery.php:573
#: pro/options-page.php:81
msgid "Update"
msgstr "Ažuriraj"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Objava ažurirana"

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr "Spam"

#: includes/input.php:259
msgid "Expand Details"
msgstr "Prošireni prikaz"

#: includes/input.php:260
msgid "Collapse Details"
msgstr "Sakrij detalje"

#: includes/input.php:261
msgid "Validation successful"
msgstr "Uspješna verifikacija"

#: includes/input.php:262 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Verifikacija nije uspjela"

#: includes/input.php:263
msgid "1 field requires attention"
msgstr "1 polje treba vašu pažnju"

#: includes/input.php:264
#, php-format
msgid "%d fields require attention"
msgstr "Nekoliko polja treba vašu pažnje: %d"

#: includes/input.php:265
msgid "Restricted"
msgstr "Ograničen pristup"

#: includes/input.php:266
msgid "Are you sure?"
msgstr "Jeste li sigurni?"

#: includes/input.php:270
msgid "Cancel"
msgstr "Otkaži"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Objava"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Stranice"

#: includes/locations.php:96
msgid "Forms"
msgstr "Forme"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Prilog"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Svi oblici %s"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Komentar"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Trenutni tip korisnika"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Trenutni korisnik"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Prijavljen"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Prikazuje web stranicu"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Prikazuje administracijki dio"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Stavka izbornika"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Izbornik"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Lokacije izbornika"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Izbornici"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Matična stranica"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Predložak stranice"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Zadani predložak"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Tip stranice"

#: includes/locations/class-acf-location-page-type.php:145
msgid "Front Page"
msgstr "Početna stranica"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Posts Page"
msgstr "Stranica za objave"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Top Level Page (no parent)"
msgstr "Matična stranica (Nije podstranica)"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Parent Page (has children)"
msgstr "Matičan stranica (Ima podstranice)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Child Page (has parent)"
msgstr "Pod-stranica (Ima matičnu stranicu)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Kategorija objave"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Format objave"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Status objave"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Taksonomija objave"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Predložak stranice"

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "Pojam takosnomije"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Korisnički obrazac"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Dodaj / Uredi"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Registriraj"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Tip korisnika"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widget"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr "Uredi"

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr "Ažuriraj"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s je obavezno"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Objavi"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Niste dodali nijedan skup polja na ovu stranicu, <a href=“%s”>Dodaj skup "
"polja</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Greška</b>. Greška prilikom spajanja na server"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Ažuriranja"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Deaktiviraj licencu"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Aktiviraj licencu"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Informacije o licenci"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Da bi omogućili ažuriranje, molimo unesite vašu licencu i polje ispod. "
"Ukoliko ne posjedujete licencu, molimo posjetite <a href=“%s” "
"target=“_blank”>detalji i cijene</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Licenca"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Ažuriraj informacije"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Trenutna vezija"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Posljednja dostupna verzija"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Dostupna nadogradnja"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Nadogradi dodatak"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Unesite licencu kako bi mogli izvršiti nadogradnju"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Provjeri ponovno"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Obavijest od nadogradnjama"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Kloniraj"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Odaberite jedno ili više polja koja želite klonirati"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Prikaz"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Odaberite način prikaza kloniranog polja"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Skupno (Prikazuje odabrana polja kao dodatni skup unutar trenutnog polja)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Zamjena (Prikazuje odabrana polja umjesto trenutnog polja)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Oznake će biti prikazane kao %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Dodaj prefiks ispred oznake"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Vrijednosti će biti spremljene kao %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Dodaj prefiks ispred naziva polja"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Nepoznato polje"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Nepoznat skup polja"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Sva polje iz %s skupa polja"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:174
#: pro/fields/class-acf-field-repeater.php:470
msgid "Add Row"
msgstr "Dodaj red"

#: pro/fields/class-acf-field-flexible-content.php:34
msgid "layout"
msgstr "raspored"

#: pro/fields/class-acf-field-flexible-content.php:35
msgid "layouts"
msgstr "rasporedi"

#: pro/fields/class-acf-field-flexible-content.php:36
msgid "remove {layout}?"
msgstr "ukloni {layout}?"

#: pro/fields/class-acf-field-flexible-content.php:37
msgid "This field requires at least {min} {identifier}"
msgstr "Polje mora sadržavati najmanje {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:38
msgid "This field has a limit of {max} {identifier}"
msgstr "Polje je ograničeno na najviše {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:39
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Polje mora sadržavati najmanje {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:40
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "Polje {label} smije sadržavati najviše {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:41
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} preostalo (najviše {max})"

#: pro/fields/class-acf-field-flexible-content.php:42
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} obavezno (najmanje {min})"

#: pro/fields/class-acf-field-flexible-content.php:43
msgid "Flexible Content requires at least 1 layout"
msgstr "Potrebno je unijeti najmanje jedno fleksibilni polje"

#: pro/fields/class-acf-field-flexible-content.php:273
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Kliknite “%s” gumb kako bi započeki kreiranje raspored"

#: pro/fields/class-acf-field-flexible-content.php:406
msgid "Add layout"
msgstr "Dodaj razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:407
msgid "Remove layout"
msgstr "Ukloni razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:408
#: pro/fields/class-acf-field-repeater.php:298
msgid "Click to toggle"
msgstr "Klikni za uključivanje/isključivanje"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Reorder Layout"
msgstr "Presloži polja povlačenjem"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Reorder"
msgstr "Presloži"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Delete Layout"
msgstr "Obriši"

#: pro/fields/class-acf-field-flexible-content.php:558
msgid "Duplicate Layout"
msgstr "Dupliciraj razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:559
msgid "Add New Layout"
msgstr "Dodaj novi razmještaj"

#: pro/fields/class-acf-field-flexible-content.php:630
msgid "Min"
msgstr "Minimum"

#: pro/fields/class-acf-field-flexible-content.php:643
msgid "Max"
msgstr "Maksimum"

#: pro/fields/class-acf-field-flexible-content.php:670
#: pro/fields/class-acf-field-repeater.php:466
msgid "Button Label"
msgstr "Tekst gumba"

#: pro/fields/class-acf-field-flexible-content.php:679
msgid "Minimum Layouts"
msgstr "Najmanje"

#: pro/fields/class-acf-field-flexible-content.php:688
msgid "Maximum Layouts"
msgstr "Najviše"

#: pro/fields/class-acf-field-gallery.php:41
msgid "Add Image to Gallery"
msgstr "Dodaj sliku u galeriju"

#: pro/fields/class-acf-field-gallery.php:45
msgid "Maximum selection reached"
msgstr "Već ste dodali najviše dozovoljenih polja"

#: pro/fields/class-acf-field-gallery.php:321
msgid "Length"
msgstr "Dužina"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Potpis"

#: pro/fields/class-acf-field-gallery.php:373
msgid "Alt Text"
msgstr "Alternativni tekst"

#: pro/fields/class-acf-field-gallery.php:544
msgid "Add to gallery"
msgstr "Dodaj u galeriju"

#: pro/fields/class-acf-field-gallery.php:548
msgid "Bulk actions"
msgstr "Grupne akcije"

#: pro/fields/class-acf-field-gallery.php:549
msgid "Sort by date uploaded"
msgstr "Razvrstaj po datumu dodavanja"

#: pro/fields/class-acf-field-gallery.php:550
msgid "Sort by date modified"
msgstr "Razvrstaj po datumu zadnje promjene"

#: pro/fields/class-acf-field-gallery.php:551
msgid "Sort by title"
msgstr "Razvrstaj po naslovu"

#: pro/fields/class-acf-field-gallery.php:552
msgid "Reverse current order"
msgstr "Obrnuti redosljed"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Close"
msgstr "Zatvori"

#: pro/fields/class-acf-field-gallery.php:624
msgid "Minimum Selection"
msgstr "Minimalni odabri"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Maximum Selection"
msgstr "Maksimalni odabir"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Insert"
msgstr "Umetni"

#: pro/fields/class-acf-field-gallery.php:643
msgid "Specify where new attachments are added"
msgstr "Precizirajte gdje se dodaju novi prilozi"

#: pro/fields/class-acf-field-gallery.php:647
msgid "Append to the end"
msgstr "Umetni na kraj"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Prepend to the beginning"
msgstr "Umetni na početak"

#: pro/fields/class-acf-field-repeater.php:36
msgid "Minimum rows reached ({min} rows)"
msgstr "Minimalni broj redova je već odabran ({min})"

#: pro/fields/class-acf-field-repeater.php:37
msgid "Maximum rows reached ({max} rows)"
msgstr "Maksimalni broj redova je već odabran ({max})"

#: pro/fields/class-acf-field-repeater.php:343
msgid "Add row"
msgstr "Dodaj red"

#: pro/fields/class-acf-field-repeater.php:344
msgid "Remove row"
msgstr "Ukloni red"

#: pro/fields/class-acf-field-repeater.php:419
msgid "Collapsed"
msgstr "Sklopljeno"

#: pro/fields/class-acf-field-repeater.php:420
msgid "Select a sub field to show when row is collapsed"
msgstr "Odaberite pod polje koje će biti prikazano dok je red sklopljen"

#: pro/fields/class-acf-field-repeater.php:430
msgid "Minimum Rows"
msgstr "Minimalno redova"

#: pro/fields/class-acf-field-repeater.php:440
msgid "Maximum Rows"
msgstr "Maksimalno redova"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Ne postoji stranica sa postavkama"

#: pro/options-page.php:51
msgid "Options"
msgstr "Postavke"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Postavke spremljene"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Da bi omogućili automatsko ažuriranje, molimo unesite licencu na stranici <a "
"href=“%s”>ažuriranja</a>. Ukoliko nemate licencu, pogledajte <a "
"href=“%s”>opcije i cijene</a>."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Getting Started"
#~ msgstr "Kako početi"

#~ msgid "Field Types"
#~ msgstr "Tipovi polja"

#~ msgid "Functions"
#~ msgstr "Funkcije"

#~ msgid "Actions"
#~ msgstr "Akcije"

#~ msgid "Features"
#~ msgstr "Mogućnosti"

#~ msgid "How to"
#~ msgstr "Pomoć"

#~ msgid "Tutorials"
#~ msgstr "Tutorijali"

#~ msgid "FAQ"
#~ msgstr "Česta pitanja"

#~ msgid "Error"
#~ msgstr "Greška"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Izvoz polja u PHP obliku"

#~ msgid "Download export file"
#~ msgstr "Preuzmi datoteku"

#~ msgid "Generate export code"
#~ msgstr "Stvori kod za izvoz"

#~ msgid "Import"
#~ msgstr "Uvoz"

#~ msgid "Term meta upgrade not possible (termmeta table does not exist)"
#~ msgstr ""
#~ "Nije moguće dovrišti nadogradnju tablice 'termmeta', tablica ne postoji u "
#~ "bazi"
