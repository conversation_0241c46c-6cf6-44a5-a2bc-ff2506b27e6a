{"packages": [{"name": "altorouter/altorouter", "version": "v1.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/dannyvankooten/AltoRouter.git", "reference": "39c50092470128c12284d332bb57f306bb5b58e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dannyvankooten/AltoRouter/zipball/39c50092470128c12284d332bb57f306bb5b58e4", "reference": "39c50092470128c12284d332bb57f306bb5b58e4", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"codeclimate/php-test-reporter": "dev-master", "phpunit/phpunit": "4.5.*"}, "time": "2015-11-30T00:47:43+00:00", "type": "library", "installation-source": "dist", "autoload": {"classmap": ["AltoRouter.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://dannyvankooten.com/"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/koenpunt"}, {"name": "niahoo", "homepage": "https://github.com/niahoo"}], "description": "A lightning fast router for PHP", "homepage": "https://github.com/dannyvankooten/AltoRouter", "keywords": ["lightweight", "router", "routing"], "support": {"issues": "https://github.com/dannyvankooten/AltoRouter/issues", "source": "https://github.com/dannyvankooten/AltoRouter/tree/master"}, "install-path": "../altorouter/altorouter"}, {"name": "asm89/twig-cache-extension", "version": "1.4.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/asm89/twig-cache-extension.git", "reference": "13787226956ec766f4770722082288097aebaaf3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/asm89/twig-cache-extension/zipball/13787226956ec766f4770722082288097aebaaf3", "reference": "13787226956ec766f4770722082288097aebaaf3", "shasum": ""}, "require": {"php": ">=5.3.2", "twig/twig": "^1.0|^2.0"}, "require-dev": {"doctrine/cache": "~1.0", "phpunit/phpunit": "^5.0 || ^4.8.10"}, "suggest": {"psr/cache-implementation": "To make use of PSR-6 cache implementation via PsrCacheAdapter."}, "time": "2020-01-01T20:47:37+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Cache fragments of templates directly within Twig.", "homepage": "https://github.com/asm89/twig-cache-extension", "keywords": ["cache", "extension", "twig"], "support": {"issues": "https://github.com/asm89/twig-cache-extension/issues", "source": "https://github.com/asm89/twig-cache-extension/tree/1.4.0"}, "abandoned": "twig/cache-extension", "install-path": "../asm89/twig-cache-extension"}, {"name": "composer/installers", "version": "v1.12.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/composer/installers.git", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/installers/zipball/d20a64ed3c94748397ff5973488761b22f6d3f19", "reference": "d20a64ed3c94748397ff5973488761b22f6d3f19", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0"}, "replace": {"roundcube/plugin-installer": "*", "shama/baton": "*"}, "require-dev": {"composer/composer": "1.6.* || ^2.0", "composer/semver": "^1 || ^3", "phpstan/phpstan": "^0.12.55", "phpstan/phpstan-phpunit": "^0.12.16", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.3"}, "time": "2021-09-13T08:19:44+00:00", "type": "composer-plugin", "extra": {"class": "Composer\\Installers\\Plugin", "branch-alias": {"dev-main": "1.x-dev"}}, "installation-source": "dist", "autoload": {"psr-4": {"Composer\\Installers\\": "src/Composer/Installers"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/shama"}], "description": "A multi-framework Composer library installer", "homepage": "https://composer.github.io/installers/", "keywords": ["Craft", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "ImageCMS", "Kanboard", "Lan Management System", "MODX Evo", "MantisBT", "Mautic", "Maya", "OXID", "Plentymarkets", "Porto", "RadPHP", "SMF", "Starbug", "Thelia", "Whmcs", "WolfCMS", "agl", "aimeos", "annotatecms", "attogram", "bitrix", "cakephp", "chef", "cockpit", "codeigniter", "concrete5", "croogo", "<PERSON><PERSON><PERSON><PERSON>", "drupal", "eZ Platform", "elgg", "expressionengine", "fuelphp", "grav", "installer", "itop", "j<PERSON><PERSON>", "known", "kohana", "laravel", "lavalite", "lithium", "magento", "majima", "mako", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "modulework", "modx", "moodle", "osclass", "pantheon", "phpbb", "piwik", "ppi", "processwire", "puppet", "pxcms", "reindex", "roundcube", "shopware", "silverstripe", "sydes", "sylius", "symfony", "tastyigniter", "typo3", "wordpress", "yawik", "zend", "zikula"], "support": {"issues": "https://github.com/composer/installers/issues", "source": "https://github.com/composer/installers/tree/v1.12.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "install-path": "./installers"}, {"name": "symfony/polyfill-ctype", "version": "v1.23.0", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/46cd95797e9df938fdd2b03693b5fca5e64b01ce", "reference": "46cd95797e9df938fdd2b03693b5fca5e64b01ce", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-ctype": "For best performance"}, "time": "2021-02-19T12:13:01+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.23.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-ctype"}, {"name": "symfony/polyfill-mbstring", "version": "v1.23.1", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9174a3d80210dca8daa7f31fec659150bbeabfc6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9174a3d80210dca8daa7f31fec659150bbeabfc6", "reference": "9174a3d80210dca8daa7f31fec659150bbeabfc6", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "time": "2021-05-27T12:26:48+00:00", "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "installation-source": "dist", "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.23.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "install-path": "../symfony/polyfill-mbstring"}, {"name": "timber/timber", "version": "1.18.2", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/timber/timber.git", "reference": "18766d1af8650ca919534cc497e7f0e8d82423a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/timber/timber/zipball/18766d1af8650ca919534cc497e7f0e8d82423a3", "reference": "18766d1af8650ca919534cc497e7f0e8d82423a3", "shasum": ""}, "require": {"asm89/twig-cache-extension": "~1.0", "composer/installers": "~1.0", "php": ">=5.3.0|7.*", "twig/twig": "^1.41|^2.10", "upstatement/routes": "0.5"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.5.0", "johnpbloch/wordpress": "*", "phpunit/phpunit": "5.7.16|6.*", "squizlabs/php_codesniffer": "3.*", "wp-coding-standards/wpcs": "^2.0", "wpackagist-plugin/advanced-custom-fields": "5.*", "wpackagist-plugin/co-authors-plus": "3.2.*|3.4.*"}, "suggest": {"satooshi/php-coveralls": "1.0.* for code coverage"}, "time": "2020-10-11T15:08:40+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"Timber\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://upstatement.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://connorburton.com"}], "description": "Plugin to write WordPress themes w Object-Oriented Code and the Twig Template Engine", "homepage": "http://timber.upstatement.com", "keywords": ["templating", "themes", "timber", "twig"], "support": {"docs": "https://timber.github.io/docs/", "issues": "https://github.com/timber/timber/issues", "source": "https://github.com/timber/timber"}, "install-path": "../timber/timber"}, {"name": "twig/twig", "version": "v2.14.6", "version_normalized": "********", "source": {"type": "git", "url": "https://github.com/twigphp/Twig.git", "reference": "27e5cf2b05e3744accf39d4c68a3235d9966d260"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twigphp/Twig/zipball/27e5cf2b05e3744accf39d4c68a3235d9966d260", "reference": "27e5cf2b05e3744accf39d4c68a3235d9966d260", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-mbstring": "^1.3"}, "require-dev": {"psr/container": "^1.0", "symfony/phpunit-bridge": "^4.4.9|^5.0.9"}, "time": "2021-05-16T12:12:47+00:00", "type": "library", "extra": {"branch-alias": {"dev-master": "2.14-dev"}}, "installation-source": "dist", "autoload": {"psr-0": {"Twig_": "lib/"}, "psr-4": {"Twig\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fabien.potencier.org", "role": "Lead Developer"}, {"name": "Twig Team", "role": "Contributors"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Project Founder"}], "description": "Twig, the flexible, fast, and secure template language for PHP", "homepage": "https://twig.symfony.com", "keywords": ["templating"], "support": {"issues": "https://github.com/twigphp/Twig/issues", "source": "https://github.com/twigphp/Twig/tree/v2.14.6"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/twig/twig", "type": "tidelift"}], "install-path": "../twig/twig"}, {"name": "upstatement/routes", "version": "0.5", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/Upstatement/routes.git", "reference": "3267d28be0a73f197087d58384e1a358d85671b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Upstatement/routes/zipball/3267d28be0a73f197087d58384e1a358d85671b6", "reference": "3267d28be0a73f197087d58384e1a358d85671b6", "shasum": ""}, "require": {"altorouter/altorouter": "^1.1.0", "composer/installers": "~1.0", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "3.7.*", "satooshi/php-coveralls": "dev-master", "wp-cli/wp-cli": "*"}, "time": "2018-12-04T01:13:41+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-0": {"Routes": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://upstatement.com"}], "description": "Manage rewrites and routes in WordPress with this dead-simple plugin", "homepage": "http://routes.upstatement.com", "keywords": ["redirects", "rewrite", "routes", "routing"], "support": {"issues": "https://github.com/jarednova/routes/issues", "source": "https://github.com/jarednova/routes", "wiki": "https://github.com/jarednova/routes/wiki"}, "install-path": "../upstatement/routes"}], "dev": true, "dev-package-names": []}