<!--
First off, hello!

Thanks for submitting a PR. We love/welcome PRs (especially if it's your first).
Have any questions? Read this section in CONTRIBUTING.md: https://github.com/timber/timber/blob/master/CONTRIBUTING.md#pull-requests.
--> 

**Ticket**: # <!-- Ignore this if not relevant -->

## Issue
<!-- Description of the problem that this code change is solving -->


## Solution
<!-- Description of the solution that this code changes are introducing to the application. -->


## Impact
<!-- What impact will this have on the current codebase, performance, backwards compatibility? -->


## Usage Changes
<!-- Are there are any usage changes that we need to know about? If so, list them here so that we can integrate it in the release notes and developers know what usage changes are associated to your PR.

Alternatively, you’re very welcome to directly edit the readme.txt file with:
- A quick summary, including your Github handle.
- A list of changes for Theme Developers (under the "Changes for Theme Developers" label).
- New usage instructions, possibly with a short code example.
-->


## Considerations
<!-- As we do not live in an ideal world it's worth to share your thought on how we could make the solution even better. -->


## Testing
<!-- Are unit tests included? If they need to be written, please provide pseudo code for a scenario that fails without your code, but succeeds with it -->
