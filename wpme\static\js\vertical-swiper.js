// 垂直轮播JavaScript实现
document.addEventListener('DOMContentLoaded', function () {
    // 延迟检查Elementor是否加载
    function checkElementorAndInit() {
        if (typeof elementorFrontend !== 'undefined' && elementorFrontend.hooks) {
            elementorFrontend.hooks.addAction('frontend/element_ready/global', function () {
                initVerticalSwiper();
            });
        } else {
            // 如果Elementor未加载或者不在Elementor环境中，直接初始化
            setTimeout(initVerticalSwiper, 100);
        }
    }

    // 等待一小段时间让Elementor加载
    setTimeout(checkElementorAndInit, 500);

    // 备用初始化 - 如果3秒后还没有初始化，强制初始化
    setTimeout(function () {
        const container = document.getElementById('niankan-container');
        if (container && !container.querySelector('.swiper-container')) {
            console.log('执行备用初始化...');
            initVerticalSwiper();
        }
    }, 3000);
});

function initVerticalSwiper() {
    console.log('开始初始化垂直轮播...');

    const container = document.getElementById('niankan-container');
    if (!container) {
        console.log('未找到niankan-container容器');
        return;
    }

    // 检查是否已经初始化
    if (container.querySelector('.swiper-container')) {
        console.log('垂直轮播已经初始化过了');
        return;
    }

    // 获取所有图片容器
    const imageContainers = container.querySelectorAll('.e-con.e-child');
    if (imageContainers.length === 0) {
        console.log('未找到图片容器');
        return;
    }

    // 创建Swiper结构
    const swiperContainer = document.createElement('div');
    swiperContainer.className = 'swiper-container';

    const swiperWrapper = document.createElement('div');
    swiperWrapper.className = 'swiper-wrapper';

    // 将每个图片容器转换为swiper-slide
    imageContainers.forEach((container, index) => {
        const slide = document.createElement('div');
        slide.className = 'swiper-slide';

        // 克隆原始内容
        const clonedContent = container.cloneNode(true);
        slide.appendChild(clonedContent);

        swiperWrapper.appendChild(slide);
    });

    // 组装Swiper
    swiperContainer.appendChild(swiperWrapper);

    // 将Swiper添加到容器
    const innerContainer = container.querySelector('.e-con-inner');
    innerContainer.appendChild(swiperContainer);

    // 加载Swiper CSS和JS
    loadSwiperAssets().then(() => {
        // 初始化Swiper
        const swiper = new Swiper('.swiper-container', {
            direction: 'vertical',
            slidesPerView: 3,
            centeredSlides: true,
            spaceBetween: 30,
            loop: true,
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            speed: 600,
            effect: 'slide',

            // 触摸/鼠标控制
            mousewheel: {
                enabled: true,
                sensitivity: 1,
            },

            // 键盘控制
            keyboard: {
                enabled: true,
                onlyInViewport: true,
            },

            // 响应式断点
            breakpoints: {
                320: {
                    slidesPerView: 2.5,
                    spaceBetween: 20,
                },
                768: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                },
                1024: {
                    slidesPerView: 3,
                    spaceBetween: 40,
                }
            },

            // 事件回调
            on: {
                init: function () {
                    console.log('垂直轮播初始化完成');
                    updateSlideStyles(this);
                },
                slideChange: function () {
                    updateSlideStyles(this);
                },
                transitionStart: function () {
                    this.slides.forEach(slide => {
                        slide.classList.add('swiper-slide-transition-start');
                    });
                },
                transitionEnd: function () {
                    this.slides.forEach(slide => {
                        slide.classList.remove('swiper-slide-transition-start');
                        slide.classList.add('swiper-slide-transition-end');
                    });

                    setTimeout(() => {
                        this.slides.forEach(slide => {
                            slide.classList.remove('swiper-slide-transition-end');
                        });
                    }, 100);
                }
            }
        });

        // 添加鼠标悬停暂停自动播放
        const swiperEl = container.querySelector('.swiper-container');
        swiperEl.addEventListener('mouseenter', () => {
            swiper.autoplay.stop();
        });

        swiperEl.addEventListener('mouseleave', () => {
            swiper.autoplay.start();
        });
    });
}

// 更新幻灯片样式
function updateSlideStyles(swiper) {
    swiper.slides.forEach((slide, index) => {
        slide.classList.remove('swiper-slide-prev', 'swiper-slide-next');

        if (index === swiper.activeIndex - 1 ||
            (swiper.activeIndex === 0 && index === swiper.slides.length - 1)) {
            slide.classList.add('swiper-slide-prev');
        } else if (index === swiper.activeIndex + 1 ||
            (swiper.activeIndex === swiper.slides.length - 1 && index === 0)) {
            slide.classList.add('swiper-slide-next');
        }
    });
}

// 动态加载Swiper资源
function loadSwiperAssets() {
    return new Promise((resolve) => {
        // 检查Swiper是否已加载
        if (typeof Swiper !== 'undefined') {
            resolve();
            return;
        }

        // 加载Swiper CSS
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = 'https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css';
        document.head.appendChild(cssLink);

        // 加载Swiper JS
        const script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js';
        script.onload = () => {
            console.log('Swiper资源加载完成');
            resolve();
        };
        document.head.appendChild(script);
    });
}

// 窗口大小改变时重新计算
window.addEventListener('resize', function () {
    const swiper = document.querySelector('.swiper-container')?.swiper;
    if (swiper) {
        swiper.update();
    }
});
