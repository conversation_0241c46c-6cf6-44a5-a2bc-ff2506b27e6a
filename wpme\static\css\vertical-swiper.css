/* 垂直轮播样式 - WordPress主题集成版本 */
#niankan-container {
    position: relative;
    height: 100vh;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.swiper-container {
    width: 100%;
    height: 100vh;
    position: relative;
}

.swiper-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.4s ease;
    opacity: 0.7;
    transform: scale(0.85);
    height: auto;
    margin: 20px 0;
}

.swiper-slide-active {
    opacity: 1;
    transform: scale(1);
    z-index: 10;
}

.swiper-slide-prev,
.swiper-slide-next {
    opacity: 0.7;
    transform: scale(0.85);
}

/* 图片容器样式 */
.swiper-slide .e-con-inner {
    width: 100%;
    display: flex;
    justify-content: center;
}

.swiper-slide .elementor-widget-container {
    width: 100%;
    text-align: center;
}

.swiper-slide img {
    width: 680px;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    transition: all 0.4s ease;
}

/* 激活状态的图片样式 */
.swiper-slide-active img {
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}



/* 响应式设计 */
@media (max-width: 768px) {
    .swiper-container {
        height: 100vh;
    }

    .swiper-slide img {
        width: 90vw;
        max-width: 500px;
        border-radius: 10px;
    }

    .swiper-slide {
        margin: 15px 0;
    }
}

@media (max-width: 480px) {
    .swiper-container {
        height: 100vh;
    }

    .swiper-slide img {
        width: 95vw;
        max-width: 350px;
    }

    .swiper-slide {
        margin: 10px 0;
        transform: scale(0.9);
    }

    .swiper-slide-active {
        transform: scale(1);
    }
}

/* 平滑过渡效果 */
.swiper-slide-transition-start,
.swiper-slide-transition-end {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 隐藏原始容器的子元素，只显示轮播 */
#niankan-container .e-con-inner>.e-con {
    display: none;
}

/* 显示轮播容器 */
#niankan-container .swiper-container {
    display: block;
}