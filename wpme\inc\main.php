<?php

function theme_check_active()
{
    return true;
}

/**
 * 获取所有 acf 配置页内容
 *
 * @return mixed
 */
function string_to_timber_context($context)
{
    if (class_exists('acf')) {
        $context['ops'] = get_fields('option');
    }
    $menus = get_registered_nav_menus();
    foreach ($menus as $key => $menu) {
        $context[$key] = new Timber\Menu($key);
    }

    $context['PROOT'] = get_template_directory();
    $context['PURL'] = get_template_directory_uri();
    $context['ROOT'] = get_stylesheet_directory();
    $context['URL'] = get_stylesheet_directory_uri();
    return $context;
}
add_filter('timber_context', 'string_to_timber_context');
/**
 * 函数Timber化
 *
 * @return mixed
 */
function functions_to_timber($twig)
{
    $twig->addFunction(
        new Timber\Twig_Function('is_front_page', 'is_front_page')
    );
    $twig->addFunction(new Timber\Twig_Function('is_404', 'is_404'));
    $twig->addFunction(new Timber\Twig_Function('is_page', 'is_page'));
    $twig->addFunction(new Timber\Twig_Function('is_category', 'is_category'));
    $twig->addFunction(new Timber\Twig_Function('is_single', 'is_single'));
    $twig->addFunction(
        new Timber\Twig_Function('get_post_pagination', 'get_post_pagination')
    );
    $twig->addFunction(new Timber\Twig_Function('body_class', 'body_class'));
    $twig->addFunction(new Timber\Twig_Function('site_url', 'site_url'));
    $twig->addFunction(new Timber\Twig_Function('home_url', 'home_url'));
    $twig->addFunction(
        new Timber\Twig_Function('get_permalink', 'get_permalink')
    );
    $twig->addFunction(new Timber\Twig_Function('wp_die', 'wp_die'));
    $twig->addFunction(
        new Timber\Twig_Function('get_bookmarks', 'get_bookmarks')
    );
    $twig->addFunction(new Timber\Twig_Function('gop', 'gop'));
    $twig->addFunction(new Timber\Twig_Function('op', 'op'));
    $twig->addFunction(
        new Timber\Twig_Function('wp_is_mobile', 'wp_is_mobile')
    );
    $twig->addFunction(
        new Timber\Twig_Function('do_shortcode', 'do_shortcode')
    );
    $twig->addFunction(new Timber\Twig_Function('post_views', 'post_views'));
    $twig->addFunction(new Timber\Twig_Function('get_field', 'get_field'));
    $twig->addFunction(new Timber\Twig_Function('_res', '_res'));
    $twig->addFunction(new Timber\Twig_Function('x_term_count', 'x_term_count'));
    $twig->addFunction(new Timber\Twig_Function('get_header', 'get_header'));
    $twig->addFunction(new Timber\Twig_Function('get_footer', 'get_footer'));
    $twig->addFunction(new Timber\Twig_Function('do_action', 'do_action'));
    return $twig;
}
add_filter('timber/twig', 'functions_to_timber');

function _res($str)
{
    $val =
        !empty($_REQUEST[$str]) || isset($_REQUEST[$str])
            ? $_REQUEST[$str]
            : null;
    return $val;
}

function _safe($string)
{
    $string = preg_replace(
        '/\<([\/]?)script([^\>]*?)\>/si',
        '&lt;\\1script\\2&gt;',
        $string
    );
    $string = preg_replace(
        '/\<([\/]?)iframe([^\>]*?)\>/si',
        '&lt;\\1iframe\\2&gt;',
        $string
    );
    $string = preg_replace(
        '/\<([\/]?)frame([^\>]*?)\>/si',
        '&lt;\\1frame\\2&gt;',
        $string
    );
    $string = str_replace('javascript:', 'javascript：', $string);
    $string = str_replace('%20', '', $string);
    $string = str_replace('%27', '', $string);
    $string = str_replace('%2527', '', $string);
    $string = str_replace('*', '', $string);
    $string = str_replace('"', '&quot;', $string);
    $string = str_replace("'", '', $string);
    $string = str_replace('"', '', $string);
    $string = str_replace(';', '', $string);
    $string = str_replace('<', '&lt;', $string);
    $string = str_replace('>', '&gt;', $string);
    $string = str_replace('{', '', $string);
    $string = str_replace('}', '', $string);
    $string = str_replace('\\', '', $string);
    $string = strip_tags($string);
    $string = htmlentities($string);

    return trim($string);
}

function do_post($url, $data)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    $ret = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return array('status' => $httpCode, 'html' => $ret);
}

function get_url_contents($url)
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_URL, $url);
    $result = curl_exec($ch);
    curl_close($ch);

    return $result;
}

// 获取分类下的子分类
// $cat=get_query_var('cat');获取当前分类
function getchild($id, $tax='category')
{
    $childs = array();
    foreach (get_term_children($id, $tax) as $i) {
        if (!empty($i)) {
            $childs[] = get_term($i);
        }
    }

    return $childs;
}

// post_parent_id函数,用于获取文章的第一个直接父分类ID:
//post_parent_id
//to get the post parent cat ID;
function post_parent_id($postid = null, $tax = 'category')
{
    if (!$postid) {
        global $post;
        $postid = $post->ID;
    }
    $c = get_the_terms($postid, $tax);

    return $c[0]->term_id;
}

// post_root_id函数,用于获取文章的根分类ID:
//post_root_id
//to get the post root cat ID;
function post_root_id($postid = null, $tax = 'category')
{
    if (!$postid) {
        global $post;
        $postid = $post->ID;
    }
    $c = get_the_terms($postid, $tax);
    return cat_root_id($c[0]->term_id, $tax);
}

/**
 * cat_is_in_under_category函数,用于判断某一分类是否归属于另一分类或就是这一分类本身:
 * cat_is_in_under_category
 * check if a cat is under another
 * @param $category 要对比的分类 id
 * @param null $cat 当前的分类id
 * @param string $tax 分类法
 *
 * @return int
 */
function cat_is_in_under_category($category, $cat = null, $tax = 'category')
{
    $is_in_under_category = 0;
    if ($cat == null) {
        global $cat;
    }
    if ($category == $cat) {
        $is_in_under_category = 1;
    }
    $descendants = get_term_children($category, $tax);
    foreach ($descendants as $desc) {
        if ($cat == $desc) {
            $is_in_under_category = 1;
        }
    }

    return $is_in_under_category;
}

// cat_parent_id函数,用于获取某一分类的父分类ID:
//cat_parent_id
//to get the cat parent cat ID, if none return the cat ID;
function cat_parent_id($catid = null, $tax = 'category')
{
    if ($catid == null) {
        global $cat;
        $catid = $cat;
    }
    $this_cat = get_term($catid, $tax);
    if ($this_cat->parent != 0) {
        $catid = $this_cat->parent;
    }

    return $catid;
}

// cat_root_id函数,用于获取某一分类的根分类ID:
//cat_root_id
//to get the cat root cat ID, if none return the cat ID;
function cat_root_id($catid = null, $tax = 'category')
{
    if ($catid == null) {
        global $cat;
        $catid = $cat;
    }
    $this_cat = get_term($catid, $tax);
    $this_cat_parent = $this_cat->parent;
    while ($this_cat_parent != 0) {
        $catid = $this_cat->parent;
        $this_cat = get_term($catid, $tax);
        $this_cat_parent = $this_cat->parent;
    }

    return $catid;
}

// page_is_in_under_page函数,用于判断某一Page是否是另一Page本身或归属其子Page:
//page_is_in_under_page
//check if a page is under another page
function page_is_in_under_page($parent_page, $pageid = null)
{
    if (!$pageid) {
        global $post;
        $pageid = $post->ID;
    }
    if ($pageid == $parent_page) {
        return true;
    }
    $this_page = get_post($pageid);
    $this_page_parent = $this_page->post_parent;
    if ($this_page_parent == $parent_page) {
        return true;
    }
    while ($this_page_parent != 0) {
        $pageid = $this_page_parent;
        $this_page = get_post($pageid);
        $this_page_parent = $this_page->post_parent;
        if ($this_page_parent == $parent_page) {
            return true;
        }
    }

    return false;
}

//page_parent_id函数,用于获取某一Page的直属父Page的ID:
//page_parent_id
//to get the parent page id, if none return the page id;
function page_parent_id($pageid = null)
{
    if (!$pageid) {
        global $post;
        $pageid = $post->ID;
    }
    $this_page = get_post($pageid);
    $this_page_parent = $this_page->post_parent;
    if ($this_page_parent != 0) {
        $pageid = $this_page_parent;
    }

    return $pageid;
}

//page_root_id函数,用于获取某一Page的根Page的ID:
//page_root_id
//to get the root parent page id, if none return the page id;
function page_root_id($pageid = null, $post_type = 'page')
{
    if (!$pageid) {
        global $post;
        $pageid = $post->ID;
    }
    $this_page = get_post($pageid, $post_type);
    $this_page_parent = $this_page->post_parent;
    while ($this_page_parent != 0) {
        $pageid = $this_page_parent;
        $this_page = get_post($pageid, $post_type);
        $this_page_parent = $this_page->post_parent;
    }

    return $pageid;
}

//获取顶级分类的别名
function cat_root_slug()
{
    global $wp_query;
    $cat_ID = get_query_var('cat');
    $cat = get_category(cat_root_id());

    return $cat->slug;
}

//通过POST ID 获取顶级分类的别名
function post_root_slug()
{
    global $wp_query;
    $cat_ID = get_query_var('cat');
    $cat = get_category(post_root_id());

    return $cat->slug;
}

//上传图片文档等重命名。
function ruidian_file_upload_fix($file)
{
    $time = date('Ymd');
    $file['name'] =
        $time .
        '' .
        mt_rand(1, 100) .
        '.' .
        pathinfo($file['name'], PATHINFO_EXTENSION);

    return $file;
}

//add_filter('wp_handle_upload_prefilter', 'ruidian_file_upload_fix');

function new_filename($filename)
{
    $info = pathinfo($filename);
    $time = date('Ymd');
    $ext = empty($info['extension']) ? '' : '.' . $info['extension'];

    //  $name = basename($filename, $ext);
    return $time . '' . mt_rand(10, 1000) . $ext;
}

add_filter('sanitize_file_name', 'new_filename', 10);

function my_upload_mimes($mimes = array())
{
    $mimes['svg'] = 'image/svg+xml';
    return $mimes;
}
add_filter('upload_mimes', 'my_upload_mimes');

/**
 * 控制台输出
 * @param $var
 * @param string $level
 */
function console($var, $level = 'debug')
{
    if (is_array($var) || is_object($var)) {
        $output = json_encode($var);
        $jsonDecode = json_decode($output);
        if (empty((array) $jsonDecode) && !empty($var)) {
            echo "<script>console.{$level}('不支持输出')</script>";
            return;
        }
    } elseif (is_string($var)) {
        $output = '"' . $var . '"';
    } else {
        $output = $var;
    }
    echo "<script>console.{$level}({$output})</script>";
    return;
}

//文章阅读次数统计与调用
function record_visitors()
{
    if (is_singular()) {
        global $post;
        $post_ID = $post->ID;
        if ($post_ID) {
            $post_views = (int) get_post_meta($post_ID, 'views', true);
            if (!update_post_meta($post_ID, 'views', $post_views + 1)) {
                add_post_meta($post_ID, 'views', 1, true);
            }
        }
    }
}

add_action('wp_head', 'record_visitors');

function post_views($echo = 1, $before = '', $after = '')
{
    global $post;
    $post_ID = $post->ID;
    $views = (int) get_post_meta($post_ID, 'views', true);
    if ($echo) {
        echo $before, number_format($views), $after;
    } else {
        return $views;
    }
}

/**
 * 获取上一篇和下一篇
 * @param string $type 类型：prev或者next
 * @param bool $in_same_term 是否只显示相同分类的文章
 * @param bool $taxonomy 分类法
 * @param string $excluded_terms 排除分类IDs
 * @return mixed
 */
function get_post_pagination(
    $type = 'prev',
    $in_same_term = 'true',
    $taxonomy = 'category',
    $excluded_terms = ''
) {
    if ($type == 'next') {
        $post = get_next_post($in_same_term, $excluded_terms, $taxonomy);
    } else {
        $post = get_previous_post($in_same_term, $excluded_terms);
    }
    if ($post->ID) {
        return WPME_TIMBER ? new Timber\Post($post->ID) : $post;
    } else {
        return false;
    }
}

/**
 * get theme option
 */
function gop($field, $default = false)
{
    if (function_exists('get_field')) {
        $options = get_field($field, 'option');
        if (isset($options)) {
            return $options;
        }
    }
    return $default;
}

/*
功能：判断某个目录下是否存在文件；
参数：$path —— 要进行判断的目录，使用绝对路径
返回值：存在 - true  不存在 - false
 */
function dir_exist_file($path)
{
    if (!is_dir($path)) {
        return false;
    }

    $files = scandir($path);

    // 删除  "." 和 ".."
    unset($files[0]);
    unset($files[1]);

    // 判断是否为空
    if (!empty($files[2])) {
        return true;
    }

    return false;
}

function qx_post($type, $same_tmp = 0)
{
    global $post;
    $postid = $post->ID;
    $term = get_the_category($postid);
    if (post_root_id($term[0]->term_id) == get_term('13')) {
        $cid = get_term('13');
    } else {
        $cid = $term[0]->term_id;
    }
    $posts = new WP_Query(array(
        'cat' => $cid,
        'posts_per_page' => -1,
        'orderby' => 'term_order'
    ));

    $pst = $posts->get_posts();
    $pst_max = $posts->post_count;
    if ($pst_max > 0) {
        foreach ($pst as $k => $v) {
            if ($v->ID == $postid) {
                $post_order = $k;
                break;
            }
        }
    }
    wp_reset_query();

    if ($post_order == 0) {
        $context['prev'] = '';
    } else {
        if ($same_tmp) {
            for ($i = $post_order - 1; $i >= 0; $i--) {
                $pp = $pst[$i];
                if (get_field('show', $pp->ID) == get_field('show', $postid)) {
                    $context['prev'] = $pp;
                    break;
                }
            }
        } else {
            $context['prev'] = $pst[$post_order - 1];
        }
    }
    if ($post_order == $pst_max - 1) {
        $context['next'] = '';
    } else {
        if ($same_tmp && get_field('show', $postid) != null) {
            for ($i = $post_order + 1; $i <= $pst_max; $i++) {
                $pp = $pst[$i];
                if (get_field('show', $pp->ID) == get_field('show', $postid)) {
                    $context['next'] = $pp;
                    break;
                }
            }
        } else {
            $context['next'] = $pst[$post_order + 1];
        }
    }
    return $context[$type]->ID;
}

//添加自定义文章类型代码
class QX_Post_Type
{
    private $type;
    private $type_category;
    private $type_supports;
    private $type_name;
    public function __construct(
        $type,
        $type_category,
        $type_name,
        $type_supports
    ) {
        $this->type = $type;
        $this->type_category = $type_category;
        $this->type_name = $type_name;
        $this->type_supports = $type_supports;
        //添加自定义文章类型
        add_action('init', array($this, 'qx_add_type'));
        //为商品类自定义类型增加分类功能
        foreach ($this->type_category as $k => $v) {
            if ($v != 'category' && $v != 'post_tag') {
                add_action('init', array($this, 'qx_add_categroy'), 0);
            }
        }
        //修改自定义类型文章的地址
        add_filter('post_type_link', array($this, 'qx_add_link'), 1, 3);
        //让自定义的url支持伪静态
        add_action('init', array($this, 'qx_rewrites_init'));
    }
    //新增自定义文章类型
    public function qx_add_type()
    {
        register_post_type($this->type, array(
            'labels' => array(
                'name' => $this->type_name,
                'title' => $this->type_name,
                'singular_name' => $this->type_name,
                'add_new' => '添加' . $this->type_name,
                'add_new_item' => '添加新' . $this->type_name,
                'edit' => '编辑',
                'edit_item' => '编辑' . $this->type_name,
                'new_item' => '新' . $this->type_name,
                'view' => '查看' . $this->type_name,
                'view_item' => '查看' . $this->type_name,
                'search_items' => '搜索' . $this->type_name,
                'not_found' => '没有找到相关' . $this->type_name,
                'not_found_in_trash' => '没有' . $this->type_name . '评论',
                'parent' => $this->type_name . '评论'
            ),
            'supports' => $this->type_supports,
            'taxonomies' => $this->type_category,
            'hierarchical' => true,
            'public' => true,
            'show_ui' => true,
            'show_in_menu' => true,
            'menu_position' => 6,
            'menu_icon' => 'dashicons-layout',
            'show_in_admin_bar' => true,
            'show_in_nav_menus' => true,
            'can_export' => true,
            'has_archive' => false,
            'exclude_from_search' => true,
            'publicly_queryable' => true
        ));
    }
    public function qx_add_categroy()
    {
        register_taxonomy($this->type_category, $this->type, array(
            'labels' => array(
                'name' => $this->type_name . '分类',
                'add_new_item' => '添加分类',
                'new_item_name' => "新'.$this->type_name.'分类"
            ),
            'show_ui' => true,
            'show_tagcloud' => true,
            'hierarchical' => true
        ));
    }
    public function qx_add_link($link, $post = 0)
    {
        global $post;
        if ($post->post_type == $this->type) {
            return home_url($this->type . '/' . $post->post_name);
        } else {
            return $link;
        }
    }
    public function qx_rewrites_init()
    {
        add_rewrite_rule(
            $this->type . '/([^/]+)/?$',
            'index.php?post_type=' . $this->type . '&p=$matches[1]',
            'top'
        );
        add_rewrite_rule(
            $this->type . '/([0-9]+)?.html/comment-page-([0-9]{1,})$',
            'index.php?post_type=' .
                $this->type .
                '&p=$matches[1]&cpage=$matches[2]',
            'top'
        );
    }
}

/**
 * WordPress 修改时间的显示格式为几Daybefore
 */
function qx_ago($time = '', $type = '', $pid = '')
{
    if (strlen($time) == '') {
        if ($pid) {
            $post_id = $pid;
        } else {
            global $post;
            $post_id = $post->ID;
        }
        if ($type == 'book') {
            $from = strtotime(get_field('last_time', $post_id));
        } else {
            $from = get_the_time('U', $post_id);
        }
    } else {
        $from = strtotime($time);
    }
    $to = time();
    $diff = (int) abs($to - $from);
    if ($diff <= 3600) {
        $mins = round($diff / 60);
        if ($mins <= 1) {
            $mins = 1;
        }
        $time = sprintf(_n('%s Minute', '%s Minute', $mins), $mins) . ' ago';
    } elseif ($diff <= 86400 && $diff > 3600) {
        $hours = round($diff / 3600);
        if ($hours <= 1) {
            $hours = 1;
        }
        $time = sprintf(_n('%s Hour', '%s Hour', $hours), $hours) . ' ago';
    } elseif ($diff >= 86400) {
        $days = round($diff / 86400);
        if ($days <= 1) {
            $days = 1;
            $time = sprintf(_n('%s Day', '%s Day', $days), $days) . ' ago';
        } elseif ($days > 29) {
            $time = get_the_time('m/d/Y h:m A');
        } else {
            $time = sprintf(_n('%s Day', '%s Day', $days), $days) . ' ago';
        }
    }
    return $time;
}

function x_term_count($id, $tax='category')
{
    
    // 获取当前分类信息
    $cat = get_term($id, $tax);
    if (count($cat->errors)>0) {
        return -1;
    }
    // 当前分类文章数
    $count = (int) $cat->count;
    // 获取当前分类所有子孙分类
    $tax_terms = get_terms($tax, array('child_of' => $id));

    foreach ($tax_terms as $tax_term) {
        // 子孙分类文章数累加
        $count +=$tax_term->count;
    }
    return $count;
}

//阅读排行
function top_views($num = 5, $cat=0, $post_type='post')
{
    if ($cat) {
        $lists = Timber::get_posts(array(
            'posts_per_page' => $num,
            'post_type' => $post_type,
            'cat' => $cat,
            'meta_key' => 'views',
            'orderby' => 'meta_value',
            'order' => 'DESC',
        ));
    } else {
        $lists = Timber::get_posts(array(
            'posts_per_page' => $num,
            'post_type' => $post_type,
            'meta_key' => 'views',
            'orderby' => 'meta_value',
            'order' => 'DESC',
        ));
    }
    return $lists;
}

//统计
function cat_views_count($cat='', $post_type='post')
{
    $views = 0;
    $arg = array(
        'post_type' => $post_type,
        'posts_per_page' => -1,
    );
    if ($cat) {
        $arg['cat'] = $cat;
    }
    $posts = new WP_Query($arg);
    if ($posts->have_posts()) {
        foreach ($posts->get_posts() as $k => $v) {
            $views= $views + (int)get_field("views", $v->ID);
        }
    }
    return $views;
}

function x_pagination($range = 5, $max_page='')
{
    $html ='';
    global $paged;
    if ($max_page >1) {
        $html .= '<ul class="uk-pagination uk-flex-center" uk-margin>';
        if (!$paged) {
            $paged = 1;
        }
        if ($paged != 1) {
            $html .= "<li class='btn prev-btn'>".get_previous_posts_link('<span uk-pagination-previous><</span>')."</li>";
        }
        if ($max_page >$range) {
            if ($paged <$range) {
                for ($i = 1; $i <= ($range +1); $i++) {
                    $html .= "<li><a href='".get_pagenum_link($i) ."'";
                    if ($i==$paged) {
                        $html .= " class='uk-active'";
                    }
                    $html .= ">$i</a></li>";
                }
            } elseif ($paged >= ($max_page -ceil(($range/2)))) {
                for ($i = $max_page -$range;$i <= $max_page;$i++) {
                    $html .= "<li><a href='".get_pagenum_link($i) ."'";
                    if ($i==$paged) {
                        $html .= " class='uk-active'";
                    }
                    $html .= ">$i</a></li>";
                }
            } elseif ($paged >= $range && $paged <($max_page -ceil(($range/2)))) {
                for ($i = ($paged -ceil($range/2));$i <= ($paged +ceil(($range/2)));$i++) {
                    $html .= "<li><a href='".get_pagenum_link($i) ."'";
                    if ($i==$paged) {
                        $html .= " class='uk-active'";
                    }
                    $html .= ">$i</a></li>";
                }
            }
        } else {
            for ($i = 1;$i <= $max_page;$i++) {
                $html .= "<li><a href='".get_pagenum_link($i) ."'";
                if ($i==$paged) {
                    $html .= " class='uk-active'";
                }
                $html .= ">$i</a></li>";
            }
        }
        if ($paged != $max_page) {
            $html .= "<li class='btn next-btn'>".get_next_posts_link(__('<span uk-pagination-next>></span>', 'xstart'))."</li>";
            // $html .= "<li><a href='".get_pagenum_link($max_page) ."' class='extend'>>></a></li>";
        }
        // $html .= '<li><span>共['.$max_page.']页</span></li>';
        $html .= "</ul>\n";
    }
    return $html;
}
