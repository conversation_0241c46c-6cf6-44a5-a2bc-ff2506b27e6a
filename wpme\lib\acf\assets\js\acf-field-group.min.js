!function(o,e){var t=new acf.Model({id:"fieldGroupManager",events:{"submit #post":"onSubmit",'click a[href="#"]':"onClick","click .submitdelete":"onClickTrash"},filters:{find_fields_args:"filterFindFieldArgs"},onSubmit:function(e,t){var i=o("#titlewrap #title");i.val()||(e.preventDefault(),acf.unlockForm(t),alert(acf.__("Field group title is required")),i.focus())},onClick:function(e){e.preventDefault()},onClickTrash:function(e){var t;confirm(acf.__("Move to trash. Are you sure?"))||e.preventDefault()},filterFindFieldArgs:function(e){return e.visible=!0,e}}),i=new acf.Model({id:"screenOptionsManager",wait:"prepare",events:{change:"onChange"},initialize:function(){var e=o("#adv-settings"),t=o("#acf-append-show-on-screen");e.find(".metabox-prefs").append(t.html()),e.find(".metabox-prefs br").remove(),t.remove(),this.$el=o("#acf-field-key-hide"),this.render()},isChecked:function(){return this.$el.prop("checked")},onChange:function(e,t){var i=this.isChecked()?1:0;acf.updateUserSetting("show_field_keys",i),this.render()},render:function(){this.isChecked()?o("#acf-field-group-fields").addClass("show-field-keys"):o("#acf-field-group-fields").removeClass("show-field-keys")}}),n=new acf.Model({actions:{new_field:"onNewField"},onNewField:function(e){if(e.has("append")){var t=e.get("append"),i=e.$el.siblings('[data-name="'+t+'"]').first();if(i.length){var n=i.children(".acf-input"),a=n.children("ul");a.length||(n.wrapInner('<ul class="acf-hl"><li></li></ul>'),a=n.children("ul"));var c=e.$(".acf-input").html(),l=o("<li>"+c+"</li>");a.append(l),a.attr("data-cols",a.children().length),e.remove()}}}})}(jQuery),function(s,i){acf.FieldObject=acf.Model.extend({eventScope:".acf-field-object",events:{"click .edit-field":"onClickEdit","click .delete-field":"onClickDelete","click .duplicate-field":"duplicate","click .move-field":"move","change .field-type":"onChangeType","change .field-required":"onChangeRequired","blur .field-label":"onChangeLabel","blur .field-name":"onChangeName",change:"onChange",changed:"onChanged"},data:{id:0,key:"",type:""},setup:function(e){this.$el=e,this.inherit(e),this.prop("ID"),this.prop("parent"),this.prop("menu_order")},$input:function(e){return s("#"+this.getInputId()+"-"+e)},$meta:function(){return this.$(".meta:first")},$handle:function(){return this.$(".handle:first")},$settings:function(){return this.$(".settings:first")},$setting:function(e){return this.$(".acf-field-settings:first > .acf-field-setting-"+e)},getParent:function(){return acf.getFieldObjects({child:this.$el,limit:1}).pop()},getParents:function(){return acf.getFieldObjects({child:this.$el})},getFields:function(){return acf.getFieldObjects({parent:this.$el})},getInputName:function(){return"acf_fields["+this.get("id")+"]"},getInputId:function(){return"acf_fields-"+this.get("id")},newInput:function(e,t){var i=this.getInputId(),n=this.getInputName();e&&(i+="-"+e,n+="["+e+"]");var a=s("<input />").attr({id:i,name:n,value:t});return this.$("> .meta").append(a),a},getProp:function(e){if(this.has(e))return this.get(e);var t=this.$input(e),i=t.length?t.val():null;return this.set(e,i,!0),i},setProp:function(e,t){var i=this.$input(e),n=i.val();return i.length||(i=this.newInput(e,t)),null===t?i.remove():i.val(t),this.has(e)?this.set(e,t):this.set(e,t,!0),this},prop:function(e,t){return t!==i?this.setProp(e,t):this.getProp(e)},props:function(t){Object.keys(t).map(function(e){this.setProp(e,t[e])},this)},getLabel:function(){var e=this.prop("label");return""===e&&(e=acf.__("(no label)")),e},getName:function(){return this.prop("name")},getType:function(){return this.prop("type")},getTypeLabel:function(){var e=this.prop("type"),t=acf.get("fieldTypes");return t[e]?t[e].label:e},getKey:function(){return this.prop("key")},initialize:function(){},render:function(){var e=this.$(".handle:first"),t=this.prop("menu_order"),i=this.getLabel(),n=this.prop("name"),a=this.getTypeLabel(),c=this.prop("key"),l=this.$input("required").prop("checked");e.find(".acf-icon").html(parseInt(t)+1),l&&(i+=' <span class="acf-required">*</span>'),e.find(".li-field-label strong a").html(i),e.find(".li-field-name").text(n),e.find(".li-field-type").text(a),e.find(".li-field-key").text(c),acf.doAction("render_field_object",this)},refresh:function(){acf.doAction("refresh_field_object",this)},isOpen:function(){return this.$el.hasClass("open")},onClickEdit:function(e){this.isOpen()?this.close():this.open()},open:function(){var e=this.$el.children(".settings");e.slideDown(),this.$el.addClass("open"),acf.doAction("open_field_object",this),this.trigger("openFieldObject"),acf.doAction("show",e)},close:function(){var e=this.$el.children(".settings");e.slideUp(),this.$el.removeClass("open"),acf.doAction("close_field_object",this),this.trigger("closeFieldObject"),acf.doAction("hide",e)},serialize:function(){return acf.serialize(this.$el,this.getInputName())},save:function(e){var t;e=e||"settings","settings"!==this.getProp("save")&&(this.setProp("save",e),this.$el.attr("data-save",e),acf.doAction("save_field_object",this,e))},submit:function(){var e=this.getInputName(),t=this.get("save");this.isOpen()&&this.close(),"settings"==t||("meta"==t?this.$('> .settings [name^="'+e+'"]').remove():this.$('[name^="'+e+'"]').remove()),acf.doAction("submit_field_object",this)},onChange:function(e,t){this.save(),acf.doAction("change_field_object",this)},onChanged:function(e,t,i,n){"save"!=i&&(-1<["menu_order","parent"].indexOf(i)?this.save("meta"):this.save(),-1<["menu_order","label","required","name","type","key"].indexOf(i)&&this.render(),acf.doAction("change_field_object_"+i,this,n))},onChangeLabel:function(e,t){var i=t.val();if(this.set("label",i),""==this.prop("name")){var n=acf.applyFilters("generate_field_object_name",acf.strSanitize(i),this);this.prop("name",n)}},onChangeName:function(e,t){var i=t.val();this.set("name",i),"field_"===i.substr(0,6)&&alert(acf.__('The string "field_" may not be used at the start of a field name'))},onChangeRequired:function(e,t){var i=t.prop("checked")?1:0;this.set("required",i)},delete:function(e){e=acf.parseArgs(e,{animate:!0});var t=this.prop("ID");if(t){var i=s("#_acf_delete_fields"),n=i.val()+"|"+t;i.val(n)}acf.doAction("delete_field_object",this),e.animate?this.removeAnimate():this.remove()},onClickDelete:function(e,t){this.$el.addClass("-hover");var i=this,n=acf.newTooltip({confirmRemove:!0,target:t,confirm:function(){i.delete(!0)},cancel:function(){i.$el.removeClass("-hover")}})},removeAnimate:function(){var e=this,t=this.$el.parent(),i=acf.findFieldObjects({sibling:this.$el});acf.remove({target:this.$el,endHeight:i.length?0:50,complete:function(){e.remove(),acf.doAction("removed_field_object",e,t)}}),acf.doAction("remove_field_object",e,t)},duplicate:function(){var e=acf.uniqid("field_"),t=acf.duplicate({target:this.$el,search:this.get("id"),replace:e});t.attr("data-key",e);var i=acf.getFieldObject(t);this.isOpen()?this.close():i.open();var n=i.$setting("label input");setTimeout(function(){n.focus()},251);var a=i.prop("label"),c=i.prop("name"),l=c.split("_").pop(),o=acf.__("copy");if(s.isNumeric(l)){var r=1*l+1;a=a.replace(l,r),c=c.replace(l,r)}else if(0===l.indexOf(o)){var r;r=(r=1*l.replace(o,""))?r+1:2,a=a.replace(l,o+r),c=c.replace(l,o+r)}else a+=" ("+o+")",c+="_"+o;i.prop("ID",0),i.prop("label",a),i.prop("name",c),i.prop("key",e),acf.doAction("duplicate_field_object",this,i),acf.doAction("append_field_object",i)},wipe:function(){var e=this.get("id"),t=this.get("key"),i=acf.uniqid("field_");acf.rename({target:this.$el,search:e,replace:i}),this.set("id",i),this.set("prevId",e),this.set("prevKey",t),this.prop("key",i),this.prop("ID",0),this.$el.attr("data-key",i),this.$el.attr("data-id",i),acf.doAction("wipe_field_object",this)},move:function(){var t=function(e){return"settings"==e.get("save")},i=t(this);if(i||acf.getFieldObjects({parent:this.$el}).map(function(e){i=t(e)||e.changed}),i)alert(acf.__("This field cannot be moved until its changes have been saved"));else{var n=this.prop("ID"),a=this,c=!1,e=function(){c=acf.newPopup({title:acf.__("Move Custom Field"),loading:!0,width:"300px"});var e={action:"acf/field_group/move_field",field_id:n};s.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(e),type:"post",dataType:"html",success:l})},l=function(e){c.loading(!1),c.content(e),c.on("submit","form",o)},o=function(e,t){e.preventDefault(),acf.startButtonLoading(c.$(".button"));var i={action:"acf/field_group/move_field",field_id:n,field_group_id:c.$("select").val()};s.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(i),type:"post",dataType:"html",success:r})},r=function(e){c.content(e),a.removeAnimate()};e()}},onChangeType:function(e,t){this.changeTimeout&&clearTimeout(this.changeTimeout),this.changeTimeout=this.setTimeout(function(){this.changeType(t.val())},300)},changeType:function(e){var t=this.prop("type"),i=acf.strSlugify("acf-field-object-"+t),n=acf.strSlugify("acf-field-object-"+e);this.$el.removeClass(i).addClass(n),this.$el.attr("data-type",e),this.$el.data("type",e),this.has("xhr")&&this.get("xhr").abort();var a=this.$("> .settings > table > tbody"),c=a.children('[data-setting="'+t+'"]');if(this.set("settings-"+t,c),c.detach(),this.has("settings-"+e)){var l=this.get("settings-"+e);return this.$setting("conditional_logic").before(l),void this.set("type",e)}var o=s('<tr class="acf-field"><td class="acf-label"></td><td class="acf-input"><div class="acf-loading"></div></td></tr>');this.$setting("conditional_logic").before(o);var r={action:"acf/field_group/render_field_settings",field:this.serialize(),prefix:this.getInputName()},d=s.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(r),type:"post",dataType:"html",context:this,success:function(e){e&&(o.after(e),acf.doAction("append",a))},complete:function(){o.remove(),this.set("type",e)}});this.set("xhr",d)},updateParent:function(){var e=acf.get("post_id"),t=this.getParent();t&&(e=parseInt(t.prop("ID"))||t.prop("key")),this.prop("parent",e)}})}(jQuery),function(i,e){var l=function(e){return acf.strPascalCase(e||"")+"FieldSetting"};acf.registerFieldSetting=function(e){var t=e.prototype,i=l(t.type+" "+t.name);this.models[i]=e},acf.newFieldSetting=function(e){var t=e.get("setting")||"",i=e.get("name")||"",n=l(t+" "+i),a=acf.models[n]||null,c;return null!==a&&new a(e)},acf.getFieldSetting=function(e){return e instanceof jQuery&&(e=acf.getField(e)),e.setting};var t=new acf.Model({actions:{new_field:"onNewField"},onNewField:function(e){e.setting=acf.newFieldSetting(e)}});acf.FieldSetting=acf.Model.extend({field:!1,type:"",name:"",wait:"ready",eventScope:".acf-field",events:{change:"render"},setup:function(e){var t=e.$el;this.$el=t,this.field=e,this.$fieldObject=t.closest(".acf-field-object"),this.fieldObject=acf.getFieldObject(this.$fieldObject),i.extend(this.data,e.data)},initialize:function(){this.render()},render:function(){}});var n=acf.FieldSetting.extend({type:"",name:"",render:function(){var e=this.$('input[type="radio"]:checked');"other"!=e.val()&&this.$('input[type="text"]').val(e.val())}}),a=n.extend({type:"date_picker",name:"display_format"}),c=n.extend({type:"date_picker",name:"return_format"});acf.registerFieldSetting(a),acf.registerFieldSetting(c);var o=n.extend({type:"date_time_picker",name:"display_format"}),r=n.extend({type:"date_time_picker",name:"return_format"});acf.registerFieldSetting(o),acf.registerFieldSetting(r);var d=n.extend({type:"time_picker",name:"display_format"}),s=n.extend({name:"time_picker",name:"return_format"});acf.registerFieldSetting(d),acf.registerFieldSetting(s)}(jQuery),function(d,e){var t=acf.FieldSetting.extend({type:"",name:"conditional_logic",events:{"change .conditions-toggle":"onChangeToggle","click .add-conditional-group":"onClickAddGroup","focus .condition-rule-field":"onFocusField","change .condition-rule-field":"onChangeField","change .condition-rule-operator":"onChangeOperator","click .add-conditional-rule":"onClickAdd","click .remove-conditional-rule":"onClickRemove"},$rule:!1,scope:function(e){return this.$rule=e,this},ruleData:function(e,t){return this.$rule.data.apply(this.$rule,arguments)},$input:function(e){return this.$rule.find(".condition-rule-"+e)},$td:function(e){return this.$rule.find("td."+e)},$toggle:function(){return this.$(".conditions-toggle")},$control:function(){return this.$(".rule-groups")},$groups:function(){return this.$(".rule-group")},$rules:function(){return this.$(".rule")},open:function(){var e=this.$control();e.show(),acf.enable(e)},close:function(){var e=this.$control();e.hide(),acf.disable(e)},render:function(){this.$toggle().prop("checked")?(this.renderRules(),this.open()):this.close()},renderRules:function(){var e=this;this.$rules().each(function(){e.renderRule(d(this))})},renderRule:function(e){this.scope(e),this.renderField(),this.renderOperator(),this.renderValue()},renderField:function(){var a=[],e=[],c=this.fieldObject.cid,t=this.$input("field");acf.getFieldObjects().map(function(e){var t={id:e.getKey(),text:e.getLabel()},i;e.cid===c&&(t.text+=acf.__("(this field)"),t.disabled=!0),acf.getConditionTypes({fieldType:e.getType()}).length||(t.disabled=!0);var n=e.getParents().length;t.text="- ".repeat(n)+t.text,a.push(t)}),a.length||a.push({id:"",text:acf.__("No toggle fields available")}),acf.renderSelect(t,a),this.ruleData("field",t.val())},renderOperator:function(){if(this.ruleData("field")){var e=this.$input("operator"),t=e.val(),i=[];null===e.val()&&acf.renderSelect(e,[{id:this.ruleData("operator"),text:""}]);var n=acf.findFieldObject(this.ruleData("field")),a=acf.getFieldObject(n),c;acf.getConditionTypes({fieldType:a.getType()}).map(function(e){i.push({id:e.prototype.operator,text:acf.strEscape(e.prototype.label)})}),acf.renderSelect(e,i),this.ruleData("operator",e.val())}},renderValue:function(){if(this.ruleData("field")&&this.ruleData("operator")){var t=this.$input("value"),e=this.$td("value"),i=t.val(),n=acf.findFieldObject(this.ruleData("field")),a=acf.getFieldObject(n),c,l,o=acf.getConditionTypes({fieldType:a.getType(),operator:this.ruleData("operator")})[0].prototype.choices(a);if(o instanceof Array){var r=d("<select></select>");acf.renderSelect(r,o)}else var r=d(o);t.detach(),e.html(r),setTimeout(function(){["class","name","id"].map(function(e){r.attr(e,t.attr(e))})},0),r.prop("disabled")||acf.val(r,i,!0),this.ruleData("value",r.val())}},onChangeToggle:function(){this.render()},onClickAddGroup:function(e,t){this.addGroup()},addGroup:function(){var e=this.$(".rule-group:last"),t=acf.duplicate(e);t.find("h4").text(acf.__("or")),t.find("tr").not(":first").remove(),this.fieldObject.save()},onFocusField:function(e,t){this.renderField()},onChangeField:function(e,t){this.scope(t.closest(".rule")),this.ruleData("field",t.val()),this.renderOperator(),this.renderValue()},onChangeOperator:function(e,t){this.scope(t.closest(".rule")),this.ruleData("operator",t.val()),this.renderValue()},onClickAdd:function(e,t){var i=acf.duplicate(t.closest(".rule"));this.renderRule(i)},onClickRemove:function(e,t){var i=t.closest(".rule");this.fieldObject.save(),0==i.siblings(".rule").length&&i.closest(".rule-group").remove(),i.remove()}});acf.registerFieldSetting(t);var i=new acf.Model({actions:{duplicate_field_objects:"onDuplicateFieldObjects"},onDuplicateFieldObjects:function(e,t,i){var n={},a=d();e.map(function(e){n[e.get("prevKey")]=e.get("key"),a=a.add(e.$(".condition-rule-field"))}),a.each(function(){var e=d(this),t=e.val();t&&n[t]&&(e.find("option:selected").attr("value",n[t]),e.val(n[t]))})}})}(jQuery),function(r,e){acf.findFieldObject=function(e){return acf.findFieldObjects({key:e,limit:1})},acf.findFieldObjects=function(e){e=e||{};var t=".acf-field-object",i=!1;return(e=acf.parseArgs(e,{id:"",key:"",type:"",limit:!1,list:null,parent:!1,sibling:!1,child:!1})).id&&(t+='[data-id="'+e.id+'"]'),e.key&&(t+='[data-key="'+e.key+'"]'),e.type&&(t+='[data-type="'+e.type+'"]'),i=e.list?e.list.children(t):e.parent?e.parent.find(t):e.sibling?e.sibling.siblings(t):e.child?e.child.parents(t):r(t),e.limit&&(i=i.slice(0,e.limit)),i},acf.getFieldObject=function(e){"string"==typeof e&&(e=acf.findFieldObject(e));var t=e.data("acf");return t||(t=acf.newFieldObject(e)),t},acf.getFieldObjects=function(e){var t=acf.findFieldObjects(e),i=[];return t.each(function(){var e=acf.getFieldObject(r(this));i.push(e)}),i},acf.newFieldObject=function(e){var t=new acf.FieldObject(e);return acf.doAction("new_field_object",t),t};var t=new acf.Model({priority:5,initialize:function(){var e;["prepare","ready","append","remove"].map(function(e){this.addFieldActions(e)},this)},addFieldActions:function(e){var n=e+"_field_objects",a=e+"_field_object",c=e+"FieldObject",t=function(e){var t=acf.getFieldObjects({parent:e});if(t.length){var i=acf.arrayArgs(arguments);i.splice(0,1,n,t),acf.doAction.apply(null,i)}},i=function(e){var t=acf.arrayArgs(arguments);t.unshift(a),e.map(function(e){t[1]=e,acf.doAction.apply(null,t)})},l=function(t){var i=acf.arrayArgs(arguments),e;i.unshift(a),["type","name","key"].map(function(e){i[0]=a+"/"+e+"="+t.get(e),acf.doAction.apply(null,i)}),i.splice(0,2),t.trigger(c,i)};acf.addAction(e,t,5),acf.addAction(n,i,5),acf.addAction(a,l,5)}}),i=new acf.Model({id:"fieldManager",events:{"submit #post":"onSubmit","mouseenter .acf-field-list":"onHoverSortable","click .add-field":"onClickAdd"},actions:{removed_field_object:"onRemovedField",sortstop_field_object:"onReorderField",delete_field_object:"onDeleteField",change_field_object_type:"onChangeFieldType",duplicate_field_object:"onDuplicateField"},onSubmit:function(e,t){var i;acf.getFieldObjects().map(function(e){e.submit()})},setFieldMenuOrder:function(e){this.renderFields(e.$el.parent())},onHoverSortable:function(e,n){n.hasClass("ui-sortable")||n.sortable({handle:".acf-sortable-handle",connectWith:".acf-field-list",start:function(e,t){var i=acf.getFieldObject(t.item);t.placeholder.height(t.item.height()),acf.doAction("sortstart_field_object",i,n)},update:function(e,t){var i=acf.getFieldObject(t.item);acf.doAction("sortstop_field_object",i,n)}})},onRemovedField:function(e,t){this.renderFields(t)},onReorderField:function(e,t){e.updateParent(),this.renderFields(t)},onDeleteField:function(e){e.getFields().map(function(e){e.delete({animate:!1})})},onChangeFieldType:function(e){},onDuplicateField:function(e,t){var i=t.getFields();i.length&&(i.map(function(e){e.wipe(),e.updateParent()}),acf.doAction("duplicate_field_objects",i,t,e)),this.setFieldMenuOrder(t)},renderFields:function(e){var t=acf.getFieldObjects({list:e});t.length?(e.removeClass("-empty"),t.map(function(e,t){e.prop("menu_order",t)})):e.addClass("-empty")},onClickAdd:function(e,t){var i=t.closest(".acf-tfoot").siblings(".acf-field-list");this.addField(i)},addField:function(i){var e=r("#tmpl-acf-field").html(),t=r(e),n=t.data("id"),a=acf.uniqid("field_"),c=acf.duplicate({target:t,search:n,replace:a,append:function(e,t){i.append(t)}}),l=acf.getFieldObject(c);l.prop("key",a),l.prop("ID",0),l.prop("label",""),l.prop("name",""),c.attr("data-key",a),c.attr("data-id",a),l.updateParent();var o=l.$input("label");setTimeout(function(){o.focus()},251),l.open(),this.renderFields(i),acf.doAction("add_field_object",l),acf.doAction("append_field_object",l)}})}(jQuery),function(a,e){var t=new acf.Model({id:"locationManager",wait:"ready",events:{"click .add-location-rule":"onClickAddRule","click .add-location-group":"onClickAddGroup","click .remove-location-rule":"onClickRemoveRule","change .refresh-location-rule":"onChangeRemoveRule"},initialize:function(){this.$el=a("#acf-field-group-locations")},onClickAddRule:function(e,t){this.addRule(t.closest("tr"))},onClickRemoveRule:function(e,t){this.removeRule(t.closest("tr"))},onChangeRemoveRule:function(e,t){this.changeRule(t.closest("tr"))},onClickAddGroup:function(e,t){this.addGroup()},addRule:function(e){acf.duplicate(e)},removeRule:function(e){0==e.siblings("tr").length?e.closest(".rule-group").remove():e.remove()},changeRule:function(t){var e=t.closest(".rule-group"),i=t.find("td.param select").attr("name").replace("[param]",""),n={action:"acf/field_group/render_location_rule"};n.rule=acf.serialize(t,i),n.rule.id=t.data("id"),n.rule.group=e.data("id"),acf.disable(t.find("td.value")),a.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(n),type:"post",dataType:"html",success:function(e){e&&t.replaceWith(e)}})},addGroup:function(){var e=this.$(".rule-group:last");$group2=acf.duplicate(e),$group2.find("h4").text(acf.__("or")),$group2.find("tr").not(":first").remove()}})}(jQuery),function(l,i){var e=acf.getCompatibility(acf);e.field_group={save_field:function(e,t){t=t!==i?t:"settings",acf.getFieldObject(e).save(t)},delete_field:function(e,t){t=t===i||t,acf.getFieldObject(e).delete({animate:t})},update_field_meta:function(e,t,i){acf.getFieldObject(e).prop(t,i)},delete_field_meta:function(e,t){acf.getFieldObject(e).prop(t,null)}},e.field_group.field_object=acf.model.extend({type:"",o:{},$field:null,$settings:null,tag:function(e){var t=this.type,i=e.split("_");return i.splice(1,0,"field"),e=i.join("_"),t&&(e+="/type="+t),e},selector:function(){var e=".acf-field-object",t=this.type;return t&&(e+="-"+t,e=acf.str_replace("_","-",e)),e},_add_action:function(e,t){var i=this;acf.add_action(this.tag(e),function(e){i.set("$field",e),i[t].apply(i,arguments)})},_add_filter:function(e,t){var i=this;acf.add_filter(this.tag(e),function(e){i.set("$field",e),i[t].apply(i,arguments)})},_add_event:function(e,t){var i=this,n=e.substr(0,e.indexOf(" ")),a=e.substr(e.indexOf(" ")+1),c=this.selector();l(document).on(n,c+" "+a,function(e){e.$el=l(this),e.$field=e.$el.closest(".acf-field-object"),i.set("$field",e.$field),i[t].apply(i,[e])})},_set_$field:function(){this.o=this.$field.data(),this.$settings=this.$field.find("> .settings > table > tbody"),this.focus()},focus:function(){},setting:function(e){return this.$settings.find("> .acf-field-setting-"+e)}});var t=new acf.Model({actions:{open_field_object:"onOpenFieldObject",close_field_object:"onCloseFieldObject",add_field_object:"onAddFieldObject",duplicate_field_object:"onDuplicateFieldObject",delete_field_object:"onDeleteFieldObject",change_field_object_type:"onChangeFieldObjectType",change_field_object_label:"onChangeFieldObjectLabel",change_field_object_name:"onChangeFieldObjectName",change_field_object_parent:"onChangeFieldObjectParent",sortstop_field_object:"onChangeFieldObjectParent"},onOpenFieldObject:function(e){acf.doAction("open_field",e.$el),acf.doAction("open_field/type="+e.get("type"),e.$el),acf.doAction("render_field_settings",e.$el),acf.doAction("render_field_settings/type="+e.get("type"),e.$el)},onCloseFieldObject:function(e){acf.doAction("close_field",e.$el),acf.doAction("close_field/type="+e.get("type"),e.$el)},onAddFieldObject:function(e){acf.doAction("add_field",e.$el),acf.doAction("add_field/type="+e.get("type"),e.$el)},onDuplicateFieldObject:function(e){acf.doAction("duplicate_field",e.$el),acf.doAction("duplicate_field/type="+e.get("type"),e.$el)},onDeleteFieldObject:function(e){acf.doAction("delete_field",e.$el),acf.doAction("delete_field/type="+e.get("type"),e.$el)},onChangeFieldObjectType:function(e){acf.doAction("change_field_type",e.$el),acf.doAction("change_field_type/type="+e.get("type"),e.$el),acf.doAction("render_field_settings",e.$el),acf.doAction("render_field_settings/type="+e.get("type"),e.$el)},onChangeFieldObjectLabel:function(e){acf.doAction("change_field_label",e.$el),acf.doAction("change_field_label/type="+e.get("type"),e.$el)},onChangeFieldObjectName:function(e){acf.doAction("change_field_name",e.$el),acf.doAction("change_field_name/type="+e.get("type"),e.$el)},onChangeFieldObjectParent:function(e){acf.doAction("update_field_parent",e.$el)}})}(jQuery);