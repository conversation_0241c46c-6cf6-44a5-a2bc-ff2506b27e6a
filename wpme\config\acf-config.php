<?php

declare(strict_types=1);
// 通过 ACF 添加独立配置页
if (\function_exists('acf_add_options_page')) {
    acf_add_options_page([
        'page_title' => '网站配置',
        'menu_title' => '网站配置',
        'menu_slug'  => 'web-settings',
        'capability' => 'edit_posts',
        'redirect'   => false,
    ]);

    $current_user       = wp_get_current_user();
    $current_user_email = $current_user->user_email;
    if (\in_array($current_user_email, $order_setting_emails)) {
        acf_add_options_page([
            'page_title' => '支付设置',
            'menu_title' => '支付设置',
            'menu_slug'  => 'pay-settings',
            'capability' => 'edit_posts',
            'redirect'   => false,
        ]);
    }
}
