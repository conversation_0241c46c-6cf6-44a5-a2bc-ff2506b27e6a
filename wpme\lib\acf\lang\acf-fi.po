msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2017-09-22 14:17+0300\n"
"PO-Revision-Date: 2018-02-06 10:05+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: fi\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.1\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:67
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:369 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Kenttäryhmät"

#: acf.php:370
msgid "Field Group"
msgstr "Kenttäryhmä"

#: acf.php:371 acf.php:403 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New"
msgstr "Lisää uusi"

#: acf.php:372
msgid "Add New Field Group"
msgstr "Lisää uusi kenttäryhmä"

#: acf.php:373
msgid "Edit Field Group"
msgstr "Muokkaa kenttäryhmää"

#: acf.php:374
msgid "New Field Group"
msgstr "Lisää uusi kenttäryhmä"

#: acf.php:375
msgid "View Field Group"
msgstr "Katso kenttäryhmää"

#: acf.php:376
msgid "Search Field Groups"
msgstr "Etsi kenttäryhmiä"

#: acf.php:377
msgid "No Field Groups found"
msgstr "Kenttäryhmiä ei löytynyt"

#: acf.php:378
msgid "No Field Groups found in Trash"
msgstr "Kenttäryhmiä ei löytynyt roskakorista"

#: acf.php:401 includes/admin/admin-field-group.php:182
#: includes/admin/admin-field-group.php:275
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:807
msgid "Fields"
msgstr "Kentät"

#: acf.php:402
msgid "Field"
msgstr "Kenttä"

#: acf.php:404
msgid "Add New Field"
msgstr "Lisää uusi kenttä"

#: acf.php:405
msgid "Edit Field"
msgstr "Muokkaa kenttää"

#: acf.php:406 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Uusi kenttä"

#: acf.php:407
msgid "View Field"
msgstr "Näytä kenttä"

#: acf.php:408
msgid "Search Fields"
msgstr "Etsi kenttiä"

#: acf.php:409
msgid "No Fields found"
msgstr "Kenttiä ei löytynyt"

#: acf.php:410
msgid "No Fields found in Trash"
msgstr "Kenttiä ei löytynyt roskakorista"

#: acf.php:449 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Ei-aktiivinen"

#: acf.php:454
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Ei-aktiivinen <span class=\"count\">(%s)</span>"
msgstr[1] "Ei-aktiivisia <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Kenttäryhmä päivitetty"

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Kenttäryhmä poistettu"

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Kenttäryhmä julkaistu"

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Kenttäryhmä tallennettu"

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Kenttäryhmä lähetetty."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Kenttäryhmä ajoitettu."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Luonnos kenttäryhmästä päivitetty"

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "Sijainti"

#: includes/admin/admin-field-group.php:184
msgid "Settings"
msgstr "Asetukset"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "Haluatko varmasti siirtää roskakoriin?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr "valittu"

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr "Ei toggle kenttiä saatavilla"

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "Kenttäryhmän otsikko on pakollinen"

#: includes/admin/admin-field-group.php:273
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "kopioi"

#: includes/admin/admin-field-group.php:274
#: includes/admin/views/field-group-field-conditional-logic.php:54
#: includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3964
msgid "or"
msgstr "tai"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "Yläkentät"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr "Serkkukentät"

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "Siirrä muokattua kenttää"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr "Tätä kenttää ei voi siirtää ennen kuin muutokset on talletettu"

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr "Tyhjä"

#: includes/admin/admin-field-group.php:281 includes/input.php:258
msgid "The changes you made will be lost if you navigate away from this page"
msgstr "Tekemäsi muutokset menetetään, jos siirryt pois tältä sivulta"

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Merkkijonoa \"field_\" ei saa käyttää kenttänimen alussa"

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr "Kenttäavaimet"

#: includes/admin/admin-field-group.php:390
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Käytössä"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "Siirto valmis."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Kenttä %s löytyy nyt %s-kenttäryhmästä"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "Sulje ikkuna"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "Valitse kohde kenttälle"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "Siirrä kenttä"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Käytössä <span class=\"count\">(%s)</span>"
msgstr[1] "Käytössä <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Kenttäryhmä monistettu. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s kenttäryhmä monistettu."
msgstr[1] "%s kenttäryhmät monistettu."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Kenttäryhmä synkronoitu. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s kenttäryhmä synkronoitu."
msgstr[1] "%s kenttäryhmät synkronoitu."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Synkronointi saatavissa"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:355
msgid "Title"
msgstr "Otsikko"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:382
msgid "Description"
msgstr "Kuvaus"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Status"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Muokkaa WordPressiä tehokkailla, ammattimaisilla ja intuitiivisilla kentillä."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Muutosloki"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Katso mitä uutta <a href=”%s”>versiossa %s</a> ."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Resurssit"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Kotisivu"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "﻿Dokumentaatio"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "﻿Tuki"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Kiitos, että luot sisältöä <a href=\"%s\">ACF:llä</a>."

#: includes/admin/admin-field-groups.php:668
msgid "Duplicate this item"
msgstr "Monista tämä kohde"

#: includes/admin/admin-field-groups.php:668
#: includes/admin/admin-field-groups.php:684
#: includes/admin/views/field-group-field.php:49
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate"
msgstr "Monista"

#: includes/admin/admin-field-groups.php:701
#: includes/fields/class-acf-field-google-map.php:112
#: includes/fields/class-acf-field-relationship.php:656
msgid "Search"
msgstr "Etsi"

#: includes/admin/admin-field-groups.php:760
#, php-format
msgid "Select %s"
msgstr "Valitse %s"

#: includes/admin/admin-field-groups.php:768
msgid "Synchronise field group"
msgstr "Synkronoi kenttäryhmä"

#: includes/admin/admin-field-groups.php:768
#: includes/admin/admin-field-groups.php:798
msgid "Sync"
msgstr "Synkronointi"

#: includes/admin/admin-field-groups.php:780
msgid "Apply"
msgstr "﻿Käytä"

#: includes/admin/admin-field-groups.php:798
msgid "Bulk Actions"
msgstr "Massatoiminnot"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Lisäkentät"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Päivitä tietokanta"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "Katso sivuja & päivitä"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "Virhe pyynnön käsittelyssä"

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "Päivityksiä ei ole saatavilla."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Lisäosat"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Virhe</b>. Lisäosa luetteloa ei voitu ladata"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Info"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Katso mitä uutta"

#: includes/admin/settings-tools.php:50
#: includes/admin/views/settings-tools-export.php:19
#: includes/admin/views/settings-tools.php:31
msgid "Tools"
msgstr "Työkalut"

#: includes/admin/settings-tools.php:147 includes/admin/settings-tools.php:380
msgid "No field groups selected"
msgstr "Ei kenttäryhmää valittu"

#: includes/admin/settings-tools.php:184
#: includes/fields/class-acf-field-file.php:155
msgid "No file selected"
msgstr "Ei tiedostoa valittu"

#: includes/admin/settings-tools.php:197
msgid "Error uploading file. Please try again"
msgstr "Virhe ladattaessa tiedostoa. Ole hyvä ja yritä uudelleen."

#: includes/admin/settings-tools.php:206
msgid "Incorrect file type"
msgstr "Virheellinen tiedostomuoto"

#: includes/admin/settings-tools.php:223
msgid "Import file empty"
msgstr "Tuotu tiedosto on tyhjä"

#: includes/admin/settings-tools.php:331
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Tuotu 1 kenttäryhmä"
msgstr[1] "Tuotu %s kenttäryhmää"

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "Ehdollinen logiikka"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "Näytä tämä kenttä, jos"

#: includes/admin/views/field-group-field-conditional-logic.php:103
#: includes/locations.php:247
msgid "is equal to"
msgstr "on sama kuin"

#: includes/admin/views/field-group-field-conditional-logic.php:104
#: includes/locations.php:248
msgid "is not equal to"
msgstr "ei ole sama kuin"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "ja"

#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Lisää sääntöryhmä"

#: includes/admin/views/field-group-field.php:41
#: pro/fields/class-acf-field-flexible-content.php:403
#: pro/fields/class-acf-field-repeater.php:296
msgid "Drag to reorder"
msgstr "Muuta järjestystä vetämällä ja pudottamalla"

#: includes/admin/views/field-group-field.php:45
#: includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "Muokkaa kenttää"

#: includes/admin/views/field-group-field.php:48
#: includes/fields/class-acf-field-file.php:137
#: includes/fields/class-acf-field-image.php:122
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:342
msgid "Edit"
msgstr "Muokkaa"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "Monista kenttä"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "Siirrä kenttä toiseen ryhmään"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "Siirrä"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "Poista kenttä"

#: includes/admin/views/field-group-field.php:51
#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete"
msgstr "Poista"

#: includes/admin/views/field-group-field.php:67
msgid "Field Label"
msgstr "Kentän nimiö"

#: includes/admin/views/field-group-field.php:68
msgid "This is the name which will appear on the EDIT page"
msgstr "Tätä nimeä käytetään Muokkaa-sivulla"

#: includes/admin/views/field-group-field.php:77
msgid "Field Name"
msgstr "Kentän nimi"

#: includes/admin/views/field-group-field.php:78
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Yksi sana, ei välilyöntejä. Alaviivat ja viivamerkit ovat sallittuja."

#: includes/admin/views/field-group-field.php:87
msgid "Field Type"
msgstr "Kenttätyyppi"

#: includes/admin/views/field-group-field.php:98
#: includes/fields/class-acf-field-tab.php:88
msgid "Instructions"
msgstr "Ohjeet"

#: includes/admin/views/field-group-field.php:99
msgid "Instructions for authors. Shown when submitting data"
msgstr "Ohjeet kirjoittajille. Näytetään muokkausnäkymässä"

#: includes/admin/views/field-group-field.php:108
msgid "Required?"
msgstr "Pakollinen?"

#: includes/admin/views/field-group-field.php:131
msgid "Wrapper Attributes"
msgstr "Kääreen attribuutit"

#: includes/admin/views/field-group-field.php:137
msgid "width"
msgstr "leveys"

#: includes/admin/views/field-group-field.php:152
msgid "class"
msgstr "luokka"

#: includes/admin/views/field-group-field.php:165
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:177
msgid "Close Field"
msgstr "Sulje kenttä"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Järjestys"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:415
#: includes/fields/class-acf-field-radio.php:306
#: includes/fields/class-acf-field-select.php:432
#: pro/fields/class-acf-field-flexible-content.php:582
msgid "Label"
msgstr "Nimiö"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:964
#: pro/fields/class-acf-field-flexible-content.php:595
msgid "Name"
msgstr "Nimi"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Avain"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tyyppi"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Ei kenttiä. Klikkaa <strong>+ Lisää kenttä</strong> painiketta luodaksesi "
"ensimmäisen kenttäsi."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Lisää kenttä"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Säännöt"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Tästä voit määrittää, missä muokkausnäkymässä tämä kenttäryhmä näytetään"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Tyyli"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standardi (WP metalaatikko)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Saumaton (ei metalaatikkoa)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Sijainti"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Korkea (otsikon jälkeen)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normaali (sisällön jälkeen)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Reuna"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Nimiön sijainti"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:102
msgid "Top aligned"
msgstr "Tasaa ylös"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:103
msgid "Left aligned"
msgstr "Tasaa vasemmalle"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Ohjeen sijainti"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Tasaa nimiön alapuolelle"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Tasaa kentän alapuolelle"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Järjestysnumero"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr ""
"Kenttäryhmät, joilla on pienempi järjestysnumero, tulostetaan ensimmäisenä."

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Näytetään kenttäryhmien listauksessa"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Piilota näytöltä"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Valitse</b> kohteita <b>piilottaaksesi</b> ne muokkausnäkymästä."

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Jos muokkausnäkymässä on useita kenttäryhmiä, ensimmäisen (pienin "
"järjestysnumero) kenttäryhmän piilotusasetuksia käytetään"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Kestolinkki"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Sisältöeditori"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Katkelma"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Keskustelu"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Kommentit"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Tarkastettu"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "Polkutunnus (slug)"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Kirjoittaja"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Muoto"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Sivun attribuutit"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:670
msgid "Featured Image"
msgstr "Artikkelikuva"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Kategoriat"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Avainsanat"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Lähetä paluuviitteet"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Näytä tämä kenttäryhmä, jos"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "Päivitä sivustot"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr "Advanced Custom Fields tietokantapäivitys"

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Seuraavat sivut vaativat tietokantapäivityksen. Valitse ne, jotka haluat "
"päivittää ja klikkaa %s"

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Sivu"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Sivusto edellyttää tietokannan päivityksen (%s -> %s)"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "Sivusto on ajan tasalla"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Tietokanta on päivitetty. <a href=\"%s\">Palaa verkon hallinnan "
"ohjausnäkymään</a>"

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Tietokannan varmuuskopio on erittäin suositeltavaa ennen kuin jatkat. Oletko "
"varma, että halua jatkaa päivitystä nyt?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Päivitys valmis"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Päivitetään data versioon %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Toista rivejä"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Joustava sisältö"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galleria"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Asetukset-sivu"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Tietokanta on päivitettävä"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Kiitos, että päivitit %s v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Ennen kuin alat käyttämään uusia mahtavia ominaisuuksia, ole hyvä ja päivitä "
"tietokantasi uuteen versioon."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""
"Varmista myös, että kaikki premium lisäosat (%s) on ensin päivitetty "
"uusimpaan versioon."

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Luetaan päivitys tehtäviä..."

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr "Tietokannan päivitys on valmis. <a href=\"%s\">Katso mitä on uutta</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Lataa ja asenna"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Asennettu"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Tervetuloa Advanced Custom Fields -lisäosaan!"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Kiitos, että päivitit! ACF %s on suurempi ja parempi kuin koskaan ennen. "
"Toivomme, että pidät siitä."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "Sujuvampi kenttien muokkaus kokemus"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Käytettävyyttä parannettu"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Mukaan otettu Select2-kirjasto on parantanut sekä käytettävyyttä että "
"nopeutta erilaisissa kenttätyypeissä kuten artikkelioliossa, sivun linkissä, "
"taksonomiassa ja valinnassa."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Parantunut muotoilu"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Monet kentät ovat käyneet läpi visuaalisen uudistuksen ja ACF näyttää "
"paremmalta kuin koskaan ennen! Huomattavat muutokset ovat nähtävissä "
"Galleria, Suodata artikkeleita ja oEmbed (uusi) kentissä!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Parannetut data"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Data arkkitehtuurin uudelleen suunnittelu mahdollisti alakenttien "
"riippumattomuuden vanhemmistaan. Tämän muutoksen myötä voit vetää ja "
"tiputtaa kenttiä riippumatta kenttähierarkiasta"

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Hyvästi lisäosat. Tervetuloa PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "Esittelyssä ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""
"Muutamme erinomaisesti tapaa, jolla korkealuokkaiset toiminnallisuudet "
"toimitetaan!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Kaikki 4 premium lisäosaa on yhdistetty uuteen <a href=\"%s\">ACF:n PRO "
"versioon</a>. Lisensseistä saatavilla on sekä henkilökohtaisia että "
"kehittäjien lisenssejä, joten korkealuokkaiset toiminnallisuudet ovat nyt "
"edullisimpia ja saavutettavampia kuin koskaan ennen!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Tehokkaat ominaisuudet"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO sisältää tehokkaita ominaisuuksia, kuten toistuva data, joustavat "
"sisältö layoutit, kaunis galleriakenttä sekä mahdollisuus luoda ylimääräisiä "
"ylläpitäjän asetussivuja!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Lue lisää <a href=\"%s\">ACF PRO:n ominaisuuksista</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Helppo päivitys"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"Tehdäksesi päivityksen helpoksi, <a href=\"%s\">Kirjaudu kauppaan</a> ja "
"lataa ilmainen kopio ACF PRO:sta!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"Kirjoitimme myös <a href=\"%s\">päivitysoppaan</a> vastataksemme "
"kysymyksiin. Jos jokin asia vielä vaivaa mieltäsi, ota yhteyttä "
"asiakaspalveluumme <a href=\"%s\">neuvontapalvelun</a> kautta."

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Konepellin alla"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "Älykkäämmät kenttäasetukset"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "ACF tallentaa nyt kenttäasetukset yksittäisenä artikkelioliona"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Enemmän AJAXia"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr ""
"Useammat kentät käyttävät AJAX käyttöistä hakua ja näin sivujen lataus "
"nopeutuu"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "Paikallinen JSON"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr "Uusi automaattinen JSON-vienti parantaa nopeutta"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "Parempi versionhallinta"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr "Uusi automaattinen JSON-vienti sallii kenttäasetuksia versionhallinnan"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "XML vaihdettu JSON:iin"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "Tuonti / Vienti käyttää nyt JSONia"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Uudet lomakkeet"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"Kentät voidaan nyt linkittää kommentteihin, vimpaimiin ja kaikkiin käyttäjä "
"lomakkeisiin!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "Lisättiin uusi kenttä sisällön upottamiseen"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Uusi galleria"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "Galleriakenttä on käynyt läpi suuresti tarvitun kasvojenkohotuksen"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Uudet asetukset"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"Kenttäryhmien asetuksiin lisättiin määritykset nimiön ja ohjeiden sijainnille"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "Paremmat Front Endin lomakkeet"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form() voi nyt luoda uuden artikkelin pyydettäessä"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Parempi validointi"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr ""
"Lomakkeen validointi tehdään nyt PHP + AJAX sen sijaan, että käytettäisiin "
"pelkästään JS:ää"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Suodata artikkeleita -kenttä"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Uudet Suodata artikkeleita -kentän asetukset 'Suodattamille' (Etsi, "
"Artikkelityyppi, Taksonomia)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Kenttien siirtäminen"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""
"Uusi kenttäryhmien toiminnallisuus sallii sinun siirtää kenttiä ryhmien & "
"vanhempien välillä"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Sivun URL"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Uusi arkistoryhmä page_link -kentän valintana"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Paremmat asetukset sivut"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"Uusi toiminnallisuus asetukset-sivulle, joka sallii sekä vanhempi että lapsi "
"menu-sivujen luomisen"

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Uskomme, että tulet rakastamaan muutoksia %s:ssa"

#: includes/admin/views/settings-tools-export.php:23
msgid "Export Field Groups to PHP"
msgstr "Vie kenttäryhmä PHP:llä"

#: includes/admin/views/settings-tools-export.php:27
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Tällä koodilla voit rekisteröidä valitut kenttäryhmät paikallisesti. "
"Paikallinen kenttäryhmä tarjoaa monia etuja, kuten nopeammat latausajat, "
"versionhallinnan & dynaamiset kentät/asetukset. Kopioi ja liitä koodi "
"teemasi functions.php tiedostoon tai sisällytä se ulkoisen tiedoston avulla."

#: includes/admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "Valitse kenttäryhmät"

#: includes/admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "Vie kenttäryhmiä"

#: includes/admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Valitse kenttäryhmät, jotka haluat viedä ja valitse sitten vientimetodisi. "
"Käytä Lataa-painiketta viedäksesi .json-tiedoston, jonka voit sitten tuoda "
"toisessa ACF asennuksessa. Käytä Generoi-painiketta luodaksesi PHP koodia, "
"jonka voit sijoittaa teemaasi."

#: includes/admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "Lataa vientitiedosto"

#: includes/admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "Generoi vientikoodi"

#: includes/admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "Tuo kenttäryhmiä"

#: includes/admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Valitse JSON-tiedosto, jonka haluat tuoda. Kenttäryhmät tuodaan, kun "
"klikkaat Tuo-painiketta."

#: includes/admin/views/settings-tools.php:77
#: includes/fields/class-acf-field-file.php:35
msgid "Select File"
msgstr "Valitse tiedosto"

#: includes/admin/views/settings-tools.php:86
msgid "Import"
msgstr "Tuo"

#: includes/api/api-helpers.php:856
msgid "Thumbnail"
msgstr "Pienoiskuva"

#: includes/api/api-helpers.php:857
msgid "Medium"
msgstr "Keskikokoinen"

#: includes/api/api-helpers.php:858
msgid "Large"
msgstr "Iso"

#: includes/api/api-helpers.php:907
msgid "Full Size"
msgstr "Täysikokoinen"

#: includes/api/api-helpers.php:1248 includes/api/api-helpers.php:1831
#: pro/fields/class-acf-field-clone.php:992
msgid "(no title)"
msgstr "(ei otsikkoa)"

#: includes/api/api-helpers.php:1868
#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:268
#: includes/fields/class-acf-field-taxonomy.php:986
msgid "Parent"
msgstr "Vanhempi"

#: includes/api/api-helpers.php:3885
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Kuvan leveys täytyy olla vähintään %dpx."

#: includes/api/api-helpers.php:3890
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Kuvan leveys ei saa ylittää %dpx."

#: includes/api/api-helpers.php:3906
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Kuvan korkeus täytyy olla vähintään %dpx."

#: includes/api/api-helpers.php:3911
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Kuvan korkeus ei saa ylittää %dpx."

#: includes/api/api-helpers.php:3929
#, php-format
msgid "File size must be at least %s."
msgstr "Tiedoston koko täytyy olla vähintään %s."

#: includes/api/api-helpers.php:3934
#, php-format
msgid "File size must must not exceed %s."
msgstr "Tiedoston koko ei saa ylittää %s:"

#: includes/api/api-helpers.php:3968
#, php-format
msgid "File type must be %s."
msgstr "Tiedoston koko täytyy olla %s."

#: includes/fields.php:144
msgid "Basic"
msgstr "Perus"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Sisältö"

#: includes/fields.php:146
msgid "Choice"
msgstr "Valinta-kentät"

#: includes/fields.php:147
msgid "Relational"
msgstr "Relationaalinen"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149
#: includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:384
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:285
#: pro/fields/class-acf-field-clone.php:839
#: pro/fields/class-acf-field-flexible-content.php:552
#: pro/fields/class-acf-field-flexible-content.php:601
#: pro/fields/class-acf-field-repeater.php:450
msgid "Layout"
msgstr "Asettelu"

#: includes/fields.php:326
msgid "Field type does not exist"
msgstr "Kenttätyyppi ei ole olemassa"

#: includes/fields.php:326
msgid "Unknown"
msgstr "Tuntematon"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Painikeryhmä"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "Valinnat"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "Syötä jokainen valinta uudelle riville."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr "Halutessasi voit määrittää sekä arvon että nimiön tähän tapaan:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "koira_istuu : Koira istuu"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:412
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-taxonomy.php:793
#: includes/fields/class-acf-field-user.php:408
msgid "Allow Null?"
msgstr "Salli tyhjä?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:114
#: includes/fields/class-acf-field-number.php:123
#: includes/fields/class-acf-field-radio.php:276
#: includes/fields/class-acf-field-range.php:142
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-text.php:115
#: includes/fields/class-acf-field-textarea.php:98
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:96
#: includes/fields/class-acf-field-wysiwyg.php:410
msgid "Default Value"
msgstr "Oletusarvo"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:115
#: includes/fields/class-acf-field-number.php:124
#: includes/fields/class-acf-field-radio.php:277
#: includes/fields/class-acf-field-range.php:143
#: includes/fields/class-acf-field-text.php:116
#: includes/fields/class-acf-field-textarea.php:99
#: includes/fields/class-acf-field-url.php:97
#: includes/fields/class-acf-field-wysiwyg.php:411
msgid "Appears when creating a new post"
msgstr "Kentän oletusarvo"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:391
#: includes/fields/class-acf-field-radio.php:292
msgid "Horizontal"
msgstr "Vaakasuuntainen"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:390
#: includes/fields/class-acf-field-radio.php:291
msgid "Vertical"
msgstr "Pystysuuntainen"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:408
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-image.php:188
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:299
#: includes/fields/class-acf-field-taxonomy.php:833
msgid "Return Value"
msgstr "Palauta arvo"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:409
#: includes/fields/class-acf-field-file.php:201
#: includes/fields/class-acf-field-image.php:189
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:300
msgid "Specify the returned value on front end"
msgstr "Määritä palautettu arvo front endiin"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-radio.php:305
#: includes/fields/class-acf-field-select.php:431
msgid "Value"
msgstr "Arvo"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:416
#: includes/fields/class-acf-field-radio.php:307
#: includes/fields/class-acf-field-select.php:433
msgid "Both (Array)"
msgstr "Molemmat (palautusmuoto on tällöin taulukko)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Checkbox"
msgstr "Valintaruutu"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Valitse kaikki"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Lisää uusi valinta"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Salli mukautettu"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Salli käyttäjän syöttää omia arvojaan"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Tallenna mukautettu"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr ""
"Tallenna 'Muu’-kentän arvo kentän valinta vaihtoehdoksi tulevaisuudessa"

#: includes/fields/class-acf-field-checkbox.php:376
#: includes/fields/class-acf-field-select.php:378
msgid "Enter each default value on a new line"
msgstr "Syötä jokainen oletusarvo uudelle riville."

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Toggle"
msgstr "Valitse kaikki?"

#: includes/fields/class-acf-field-checkbox.php:399
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Näytetäänkö ”Valitse kaikki” valintaruutu"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Värinvalitsin"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Tyhjennä"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Oletus"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Valitse väri"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Nykyinen väri"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Päivämäärävalitsin"

#: includes/fields/class-acf-field-date_picker.php:33
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Sulje"

#: includes/fields/class-acf-field-date_picker.php:34
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Tänään"

#: includes/fields/class-acf-field-date_picker.php:35
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Seuraava"

#: includes/fields/class-acf-field-date_picker.php:36
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Edellinen"

#: includes/fields/class-acf-field-date_picker.php:37
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Vk"

#: includes/fields/class-acf-field-date_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:181
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Muokkausnäkymän muoto"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Missä muodossa haluat päivämäärän näkyvän muokkausnäkymässä?"

#: includes/fields/class-acf-field-date_picker.php:216
#: includes/fields/class-acf-field-date_picker.php:247
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-date_time_picker.php:208
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Mukautettu:"

#: includes/fields/class-acf-field-date_picker.php:226
msgid "Save Format"
msgstr "Tallennusmuoto"

#: includes/fields/class-acf-field-date_picker.php:227
msgid "The format used when saving a value"
msgstr "Arvo tallennetaan tähän muotoon"

#: includes/fields/class-acf-field-date_picker.php:237
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-relationship.php:697
#: includes/fields/class-acf-field-select.php:426
#: includes/fields/class-acf-field-time_picker.php:124
msgid "Return Format"
msgstr "Palautusmuoto"

#: includes/fields/class-acf-field-date_picker.php:238
#: includes/fields/class-acf-field-date_time_picker.php:199
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr ""
"Missä muodossa haluat päivämäärän näkyvän, kun sivupohjan funktiot "
"palauttavat sen?"

#: includes/fields/class-acf-field-date_picker.php:256
#: includes/fields/class-acf-field-date_time_picker.php:215
msgid "Week Starts On"
msgstr "Viikon ensimmäinen päivä"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Päivämäärä- ja kellonaikavalitsin"

#: includes/fields/class-acf-field-date_time_picker.php:33
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Valitse aika"

#: includes/fields/class-acf-field-date_time_picker.php:34
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Aika"

#: includes/fields/class-acf-field-date_time_picker.php:35
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Tunti"

#: includes/fields/class-acf-field-date_time_picker.php:36
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "minuuttia"

#: includes/fields/class-acf-field-date_time_picker.php:37
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekunti"

#: includes/fields/class-acf-field-date_time_picker.php:38
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "millisekunti"

#: includes/fields/class-acf-field-date_time_picker.php:39
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "mikrosekunti"

#: includes/fields/class-acf-field-date_time_picker.php:40
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Aikavyöhyke"

#: includes/fields/class-acf-field-date_time_picker.php:41
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Nyt"

#: includes/fields/class-acf-field-date_time_picker.php:42
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Sulje"

#: includes/fields/class-acf-field-date_time_picker.php:43
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Valitse"

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Sähköposti"

#: includes/fields/class-acf-field-email.php:123
#: includes/fields/class-acf-field-number.php:132
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:124
#: includes/fields/class-acf-field-textarea.php:107
#: includes/fields/class-acf-field-url.php:105
msgid "Placeholder Text"
msgstr "Täyteteksti"

#: includes/fields/class-acf-field-email.php:124
#: includes/fields/class-acf-field-number.php:133
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:125
#: includes/fields/class-acf-field-textarea.php:108
#: includes/fields/class-acf-field-url.php:106
msgid "Appears within the input"
msgstr "Näkyy input-kentän sisällä"

#: includes/fields/class-acf-field-email.php:132
#: includes/fields/class-acf-field-number.php:141
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:181
#: includes/fields/class-acf-field-text.php:133
msgid "Prepend"
msgstr "Etuliite"

#: includes/fields/class-acf-field-email.php:133
#: includes/fields/class-acf-field-number.php:142
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:182
#: includes/fields/class-acf-field-text.php:134
msgid "Appears before the input"
msgstr "Näkyy ennen input-kenttää"

#: includes/fields/class-acf-field-email.php:141
#: includes/fields/class-acf-field-number.php:150
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:190
#: includes/fields/class-acf-field-text.php:142
msgid "Append"
msgstr "Loppuliite"

#: includes/fields/class-acf-field-email.php:142
#: includes/fields/class-acf-field-number.php:151
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:191
#: includes/fields/class-acf-field-text.php:143
msgid "Appears after the input"
msgstr "Näkyy input-kentän jälkeen"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Tiedosto"

#: includes/fields/class-acf-field-file.php:36
msgid "Edit File"
msgstr "Muokkaa tiedostoa"

#: includes/fields/class-acf-field-file.php:37
msgid "Update File"
msgstr "Päivitä tiedosto"

#: includes/fields/class-acf-field-file.php:38
#: includes/fields/class-acf-field-image.php:43 includes/media.php:57
#: pro/fields/class-acf-field-gallery.php:44
msgid "Uploaded to this post"
msgstr "Tähän kenttäryhmään ladatut kuvat"

#: includes/fields/class-acf-field-file.php:126
msgid "File name"
msgstr "Tiedoston nimi"

#: includes/fields/class-acf-field-file.php:130
#: includes/fields/class-acf-field-file.php:233
#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: pro/fields/class-acf-field-gallery.php:690
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Tiedoston koko"

#: includes/fields/class-acf-field-file.php:139
#: includes/fields/class-acf-field-image.php:124
#: includes/fields/class-acf-field-link.php:140 includes/input.php:269
#: pro/fields/class-acf-field-gallery.php:343
#: pro/fields/class-acf-field-gallery.php:531
msgid "Remove"
msgstr "Poista"

#: includes/fields/class-acf-field-file.php:155
msgid "Add File"
msgstr "Lisää tiedosto"

#: includes/fields/class-acf-field-file.php:206
msgid "File Array"
msgstr "Tiedosto"

#: includes/fields/class-acf-field-file.php:207
msgid "File URL"
msgstr "Tiedoston URL"

#: includes/fields/class-acf-field-file.php:208
msgid "File ID"
msgstr "Tiedoston ID"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:213
#: pro/fields/class-acf-field-gallery.php:655
msgid "Library"
msgstr "Kirjasto"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:214
#: pro/fields/class-acf-field-gallery.php:656
msgid "Limit the media library choice"
msgstr "Rajoita valintaa mediakirjastosta"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:219
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:661
msgid "All"
msgstr "Kaikki"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:220
#: pro/fields/class-acf-field-gallery.php:662
msgid "Uploaded to post"
msgstr "Vain tähän artikkeliin ladatut"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-image.php:227
#: pro/fields/class-acf-field-gallery.php:669
msgid "Minimum"
msgstr "Minimiarvo(t)"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-file.php:241
msgid "Restrict which files can be uploaded"
msgstr "Määritä tiedoston koko"

#: includes/fields/class-acf-field-file.php:240
#: includes/fields/class-acf-field-image.php:256
#: pro/fields/class-acf-field-gallery.php:698
msgid "Maximum"
msgstr "Maksimiarvo(t)"

#: includes/fields/class-acf-field-file.php:251
#: includes/fields/class-acf-field-image.php:285
#: pro/fields/class-acf-field-gallery.php:727
msgid "Allowed file types"
msgstr "Sallitut tiedostotyypit"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:286
#: pro/fields/class-acf-field-gallery.php:728
msgid "Comma separated list. Leave blank for all types"
msgstr "Erota pilkulla. Jätä tyhjäksi, jos haluat sallia kaikki tiedostyypit"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google Kartta"

#: includes/fields/class-acf-field-google-map.php:40
msgid "Locating"
msgstr "Paikannus"

#: includes/fields/class-acf-field-google-map.php:41
msgid "Sorry, this browser does not support geolocation"
msgstr "Pahoittelut, mutta tämä selain ei tuo paikannusta"

#: includes/fields/class-acf-field-google-map.php:113
msgid "Clear location"
msgstr "Tyhjennä paikkatieto"

#: includes/fields/class-acf-field-google-map.php:114
msgid "Find current location"
msgstr "Etsi nykyinen sijainti"

#: includes/fields/class-acf-field-google-map.php:117
msgid "Search for address..."
msgstr "Etsi osoite..."

#: includes/fields/class-acf-field-google-map.php:147
#: includes/fields/class-acf-field-google-map.php:158
msgid "Center"
msgstr "Sijainti"

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-google-map.php:159
msgid "Center the initial map"
msgstr "Kartan oletussijainti"

#: includes/fields/class-acf-field-google-map.php:170
msgid "Zoom"
msgstr "Zoomaus"

#: includes/fields/class-acf-field-google-map.php:171
msgid "Set the initial zoom level"
msgstr "Aseta oletuszoomaus"

#: includes/fields/class-acf-field-google-map.php:180
#: includes/fields/class-acf-field-image.php:239
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-oembed.php:281
#: pro/fields/class-acf-field-gallery.php:681
#: pro/fields/class-acf-field-gallery.php:710
msgid "Height"
msgstr "Korkeus"

#: includes/fields/class-acf-field-google-map.php:181
msgid "Customise the map height"
msgstr "Muotoile kartan korkeus"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Ryhmä"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:389
msgid "Sub Fields"
msgstr "Alakentät"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:840
msgid "Specify the style used to render the selected fields"
msgstr "Määritä tyyli, jota käytetään valittujen kenttien luomisessa"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:845
#: pro/fields/class-acf-field-flexible-content.php:612
#: pro/fields/class-acf-field-repeater.php:458
msgid "Block"
msgstr "Lohko"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:846
#: pro/fields/class-acf-field-flexible-content.php:611
#: pro/fields/class-acf-field-repeater.php:457
msgid "Table"
msgstr "Taulukko"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:847
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:459
msgid "Row"
msgstr "Rivi"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Kuva"

#: includes/fields/class-acf-field-image.php:40
msgid "Select Image"
msgstr "Valitse kuva"

#: includes/fields/class-acf-field-image.php:41
#: pro/fields/class-acf-field-gallery.php:42
msgid "Edit Image"
msgstr "Muokkaa kuvaa"

#: includes/fields/class-acf-field-image.php:42
#: pro/fields/class-acf-field-gallery.php:43
msgid "Update Image"
msgstr "Päivitä kuva"

#: includes/fields/class-acf-field-image.php:44
msgid "All images"
msgstr "Kaikki kuvat"

#: includes/fields/class-acf-field-image.php:140
msgid "No image selected"
msgstr "Ei kuvia valittu"

#: includes/fields/class-acf-field-image.php:140
msgid "Add Image"
msgstr "Lisää kuva"

#: includes/fields/class-acf-field-image.php:194
msgid "Image Array"
msgstr "Kuva"

#: includes/fields/class-acf-field-image.php:195
msgid "Image URL"
msgstr "Kuvan URL"

#: includes/fields/class-acf-field-image.php:196
msgid "Image ID"
msgstr "Kuvan ID"

#: includes/fields/class-acf-field-image.php:203
msgid "Preview Size"
msgstr "Esikatselukuvan koko"

#: includes/fields/class-acf-field-image.php:204
msgid "Shown when entering data"
msgstr "Näytetään muokkausnäkymässä"

#: includes/fields/class-acf-field-image.php:228
#: includes/fields/class-acf-field-image.php:257
#: pro/fields/class-acf-field-gallery.php:670
#: pro/fields/class-acf-field-gallery.php:699
msgid "Restrict which images can be uploaded"
msgstr "Määritä millaisia kuvia voidaan ladata"

#: includes/fields/class-acf-field-image.php:231
#: includes/fields/class-acf-field-image.php:260
#: includes/fields/class-acf-field-oembed.php:270
#: pro/fields/class-acf-field-gallery.php:673
#: pro/fields/class-acf-field-gallery.php:702
msgid "Width"
msgstr "Leveys"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Linkki"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Valitse linkki"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Avaa uuteen ikkunaan/välilehteen"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Linkkitaulukko (array)"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "Linkin URL-osoite"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Viesti"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:135
msgid "New Lines"
msgstr "Uudet rivit"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:136
msgid "Controls how new lines are rendered"
msgstr "Määrittää kuinka uudet rivit muotoillaan"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:140
msgid "Automatically add paragraphs"
msgstr "Lisää automaattisesti kappale"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:141
msgid "Automatically add &lt;br&gt;"
msgstr "Lisää automaattisesti &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:142
msgid "No Formatting"
msgstr "Ei muotoilua"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Haluatko, että HTML-merkinnät näkyvät tekstinä?"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Numero"

#: includes/fields/class-acf-field-number.php:159
#: includes/fields/class-acf-field-range.php:151
msgid "Minimum Value"
msgstr "Minimiarvo"

#: includes/fields/class-acf-field-number.php:168
#: includes/fields/class-acf-field-range.php:161
msgid "Maximum Value"
msgstr "Maksimiarvo"

#: includes/fields/class-acf-field-number.php:177
#: includes/fields/class-acf-field-range.php:171
msgid "Step Size"
msgstr "Askelluksen koko"

#: includes/fields/class-acf-field-number.php:215
msgid "Value must be a number"
msgstr "Arvon täytyy olla numero"

#: includes/fields/class-acf-field-number.php:233
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Arvon täytyy olla sama tai suurempi kuin %d"

#: includes/fields/class-acf-field-number.php:241
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Arvon täytyy olla sama tai pienempi kuin %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "Syötä URL"

#: includes/fields/class-acf-field-oembed.php:234
#: includes/fields/class-acf-field-taxonomy.php:898
msgid "Error."
msgstr "Virhe."

#: includes/fields/class-acf-field-oembed.php:234
msgid "No embed found for the given URL."
msgstr "Upotettavaa ei löytynyt annetusta URL-osoitteesta."

#: includes/fields/class-acf-field-oembed.php:267
#: includes/fields/class-acf-field-oembed.php:278
msgid "Embed Size"
msgstr "Upotuksen koko"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Arkistot"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:384
#: includes/fields/class-acf-field-relationship.php:623
msgid "Filter by Post Type"
msgstr "Suodata tyypin mukaan"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:392
#: includes/fields/class-acf-field-relationship.php:631
msgid "All post types"
msgstr "Kaikki artikkelityypit"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:398
#: includes/fields/class-acf-field-relationship.php:637
msgid "Filter by Taxonomy"
msgstr "Suodata taksonomian mukaan"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:406
#: includes/fields/class-acf-field-relationship.php:645
msgid "All taxonomies"
msgstr "Kaikki taksonomiat"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Salli arkistojen URL-osoitteita"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:422
#: includes/fields/class-acf-field-select.php:396
#: includes/fields/class-acf-field-user.php:418
msgid "Select multiple values?"
msgstr "Valitse useita arvoja?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Salasana"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:702
msgid "Post Object"
msgstr "Artikkeliolio"

#: includes/fields/class-acf-field-post_object.php:438
#: includes/fields/class-acf-field-relationship.php:703
msgid "Post ID"
msgstr "Artikkelin ID"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Valintanappi"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Muu"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Lisää 'Muu' vaihtoehto salliaksesi mukautettuja arvoja"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Tallenna Muu"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Tallenna 'Muu’-kentän arvo kentän valinnaksi"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Liukusäädin"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Suodata artikkeleita"

#: includes/fields/class-acf-field-relationship.php:37
msgid "Minimum values reached ( {min} values )"
msgstr "Pienin määrä arvoja saavutettu ({min} arvoa)"

#: includes/fields/class-acf-field-relationship.php:38
msgid "Maximum values reached ( {max} values )"
msgstr "Maksimiarvo saavutettu ( {max} artikkelia )"

#: includes/fields/class-acf-field-relationship.php:39
msgid "Loading"
msgstr "Ladataan"

#: includes/fields/class-acf-field-relationship.php:40
msgid "No matches found"
msgstr "Ei yhtään osumaa"

#: includes/fields/class-acf-field-relationship.php:423
msgid "Select post type"
msgstr "Valitse artikkelityyppi"

#: includes/fields/class-acf-field-relationship.php:449
msgid "Select taxonomy"
msgstr "Valitse taksonomia"

#: includes/fields/class-acf-field-relationship.php:539
msgid "Search..."
msgstr "Etsi..."

#: includes/fields/class-acf-field-relationship.php:651
msgid "Filters"
msgstr "Suodattimet"

#: includes/fields/class-acf-field-relationship.php:657
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Artikkelityyppi"

#: includes/fields/class-acf-field-relationship.php:658
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:763
msgid "Taxonomy"
msgstr "Taksonomia"

#: includes/fields/class-acf-field-relationship.php:665
msgid "Elements"
msgstr "Elementit"

#: includes/fields/class-acf-field-relationship.php:666
msgid "Selected elements will be displayed in each result"
msgstr "Valitut elementit näytetään jokaisessa tuloksessa"

#: includes/fields/class-acf-field-relationship.php:677
msgid "Minimum posts"
msgstr "Vähimmäismäärä artikkeleita"

#: includes/fields/class-acf-field-relationship.php:686
msgid "Maximum posts"
msgstr "Maksimi artikkelit"

#: includes/fields/class-acf-field-relationship.php:790
#: pro/fields/class-acf-field-gallery.php:800
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s vaatii vähintään %s valinnan"
msgstr[1] "%s vaatii vähintään %s valintaa"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:785
msgctxt "noun"
msgid "Select"
msgstr "Valintalista"

#: includes/fields/class-acf-field-select.php:38
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Yksi tulos on saatavilla. Valitse se painamalla enter-näppäintä."

#: includes/fields/class-acf-field-select.php:39
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""
"%d tulosta on saatavilla. Voit navigoida tuloksian välillä käyttämällä "
"”ylös” ja ”alas” -näppäimiä."

#: includes/fields/class-acf-field-select.php:40
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Osumia ei löytynyt"

#: includes/fields/class-acf-field-select.php:41
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Kirjoita yksi tai useampi merkki"

#: includes/fields/class-acf-field-select.php:42
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Kirjoita %d tai useampi merkkiä"

#: includes/fields/class-acf-field-select.php:43
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Poista 1 merkki"

#: includes/fields/class-acf-field-select.php:44
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Poista %d merkkiä"

#: includes/fields/class-acf-field-select.php:45
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Voit valita vain yhden kohteen"

#: includes/fields/class-acf-field-select.php:46
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Voit valita vain %d kohdetta"

#: includes/fields/class-acf-field-select.php:47
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Lataa lisää tuloksia &hellip;"

#: includes/fields/class-acf-field-select.php:48
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Etsii&hellip;"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Lataus epäonnistui"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:54
msgctxt "verb"
msgid "Select"
msgstr "Valitse"

#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Tyylikäs käyttöliittymä"

#: includes/fields/class-acf-field-select.php:416
msgid "Use AJAX to lazy load choices?"
msgstr "Haluatko ladata valinnat laiskasti (käyttää AJAXia)?"

#: includes/fields/class-acf-field-select.php:427
msgid "Specify the value returned"
msgstr "Määritä palautetun arvon muoto"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Erotusmerkki"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Välilehti"

#: includes/fields/class-acf-field-tab.php:82
msgid ""
"The tab field will display incorrectly when added to a Table style repeater "
"field or flexible content field layout"
msgstr ""
"Välilehtikentän ulkoasu rikkoutuu, jos lisätään taulukko-tyyli toistin "
"kenttä tai joustava sisältö kenttä asettelu"

#: includes/fields/class-acf-field-tab.php:83
msgid ""
"Use \"Tab Fields\" to better organize your edit screen by grouping fields "
"together."
msgstr ""
"Ryhmittele kenttiä käyttämällä ”välilehtikenttiä”. Näin saat selkeämmän "
"muokkausnäkymän."

#: includes/fields/class-acf-field-tab.php:84
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is "
"defined) will be grouped together using this field's label as the tab "
"heading."
msgstr ""
"Kaikki kentät, jotka seuraavat tätä \"välilehtikenttää\" (tai kunnes toinen "
"\"välilehtikenttä\" määritellään) ryhmitellään yhteen ja välilehden "
"otsikoksi tulee tämän kentän nimiö."

#: includes/fields/class-acf-field-tab.php:98
msgid "Placement"
msgstr "Sijainti"

#: includes/fields/class-acf-field-tab.php:110
msgid "End-point"
msgstr "Välilehtiryhmän aloitus"

#: includes/fields/class-acf-field-tab.php:111
msgid "Use this field as an end-point and start a new group of tabs"
msgstr "Valitse ”kyllä”, jos haluat aloittaa uuden välilehtiryhmän."

#: includes/fields/class-acf-field-taxonomy.php:713
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Ei %s"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "None"
msgstr "Ei mitään"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Select the taxonomy to be displayed"
msgstr "Valitse taksonomia, joka näytetään"

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Appearance"
msgstr "Ulkoasu"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Select the appearance of this field"
msgstr "Valitse ulkoasu tälle kenttälle"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Multiple Values"
msgstr "Mahdollisuus valita useita arvoja"

#: includes/fields/class-acf-field-taxonomy.php:781
msgid "Multi Select"
msgstr "Valitse useita"

#: includes/fields/class-acf-field-taxonomy.php:783
msgid "Single Value"
msgstr "Mahdollisuus valita vain yksi arvo"

#: includes/fields/class-acf-field-taxonomy.php:784
msgid "Radio Buttons"
msgstr "Valintanappi"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "Create Terms"
msgstr "Uusien ehtojen luominen"

#: includes/fields/class-acf-field-taxonomy.php:804
msgid "Allow new terms to be created whilst editing"
msgstr "Salli uusien ehtojen luominen samalla kun muokataan"

#: includes/fields/class-acf-field-taxonomy.php:813
msgid "Save Terms"
msgstr "Tallenna ehdot"

#: includes/fields/class-acf-field-taxonomy.php:814
msgid "Connect selected terms to the post"
msgstr "Yhdistä valitut ehdot artikkeliin"

#: includes/fields/class-acf-field-taxonomy.php:823
msgid "Load Terms"
msgstr "Lataa ehdot"

#: includes/fields/class-acf-field-taxonomy.php:824
msgid "Load value from posts terms"
msgstr "Lataa arvo artikkelin ehdoista"

#: includes/fields/class-acf-field-taxonomy.php:838
msgid "Term Object"
msgstr "Ehto"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Term ID"
msgstr "Ehdon ID"

#: includes/fields/class-acf-field-taxonomy.php:898
#, php-format
msgid "User unable to add new %s"
msgstr "Käyttäjä ei voi lisätä uutta %s"

#: includes/fields/class-acf-field-taxonomy.php:911
#, php-format
msgid "%s already exists"
msgstr "%s on jo olemassa"

#: includes/fields/class-acf-field-taxonomy.php:952
#, php-format
msgid "%s added"
msgstr "%s lisättiin"

#: includes/fields/class-acf-field-taxonomy.php:997
msgid "Add"
msgstr "Lisää"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Teksti"

#: includes/fields/class-acf-field-text.php:151
#: includes/fields/class-acf-field-textarea.php:116
msgid "Character Limit"
msgstr "Merkkirajoitus"

#: includes/fields/class-acf-field-text.php:152
#: includes/fields/class-acf-field-textarea.php:117
msgid "Leave blank for no limit"
msgstr "Jos et halua rajoittaa, jätä tyhjäksi"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Tekstialue"

#: includes/fields/class-acf-field-textarea.php:125
msgid "Rows"
msgstr "Rivit"

#: includes/fields/class-acf-field-textarea.php:126
msgid "Sets the textarea height"
msgstr "Aseta tekstialueen koko"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Kellonaikavalitsin"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "”Tosi / Epätosi” -valinta"

#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159 includes/input.php:267
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Kyllä"

#: includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:169 includes/input.php:268
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Ei"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Näytä teksti valintaruudun rinnalla"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Päällä -teksti"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Teksti, joka näytetään kun valinta on aktiivinen"

#: includes/fields/class-acf-field-true_false.php:165
msgid "Off Text"
msgstr "Pois päältä -teksti"

#: includes/fields/class-acf-field-true_false.php:166
msgid "Text shown when inactive"
msgstr "Teksti, joka näytetään kun valinta ei ole aktiivinen"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:147
msgid "Value must be a valid URL"
msgstr "Arvon täytyy olla validi URL"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Käyttäjä"

#: includes/fields/class-acf-field-user.php:393
msgid "Filter by role"
msgstr "Suodata roolin mukaan"

#: includes/fields/class-acf-field-user.php:401
msgid "All user roles"
msgstr "Kaikki käyttäjäroolit"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Wysiwyg-editori"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual"
msgstr "Graafinen"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Teksti"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Click to initialize TinyMCE"
msgstr "Klikkaa ottaaksesi käyttöön graafisen editorin"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Tabs"
msgstr "Välilehdet"

#: includes/fields/class-acf-field-wysiwyg.php:424
msgid "Visual & Text"
msgstr "Graafinen ja teksti"

#: includes/fields/class-acf-field-wysiwyg.php:425
msgid "Visual Only"
msgstr "Vain graafinen"

#: includes/fields/class-acf-field-wysiwyg.php:426
msgid "Text Only"
msgstr "Vain teksti"

#: includes/fields/class-acf-field-wysiwyg.php:433
msgid "Toolbar"
msgstr "Työkalupalkki"

#: includes/fields/class-acf-field-wysiwyg.php:443
msgid "Show Media Upload Buttons?"
msgstr "Näytä Lisää media -painike?"

#: includes/fields/class-acf-field-wysiwyg.php:453
msgid "Delay initialization?"
msgstr "Viivytä alustusta?"

#: includes/fields/class-acf-field-wysiwyg.php:454
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "Graafista editoria ei käytetä ennen kuin kenttää klikataan"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr "Muokkaa kenttäryhmää"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Validoi sähköposti"

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:573 pro/options-page.php:81
msgid "Update"
msgstr "Päivitä"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Artikkeli päivitetty"

#: includes/forms/form-front.php:229
msgid "Spam Detected"
msgstr "Roskapostia havaittu"

#: includes/input.php:259
msgid "Expand Details"
msgstr "Enemmän tietoja"

#: includes/input.php:260
msgid "Collapse Details"
msgstr "Vähemmän tietoja"

#: includes/input.php:261
msgid "Validation successful"
msgstr "Kenttäryhmän validointi onnistui"

#: includes/input.php:262 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Lisäkentän validointi epäonnistui"

#: includes/input.php:263
msgid "1 field requires attention"
msgstr "Yksi kenttä vaatii huomiota"

#: includes/input.php:264
#, php-format
msgid "%d fields require attention"
msgstr "%d kenttää vaativat huomiota"

#: includes/input.php:265
msgid "Restricted"
msgstr "Rajoitettu"

#: includes/input.php:266
msgid "Are you sure?"
msgstr "Oletko varma?"

#: includes/input.php:270
msgid "Cancel"
msgstr "Peruuta"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Artikkeli"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Sivu"

#: includes/locations.php:96
msgid "Forms"
msgstr "Lomakkeet"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Liite"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Kaikki %s muodot"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Kommentti"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Nykyinen käyttäjärooli"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Super pääkäyttäjä"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Nykyinen käyttäjä"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Kirjautunut sisään"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Käyttää front endiä"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Käyttää back endiä"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Valikkokohde"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Valikko"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Valikkosijainnit"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Valikot"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Sivun vanhempi"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Sivupohja"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Oletus sivupohja"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Sivun tyyppi"

#: includes/locations/class-acf-location-page-type.php:145
msgid "Front Page"
msgstr "Etusivu"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Posts Page"
msgstr "Artikkelit -sivu"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Top Level Page (no parent)"
msgstr "Ylätason sivu (sivu, jolla ei ole vanhempia)"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Parent Page (has children)"
msgstr "Vanhempi sivu (sivu, jolla on alasivuja)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Child Page (has parent)"
msgstr "Lapsi sivu (sivu, jolla on vanhempi)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Artikkelin kategoria"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Artikkelin muoto"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Artikkelin tila"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Artikkelin taksonomia"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Sivupohja"

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "Taksonomian ehto"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Käyttäjälomake"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Lisää / Muokkaa"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Rekisteröi"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Käyttäjän rooli"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Vimpain"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr "Muokkaa"

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr "Päivitä"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s arvo on pakollinen"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO -lisäosan"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Julkaistu"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Yhtään lisäkenttäryhmää ei löytynyt tälle asetussivulle. <a href=\"%s\">Luo "
"lisäkenttäryhmä</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Virhe</b>. Ei voitu yhdistää päivityspalvelimeen"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Päivitykset"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Poista lisenssi käytöstä "

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Aktivoi lisenssi"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Näytä lisenssitiedot"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Ottaaksesi käyttöön päivitykset, syötä lisenssiavaimesi alle. Jos sinulla ei "
"ole lisenssiavainta, katso <a href=\"%s\" target=”_blank”>tarkemmat tiedot & "
"hinnoittelu</a>"

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Lisenssiavain"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Päivitä tiedot"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Nykyinen versio"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Uusin versio"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Päivitys saatavilla?"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Päivitä lisäosa"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Syötä lisenssiavain saadaksesi päivityksiä"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Tarkista uudelleen"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Päivitys Ilmoitus"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Klooni"

#: pro/fields/class-acf-field-clone.php:808
msgid "Select one or more fields you wish to clone"
msgstr "Valitse kentä(t), jotka haluat kopioida"

#: pro/fields/class-acf-field-clone.php:825
msgid "Display"
msgstr "Näytä"

#: pro/fields/class-acf-field-clone.php:826
msgid "Specify the style used to render the clone field"
msgstr "Määritä tyyli, jota käytetään kloonikentän luomisessa"

#: pro/fields/class-acf-field-clone.php:831
msgid "Group (displays selected fields in a group within this field)"
msgstr "Ryhmä (valitut kentät näytetään ryhmänä tämän klooni-kentän sisällä)"

#: pro/fields/class-acf-field-clone.php:832
msgid "Seamless (replaces this field with selected fields)"
msgstr "Saumaton (korvaa tämä klooni-kenttä valituilla kentillä)"

#: pro/fields/class-acf-field-clone.php:853
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Kenttän nimiö näytetään seuraavassa muodossa: %s"

#: pro/fields/class-acf-field-clone.php:856
msgid "Prefix Field Labels"
msgstr "Kentän nimiön etuliite"

#: pro/fields/class-acf-field-clone.php:867
#, php-format
msgid "Values will be saved as %s"
msgstr "Arvot tallennetaan muodossa: %s"

#: pro/fields/class-acf-field-clone.php:870
msgid "Prefix Field Names"
msgstr "Kentän nimen etuliite"

#: pro/fields/class-acf-field-clone.php:988
msgid "Unknown field"
msgstr "Tuntematon kenttä"

#: pro/fields/class-acf-field-clone.php:1027
msgid "Unknown field group"
msgstr "Tuntematon kenttäryhmä"

#: pro/fields/class-acf-field-clone.php:1031
#, php-format
msgid "All fields from %s field group"
msgstr "Kaikki kentät kenttäryhmästä %s"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:174
#: pro/fields/class-acf-field-repeater.php:470
msgid "Add Row"
msgstr "Lisää rivi"

#: pro/fields/class-acf-field-flexible-content.php:34
msgid "layout"
msgstr "asettelu"

#: pro/fields/class-acf-field-flexible-content.php:35
msgid "layouts"
msgstr "asettelua"

#: pro/fields/class-acf-field-flexible-content.php:36
msgid "remove {layout}?"
msgstr "poista {layout}?"

#: pro/fields/class-acf-field-flexible-content.php:37
msgid "This field requires at least {min} {identifier}"
msgstr "Tämä kenttä vaatii vähintään {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:38
msgid "This field has a limit of {max} {identifier}"
msgstr "Tämän kentän yläraja on {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:39
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Tämä kenttä vaatii vähintään {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:40
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "Maksimi {label} saavutettu ({max} {identifier})"

#: pro/fields/class-acf-field-flexible-content.php:41
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} saatavilla (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:42
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} vaadittu (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:43
msgid "Flexible Content requires at least 1 layout"
msgstr "Vaaditaan vähintään yksi asettelu"

#: pro/fields/class-acf-field-flexible-content.php:273
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Klikkaa ”%s” -painiketta luodaksesi oman asettelun"

#: pro/fields/class-acf-field-flexible-content.php:406
msgid "Add layout"
msgstr "Lisää asettelu"

#: pro/fields/class-acf-field-flexible-content.php:407
msgid "Remove layout"
msgstr "Poista asettelu"

#: pro/fields/class-acf-field-flexible-content.php:408
#: pro/fields/class-acf-field-repeater.php:298
msgid "Click to toggle"
msgstr "Piilota/Näytä"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder Layout"
msgstr "Järjestä asettelu uudelleen"

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder"
msgstr "Järjestä uudelleen"

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete Layout"
msgstr "Poista asettelu"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate Layout"
msgstr "Monista asettelu"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New Layout"
msgstr "Lisää uusi asettelu"

#: pro/fields/class-acf-field-flexible-content.php:628
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:641
msgid "Max"
msgstr "Maks"

#: pro/fields/class-acf-field-flexible-content.php:668
#: pro/fields/class-acf-field-repeater.php:466
msgid "Button Label"
msgstr "Painikkeen teksti"

#: pro/fields/class-acf-field-flexible-content.php:677
msgid "Minimum Layouts"
msgstr "Asetteluita vähintään"

#: pro/fields/class-acf-field-flexible-content.php:686
msgid "Maximum Layouts"
msgstr "Asetteluita enintään"

#: pro/fields/class-acf-field-gallery.php:41
msgid "Add Image to Gallery"
msgstr "Lisää kuva galleriaan"

#: pro/fields/class-acf-field-gallery.php:45
msgid "Maximum selection reached"
msgstr "Et voi valita enempää kuvia"

#: pro/fields/class-acf-field-gallery.php:321
msgid "Length"
msgstr "Pituus"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Kuvateksti"

#: pro/fields/class-acf-field-gallery.php:373
msgid "Alt Text"
msgstr "Vaihtoehtoinen teksti"

#: pro/fields/class-acf-field-gallery.php:544
msgid "Add to gallery"
msgstr "Lisää galleriaan"

#: pro/fields/class-acf-field-gallery.php:548
msgid "Bulk actions"
msgstr "Massatoiminnot"

#: pro/fields/class-acf-field-gallery.php:549
msgid "Sort by date uploaded"
msgstr "Lajittele latauksen päivämäärän mukaan"

#: pro/fields/class-acf-field-gallery.php:550
msgid "Sort by date modified"
msgstr "Lajittele viimeisimmän muokkauksen päivämäärän mukaan"

#: pro/fields/class-acf-field-gallery.php:551
msgid "Sort by title"
msgstr "Lajittele otsikon mukaan"

#: pro/fields/class-acf-field-gallery.php:552
msgid "Reverse current order"
msgstr "Käännän nykyinen järjestys"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Close"
msgstr "Sulje"

#: pro/fields/class-acf-field-gallery.php:624
msgid "Minimum Selection"
msgstr "Pienin määrä kuvia"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Maximum Selection"
msgstr "Suurin määrä kuvia"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Insert"
msgstr "Lisää"

#: pro/fields/class-acf-field-gallery.php:643
msgid "Specify where new attachments are added"
msgstr "Määritä mihin uudet liitteet lisätään"

#: pro/fields/class-acf-field-gallery.php:647
msgid "Append to the end"
msgstr "Lisää loppuun"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Prepend to the beginning"
msgstr "Lisää alkuun"

#: pro/fields/class-acf-field-repeater.php:36
msgid "Minimum rows reached ({min} rows)"
msgstr "Pienin määrä rivejä saavutettu ({min} riviä)"

#: pro/fields/class-acf-field-repeater.php:37
msgid "Maximum rows reached ({max} rows)"
msgstr "Suurin määrä rivejä saavutettu ({max} riviä)"

#: pro/fields/class-acf-field-repeater.php:343
msgid "Add row"
msgstr "Lisää rivi"

#: pro/fields/class-acf-field-repeater.php:344
msgid "Remove row"
msgstr "Poista rivi"

#: pro/fields/class-acf-field-repeater.php:419
msgid "Collapsed"
msgstr "Piilotettu"

#: pro/fields/class-acf-field-repeater.php:420
msgid "Select a sub field to show when row is collapsed"
msgstr "Valitse alakenttä, joka näytetään, kun rivi on piilotettu"

#: pro/fields/class-acf-field-repeater.php:430
msgid "Minimum Rows"
msgstr "Pienin määrä rivejä"

#: pro/fields/class-acf-field-repeater.php:440
msgid "Maximum Rows"
msgstr "Suurin määrä rivejä"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Yhtään Asetukset-sivua ei ole olemassa"

#: pro/options-page.php:51
msgid "Options"
msgstr "Asetukset"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Asetukset päivitetty"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Ottaaksesi käyttöön päivitykset, ole hyvä ja syötä lisenssiavaimesi <a href="
"\"%s\">Päivitykset</a> -sivulle. jos sinulla ei ole lisenssiavainta, katso "
"<a href=\"%s\">tarkemmat tiedot & hinnoittelu</a>"

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "http://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Disabled"
#~ msgstr "Poistettu käytöstä"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Poistettu käytöstä <span class=”count”>(%s)</span>"
#~ msgstr[1] "Poistettu käytöstä <span class=”count”>(%s)</span>"

#~ msgid "Getting Started"
#~ msgstr "Miten pääset alkuun"

#~ msgid "Field Types"
#~ msgstr "Kenttätyypit"

#~ msgid "Functions"
#~ msgstr "Funktiot"

#~ msgid "Actions"
#~ msgstr "Toiminnot"

#~ msgid "'How to' guides"
#~ msgstr "\"Miten\" oppaat"

#~ msgid "Tutorials"
#~ msgstr "Oppaat"

#~ msgid "FAQ"
#~ msgstr "UKK"

#~ msgid "Created by"
#~ msgstr "Tekijä"

#~ msgid "Error loading update"
#~ msgstr "Virhe ladattaessa päivitystä"

#~ msgid "Error"
#~ msgstr "Virhe"

#~ msgid "See what's new"
#~ msgstr "Katso mitä uutta"

#~ msgid "eg. Show extra content"
#~ msgstr "Esim. näytä ylimääräinen sisältö"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "Yksi kenttä vaatii huomiota"
#~ msgstr[1] "%d kenttää vaatii huomiota."

#~ msgid ""
#~ "Error validating license URL (website does not match). Please re-activate "
#~ "your license"
#~ msgstr ""
#~ "Virhe lisenssin URL:n validoinnissa (websivu ei täsmää). Ole hyvä ja "
#~ "aktivoi lisenssisi uudelleen"

#~ msgid "See what's new in"
#~ msgstr "Katso mitä uutta löytyy"

#~ msgid "version"
#~ msgstr "versiosta"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Onnistui!</b> Tuontityökalu lisäsi %s kenttäryhmään: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Varoitus!</b> Tuontityökalu havaitsi %s kenttäryhmää on jo olemassa ja "
#~ "siksi ne jätettiin huomiotta: %s\t"

#~ msgid "Upgrade ACF"
#~ msgstr "Päivitä ACF"

#~ msgid "Upgrade"
#~ msgstr "Päivitä"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Vedä ja pudota muuttaaksesi järjestystä"

#~ msgid "Show a different month"
#~ msgstr "Näytä eri kuuakusi"

#~ msgid "Return format"
#~ msgstr "Palautusmuoto"

#~ msgid "uploaded to this post"
#~ msgstr "ladattu tähän artikkeliin"

#~ msgid "File Size"
#~ msgstr "Tiedoston koko"

#~ msgid "No File selected"
#~ msgstr "Ei tiedostoa valittu"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr "Huomioithan, että teksti syötetään aina funktiolle  "

#~ msgid "Warning"
#~ msgstr "Varoitus"

#~ msgid "Add new %s "
#~ msgstr "Lisää uusi %s "

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr ""
#~ "Olemme pahoillamme, mutta tapahtui <b>Yhteysvirhe</b>. Ole hyvä ja yritä "
#~ "uudelleen"

#~ msgid "Save Options"
#~ msgstr "Tallenna asetukset"

#~ msgid "License"
#~ msgstr "lisenssi"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Saadaksesi mahdollisuuden päivityksiin, syötä lisenssiavain. Jos sinulla "
#~ "ei ole lisenssiavainta, katso"

#~ msgid "details & pricing"
#~ msgstr "lisätiedot & hinnoittelu"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "Show Field Keys"
#~ msgstr "Näytä kenttäavain"

#~ msgid "Import / Export"
#~ msgstr "Tuonti / vienti"

#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr ""
#~ "Kenttäryhmät luodaan järjestyksessä alkaen pienimmästä järjestysnumerosta"

#~ msgid "Upgrading data to "
#~ msgstr "Päivitetään data versioon %s"

#~ msgid "Hide / Show All"
#~ msgstr "Piilota / Näytä kaikki"

#~ msgid "Pending Review"
#~ msgstr "Odottaa tarkistusta"

#~ msgid "Draft"
#~ msgstr "Luonnos"

#~ msgid "Future"
#~ msgstr "Tuleva"

#~ msgid "Private"
#~ msgstr "Yksityinen"

#~ msgid "Revision"
#~ msgstr "Tarkastettu"

#~ msgid "Trash"
#~ msgstr "Roskakori"

#~ msgid "ACF PRO Required"
#~ msgstr "Vaaditaan ACF PRO"

#~ msgid ""
#~ "We have detected an issue which requires your attention: This website "
#~ "makes use of premium add-ons (%s) which are no longer compatible with ACF."
#~ msgstr ""
#~ "Olemme havainneet ongelman, joka vaatii huomiotasi: Tämä websivu käyttää "
#~ "premium lisäosia (%s), jotka eivät enää ole yhteensopivia ACF:n kanssa."

#~ msgid ""
#~ "Don't panic, you can simply roll back the plugin and continue using ACF "
#~ "as you know it!"
#~ msgstr ""
#~ "Ei kuitenkaan hätää! Voit helposti palata ja jatkaa ACF:n käyttöä "
#~ "sellaisena kuin sen tunnet!"

#~ msgid "Roll back to ACF v%s"
#~ msgstr "Palaa takaisin ACF v%s:ään"

#~ msgid "Learn why ACF PRO is required for my site"
#~ msgstr "Lue miksi ACF PRO vaaditaan sivustollani"

#~ msgid "Data Upgrade"
#~ msgstr "Tietojen päivitys"

#~ msgid "Data upgraded successfully."
#~ msgstr "Tietojen päivitys onnistui!"

#~ msgid "Data is at the latest version."
#~ msgstr "Tiedot ovat ajan tasalla."

#~ msgid "1 required field below is empty"
#~ msgid_plural "%s required fields below are empty"
#~ msgstr[0] "Yksi vaadittu kenttä on tyhjä"
#~ msgstr[1] "%s valittua kenttää ovat tyhjiä"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "Lataa & tallenna taksonomian ehdot artikkeliin"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr ""
#~ "Lataa arvo perustuen artikkelin ehtoihin ja päivitä artikkelin ehdot "
#~ "tallennettaessa"
