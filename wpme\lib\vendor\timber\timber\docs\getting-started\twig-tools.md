---
title: "Twig Tools"
menu:
  main:
    parent: "getting-started"
---

The purpose of this page is to identify helpful tools for working with Twig.

## Text editor add-ons

* Text Mate & Sublime text bundle – [Anomareh's PHP-Twig](https://github.com/Anomareh/PHP-Twig.tmbundle)
* Emacs – [Web Mode](http://web-mode.org/)
* Geany – Add [Twig/Symfony2 detection and highlighting](https://wiki.geany.org/howtos/geany_and_django#twigsymfony2_support)
* PhpStorm – Built in coloring and code hinting. The Twig extension is recognized and has been for some time. [Twig Details for PhpStorm](http://blog.jetbrains.com/phpstorm/2013/06/twig-support-in-phpstorm/).
* Atom – Syntax highlighting with the [Atom Component](https://atom.io/packages/php-twig).

## WordPress tools

* [Lisa Templates](https://github.com/pierreminik/lisa-templates/) – allows you to write Twig-templates in the WordPress admin that renders through a shortcode, widget or on the_content hook.
	

## Other

 * [<PERSON>-<PERSON>](http://nhmood.github.io/watson-ruby/) – An inline issue manager. Put tags like `[todo]` in a Twig comment and find it easily later. Watson supports Twig as of version 1.6.3.

## JavaScript

* [Twig.js](https://github.com/justjohn/twig.js) – Use those `.twig` files in the Javascript and AJAX components of your site.
* [Nunjucks](http://mozilla.github.io/nunjucks/) – Another JS template language that is also based on [Jinja2](http://jinja.pocoo.org/docs/)
