os: linux

dist: xenial

services: mysql

language: php

addons:
  apt:
    packages:
      - libwebp-dev
      - apt-transport-https
      - apt-utils
      - python-software-properties

php:
  - 7.2

env:
  global:
    - TMPDIR=/tmp
    - COVERALLS_PARALLEL=true
  jobs:
    - WP_VERSION=4.9.8 WP_MULTISITE=0 
    - WP_VERSION=4.9.8 WP_MULTISITE=1
    - WP_VERSION=latest WP_MULTISITE=0
    - WP_VERSION=latest WP_MULTISITE=1

jobs:
  include:
    - php: 5.6
      env: WP_VERSION=4.9.8 WP_MULTISITE=0 PREFER_LOWEST="--prefer-lowest --prefer-stable"
      before_install: skip
      install:
        - bash bin/install-wp-tests.sh wordpress_test root '' localhost $WP_VERSION
        - composer update $PREFER_LOWEST --prefer-source
        - composer require --dev 'satooshi/php-coveralls:1.0.*'
      script:
        - mkdir -p build/logs
        - vendor/phpunit/phpunit/phpunit -c phpunit.xml --verbose --coverage-clover build/logs/clover.xml
      after_success:
        - vendor/bin/coveralls -v
    - php: 7.4
      env: WP_VERSION=latest WP_MULTISITE=0
      before_install:
        - phpenv config-rm xdebug.ini
        - yes '' | pecl install pcov
        # Install imagick PHP extension
        - pear config-set preferred_state beta
        - pecl channel-update pecl.php.net
        - yes '' | pecl install imagick
      install:
        - bash bin/install-wp-tests.sh wordpress_test root '' localhost $WP_VERSION
        - composer update $PREFER_LOWEST --prefer-source
        - composer require --dev 'pcov/clobber:^2.0'
        - vendor/bin/pcov clobber
        - composer require --dev 'satooshi/php-coveralls:1.0.*'
      script:
        - mkdir -p build/logs
        - vendor/phpunit/phpunit/phpunit -c phpunit.xml --verbose --coverage-clover build/logs/clover.xml
      after_success:
        - vendor/bin/coveralls -v
    - php: 7.4
      env: WP_VERSION=latest WP_MULTISITE=1
      before_install:
        - phpenv config-rm xdebug.ini
        - yes '' | pecl install pcov
      install:
        - bash bin/install-wp-tests.sh wordpress_test root '' localhost $WP_VERSION
        - composer update $PREFER_LOWEST --prefer-source
        - composer require --dev 'pcov/clobber:^2.0'
        - vendor/bin/pcov clobber
        - composer require --dev 'satooshi/php-coveralls:1.0.*'
      script:
        - mkdir -p build/logs
        - vendor/phpunit/phpunit/phpunit -c phpunit.xml --verbose --coverage-clover build/logs/clover.xml
      after_success:
        - vendor/bin/coveralls -v
    - php: 7.2
      env: WP_VERSION=latest WP_MULTISITE=0
      before_install:
        # Install PHP with WebP support
        - LC_ALL=C.UTF-8 sudo add-apt-repository -y ppa:ondrej/php
        - sudo apt-get update
        - sudo apt-get install -y "php${TRAVIS_PHP_VERSION}-cli" "php${TRAVIS_PHP_VERSION}-gd" "php${TRAVIS_PHP_VERSION}-xml" "php${TRAVIS_PHP_VERSION}-mysql"
        - export PATH="/usr/bin:${PATH}"
        - php --rf imagewebp
      install:
        - bash bin/install-wp-tests.sh wordpress_test root '' localhost $WP_VERSION
        - composer update $PREFER_LOWEST --prefer-source
      script:
        - vendor/phpunit/phpunit/phpunit -c phpunit.xml --verbose ./tests/test-timber-image-towebp.php
      after_success: skip

cache:
  directories:
    - "${HOME}/.composer/cache"

before_install:
  # phpenv config-add myconfig.ini --with-webp-dir
  - phpenv config-rm xdebug.ini

install:
  - bash bin/install-wp-tests.sh wordpress_test root '' localhost $WP_VERSION
  - composer update $PREFER_LOWEST --prefer-source

script:
  - vendor/phpunit/phpunit/phpunit -c phpunit-nocover.xml --verbose

notifications:
  webhooks:
    # https://docs.coveralls.io/parallel-build-webhook
    - 'https://coveralls.io/webhook'
