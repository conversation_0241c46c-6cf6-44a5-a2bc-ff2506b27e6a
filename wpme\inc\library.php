<?php
if (!defined('ABSPATH')) {
    exit();
}

require_once __PROOT__ . '/lib/no-category.php';
if (WPME_ACF) :
require_once __PROOT__ . '/lib/wp-json-add-acf.php';
if (!function_exists('acf_add_options_page')) {
    // Define path and URL to the ACF plugin.
    define('MY_ACF_PATH', __PROOT__ . '/lib/acf/');
    define('MY_ACF_URL', __PURL__ . '/lib/acf/');

    // Include the ACF plugin.
    include_once MY_ACF_PATH . 'acf.php';

    // Customize the url setting to fix incorrect asset URLs.
    add_filter('acf/settings/url', 'my_acf_settings_url');
    function my_acf_settings_url($url)
    {
        return MY_ACF_URL;
    }

    add_filter('acf/settings/path', 'qx_settings_path');
    function qx_settings_path($path)
    {
        // update path
        $path = MY_ACF_PATH;

        // return
        return $path;
    }

    add_filter('acf/settings/dir', 'qx_settings_dir');

    function qx_settings_dir($dir)
    {
        // update path
        $dir = MY_ACF_URL;

        // return
        return $dir;
    }

    add_action('acf/settings/save_json', 'qx_seva_path');

    function qx_seva_path()
    {
        $path = WP_CONTENT_DIR . '/' . $_POST['acf_field_group']['config_save_path'];

        return $path;
    }

    function qx_load_config()
    {
        do_action('acf/include_fields');
    }
    add_action('after_setup_theme', 'qx_load_config', 999999);

    $current_user = wp_get_current_user();
    $admin_arr = array('qiaxing','maxking','webadmin');
    if (in_array($current_user->user_login, $admin_arr)):
        add_filter('acf/settings/show_admin', '__return_true');
    add_filter('acf/settings/remove_wp_meta_box', '__return_true'); else:
        add_filter('acf/settings/show_admin', '__return_false');
    add_filter('acf/settings/remove_wp_meta_box', '__return_false');
    endif;
}
endif;
