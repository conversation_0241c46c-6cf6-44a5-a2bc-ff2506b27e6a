<?php

declare(strict_types=1);

namespace WPCN;

use Timber\Timber;

class cnp {
    public static $isTest;
    public static $siteUrl;
    public static $url;
    protected static $_payinfo;
    protected static $_table_name;
    protected static $_table_refund;

    public function __construct() {
        global $wpdb;
        self::$isTest = 0;
        self::init();
        self::$siteUrl       = get_site_url();
        self::$_table_name   = $wpdb->prefix . 'pay_orders';
        self::$_table_refund = $wpdb->prefix . 'pay_refund';

        date_default_timezone_set('Asia/Shanghai');

        self::$url = [
            'notify'       => home_url() . '/payment/cnp/notify',
            'orderstatus'  => home_url() . '/payment/cnp/orderstatus',
            'paycode'      => home_url() . '/payment/cnp/paycode',
            'payto'        => home_url() . '/payment/cnp/payto',
            'orderquery'   => home_url() . '/payment/cnp/orderquery',
            'exchange'     => home_url() . '/payment/cnp/exchange',
            'refund'       => home_url() . '/payment/cnp/refund',
            'refundlist'   => home_url() . '/payment/cnp/refundlist',
            'refundnotify' => home_url() . '/payment/cnp/refund/notify',
            'export'       => home_url() . '/payment/cnp/order/export',
        ];

        // 路由https://www.u-igroup.com/payment/cnp/callback
        \Routes::map('payment/cnp/notify', [$this, 'notify']);
        \Routes::map('payment/cnp/orderstatus', [$this, 'orderstatus']);
        // \Routes::map('payment/response', [$this, 'pageResponse']);
        \Routes::map('payment/cnp/paycode', [$this, 'payCode']);
        \Routes::map('payment/cnp/payto', [$this, 'pagePayRedirect']);
        \Routes::map('payment/cnp/orderquery', [$this, 'orderQuery']);
        \Routes::map('payment/cnp/exchange', [$this, 'exchange']);
        \Routes::map('payment/cnp/refund', [$this, 'orderRefund']);
        \Routes::map('payment/cnp/refund/list', [$this, 'refundList']);
        \Routes::map('payment/cnp/refund/notify', [$this, 'refundNotify']);
        \Routes::map('payment/cnp/order/export', [$this, 'orderExport']);

        // 订单路由
        \Routes::map('order/list', [$this, 'orderList']);
        \Routes::map('order/delete', [$this, 'orderDelete']);
        \Routes::map('order/clear', [$this, 'orderClear']);

        // 短码[cnp]
        add_shortcode('cnp', [$this, 'shortCode']);

        // 创建数据库表
        add_action('init', [$this, 'createTable']);

        // 添加后台菜单
        add_action('admin_menu', [$this, 'adminMenu']);
    }

    public static function adminMenu() {
        global $order_list_emails;
        $current_user       = wp_get_current_user();
        $current_user_email = $current_user->user_email;

        if (!\in_array($current_user_email, $order_list_emails)) {
            return false;
        }

        add_menu_page(
            '支付订单',
            '支付订单',
            'read',
            'pay_orders',
            [self::class, 'adminPage'],
            'dashicons-cart',
            6
        );

        return true;
    }

    public static function pagePayRedirect(): void {
        $outTransNo = $_GET['outTransNo'];
        if (!$outTransNo) {
            exit('outTransNo is empty');
        }
        $payInfo = get_transient($outTransNo);
        if ($payInfo) {
            // delete_transient($outTransNo);
            $data = ['js' => '<script>var data = ' . $payInfo . ';</script>', 'payinfo' => json_decode($payInfo, true)];
        }
        else {
            $data = ['error' => '支付信息不存在<br> 请返回重新发起支付，重新扫码支付'];
        }

        // ddd($data);
        echo Timber::compile('cnp_alipay.html', ['data' => $data]);
        exit;
    }

    public static function accessTime() {
        date_default_timezone_set('PRC');

        return date(self::$_payinfo['date_type'], time());
    }

    public static function adminPage(): void {
        global $wpdb;
        $table_name = $wpdb->prefix . 'pay_orders';
        $sql        = "SELECT * FROM {$table_name}";
        $orders     = $wpdb->get_results($sql);
        unset($orders->other);
        echo Timber::compile('admin/pay_orders.html', ['orders' => $orders]);
    }

    public static function notify(): void {
        $post_str = file_get_contents('php://input');
        // 将收到的信息保存到log文件中
        $log_file = ABSPATH . 'wp-content/uploads/cnp_log_max.txt';
        file_put_contents($log_file, $post_str . \PHP_EOL, \FILE_APPEND);
        parse_str($post_str, $post_data);
        if (!isset($post_data['resultCode'])) {
            exit('Missing resultCode');
        }
        if ('0000' != $post_data['resultCode']) {
            exit('FAIL1');
        }

        if (!self::validSign($post_data)) {
            exit('FAIL2');
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'pay_orders';
        // 判断订单是否已存在并且已支付
        if (self::orderCheck($post_data['outTransNo'], 1)) {
            exit('SUCCESS');
        }
        // 设置时区为上海
        date_default_timezone_set('PRC');
        $args = [
            'transNo'    => $post_data['transNo'],
            'transType'  => $post_data['transType'],
            'amount'     => number_format((float) $post_data['transAmount'] / 100, 2, '.', ''),
            'updated_at' => date('Y-m-d H:i:s', time()),
            'status'     => 1,
            'other'      => json_encode($post_data),
        ];
        $dd = $wpdb->update($table_name, $args, ['outTransNo' => $post_data['outTransNo']]);

        $log_file = ABSPATH . 'wp-content/uploads/cnp_log_max.txt';
        file_put_contents($log_file, mb_convert_encoding('修改状态：' . $dd, 'UTF-8') . \PHP_EOL, \FILE_APPEND);

        if (false === $dd) {
            exit('FAIL2');
        }
        else {
            $row = $wpdb->get_row($wpdb->prepare('SELECT * FROM ' . $table_name . " WHERE outTransNo = '%s' AND status = 1", $post_data['outTransNo']), ARRAY_A);
            if (isset($row['id'])) {
                $msg_data = [
                    '订单号'  => $row['outTransNo'],
                    '支付方式' => $row['transType'],
                    '待付金额' => $row['fee'] . ' ' . $row['fee_type'],
                    '实付金额' => "{$args['amount']} HKD",
                    '您的名字' => $row['name'],
                    '销售机构' => $row['jigou'],
                    '业务经理' => $row['seller'],
                    '附言信息' => $row['beizhu'],
                ];
                self::sendEmail('【支付通知】有新的客户订单已支付成功。', $msg_data, '有新的客户订单已支付成功，请尽快处理。');
            }
        }
        exit('SUCCESS');
    }

    public static function createTable() {
        global $wpdb;
        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        $table_name      = self::$_table_name;
        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE IF NOT EXISTS {$table_name} (
            `id` int(10) NOT NULL AUTO_INCREMENT,
            `outTransNo` varchar(50) NOT NULL COMMENT '商户交易号',
            `transNo` varchar(50) DEFAULT NULL COMMENT '平台交易号',
            `transType` varchar(50) DEFAULT NULL COMMENT '交易类型',
            `fee` varchar(255) DEFAULT NULL COMMENT '待付金额',
            `fee_type` varchar(255) DEFAULT NULL COMMENT '币种',
            `amount` float DEFAULT NULL COMMENT '支付金额',
            `created_at` datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            `updated_at` datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            `status` tinyint(1) DEFAULT '0' COMMENT '支付状态',
            `beizhu` text DEFAULT NULL,
            `name` varchar(255) DEFAULT NULL,
            `jigou` varchar(255) DEFAULT NULL,
            `seller` varchar(255) DEFAULT NULL,
            `other` text DEFAULT NULL COMMENT '其他信息',
            PRIMARY KEY (`id`,`outTransNo`) USING BTREE
        ) {$charset_collate};";

        dbDelta($sql);

        // 退款记录表
        $table_name = self::$_table_refund;
        $sql        = "CREATE TABLE IF NOT EXISTS {$table_name} (
            `id` int(10) NOT NULL AUTO_INCREMENT,
            `orioutTransNo` varchar(50) NOT NULL COMMENT '原商户交易号',
            `outTransNo` varchar(50) NOT NULL COMMENT '商户交易号',
            `transNo` varchar(50) DEFAULT NULL COMMENT '平台交易号',
            `amount` float DEFAULT NULL COMMENT '金额',
            `operator_name` float DEFAULT NULL COMMENT '操作员',
            `created_at` datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            `updated_at` datetime DEFAULT '0000-00-00 00:00:00' NOT NULL,
            `status` tinyint(1) DEFAULT '0' COMMENT '支付状态',
            `reason` text DEFAULT NULL,
            `other` text DEFAULT NULL COMMENT '其他信息',
            PRIMARY KEY (`id`,`orioutTransNo`,`outTransNo`) USING BTREE
        ) {$charset_collate};";
        dbDelta($sql);

        return true;
    }

    public static function doPost($url, array $array) {
        // url encode request data
        $paramsStr   = self::toUrlParams($array, true);
        $ch          = curl_init();
        $this_header = ['content-type: application/x-www-form-urlencoded;charset=UTF-8'];
        curl_setopt($ch, \CURLOPT_HTTPHEADER, $this_header);
        curl_setopt($ch, \CURLOPT_URL, $url);
        curl_setopt($ch, \CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, \CURLOPT_TIMEOUT, 30);

        curl_setopt($ch, \CURLOPT_POST, 1);
        curl_setopt($ch, \CURLOPT_POSTFIELDS, $paramsStr);
        curl_setopt($ch, \CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, \CURLOPT_SSL_VERIFYHOST, false);

        $output = curl_exec($ch);
        curl_close($ch);

        return $output;
    }

    /**
     * 汇率转换，先转换为人民币，再转换为港币
     */
    public static function exchange(): void {
        $amount = $_POST['fee'];
        $from   = $_POST['from'];
        $to     = 'HKD';

        if ('' == $amount) {
            wp_send_json_error(['msg' => '金额不能为空', 'code' => 999]);
        }

        if ($_POST['nonce'] && !wp_verify_nonce($_POST['nonce'], 'cnp')) {
            wp_send_json_error(['msg' => '验证失效，请刷新页面重试']);
        }

        if ('HKD' == $from) {
            wp_send_json_success(['amount' => $amount]);
        }
        elseif ('CNY' != $from) {
            // 转换为人民币
            $to_CNY_rate = self::getRate($from);
            if (!$to_CNY_rate) {
                wp_send_json_error(['msg' => '获取汇率失败', 'code' => 999]);
            }
            $amount *= ($to_CNY_rate / 100);
        }

        // 转换为港币
        $to_HKD_rate = self::getRate($to);
        if (!$to_HKD_rate) {
            wp_send_json_error(['msg' => '获取汇率失败', 'code' => 999]);
        }
        $amount *= (100 / $to_HKD_rate + self::getRateFix($from));

        // ddd([$amount, $to_HKD_rate, $from, $to]);
        wp_send_json_success(['amount' => round($amount, 2), 'rate' => 100 / $to_HKD_rate, 'from' => $from, 'to' => $to]);
    }

    /**
     * 获取汇率,兑RMB的汇率.
     *
     * @param string $code 货币代码
     */
    public static function getRate(string $code): float {
        $host    = 'https://ali-waihui.showapi.com';
        $path    = '/waihui-list';
        $appcode = gop('appcode');
        if (!$appcode) {
            return 0;
        }

        $args = [
            'headers' => [
                'Authorization' => 'APPCODE ' . $appcode,
            ],
        ];
        $querys = "code={$code}";
        $url    = $host . $path . '?' . $querys;

        $response = wp_remote_get($url, $args);

        if (is_wp_error($response)) {
            return 0;
        }
        else {
            $body = wp_remote_retrieve_body($response);
            $body = json_decode($body, true);
            if (0 == $body['showapi_res_code']) {
                $list = $body['showapi_res_body']['list'];
                $type = gop('huilv');

                return (float) $list[0][$type];
            }
            else {
                return 0;
            }
        }
    }

    public static function getRateFix($code): float {
        $data = gop('bizhong');
        foreach ($data as $key => $value) {
            if ($value['code'] == $code) {
                return (float) $value['fix'];
            }
        }

        return 0;
    }

    public static function init(): void {
        if (!self::$_payinfo) {
            if (!self::$isTest) {
                self::payinfo();
            }
            else {
                self::testPayInfo();
            }
        }
    }

    public static function orderCheck($outTransNo, $status = 0) {
        global $wpdb;
        $where = $status ? " AND status = {$status}" : '';
        $sql   = $wpdb->prepare('SELECT * FROM ' . self::$_table_name . " WHERE outTransNo = '%s'" . $where, $outTransNo);
        $order = $wpdb->get_row($sql);

        return null !== $order;
    }

    public static function orderList(): void {
        $post_data = file_get_contents('php://input');
        $post_data = json_decode($post_data, true);
        // 每页20条数据
        $perpage = 20;
        $paged   = $post_data['pageNo'] ?? 1;
        $offset  = ($paged - 1) * $perpage;

        global $wpdb;
        $table_name = self::$_table_name;

        $sql    = "SELECT * FROM {$table_name} ORDER BY id DESC LIMIT {$offset}, {$perpage}";
        $orders = $wpdb->get_results($sql);

        foreach ($orders as $key => $order) {
            if ($order->other) {
                $other                   = json_decode($order->other);
                $orders[$key]->new_amount = number_format((float)$other->paymentAmount /100, 2, '.', '');
            }
            $orders[$key]->can_refund     = self::canRefundAmount($order->outTransNo);
            $refund_count                 = $wpdb->get_var($wpdb->prepare('SELECT SUM(amount) FROM ' . self::$_table_refund . " WHERE orioutTransNo = '%s'", $order->outTransNo));
            $orders[$key]->refunded_count = number_format((float) $refund_count, 2, '.', '');
        }

        $sql   = "SELECT COUNT(*) FROM {$table_name}";
        $total = $wpdb->get_var($sql);

        $totalpage = ceil($total / $perpage);
        exit(json_encode(['data' => ['rows' => $orders, 'total' => $total, 'totalpage' => $totalpage], 'status' => 0], \JSON_UNESCAPED_SLASHES | \JSON_UNESCAPED_UNICODE));
        // wp_send_json(['rows' => $orders, 'total' => $total, 'totalpage' => $totalpage], 0);
    }

    public static function orderDelete() {
        $id = $_GET['id'];
        if (!$id) {
            return wp_send_json_error(['msg' => 'ID不能为空']);
        }
        global $wpdb;
        $table_name = self::$_table_name;
        $wpdb->delete($table_name, ['id' => $id]);

        return wp_send_json([], 0);
    }

    /**
     * 删除未支付订单.不包含今天的订单.
     */
    public static function orderClear() {
        global $wpdb;
        $table_name = self::$_table_name;

        $sql = "DELETE FROM {$table_name} WHERE status = 0 AND created_at < '" . date('Y-m-d H:i:s', strtotime('-1 day')) . "'";
        $aa  = $wpdb->query($sql);

        return wp_send_json([
            'msg'    => '清理完成',
            'status' => 0,
        ]);
        exit;
    }

    /**
     * order query.
     */
    public static function orderQuery(): void {
        $orderId = $_GET['orderid'];

        if (!isset($orderId)) {
            wp_send_json_error(['msg' => '订单号不能为空']);
        }

        $reNo                    = date('YmdHis', time()) . random_int(1000, 9999);
        $params                  = [];
        $params['requestNo']     = md5($reNo);
        $params['version']       = self::$_payinfo['version'];
        $params['accessCode']    = self::$_payinfo['access_code'];
        $params['transType']     = 'TRANS_QUERY';
        $params['signType']      = self::$_payinfo['sign_type'];
        $params['mchNo']         = self::$_payinfo['merchant_id'];
        $params['oriOutTransNo'] = $orderId;
        $params['signature']     = self::signSHA256RSA($params);
        $ret                     = self::doPost(self::$_payinfo['host_url'], $params);

        exit($ret);
    }

    /**
     * 订单退款.
     */
    public static function orderRefund() {
        global $wpdb;
        $post_str  = file_get_contents('php://input');
        $post_data = json_decode($post_str, true);
        if (!isset($post_data['oriOutTransNo'])) {
            wp_send_json(['msg' => '订单号不能为空', 'status' => 10]);
        }

        $order = $wpdb->get_row($wpdb->prepare('SELECT * FROM ' . self::$_table_name . " WHERE outTransNo = '%s'", $post_data['oriOutTransNo']), ARRAY_A);

        if (!$order) {
            wp_send_json(['msg' => '订单不存在', 'status' => 20]);
        }
        $can_refund_amount = self::canRefundAmount($post_data['oriOutTransNo']);
        if (!$can_refund_amount) {
            wp_send_json(['msg' => '可退金额为0，请刷新重试', 'status' => 25]);
        }

        $params    = [];
        $transtype = $order['transType'];
        // 如果包含alipay,则为支付宝退款
        if (str_contains($transtype, 'ALIPAY')) {
            $params['transType'] = 'ALIPAY_RETURN';
        }
        else {
            $params['transType'] = 'WXPAY_RETURN';
        }

        // 判断退款金额是否大于支付金额
        if ($post_data['refund_amount'] > $can_refund_amount) {
            return wp_send_json(['msg' => '退款金额不能大于可退金额', 'status' => 30]);
        }

        $reNo                    = date('YmdHis', time()) . random_int(1000, 9999);
        $params['requestNo']     = md5($reNo);
        $params['version']       = self::$_payinfo['version'];
        $params['accessCode']    = self::$_payinfo['access_code'];
        $params['signType']      = self::$_payinfo['sign_type'];
        $params['mchNo']         = self::$_payinfo['merchant_id'];
        $params['outTransNo']    = $reNo;
        $params['oriOutTransNo'] = $post_data['oriOutTransNo'];
        $params['transAmount']   = $post_data['refund_amount'] * 100; // 申请退货金额，单位为分
        $params['currency']      = 'HKD'; // 币种
        $params['refundReson']   = $post_data['reson']; // 退货原因
        $params['notifyUrl']     = self::$url['refundnotify'];
        $params['signature']     = self::signSHA256RSA($params);
        // exit(json_encode($params));
        $ret = self::doPost(self::$_payinfo['host_url'], $params);

        $res = json_decode($ret, true);
        if ('0000' == $res['returnCode'] && '0000' == $res['resultCode']) {
            $user = wp_get_current_user();

            $insert_data = [
                'orioutTransNo' => $post_data['oriOutTransNo'],
                'outTransNo'    => $reNo,
                'transNo'       => $res['transNo'],
                'amount'        => $post_data['refund_amount'],
                'operator_name' => $user->display_name,
                'created_at'    => date('Y-m-d H:i:s', time()),
                'status'        => 1,
                'reason'        => $post_data['reason'],
                'other'         => json_encode($res),
            ];
            $wpdb->insert(self::$_table_refund, $insert_data);

            if (!$wpdb->insert_id) {
                wp_send_json(['msg' => '退款记录创建失败', 'status' => 35]);
            }

            $msg_data = [
                '商户交易号：' => $post_data['oriOutTransNo'],
                '港币金额：'  => $post_data['refund_amount'],
                '支付状态：'  => '退款成功',
                '可退金额：'  => self::canRefundAmount($post_data['oriOutTransNo']),
                '已退金额：'  => self::refundAmount($post_data['oriOutTransNo']),
                '客户名称：'  => $order['name'],
                '销售机构：'  => $order['jigou'],
                '业务经理：'  => $order['seller'],
                '备注：'    => $order['beizhu'],
                '退款原因：'  => $post_data['reason'],
                '退款时间：'  => date('Y-m-d H:i:s', time()),
                '退款操作人：' => $user->display_name,
            ];

            self::sendEmail('【退款通知】有新的客户订单已退款成功', $msg_data, '有新的客户订单已退款成功，退款详情如下：');

            wp_send_json(['msg' => '退款成功', 'status' => 0]);
        }
        else {
            wp_send_json(['msg' => '<h4>退款失败</h4>' . \PHP_EOL . "returnCode:{$res['returnCode']}" . \PHP_EOL . "returnMsg:{$res['returnMsg']}" . \PHP_EOL . "resultCode:{$res['resultCode']}" . \PHP_EOL . "resultMsg:{$res['resultMsg']}" . \PHP_EOL . '
            ', 'status'         => 40]);
        }
    }

    public static function refundNotify(): void {
        $post_str = file_get_contents('php://input');
        // 将收到的信息保存到log文件中
        $log_file = ABSPATH . 'wp-content/uploads/cnp_log_max.txt';
        file_put_contents($log_file, 'refund Notify：' . \PHP_EOL . $post_str . \PHP_EOL, \FILE_APPEND);
        parse_str($post_str, $post_data);
        ddd($post_data);
        exit;
        if (!isset($post_data['resultCode'])) {
            exit('Missing resultCode');
        }
        if ('0000' != $post_data['resultCode']) {
            exit('FAIL1');
        }

        if (!self::validSign($post_data)) {
            exit('FAIL2');
        }

        global $wpdb;
        $table_name = $wpdb->prefix . 'pay_orders';
        // 判断订单是否已存在并且已支付
        if (self::orderCheck($post_data['outTransNo'], 1)) {
            exit('SUCCESS');
        }
        // 设置时区为上海
        date_default_timezone_set('PRC');
        $args = [
            'transNo'    => $post_data['transNo'],
            'transType'  => $post_data['transType'],
            'amount'     => round($post_data['transAmount'] / 100, 2),
            'updated_at' => date('Y-m-d H:i:s', time()),
            'status'     => 1,
            'other'      => json_encode($post_data),
        ];
        $dd = $wpdb->update($table_name, $args, ['outTransNo' => $post_data['outTransNo']]);

        $log_file = ABSPATH . 'wp-content/uploads/cnp_log_max.txt';
        file_put_contents($log_file, mb_convert_encoding('修改状态：' . $dd, 'UTF-8') . \PHP_EOL, \FILE_APPEND);

        if (false === $dd) {
            exit('FAIL2');
        }
        else {
        }
    }

    /**
     * 订单可退金额.
     */
    public static function canRefundAmount(string $transNo): float {
        // 获取订单信息
        global $wpdb;
        $table_name = $wpdb->prefix . 'pay_orders';
        $order      = $wpdb->get_row($wpdb->prepare('SELECT * FROM ' . $table_name . " WHERE outTransNo = '%s' and status = 1", $transNo), ARRAY_A);

        if (!$order) {
            return 0;
        }

        // 查询退款记录，合计已退金额
        $table_refund  = $wpdb->prefix . 'pay_refund';
        $sql           = $wpdb->prepare('SELECT SUM(amount) FROM ' . $table_refund . " WHERE orioutTransNo = '%s'", $transNo);
        $refund_amount = $wpdb->get_var($sql);

        // 判断可退金额，可退金额 = 已支付金额 - 已退金额 - (手续费 = 已支付金额*1.6%)
        $can_refund_amount = $order['amount'] - $refund_amount - ($order['amount'] * 0.016);
        // 四舍五入
        $can_refund_amount = round($can_refund_amount, 2);

        return $can_refund_amount;
        exit;
    }

    /**
     * 查询订单已退金额.
     */
    public static function refundAmount(string $transNo): float {
        // 查询退款记录，合计已退金额
        global $wpdb;
        $table_refund  = $wpdb->prefix . 'pay_refund';
        $sql           = $wpdb->prepare('SELECT SUM(amount) FROM ' . $table_refund . " WHERE orioutTransNo = '%s'", $transNo);
        $refund_amount = $wpdb->get_var($sql);

        return (float) $refund_amount;
    }

    public static function refundList(): void {
        $post_str  = file_get_contents('php://input');
        $post_data = json_decode($post_str, true);
        if (!isset($post_data['oriOutTransNo'])) {
            wp_send_json_error(['msg' => '订单号不能为空']);
        }

        global $wpdb;
        $table_name = self::$_table_refund;
        $sql        = $wpdb->prepare('SELECT *, amount AS refunded_amount FROM ' . $table_name . " WHERE orioutTransNo = '%s'", $post_data['oriOutTransNo']);
        $refunds    = $wpdb->get_results($sql);

        wp_send_json(['rows' => $refunds], 0);
    }

    /**
     * 订单状态查询.
     */
    public static function orderstatus(): string {
        global $wpdb;
        $post_data = $_POST;
        $data      = $post_data['data'];
        // wpnonce
        if (!wp_verify_nonce($post_data['nonce'], 'cnp')) {
            exit('FAIL2');
        }

        if (!isset($data['outTransNo'])) {
            exit('FAIL1');
        }

        // 判断订单是否已支付
        if (self::orderCheck($data['outTransNo'], 1)) {
            $status = true;
        }
        else {
            $status = false;
        }

        return wp_send_json_success(['status' => $status]);
    }

    public static function payCode(): void {
        $post_data = $_POST;
        if (!isset($post_data['amount']) || !is_numeric($post_data['amount'])) {
            wp_send_json_error(['msg' => '金额不能为空'], 550);
        }
        // 验证码nonce
        if (!wp_verify_nonce($post_data['nonce'], 'cnp')) {
            wp_send_json_error(['msg' => '验证失效，请刷新页面重试'], 552);
        }

        $params = self::payParams($post_data);
        $ret    = self::doPost(self::$_payinfo['host_url'], $params);
        // 输出utf-8
        header('Content-Type:application/json; charset=utf-8');

        $res = json_decode($ret, true);

        $res_data = [];
        if ('0000' == $res['returnCode']) {
            if (!('0000' == $res['resultCode'] || 'P000' == $res['resultCode'])) {
                wp_send_json_error(['msg' => $res['resultMsg'], 'code' => $res['resultCode'], 'data' => $res_data]);
            }
            if (0 == $post_data['paytype']) {
                // 微信支付
                if (str_contains($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger')) {
                    $codeUrl = $res['payInfo'];
                }
                else {
                    $codeUrl = $res['codeUrl'];
                }
            }
            else {
                // 将payInfo存入到options表中（瞬存）,并在10分钟后自动删除
                set_transient('cnp_' . $params['outTransNo'], $res['payInfo'], 10 * MINUTE_IN_SECONDS);
                $codeUrl = home_url() . '/payment/cnp/payto?outTransNo=cnp_' . $params['outTransNo'];
            }

            $res_data = [
                'requestNo'  => $params['requestNo'],
                'outTransNo' => $params['outTransNo'],
                'fee'        => $post_data['fee'],
                'fee_type'   => $post_data['from'],
                'amount'     => $post_data['amount'],
                'beizhu'     => $post_data['beizhu'],
                'name'       => $post_data['name'],
                'jigou'      => $post_data['jigou'],
                'seller'     => $post_data['seller'],
                'paytype'    => $post_data['paytype'] ? 'ALIPAY' : 'WECHAT',
            ];
            self::insert($res_data);

            wp_send_json_success(['url' => $codeUrl, 'fee' => $post_data['fee'], 'data' => $res_data]);
        }
        else {
            wp_send_json_error(['msg' => $res['resultMsg'], 'code' => $res['resultCode'], 'data' => $res_data]);
        }
    }

    public static function insert($data): void {
        global $wpdb;
        $table_name = $wpdb->prefix . 'pay_orders';

        $insert_data = [
            'outTransNo' => $data['outTransNo'],
            'fee'        => $data['fee'],
            'fee_type'   => $data['fee_type'],
            'transType'  => $data['paytype'] ? 'ALIPAY' : 'WECHAT',
            'amount'     => $data['amount'],
            'created_at' => date('Y-m-d H:i:s', time()),
            'status'     => 0,
            'beizhu'     => $data['beizhu'],
            'name'       => $data['name'],
            'jigou'      => $data['jigou'],
            'seller'     => $data['seller'],
        ];

        $aa = $wpdb->insert($table_name, $insert_data);

        $post_id = $wpdb->insert_id;
        if (!$post_id) {
            wp_send_json_error(['msg' => '支付创建失败', 'error' => $post_id]);
        }
    }

    public static function payParams($data) {
        // 将当前时间修改为北京时间
        date_default_timezone_set('PRC');
        $reNo                  = date('YmdHis', time()) . random_int(1000, 9999);
        $params                = [];
        $params['requestNo']   = md5($reNo);
        $params['version']     = self::$_payinfo['version'];
        $params['accessCode']  = self::$_payinfo['access_code'];
        $params['transType']   = 'ALIPAY_H5';
        $params['signType']    = self::$_payinfo['sign_type'];
        $params['mchNo']       = self::$_payinfo['merchant_id'];
        $params['outTransNo']  = $reNo;                 // 商户交易号
        $params['transAmount'] = $data['amount'] * 100; // 交易金额,转换为分
        $params['currency']    = 'HKD';
        $params['notifyUrl']   = self::$_payinfo['notifyUrl'];
        $params['referUrl']    = site_url();

        if ('1' == $data['paytype']) {
            $params['paymentInstitution'] = 'ALIPAYCN';
        }
        else {
            // 判断是不是来自微信
            if (str_contains($_SERVER['HTTP_USER_AGENT'], 'MicroMessenger')) {
                $params['transType'] = 'WXPAY_BRANCH_MP';
            }
            else {
                $params['transType'] = 'WXPAY_SCANCODE';
            }
        }

        $params['signature'] = self::signSHA256RSA($params);

        return $params;
    }

    public static function payinfo(): void {
        $payinfo = [
            'version'   => 'V2.0.0',
            'date_type' => 'YmdHis',
            'host_url'  => 'https://oats.allinpay.com/gateway/pay/consumeTrans',
            'sign_type' => 'RSA2',
            'notifyUrl' => home_url() . '/payment/cnp/notify',
        ];

        $payinfo['access_code'] = trim((string) get_field('access_code', 'option'));
        $payinfo['merchant_id'] = trim((string) get_field('merchant_id', 'option'));
        $payinfo['private_key'] = trim((string) get_field('private_key', 'option'));
        $payinfo['public_key']  = trim((string) get_field('public_key', 'option'));
        $payinfo['public_key2'] = trim((string) get_field('public_key2', 'option'));

        self::$_payinfo = $payinfo;
    }

    public static function orderExport(): void {
        global $wpdb;
        $table_name = $wpdb->prefix . 'pay_orders';
        $sql        = "SELECT * FROM {$table_name}";
        $orders     = $wpdb->get_results($sql);

        foreach ($orders as $key => $order) {
            $orders[$key]->can_refund     = self::canRefundAmount($order->outTransNo);
            $refund_count                 = self::refundAmount($order->outTransNo);
            $orders[$key]->refunded_count = number_format((float) $refund_count, 2, '.', '');
        }

        wp_send_json(['data' => [
            'count' => \count($orders),
            'rows'  => $orders,
        ], 'status' => 0]);
    }

    public static function shortCode() {
        $nonce = wp_create_nonce('cnp');

        return Timber::compile('cnp.html', ['payinfo' => self::$_payinfo, 'URL' => get_stylesheet_directory_uri(), 'nonce' => $nonce, 'jigou' => gop('jigou'), 'bizhong' => gop('bizhong')]);
    }

    public static function sendEmail($title, $msg_data, $msg = '') {
        // 发送邮件
        $to      = gop('mailto', get_option('admin_email'));
        $subject = $title;
        $message = Timber::compile('cnp_email.html', ['data' => $msg_data, 'title' => $title, 'msg' => $msg]);
        $headers = ['Content-Type: text/html; charset=UTF-8'];

        return wp_mail($to, $subject, $message, $headers);
    }

    /**
     * sign request data with SHA256RSA algorithm.
     *
     * @param array $array request data
     *
     * @return string signature
     */
    public static function signSHA256RSA(array $array): string {
        ksort($array);
        $preSign      = self::toUrlParams($array, false);
        $privateKeyId = openssl_pkey_get_private(self::$_payinfo['private_key']);

        $encrypted = '';
        openssl_sign($preSign, $encrypted, $privateKeyId, \OPENSSL_ALGO_SHA256);

        return base64_encode($encrypted);
    }

    public static function testPayInfo(): void {
        $payinfo = [
            'version'   => 'V2.0.0',
            'date_type' => 'YmdHis',
            'host_url'  => 'https://test.allinpayhk.com/gateway/pay/consumeTrans',
            'sign_type' => 'RSA2',
            'notifyUrl' => self::$siteUrl . '/payment/cnp/notify',
        ];

        $payinfo['access_code'] = 85200258;
        $payinfo['merchant_id'] = 852999958120566;
        $payinfo['private_key'] = '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************';
        $payinfo['public_key'] = '-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsNGWmxYl6uYA/X3k1OOY
nMlLvmgJpYMvahxDLUc9CEXbaqMoMMuLbsvfR6Zf3JIeYCjjfgNVDJTsJY/HWWbp
HEd4GvQAgv6wywsQ8AJqIHe3fM3B8iwS3XIxZM9fs92lU+mHuVHQdMvciQyaB3iw
3IvBBzCEgyqSFcGBrlQOGf1x7fZZKY9RH3EDxqzE+Zrs27BjE8T3sNvUCKcfWGhQ
GKX80jcqLEnFBl9CIlgL4TRSksQ1U4GhOY0/4Db6UsmbTAQeG2plWgbJ0l0khJ2O
DYqTtDujl4zN3tGXQ2gwCErxqBbukIFMkT+jS1lxrBeGGrvuOXTz408tDbAsJnbU
YwIDAQAB
-----END PUBLIC KEY-----';

        self::$_payinfo = $payinfo;
    }

    public static function toUrlParams(array $array, $isUrlEncode) {
        $buff = '';
        foreach ($array as $k => $v) {
            if ('' != $v && !\is_array($v)) {
                $buff .= $k . '=';
                if ($isUrlEncode) {
                    $buff .= urlencode((string) $v);
                }
                else {
                    $buff .= $v;
                }
                $buff .= '&';
            }
        }

        return trim($buff, '&');
    }

    public static function validSign(array $array) {
        if (!isset($array['signature'])) {
            return false;
        }

        $sign = $array['signature'];
        unset($array['signature']);

        $publicKeyId = openssl_get_publickey(self::$_payinfo['public_key2']);
        ksort($array);
        $preSign = self::toUrlParams($array, false);
        $ok      = openssl_verify($preSign, base64_decode($sign), $publicKeyId, \OPENSSL_ALGO_SHA256);

        return 1 == $ok ? true : false;
    }
}

new CNP();
/* acf字段 */
if (\function_exists('acf_add_local_field_group')) {
    acf_add_local_field_group([
        'key'    => 'group_661f90f232b24',
        'title'  => '支付相关配置',
        'fields' => [
            [
                'key'               => 'field_661f90f9a2f75',
                'label'             => 'ACCESS_CODE',
                'name'              => 'access_code',
                'type'              => 'text',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => [
                    'width' => '50',
                    'class' => '',
                    'id'    => '',
                ],
                'default_value' => '',
                'placeholder'   => '',
                'prepend'       => '',
                'append'        => '',
                'maxlength'     => '',
            ],
            [
                'key'               => 'field_661f92eba2f76',
                'label'             => 'MERCHANT_ID',
                'name'              => 'merchant_id',
                'type'              => 'text',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => [
                    'width' => '50',
                    'class' => '',
                    'id'    => '',
                ],
                'default_value' => '',
                'placeholder'   => '',
                'prepend'       => '',
                'append'        => '',
                'maxlength'     => '',
            ],
            [
                'key'               => 'field_661f92f5a2f77',
                'label'             => 'PUBLIC_KEY',
                'name'              => 'public_key',
                'type'              => 'textarea',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => [
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ],
                'default_value' => '',
                'placeholder'   => '',
                'maxlength'     => '',
                'rows'          => '',
                'new_lines'     => '',
            ],
            [
                'key'               => 'field_661f930da2f78',
                'label'             => 'PRIVATE_KEY',
                'name'              => 'private_key',
                'type'              => 'textarea',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => [
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ],
                'default_value' => '',
                'placeholder'   => '',
                'maxlength'     => '',
                'rows'          => '',
                'new_lines'     => '',
            ],
            [
                'key'               => 'field_663f28f9c3085',
                'label'             => 'PUBLIC_KEY (验签)',
                'name'              => 'public_key2',
                'type'              => 'textarea',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => [
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ],
                'default_value' => '',
                'placeholder'   => '',
                'maxlength'     => '',
                'rows'          => '',
                'new_lines'     => '',
            ],
            [
                'key'               => 'field_663f2d143502f',
                'label'             => '接收邮箱',
                'name'              => 'mailto',
                'type'              => 'text',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => [
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ],
                'default_value' => '',
                'placeholder'   => '',
                'prepend'       => '',
                'append'        => '',
                'maxlength'     => '',
            ],
            [
                'key'               => 'field_66389539be697',
                'label'             => '汇率AppCode',
                'name'              => 'appcode',
                'type'              => 'text',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => [
                    'width' => '50',
                    'class' => '',
                    'id'    => '',
                ],
                'default_value' => '',
                'placeholder'   => '',
                'prepend'       => '',
                'append'        => '',
                'maxlength'     => '',
            ],
            [
                'key'               => 'field_663c9723e489a',
                'label'             => '汇率类型',
                'name'              => 'huilv',
                'type'              => 'select',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => [
                    'width' => '50',
                    'class' => '',
                    'id'    => '',
                ],
                'choices' => [
                    'hui_in'   => '现汇买入价',
                    'chao_in'  => '现钞买入价',
                    'hui_out'  => '现汇卖出价',
                    'chao_out' => '现钞卖出价',
                    'zhesuan'  => '中间价',
                ],
                'default_value' => [
                    0 => 'hui_in',
                ],
                'allow_null'    => 0,
                'multiple'      => 0,
                'ui'            => 1,
                'ajax'          => 0,
                'return_format' => 'value',
                'placeholder'   => '',
            ],
            [
                'key'               => 'field_666a3fa10fe75',
                'label'             => '币种',
                'name'              => 'bizhong',
                'type'              => 'repeater',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => [
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ],
                'collapsed'    => 'field_666a3fd30fe76',
                'min'          => 0,
                'max'          => 0,
                'layout'       => 'table',
                'button_label' => '添加',
                'sub_fields'   => [
                    [
                        'key'               => 'field_666a3fd30fe76',
                        'label'             => '名称',
                        'name'              => 'name',
                        'type'              => 'text',
                        'instructions'      => '',
                        'required'          => 0,
                        'conditional_logic' => 0,
                        'wrapper'           => [
                            'width' => '',
                            'class' => '',
                            'id'    => '',
                        ],
                        'default_value' => '',
                        'placeholder'   => '',
                        'prepend'       => '',
                        'append'        => '',
                        'maxlength'     => '',
                    ],
                    [
                        'key'               => 'field_666a3fe50fe77',
                        'label'             => '代码',
                        'name'              => 'code',
                        'type'              => 'text',
                        'instructions'      => '',
                        'required'          => 0,
                        'conditional_logic' => 0,
                        'wrapper'           => [
                            'width' => '',
                            'class' => '',
                            'id'    => '',
                        ],
                        'default_value' => '',
                        'placeholder'   => '',
                        'prepend'       => '',
                        'append'        => '',
                        'maxlength'     => '',
                    ],
                    [
                        'key'               => 'field_666a3ff70fe78',
                        'label'             => '修正值',
                        'name'              => 'fix',
                        'type'              => 'text',
                        'instructions'      => '',
                        'required'          => 0,
                        'conditional_logic' => 0,
                        'wrapper'           => [
                            'width' => '',
                            'class' => '',
                            'id'    => '',
                        ],
                        'default_value' => '',
                        'placeholder'   => '',
                        'prepend'       => '',
                        'append'        => '',
                        'maxlength'     => '',
                    ],
                ],
            ],
            [
                'key'               => 'field_663c9879ad9a9',
                'label'             => '销售机构',
                'name'              => 'jigou',
                'type'              => 'repeater',
                'instructions'      => '',
                'required'          => 0,
                'conditional_logic' => 0,
                'wrapper'           => [
                    'width' => '',
                    'class' => '',
                    'id'    => '',
                ],
                'collapsed'    => '',
                'min'          => 0,
                'max'          => 0,
                'layout'       => 'table',
                'button_label' => '',
                'sub_fields'   => [
                    [
                        'key'               => 'field_663c992bad9aa',
                        'label'             => '机构名称',
                        'name'              => 'name',
                        'type'              => 'text',
                        'instructions'      => '',
                        'required'          => 0,
                        'conditional_logic' => 0,
                        'wrapper'           => [
                            'width' => '',
                            'class' => '',
                            'id'    => '',
                        ],
                        'default_value' => '',
                        'placeholder'   => '',
                        'prepend'       => '',
                        'append'        => '',
                        'maxlength'     => '',
                    ],
                ],
            ],
        ],
        'location' => [
            [
                [
                    'param'    => 'options_page',
                    'operator' => '==',
                    'value'    => 'pay-settings',
                ],
            ],
        ],
        'menu_order'            => 0,
        'position'              => 'normal',
        'style'                 => 'default',
        'label_placement'       => 'top',
        'instruction_placement' => 'label',
        'hide_on_screen'        => '',
        'active'                => true,
        'description'           => '',
        'config_save_path'      => 'default',
    ]);
}