build:
    nodes:
        analysis:
            dependencies:
                before:
                    - composer require --dev johnpbloch/wordpress
            tests:
                override:
                    - php-scrutinizer-run

filter:
    excluded_paths:
        - 'tests/*'
        - 'bin/*'
        
checks:
    php:
        fix_php_opening_tag: false
        remove_php_closing_tag: false
        one_class_per_file: false
        side_effects_or_types: false
        no_mixed_inline_html: false
        require_braces_around_control_structures: false
        php5_style_constructor: true
        no_global_keyword: false
        avoid_usage_of_logical_operators: true
        psr2_class_declaration: false
        no_underscore_prefix_in_properties: false
        no_underscore_prefix_in_methods: false
        blank_line_after_namespace_declaration: true
        single_namespace_per_use: false
        psr2_switch_declaration: false
        psr2_control_structure_declaration: false
        avoid_superglobals: false
        security_vulnerabilities: false
        no_exit: false
        return_doc_comments: true
        parameter_doc_comments: true
        uppercase_constants: true
        avoid_perl_style_comments: true

coding_style:
    php:
        indentation:
            general:
                use_tabs: true
        spaces:
            within:
                function_declaration: true
                if: true
                for: true
                while: true
                switch: true
                catch: true
        braces:
            classes_functions:
                class: end-of-line
                function: end-of-line
            if:
                opening: end-of-line
                always: true
            for:
                opening: end-of-line
            while:
                opening: end-of-line
            do_while:
                opening: end-of-line
        upper_lower_casing:
            keywords:
                general: lower
            constants:
                true_false_null: lower

