<?php

$user = _wp_get_current_user();
$admin_arr = array('qiaxing','hy','wpme_demo');
//移除 WP_Head 无关紧要的代码
remove_action('wp_head', 'wp_generator'); //删除 head 中的 WP 版本号
foreach (['rss2_head', 'commentsrss2_head', 'rss_head', 'rdf_header', 'atom_head', 'comments_atom_head', 'opml_head', 'app_head'] as $action) {
    remove_action($action, 'the_generator');
}

remove_action('wp_head', 'rsd_link'); //删除 head 中的 RSD LINK
remove_action('wp_head', 'wlwmanifest_link'); //删除 head 中的 Windows Live Writer 的适配器？

remove_action('wp_head', 'feed_links_extra', 3); //删除 head 中的 Feed 相关的link
//remove_action( 'wp_head', 'feed_links', 2 );

remove_action('wp_head', 'index_rel_link'); //删除 head 中首页，上级，开始，相连的日志链接
remove_action('wp_head', 'parent_post_rel_link', 10);
remove_action('wp_head', 'start_post_rel_link', 10);
remove_action('wp_head', 'adjacent_posts_rel_link_wp_head', 10);

remove_action('wp_head', 'wp_shortlink_wp_head', 10, 0); //删除 head 中的 shortlink
remove_action('wp_head', 'rest_output_link_wp_head', 10); // 删除头部输出 WP RSET API 地址

remove_action('template_redirect', 'wp_shortlink_header', 11); //禁止短链接 Header 标签。
remove_action('template_redirect', 'rest_output_link_header', 11); // 禁止输出 Header Link 标签。

//禁用日志修订功能
add_action('wp_print_scripts', 'fanly_no_autosave');
function fanly_no_autosave()
{
    wp_deregister_script('autosave');
}

add_filter('wp_revisions_to_keep', 'fanly_wp_revisions_to_keep', 10, 2);
function fanly_wp_revisions_to_keep($num, $post)
{
    return 0;
}

//移除 admin bar

// add_filter('show_admin_bar', '__return_false');

//禁用 XML-RPC 接口

add_filter('xmlrpc_enabled', '__return_false');
remove_action('xmlrpc_rsd_apis', 'rest_output_rsd');

//彻底关闭 pingback
add_filter('xmlrpc_methods', function ($methods) {
    $methods['pingback.ping']                    = '__return_false';
    $methods['pingback.extensions.getPingbacks'] = '__return_false';

    return $methods;
});

//禁用 pingbacks, enclosures, trackbacks
remove_action('do_pings', 'do_all_pings', 10);

//去掉 _encloseme 和 do_ping 操作。
remove_action('publish_post', '_publish_post_hook', 5);

//前台不加载语言包
/*
$wpjam_locale = get_locale();

add_filter('language_attributes', function ($language_attributes) use ($wpjam_locale) {
if (function_exists('is_rtl') && is_rtl()) {
$attributes[] = 'dir="rtl"';
}

if ($wpjam_locale) {
if (get_option('html_type') == 'text/html') {
$attributes[] = 'lang="' . $wpjam_locale . '"';
}

if (get_option('html_type') != 'text/html') {
$attributes[] = 'xml:lang="' . $wpjam_locale . '"';
}
}

return implode(' ', $attributes);
});

add_filter('locale', function ($locale) {
return (is_admin()) ? 'zh_CN' : 'en_US';
}); */

// 屏蔽 Emoji

remove_action('admin_print_scripts', 'print_emoji_detection_script');
remove_action('admin_print_styles', 'print_emoji_styles');

remove_action('wp_head', 'print_emoji_detection_script', 7);
remove_action('wp_print_styles', 'print_emoji_styles');

remove_action('embed_head', 'print_emoji_detection_script');

remove_filter('the_content_feed', 'wp_staticize_emoji');
remove_filter('comment_text_rss', 'wp_staticize_emoji');
remove_filter('wp_mail', 'wp_staticize_emoji_for_email');

add_filter('tiny_mce_plugins', function ($plugins) {
    return array_diff($plugins, array('wpemoji'));
});

add_filter('emoji_svg_url', '__return_false');

// 移除头部 wp-json 标签和 HTTP header 中的 link
remove_action('wp_head', 'rest_output_link_wp_head', 10);
remove_action('template_redirect', 'rest_output_link_header', 11);

remove_action('xmlrpc_rsd_apis', 'rest_output_rsd');

remove_action('auth_cookie_malformed', 'rest_cookie_collect_status');
remove_action('auth_cookie_expired', 'rest_cookie_collect_status');
remove_action('auth_cookie_bad_username', 'rest_cookie_collect_status');
remove_action('auth_cookie_bad_hash', 'rest_cookie_collect_status');
remove_action('auth_cookie_valid', 'rest_cookie_collect_status');
remove_filter('rest_authentication_errors', 'rest_cookie_check_errors', 100);

function remove_open_sans()
{
    wp_deregister_style('open-sans');
    wp_register_style('open-sans', false);
    wp_enqueue_style('open-sans', '');
}

add_action('init', 'remove_open_sans');

if (!function_exists('disable_embeds_init')):
    function disable_embeds_init()
    {
        global $wp;
        $wp->public_query_vars = array_diff($wp->public_query_vars, array('embed'));
        remove_action('rest_api_init', 'wp_oembed_register_route');
        add_filter('embed_oembed_discover', '__return_false');
        remove_filter('oembed_dataparse', 'wp_filter_oembed_result', 10);
        remove_action('wp_head', 'wp_oembed_add_discovery_links');
        remove_action('wp_head', 'wp_oembed_add_host_js');
        add_filter('tiny_mce_plugins', 'disable_embeds_tiny_mce_plugin');
        add_filter('rewrite_rules_array', 'disable_embeds_rewrites');
    }

    add_action('init', 'disable_embeds_init', 9999);

    function disable_embeds_tiny_mce_plugin($plugins)
    {
        return array_diff($plugins, array('wpembed'));
    }

    function disable_embeds_rewrites($rules)
    {
        foreach ($rules as $rule => $rewrite) {
            if (false !== strpos($rewrite, 'embed=true')) {
                unset($rules[$rule]);
            }
        }

        return $rules;
    }

    function disable_embeds_remove_rewrite_rules()
    {
        add_filter('rewrite_rules_array', 'disable_embeds_rewrites');
        flush_rewrite_rules();
    }

    register_activation_hook(__FILE__, 'disable_embeds_remove_rewrite_rules');

    function disable_embeds_flush_rewrite_rules()
    {
        remove_filter('rewrite_rules_array', 'disable_embeds_rewrites');
        flush_rewrite_rules();
    }

    register_deactivation_hook(__FILE__, 'disable_embeds_flush_rewrite_rules');
endif;

function QXThemes_del_tags($str)
{
    return trim(strip_tags($str));
}

add_filter('category_description', 'QXThemes_del_tags');

add_filter('login_headerurl', 'QXThemes_login_logo_url');
function QXThemes_login_logo_url($url)
{
    return home_url();
}

function QXThemes_login_logo_url_title()
{
    return get_bloginfo("name");
}

add_filter('login_headertext', 'QXThemes_login_logo_url_title');

function QXThemes_login_logo()
{
    ?>
<style type="text/css">
    #login h1 a,
    .login h1 a {
        background-image: url(<?php echo gop('logo_login'); ?>);
        background-size: auto;
        width: 300px;
    }
</style>
<?php
}

add_action('login_enqueue_scripts', 'QXThemes_login_logo');

add_action('after_setup_theme', 'default_attachment_display_settings');
function default_attachment_display_settings()
{
    update_option('image_default_align', 'center');
    update_option('image_default_link_type', 'file');
    update_option('image_default_size', 'full');
}

//去除后台不必要菜单
function remove_menus()
{
    global $menu;
    $restricted = array(
        // __('Dashboard'),
        // __('Posts'),
        // __('Media'),
        // __('Plugins'),
        // __('Pages'),
        // __('Appearance'),
        // __('Users'),
        // __('Settings'),
        // __('Links'),
    );
    remove_menu_page('oceanwp-panel');
//     remove_menu_page('elementor');
    // remove_menu_page('edit.php?post_type=elementor_library');
    // remove_menu_page('link-manager.php');
    remove_menu_page('edit-comments.php');

    /* while (prev($menu)) {
        $value = explode(' ', $menu[key($menu)][0]);
        if (strpos($value[0], '<') === false) {
            if (in_array($value[0] != null ? $value[0] : "", $restricted)) {
                unset($menu[key($menu)]);
            }
        } else {
            $value2 = explode('<', $value[0]);
            if (in_array($value2[0] != null ? $value2[0] : "", $restricted)) {
                unset($menu[key($menu)]);
            }
        }
    } */
}

// 去除后台不必要子菜单
function remove_submenu()
{
    // 删除"首页"下面的子菜单
    remove_submenu_page('index.php', 'update-core.php');

    // 删除"外观"下面的子菜单"编辑"
    remove_submenu_page('themes.php', 'themes.php');
    //  remove_submenu_page('themes.php', 'widgets.php');
    remove_submenu_page('themes.php', 'themes.php?page=options-framework');
    remove_submenu_page('themes.php', 'theme-editor.php');
    remove_submenu_page('themes.php', 'customize.php');
    remove_submenu_page('index.php', 'update-core.php');
}

 if (!in_array($user->user_login, $admin_arr)) {
     add_action('admin_init', 'remove_menus');
     add_action('admin_init', 'remove_submenu');
 }

// image_default_link_type:file,post,custom,none
//去掉隐藏显示选项
// function remove_screen_options_tab() { return false; }
// add_filter('screen_options_show_screen', 'remove_screen_options_tab');
// // 隐藏帮助选项卡
add_filter('contextual_help', 'wpse50723_remove_help', 999, 3);
function wpse50723_remove_help($old_help, $screen_id, $screen)
{
    $screen->remove_help_tabs();

    return $old_help;
}

function example_remove_dashboard_widgets()
{
    // Globalize the metaboxes array, this holds all the widgets for wp-admin
    global $wp_meta_boxes;

    // 以下这一行代码将删除 "快速发布" 面板，如果你想保留快速发布，请删除这行代码
    unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_quick_press']);
    // 以下这一行代码将删除 "引入链接" 面板
    unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_incoming_links']);
    // 以下这一行代码将删除 "插件" 面板
    unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_plugins']);
    // 以下这一行代码将删除 "近期评论" 面板
    unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_recent_comments']);
    // 以下这一行代码将删除 "近期草稿" 面板
    unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_recent_drafts']);
    // 以下这一行代码将删除 "WordPress 开发日志" 面板
    unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_primary']);
    // 以下这一行代码将删除 "其它 WordPress 新闻" 面板
    unset($wp_meta_boxes['dashboard']['side']['core']['dashboard_secondary']);
    // 以下这一行代码将删除 "概况" 面板
    unset($wp_meta_boxes['dashboard']['normal']['core']['dashboard_right_now']);
    // 以下这一行代码将删除 "欢迎" 面板
    unset($wp_meta_boxes['dashboard']['normal']['core']['welcome-panel']);
}

add_action('wp_dashboard_setup', 'example_remove_dashboard_widgets');
// WordPress 后台媒体库显示文件的链接地址
add_filter('media_row_actions', 'wpdaxue_media_row_actions', 10, 2);
function wpdaxue_media_row_actions($actions, $object)
{
    $actions['url'] = '<a href="javascript:void()">URL：' . wp_get_attachment_url($object->ID) . '</a>';

    return $actions;
}

//移除后台顶部左上角图标
function annointed_admin_bar_remove()
{
    global $wp_admin_bar;

    $wp_admin_bar->remove_menu('wp-logo');
}

add_action('wp_before_admin_bar_render', 'annointed_admin_bar_remove', 0);

function qx_admin_font()
{
    echo '<style>#wp-admin-bar-blog-1-c,#wp-admin-bar-blog-2-c,#wp-admin-bar-comments,#accordion-section-freemius_upsell {display:none !important;}</style>';
    $user = _wp_get_current_user();
    if (is_array($user->user_login) && in_array($user->user_login, $admin_arr)) {
        echo '<style>.acf-postbox > .hndle .acf-hndle-cog,#toplevel_page_edit-post_type-acf-field-group,#user-1,#toplevel_page_pods,#toplevel_page_pods-add-new,#add_pod_button,#toplevel_page_elementor,#toplevel_page_mlang{display:none !important;}</style>';
    }
    ;
}

add_action('admin_head', 'qx_admin_font');
//添加底部信息
function remove_footer_admin()
{
    echo '技术支持：<a href="https://www.shqiaxing.com" target="_blank">恰星网络</a>';
}
if (WPME_COPYRIGHT) {
    add_filter('admin_footer_text', 'remove_footer_admin');
}
// 去掉oceanwp 自定义中的选项
add_action('customize_register', 'prefix_remove_oceanwp_section', 15);
function prefix_remove_oceanwp_section($wp_customize)
{
    $wp_customize->remove_section('freemius_upsell');
}

//自定义登录页面
function custom_loginlogo()
{
    echo '<style type="text/css">.login #login h1 a {background:url(' . gop('logo') . ') center 0 no-repeat;background-size: contain;width: 320px;}
</style>';
}

add_action('login_head', 'custom_loginlogo');

//为新用户预设默认的后台配色方案
function set_default_admin_color($user_id)
{
    $args = array(
        'ID'          => $user_id,
        'admin_color' => 'midnight',
    );
    wp_update_user($args);
}

add_action('user_register', 'set_default_admin_color');

// 后台添加链接功能
add_filter('pre_option_link_manager_enabled', '__return_true');

function qx_force_permalink()
{
    if (!get_option('permalink_structure')) {
        update_option('permalink_structure', '/%category%/%post_id%.html');
        // TODO: 添加后台消息提示已更改默认固定链接，并请配置伪静态(伪静态教程等)
    }
}

add_action('load-themes.php', 'qx_force_permalink');


function add_editor_buttons($buttons)
{
    $buttons[] = 'fontselect';

    $buttons[] = 'fontsizeselect';

    $buttons[] = 'cleanup';

    $buttons[] = 'styleselect';

    $buttons[] = 'hr';

    $buttons[] = 'del';

    $buttons[] = 'sub';

    $buttons[] = 'sup';

    $buttons[] = 'copy';

    $buttons[] = 'paste';

    $buttons[] = 'cut';

    $buttons[] = 'undo';

    $buttons[] = 'image';

    $buttons[] = 'anchor';

    $buttons[] = 'backcolor';

    $buttons[] = 'wp_page';

    $buttons[] = 'charmap';

    return $buttons;
}

add_filter("mce_buttons_3", "add_editor_buttons");

/**
 * Remove all Google font options from the Customizer
 */

function remove_customizer_google_fonts($fonts)
{
    if (is_customize_preview()) {
        $fonts = '';
    }
    return $fonts;
}
//禁止加载默认jq库
function my_enqueue_scripts()
{
    wp_deregister_script('google-fonts-1');
    wp_register_style('google-fonts-1', false);
    wp_register_style('google-fonts-1', '');
    wp_deregister_script('google-font-roboto');
    wp_register_style('google-font-roboto', false);
    wp_register_style('google-font-roboto', '');
}
if (WPME_NO_GOOGLEFONT) {
    add_filter('oceanwp_google_fonts_array', 'remove_customizer_google_fonts');
    add_action('wp_enqueue_scripts', 'my_enqueue_scripts', 100);
}
