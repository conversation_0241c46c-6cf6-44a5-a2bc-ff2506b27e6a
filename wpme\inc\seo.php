<?php

function seo_to_context($context)
{
    
    $paged = get_query_var( 'paged', 1 );
    $line = " - ";

    if (function_exists('is_tag') && is_tag()) {
        $title = single_tag_title(__("Tag Archive for seo", "qx"), false) . $line . get_bloginfo('name');
    } elseif (is_archive()) {
        $term = get_queried_object();
        if (get_field('seo_title', $term)) {
            $title = get_field('seo_title', $term);
        } else {
            $title = wp_title('', false) . $line . get_bloginfo('name');
        }
    } elseif (is_search()) {
        $title = __("Search for seo", "qx") . ' "' . esc_html(get_search_query()) . '"' . $line . get_bloginfo('name');
    } elseif (!(is_404()) && (is_single()) || (is_page())) {
        if (get_field('seo_title')) {
            $title = get_field('seo_title');
        } else {
            $title = wp_title('|', false, 'right') . get_bloginfo('name');
        }
    } elseif (is_404()) {
        $title = 'Not Found | ' . get_bloginfo('name');
    } elseif (is_home()) {
        if (get_field('seo_title')) {
            $title = get_field('seo_title');
        } else {
            $title = get_bloginfo('name') . $line . get_bloginfo('description');
        }
    } else {
        if (get_field('seo_title') == null) {
            $title = get_bloginfo('name');
        }
    }
    if ($paged > 1) {
        $title .= $line . __("page  seo", "qx") . $paged;
    }

    if (is_single()) {
        $keywords = get_field('seo_keyword');
        if ($keywords == "") {
            $tags = wp_get_post_tags($post->ID);
            foreach ($tags as $tag) {
                $keywords = $keywords . $tag->name . ",";
            }
            $keywords = rtrim($keywords, ', ');
        }
        $description = get_field('seo_description');
        if ($description == "") {
            if ($post->post_excerpt) {
                $description = $post->post_excerpt;
            } else {
                $description = mb_strimwidth(strip_tags(apply_filters('the_content', $post->post_content)), 0, 200);
            }
        }
    } elseif (is_page()) {
        $keywords    = get_field('seo_keyword') ? get_field('seo_keyword') : get_the_title();
        $description = get_field('seo_description') ? get_field('seo_description') : mb_strimwidth(strip_tags(apply_filters('the_content', $post->post_content)), 0, 200);
    } elseif (is_category()) {
        $term        = get_queried_object();
        $keywords    = get_field('seo_keyword', $term) ? get_field('seo_keyword', $term) : single_cat_title('', false);
        $description = get_field('seo_description', $term) ? get_field('seo_description', $term) : category_description();
    } elseif (is_tag()) {
        $keywords    = single_tag_title('', false);
        $description = tag_description();
    } else {
        $keywords    = get_field('seo_keyword');
        $description = get_field('seo_description');
    }
    $keywords    = trim(strip_tags($keywords));
    $description = trim(strip_tags($description));

    $context['title']       = $title;
    $context['keywords']    = $keywords;
    $context['description'] = $description;

    return $context;
}
add_filter('timber/context', 'seo_to_context');