msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2018-04-16 17:11+1000\n"
"PO-Revision-Date: 2018-07-16 09:34+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON> <<EMAIL>>\n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 1.8.1\n"
"X-Loco-Target-Locale: it_IT\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:81
msgid "Advanced Custom Fields"
msgstr "Campi Personalizzati Avanzati"

#: acf.php:388 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Field Group"

#: acf.php:389
msgid "Field Group"
msgstr "Field Group"

#: acf.php:390 acf.php:422 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Add New"
msgstr "Aggiungi Nuovo"

#: acf.php:391
msgid "Add New Field Group"
msgstr ""
"Aggiungi Nuovo \n"
"Field Group"

#: acf.php:392
msgid "Edit Field Group"
msgstr ""
"Modifica \n"
"Field Group"

#: acf.php:393
msgid "New Field Group"
msgstr ""
"Nuovo \n"
"Field Group"

#: acf.php:394
msgid "View Field Group"
msgstr ""
"Visualizza \n"
"Field Group"

#: acf.php:395
msgid "Search Field Groups"
msgstr ""
"Cerca \n"
"Field Group"

#: acf.php:396
msgid "No Field Groups found"
msgstr ""
"Nessun \n"
"Field Group\n"
" Trovato"

#: acf.php:397
msgid "No Field Groups found in Trash"
msgstr ""
"Nessun \n"
"Field Group\n"
" trovato nel cestino"

#: acf.php:420 includes/admin/admin-field-group.php:196
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Campi"

#: acf.php:421
msgid "Field"
msgstr "Campo"

#: acf.php:423
msgid "Add New Field"
msgstr "Aggiungi Nuovo Campo"

#: acf.php:424
msgid "Edit Field"
msgstr "Modifica Campo"

#: acf.php:425 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Nuovo Campo"

#: acf.php:426
msgid "View Field"
msgstr "Visualizza Campo"

#: acf.php:427
msgid "Search Fields"
msgstr "Ricerca Campi"

#: acf.php:428
msgid "No Fields found"
msgstr "Nessun Campo trovato"

#: acf.php:429
msgid "No Fields found in Trash"
msgstr "Nessun Campo trovato nel cestino"

#: acf.php:468 includes/admin/admin-field-group.php:377
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Inattivo"

#: acf.php:473
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Inattivo <span class=\"count\">(%s)</span>"
msgstr[1] "Inattivo <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr ""
"Field Group\n"
" aggiornato."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr ""
"Field Group\n"
" cancellato."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr ""
"Field Group\n"
" pubblicato."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr ""
"Field Group\n"
" salvato."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr ""
"Field Group\n"
" inviato."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr ""
"Field Group\n"
" previsto."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr ""
"Bozza \n"
"Field Group\n"
" aggiornata."

#: includes/admin/admin-field-group.php:154
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""
"La stringa \"field_\" non può essere usata come inizio nel nome di un Campo"

#: includes/admin/admin-field-group.php:155
msgid "This field cannot be moved until its changes have been saved"
msgstr ""
"Questo Campo non può essere spostato fino a quando non saranno state salvate "
"le modifiche"

#: includes/admin/admin-field-group.php:156
msgid "Field group title is required"
msgstr "Il titolo del Field Group è richiesto"

#: includes/admin/admin-field-group.php:157
msgid "Move to trash. Are you sure?"
msgstr "Sposta nel cestino. Sei sicuro?"

#: includes/admin/admin-field-group.php:158
msgid "Move Custom Field"
msgstr "Sposta Campo Personalizzato"

#: includes/admin/admin-field-group.php:159
msgid "checked"
msgstr "selezionato"

#: includes/admin/admin-field-group.php:160
msgid "(no label)"
msgstr "(nessuna etichetta)"

#: includes/admin/admin-field-group.php:161
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "copia"

#: includes/admin/admin-field-group.php:162
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:139
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:4158
msgid "or"
msgstr "o"

#: includes/admin/admin-field-group.php:163
msgid "Null"
msgstr "Nullo"

#: includes/admin/admin-field-group.php:197
msgid "Location"
msgstr "Posizione"

#: includes/admin/admin-field-group.php:198
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Impostazioni"

#: includes/admin/admin-field-group.php:347
msgid "Field Keys"
msgstr "Field Key"

#: includes/admin/admin-field-group.php:377
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Attivo"

#: includes/admin/admin-field-group.php:753
msgid "Move Complete."
msgstr "Spostamento Completato."

#: includes/admin/admin-field-group.php:754
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr ""
"Il Campo %s può essere trovato nel \n"
"Field Group\n"
" %s"

#: includes/admin/admin-field-group.php:755
msgid "Close Window"
msgstr "Chiudi Finestra"

#: includes/admin/admin-field-group.php:796
msgid "Please select the destination for this field"
msgstr "Per favore seleziona la destinazione per questo Campo"

#: includes/admin/admin-field-group.php:803
msgid "Move Field"
msgstr "Sposta Campo"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Attivo <span class=\"count\">(%s)</span>"
msgstr[1] "Attivo <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr ""
"Field Group\n"
" duplicato. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "%s Field Group duplicato."
msgstr[1] "%s Field Group duplicati."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr ""
"Field Group\n"
" sincronizzato. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s Field Group sincronizzato."
msgstr[1] "%s Field Group sincronizzati."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Sync disponibile"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:355
msgid "Title"
msgstr "Titolo"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:382
msgid "Description"
msgstr "Descrizione"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Stato"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr "Personalizza WordPress con campi potenti, professionali e intuitivi."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Novità"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Guarda cosa c'è di nuovo nella <a href=\"%s\">versione %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Risorse"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Sito Web"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Documentazione"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Supporto"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "PRO"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Grazie per aver creato con <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:667
msgid "Duplicate this item"
msgstr "Duplica questo elemento"

#: includes/admin/admin-field-groups.php:667
#: includes/admin/admin-field-groups.php:683
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:550
msgid "Duplicate"
msgstr "Duplica"

#: includes/admin/admin-field-groups.php:700
#: includes/fields/class-acf-field-google-map.php:113
#: includes/fields/class-acf-field-relationship.php:657
msgid "Search"
msgstr "Ricerca"

#: includes/admin/admin-field-groups.php:759
#, php-format
msgid "Select %s"
msgstr "Seleziona %s"

#: includes/admin/admin-field-groups.php:767
msgid "Synchronise field group"
msgstr ""
"Sincronizza \n"
"Field Group"

#: includes/admin/admin-field-groups.php:767
#: includes/admin/admin-field-groups.php:797
msgid "Sync"
msgstr "Sync"

#: includes/admin/admin-field-groups.php:779
msgid "Apply"
msgstr "Applica"

#: includes/admin/admin-field-groups.php:797
msgid "Bulk Actions"
msgstr "Azioni di massa"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Strumenti"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Campi Personalizzati"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Aggiorna Database"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr "Rivedi siti e aggiornamenti"

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr "Errore di convalida richiesta"

#: includes/admin/install.php:210 includes/admin/views/install.php:104
msgid "No updates available."
msgstr "Nessun aggiornamento disponibile."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Add-ons"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Errore</b>. Impossibile caricare l'elenco Add-ons"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informazioni"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Cosa c'è di nuovo"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr ""
"Esporta \n"
"Field Group"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Genera PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr ""
"Nessun \n"
"Field Group\n"
" selezionato"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Esportato 1 gruppo di campi."
msgstr[1] "Esportati %s gruppi di campi."

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr ""
"Cerca \n"
"Field Group"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Selezionare i \n"
"Field Group\n"
" che si desidera esportare e quindi selezionare il metodo di esportazione. "
"Utilizzare il pulsante di download per esportare in un file .json che sarà "
"poi possibile importare in un'altra installazione ACF. Utilizzare il "
"pulsante generare per esportare il codice PHP che è possibile inserire nel "
"vostro tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Esporta file"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Il codice seguente può essere utilizzato per registrare una versione locale "
"del Field Group selezionato(i). Un Field Group locale può fornire numerosi "
"vantaggi come ad esempio i tempi di caricamento più veloci, controllo di "
"versione e campi / impostazioni dinamiche. Semplicemente copia e incolla il "
"seguente codice nel file functions.php del vostro tema."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Copia negli appunti"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "Copiato"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr ""
"Importa \n"
"Field Group"

#: includes/admin/tools/class-acf-admin-tool-import.php:61
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Selezionare il file JSON di Advanced Custom Fields che si desidera "
"importare. Quando si fa clic sul pulsante di importazione di seguito, ACF "
"importerà i \n"
"Field Group\n"
"."

#: includes/admin/tools/class-acf-admin-tool-import.php:66
#: includes/fields/class-acf-field-file.php:37
msgid "Select File"
msgstr "Seleziona File"

#: includes/admin/tools/class-acf-admin-tool-import.php:76
msgid "Import File"
msgstr "Importa file"

#: includes/admin/tools/class-acf-admin-tool-import.php:100
#: includes/fields/class-acf-field-file.php:154
msgid "No file selected"
msgstr "Nessun file selezionato"

#: includes/admin/tools/class-acf-admin-tool-import.php:113
msgid "Error uploading file. Please try again"
msgstr "Errore caricamento file. Per favore riprova"

#: includes/admin/tools/class-acf-admin-tool-import.php:122
msgid "Incorrect file type"
msgstr "Tipo file non corretto"

#: includes/admin/tools/class-acf-admin-tool-import.php:139
msgid "Import file empty"
msgstr "File importato vuoto"

#: includes/admin/tools/class-acf-admin-tool-import.php:247
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Importato 1 field group"
msgstr[1] "Importati %s field groups"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Condizione Logica"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Mostra questo Campo se"

#: includes/admin/views/field-group-field-conditional-logic.php:126
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "e"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Aggiungi gruppo di regole"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:403
#: pro/fields/class-acf-field-repeater.php:296
msgid "Drag to reorder"
msgstr "Trascinare per riordinare"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Modifica Campo"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:136
#: includes/fields/class-acf-field-image.php:122
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:342
msgid "Edit"
msgstr "Modifica"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Duplica Campo"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Sposta"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Sposta"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Cancella Campo"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:549
msgid "Delete"
msgstr "Cancella"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Etichetta Campo"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Questo è il nome che apparirà sulla pagina Modifica"

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Nome Campo"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Singola parola, nessun spazio. Sottolineatura e trattini consentiti"

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Tipo di Campo"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Istruzioni"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr ""
"Istruzioni per gli autori. Mostrato al momento della presentazione dei dati"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Richiesto?"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Attributi Contenitore"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "larghezza"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "classe"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Chiudi Campo"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Ordinamento"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:418
#: pro/fields/class-acf-field-flexible-content.php:576
msgid "Label"
msgstr "Etichetta"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:964
#: pro/fields/class-acf-field-flexible-content.php:589
msgid "Name"
msgstr "Nome"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Chiave"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tipo"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Nessun Campo. Clicca il bottone <strong>+ Aggiungi Campo</strong> per creare "
"il primo campo."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Aggiungi Campo"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Regole"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Creare un insieme di regole per determinare quale schermate in modifica "
"dovranno utilizzare i campi personalizzati avanzati"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Stile"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standard (metabox WP)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Senza giunte (senza metabox)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Posizione"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Alto (dopo il titolo)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normale (dopo contenuto)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "A lato"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Posizionamento etichette"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Allineamento in alto"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Allineamento a sinistra"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Posizionamento Istruzione"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Sotto etichette"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Sotto campi"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "N. Ordinamento"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr ""
"Field groups come inizialmente viene visualizzato in un ordine inferiore"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Mostrato in lista field group"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Nascondi nello schermo"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr ""
"<b>Seleziona</b> gli elementi per <b>nasconderli</b> dalla pagina Modifica."

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Se più gruppi di campi appaiono su una schermata di modifica, verranno usate "
"le opzioni del primo Field Group usato (quello con il numero d'ordine più "
"basso)"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Permalink"

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Editor Contenuto"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Estratto"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Discussione"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Commenti"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Revisioni"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Autore"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Formato"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Attributi di Pagina"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:671
msgid "Featured Image"
msgstr "Immagine di presentazione"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Categorie"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Tag"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Invia Trackbacks"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr ""
"Mostra questo \n"
"Field Group\n"
" se"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "Aggiornamento siti"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr ""
"Aggiornamento Database \n"
"Advanced Custom Fields"

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"I seguenti siti hanno necessità di un aggiornamento del DB. Controlla quelli "
"che vuoi aggiornare e clicca %s."

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Sito"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Il sito necessita di un aggiornamento Database da %s a %s"

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "Il sito è aggiornato"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Aggiornamento Database completato. <a href=\"%s\">Ritorna alla Network "
"Dashboard</a>"

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Si raccomanda vivamente di eseguire il backup del database prima di "
"procedere. Sei sicuro che si desidera eseguire il programma di aggiornamento "
"adesso?"

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Aggiornamento completato"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Aggiornamento dati alla versione %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Ripetitore"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Contenuto Flessibile"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galleria"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Pagina Opzioni"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Aggiornamento Database richiesto"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Grazie per aver aggiornato a %s v%s!"

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""
"Prima di iniziare ad utilizzare queste nuove fantastiche funzionalità, "
"aggiorna il tuo Database alla versione più attuale."

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""
"Si prega di assicurarsi che anche i componenti premium (%s) siano prima "
"stati aggiornati all'ultima versione."

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr "Lettura attività di aggiornamento ..."

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Aggiornamento del database completato. <a href=\"%s\">Guarda le novità</a>"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Scarica & Installa"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Installato"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Benvenuto in Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Grazie per l'aggiornamento! ACF %s è più grande e migliore che mai. Speriamo "
"che vi piaccia."

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr "Campi Personalizzati come non li avete mai visti"

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr "Migliorata Usabilità"

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Inclusa la famosa biblioteca Select2, che ha migliorato sia l'usabilità, che "
"la velocità di Campi come Post, Link, Tassonomie e Select."

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr "Miglioramento del Design"

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Molti Campi hanno subito un aggiornamento visivo per rendere ACF un aspetto "
"migliore che mai! Notevoli cambiamenti li trovate nei Campi Gallery, "
"Relazioni e oEmbed (nuovo)!"

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr "Miglioramento dei dati"

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Ridisegnare l'architettura dei dati ha permesso ai Sotto-Campi di vivere in "
"modo indipendente dai loro Genitori. Ciò consente di trascinare e rilasciare "
"i Campi dentro e fuori i Campi Genitore!"

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Ciao, ciao Add-ons. Benvenuto PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr "Introduzione ACF PRO"

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr "Stiamo cambiando in modo eccitante le funzionalità Premium!"

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Parallelamente ACF5 è la versione tutta nuova di <a href=\"%s\">ACF5 PRO</"
"a>! Questa versione PRO include tutti e 4 i componenti aggiuntivi premium "
"(Repeater, Gallery, Flexible Content e Pagina Opzioni) e con le licenze "
"personali e di sviluppo disponibili, funzionalità premium è più conveniente "
"che mai!"

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Potenti funzionalità"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO contiene caratteristiche impressionanti come i Campi Repeater, "
"Flexible Layout, Gallery e la possibilità di creare Options Page (pagine "
"opzioni di amministrazione) personalizzabili!"

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Scopri di più sulle <a href=\"%s\">funzionalità di ACF PRO</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Aggiornamento facile"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""
"Per rendere più semplice gli aggiornamenti, \n"
"<a href=\"%s\">accedi al tuo account</a> e richiedi una copia gratuita di "
"ACF PRO!"

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""
"Abbiamo inoltre scritto una <a href=\"%s\">guida all'aggiornamento</a> per "
"rispondere alle vostre richieste, ma se ne avete di nuove, contattate il "
"nostro <a href=\"%s\">help desk</a>"

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Sotto il cofano"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr "Impostazioni dei Campi più intelligenti"

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr "ACF ora salva le impostazioni dei Campi come oggetti Post individuali"

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Più AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr ""
"Altri campi utilizzano la ricerca di AJAX per velocizzare il caricamento "
"della pagina"

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "JSON locale"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr ""
"Nuovo esportazione automatica di funzionalità JSON migliora la velocità"

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr "Migliore versione di controllo"

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr ""
"Nuova esportazione automatica di funzione JSON consente impostazioni dei "
"campi da versione controllati"

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr "XML scambiato per JSON"

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr "Importa / Esporta ora utilizza JSON a favore di XML"

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Nuovi Forme"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""
"I campi possono essere mappati con i commenti, widget e tutte le forme degli "
"utenti!"

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr "È stato aggiunto un nuovo campo per incorporare contenuti"

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Nuova Galleria"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr "Il campo galleria ha subito un lifting tanto necessario"

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Nuove Impostazioni"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""
"Sono state aggiunte impostazioni di gruppo sul Campo per l'inserimento "
"dell'etichetta e il posizionamento di istruzioni"

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr "Forme Anteriori migliori"

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr "acf_form() può ora creare un nuovo post di presentazione"

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Validazione Migliore"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr ""
"Validazione del form ora avviene tramite PHP + AJAX in favore del solo JS"

#: includes/admin/views/settings-info.php:132
msgid "Relationship Field"
msgstr "Campo Relazione"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""
"Nuove Impostazione Campo Relazione per i 'Filtri' (Ricerca, Tipo di Post, "
"Tassonomia)"

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Spostamento Campi"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""
"La nuova funzionalità di Field Group consente di spostare un campo tra i "
"gruppi e genitori"

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Link Pagina"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr "Nuovo gruppo archivi in materia di selezione page_link"

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Migliori Pagine Opzioni"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""
"Nuove funzioni per la Pagina Opzioni consentono la creazione di pagine menu "
"genitore e figlio"

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Pensiamo che amerete i cambiamenti in %s."

#: includes/api/api-helpers.php:1039
msgid "Thumbnail"
msgstr "Thumbnail"

#: includes/api/api-helpers.php:1040
msgid "Medium"
msgstr "Medio"

#: includes/api/api-helpers.php:1041
msgid "Large"
msgstr "Grande"

#: includes/api/api-helpers.php:1090
msgid "Full Size"
msgstr "Dimensione piena"

#: includes/api/api-helpers.php:1431 includes/api/api-helpers.php:2004
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(nessun titolo)"

#: includes/api/api-helpers.php:4079
#, php-format
msgid "Image width must be at least %dpx."
msgstr "La larghezza dell'immagine deve essere di almeno %dpx."

#: includes/api/api-helpers.php:4084
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "La larghezza dell'immagine non deve superare i %dpx."

#: includes/api/api-helpers.php:4100
#, php-format
msgid "Image height must be at least %dpx."
msgstr "L'altezza dell'immagine deve essere di almeno %dpx."

#: includes/api/api-helpers.php:4105
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "L'altezza dell'immagine non deve superare i %dpx."

#: includes/api/api-helpers.php:4123
#, php-format
msgid "File size must be at least %s."
msgstr "La dimensione massima deve essere di almeno %s."

#: includes/api/api-helpers.php:4128
#, php-format
msgid "File size must must not exceed %s."
msgstr "La dimensione massima non deve superare i %s."

#: includes/api/api-helpers.php:4162
#, php-format
msgid "File type must be %s."
msgstr "La tipologia del File deve essere %s."

#: includes/assets.php:164
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Le modifiche effettuate verranno cancellate se si esce da questa pagina"

#: includes/assets.php:167 includes/fields/class-acf-field-select.php:257
msgctxt "verb"
msgid "Select"
msgstr "Seleziona"

#: includes/assets.php:168
msgctxt "verb"
msgid "Edit"
msgstr "Modifica"

#: includes/assets.php:169
msgctxt "verb"
msgid "Update"
msgstr "Aggiorna"

#: includes/assets.php:170 pro/fields/class-acf-field-gallery.php:44
msgid "Uploaded to this post"
msgstr "Caricato in questo Post"

#: includes/assets.php:171
msgid "Expand Details"
msgstr "Espandi Dettagli"

#: includes/assets.php:172
msgid "Collapse Details"
msgstr "Chiudi Dettagli"

#: includes/assets.php:173
msgid "Restricted"
msgstr "Limitato"

#: includes/assets.php:174
msgid "All images"
msgstr "Tutte le immagini"

#: includes/assets.php:177
msgid "Validation successful"
msgstr "Validazione avvenuta con successo"

#: includes/assets.php:178 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Validazione fallita"

#: includes/assets.php:179
msgid "1 field requires attention"
msgstr "1 Campo necessita di attenzioni"

#: includes/assets.php:180
#, php-format
msgid "%d fields require attention"
msgstr "%d Campi necessitano di attenzioni"

#: includes/assets.php:183
msgid "Are you sure?"
msgstr "Sei sicuro?"

#: includes/assets.php:184 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Si"

#: includes/assets.php:185 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "No"

#: includes/assets.php:186 includes/fields/class-acf-field-file.php:138
#: includes/fields/class-acf-field-image.php:124
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:343
#: pro/fields/class-acf-field-gallery.php:531
msgid "Remove"
msgstr "Rimuovi"

#: includes/assets.php:187
msgid "Cancel"
msgstr "Annulla"

#: includes/assets.php:190
msgid "Has any value"
msgstr "Ha qualunque valore"

#: includes/assets.php:191
msgid "Has no value"
msgstr "Non ha un valore"

#: includes/assets.php:192
msgid "Value is equal to"
msgstr "Valore è uguale a"

#: includes/assets.php:193
msgid "Value is not equal to"
msgstr "Valore non è uguale a"

#: includes/assets.php:194
msgid "Value matches pattern"
msgstr "Valore corrisponde a modello"

#: includes/assets.php:195
msgid "Value contains"
msgstr "Valore contiene"

#: includes/assets.php:196
msgid "Value is greater than"
msgstr "Valore è maggiore di"

#: includes/assets.php:197
msgid "Value is less than"
msgstr "Valore è meno di"

#: includes/assets.php:198
msgid "Selection is greater than"
msgstr "Selezione è maggiore di"

#: includes/assets.php:199
msgid "Selection is less than"
msgstr "Selezione è meno di"

#: includes/fields.php:144
msgid "Basic"
msgstr "Base"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Contenuto"

#: includes/fields.php:146
msgid "Choice"
msgstr "Scegli"

#: includes/fields.php:147
msgid "Relational"
msgstr "Relazionale"

#: includes/fields.php:148
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:149
#: includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:546
#: pro/fields/class-acf-field-flexible-content.php:595
#: pro/fields/class-acf-field-repeater.php:442
msgid "Layout"
msgstr "Layout"

#: includes/fields.php:326
msgid "Field type does not exist"
msgstr "Il tipo di Campo non esiste"

#: includes/fields.php:326
msgid "Unknown"
msgstr "Sconosciuto"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Fisarmonica"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Apri"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Mostra questa fisarmonica aperta sul caricamento della pagina."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Espansione multipla"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Permetti a questa fisarmonica di aprirsi senza chiudere gli altri."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Endpoint"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Definisce un endpoint per la precedente fisarmonica che deve fermarsi. "
"Questa fisarmonica non sarà visibile."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Gruppo Bottoni"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:349
msgid "Choices"
msgstr "Scelte"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:350
msgid "Enter each choice on a new line."
msgstr "Immettere ogni scelta su una nuova linea."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:350
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Per un maggiore controllo, è possibile specificare sia un valore ed "
"etichetta in questo modo:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:350
msgid "red : Red"
msgstr "rosso : Rosso"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:412
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:367
#: includes/fields/class-acf-field-taxonomy.php:793
#: includes/fields/class-acf-field-user.php:409
msgid "Allow Null?"
msgstr "Consenti Nullo?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:146
#: includes/fields/class-acf-field-select.php:358
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:410
msgid "Default Value"
msgstr "Valore di default"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:147
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:411
msgid "Appears when creating a new post"
msgstr "Appare quando si crea un nuovo post"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Orizzontale"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Verticale"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:199
#: includes/fields/class-acf-field-image.php:188
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:833
msgid "Return Value"
msgstr "Valore di ritorno"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-image.php:189
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Specificare il valore restituito sul front-end"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:417
msgid "Value"
msgstr "Valore"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:419
msgid "Both (Array)"
msgstr "Entrambi (Array)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Checkbox"
msgstr "Checkbox"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Seleziona tutti"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Aggiungi nuova scelta"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Consenti Personalizzato"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Consenti valori 'personalizzati' da aggiungere"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Salva Personalizzato"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Salvare i valori 'personalizzati' per le scelte del campo"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:359
msgid "Enter each default value on a new line"
msgstr "Immettere ogni valore di default su una nuova linea"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Toggle"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr "Inserisci un Checkbox extra per poter selezionare tutte le opzioni"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Selettore colore"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Chiaro"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Default"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Seleziona colore"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Colore Corrente"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Selettore data"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Fatto"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Oggi"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Prossimo"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Precedente"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Sett"

#: includes/fields/class-acf-field-date_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Formato di visualizzazione"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Il formato visualizzato durante la modifica di un post"

#: includes/fields/class-acf-field-date_picker.php:189
#: includes/fields/class-acf-field-date_picker.php:220
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Personalizzato:"

#: includes/fields/class-acf-field-date_picker.php:199
msgid "Save Format"
msgstr "Salva Formato"

#: includes/fields/class-acf-field-date_picker.php:200
msgid "The format used when saving a value"
msgstr "Il formato utilizzato durante il salvataggio di un valore"

#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-relationship.php:698
#: includes/fields/class-acf-field-select.php:412
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:428
msgid "Return Format"
msgstr "Formato di ritorno"

#: includes/fields/class-acf-field-date_picker.php:211
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "Il formato restituito tramite funzioni template"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "La settimana inizia il"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Selettore data/ora"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Scegli tempo"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Orario"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Ore"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuto"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Secondo"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Millisecondo"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Microsecondo"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Fuso orario"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Ora"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Fatto"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seleziona"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "Email"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Testo segnaposto"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Appare nella finestra di input"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:185
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Anteponi"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:186
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Appare prima dell'input"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:194
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Accodare"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:195
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Accodare dopo l'input"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "File"

#: includes/fields/class-acf-field-file.php:38
msgid "Edit File"
msgstr "Modifica File"

#: includes/fields/class-acf-field-file.php:39
msgid "Update File"
msgstr "Aggiorna File"

#: includes/fields/class-acf-field-file.php:125
msgid "File name"
msgstr "Nome file"

#: includes/fields/class-acf-field-file.php:129
#: includes/fields/class-acf-field-file.php:232
#: includes/fields/class-acf-field-file.php:243
#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: pro/fields/class-acf-field-gallery.php:690
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Dimensione File"

#: includes/fields/class-acf-field-file.php:154
msgid "Add File"
msgstr "Aggiungi file"

#: includes/fields/class-acf-field-file.php:205
msgid "File Array"
msgstr "File Array"

#: includes/fields/class-acf-field-file.php:206
msgid "File URL"
msgstr "File URL"

#: includes/fields/class-acf-field-file.php:207
msgid "File ID"
msgstr "File ID"

#: includes/fields/class-acf-field-file.php:214
#: includes/fields/class-acf-field-image.php:213
#: pro/fields/class-acf-field-gallery.php:655
msgid "Library"
msgstr "Libreria"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:214
#: pro/fields/class-acf-field-gallery.php:656
msgid "Limit the media library choice"
msgstr "Limitare la scelta alla libreria multimediale"

#: includes/fields/class-acf-field-file.php:220
#: includes/fields/class-acf-field-image.php:219
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:661
msgid "All"
msgstr "Tutti"

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:220
#: pro/fields/class-acf-field-gallery.php:662
msgid "Uploaded to post"
msgstr "Caricato al post"

#: includes/fields/class-acf-field-file.php:228
#: includes/fields/class-acf-field-image.php:227
#: pro/fields/class-acf-field-gallery.php:669
msgid "Minimum"
msgstr "Minimo"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-file.php:240
msgid "Restrict which files can be uploaded"
msgstr "Limita i tipi di File che possono essere caricati"

#: includes/fields/class-acf-field-file.php:239
#: includes/fields/class-acf-field-image.php:256
#: pro/fields/class-acf-field-gallery.php:698
msgid "Maximum"
msgstr "Massimo"

#: includes/fields/class-acf-field-file.php:250
#: includes/fields/class-acf-field-image.php:285
#: pro/fields/class-acf-field-gallery.php:727
msgid "Allowed file types"
msgstr "Tipologie File permesse"

#: includes/fields/class-acf-field-file.php:251
#: includes/fields/class-acf-field-image.php:286
#: pro/fields/class-acf-field-gallery.php:728
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista separata da virgole. Lascia bianco per tutti i tipi"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google Map"

#: includes/fields/class-acf-field-google-map.php:43
msgid "Sorry, this browser does not support geolocation"
msgstr "Spiacente, questo browser non supporta la geolocalizzazione"

#: includes/fields/class-acf-field-google-map.php:114
msgid "Clear location"
msgstr "Pulisci posizione"

#: includes/fields/class-acf-field-google-map.php:115
msgid "Find current location"
msgstr "Trova posizione corrente"

#: includes/fields/class-acf-field-google-map.php:118
msgid "Search for address..."
msgstr "Cerca per indirizzo..."

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-google-map.php:159
msgid "Center"
msgstr "Centro"

#: includes/fields/class-acf-field-google-map.php:149
#: includes/fields/class-acf-field-google-map.php:160
msgid "Center the initial map"
msgstr "Centrare la mappa iniziale"

#: includes/fields/class-acf-field-google-map.php:171
msgid "Zoom"
msgstr "Zoom"

#: includes/fields/class-acf-field-google-map.php:172
msgid "Set the initial zoom level"
msgstr "Imposta il livello di zoom iniziale"

#: includes/fields/class-acf-field-google-map.php:181
#: includes/fields/class-acf-field-image.php:239
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:681
#: pro/fields/class-acf-field-gallery.php:710
msgid "Height"
msgstr "Altezza"

#: includes/fields/class-acf-field-google-map.php:182
msgid "Customise the map height"
msgstr "Personalizza l'altezza della mappa iniziale"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Gruppo"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:381
msgid "Sub Fields"
msgstr "Campi Sub"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Specificare lo stile utilizzato per il rendering dei campi selezionati"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:606
#: pro/fields/class-acf-field-repeater.php:450
msgid "Block"
msgstr "Blocco"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:605
#: pro/fields/class-acf-field-repeater.php:449
msgid "Table"
msgstr "Tabella"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:607
#: pro/fields/class-acf-field-repeater.php:451
msgid "Row"
msgstr "Riga"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Immagine"

#: includes/fields/class-acf-field-image.php:42
msgid "Select Image"
msgstr "Seleziona Immagine"

#: includes/fields/class-acf-field-image.php:43
#: pro/fields/class-acf-field-gallery.php:42
msgid "Edit Image"
msgstr "Modifica Immagine"

#: includes/fields/class-acf-field-image.php:44
#: pro/fields/class-acf-field-gallery.php:43
msgid "Update Image"
msgstr "Aggiorna Immagine"

#: includes/fields/class-acf-field-image.php:140
msgid "No image selected"
msgstr "Nessun immagine selezionata"

#: includes/fields/class-acf-field-image.php:140
msgid "Add Image"
msgstr "Aggiungi Immagine"

#: includes/fields/class-acf-field-image.php:194
msgid "Image Array"
msgstr "Array Immagine"

#: includes/fields/class-acf-field-image.php:195
msgid "Image URL"
msgstr "URL Immagine"

#: includes/fields/class-acf-field-image.php:196
msgid "Image ID"
msgstr "ID Immagine"

#: includes/fields/class-acf-field-image.php:203
msgid "Preview Size"
msgstr "Dimensione Anteprima"

#: includes/fields/class-acf-field-image.php:204
msgid "Shown when entering data"
msgstr "Mostrato durante l'immissione dei dati"

#: includes/fields/class-acf-field-image.php:228
#: includes/fields/class-acf-field-image.php:257
#: pro/fields/class-acf-field-gallery.php:670
#: pro/fields/class-acf-field-gallery.php:699
msgid "Restrict which images can be uploaded"
msgstr "Limita i tipi di immagine che possono essere caricati"

#: includes/fields/class-acf-field-image.php:231
#: includes/fields/class-acf-field-image.php:260
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:673
#: pro/fields/class-acf-field-gallery.php:702
msgid "Width"
msgstr "Larghezza"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Seleziona Link"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Apri in una nuova scheda/finestra"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Link Array"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "Link URL"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Messaggio"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Nuove Linee"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Controlla come le nuove linee sono renderizzate"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Aggiungi automaticamente paragrafi"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Aggiungi automaticamente &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Nessuna formattazione"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Escape HTML"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Visualizza HTML come testo"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Numero"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:155
msgid "Minimum Value"
msgstr "Valore Minimo"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:165
msgid "Maximum Value"
msgstr "Valore Massimo"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:175
msgid "Step Size"
msgstr "Step Dimensione"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Il valore deve essere un numero"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Il valore deve essere uguale o superiore a %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Il valore deve essere uguale o inferiore a %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "Inserisci URL"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Dimensione Embed"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Archivi"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:268
#: includes/fields/class-acf-field-taxonomy.php:986
msgid "Parent"
msgstr "Genitore"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:384
#: includes/fields/class-acf-field-relationship.php:624
msgid "Filter by Post Type"
msgstr "Filtra per tipo di Post"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:392
#: includes/fields/class-acf-field-relationship.php:632
msgid "All post types"
msgstr "Tutti i tipi di post"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:398
#: includes/fields/class-acf-field-relationship.php:638
msgid "Filter by Taxonomy"
msgstr "Fitra per tassonomia"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:406
#: includes/fields/class-acf-field-relationship.php:646
msgid "All taxonomies"
msgstr "Tutte le Tassonomie"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Consentire URL degli Archivi"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:422
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-user.php:419
msgid "Select multiple values?"
msgstr "Selezionare più valori?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Password"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:703
msgid "Post Object"
msgstr "Oggetto Post"

#: includes/fields/class-acf-field-post_object.php:438
#: includes/fields/class-acf-field-relationship.php:704
msgid "Post ID"
msgstr "ID Post"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Bottone Radio"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Altro"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Aggiungi scelta 'altro' per consentire valori personalizzati"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Salva Altro"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Salvare i valori 'altri' alle scelte di campo"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Intervallo"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relazioni"

#: includes/fields/class-acf-field-relationship.php:40
msgid "Maximum values reached ( {max} values )"
msgstr "Valori massimi raggiunti ( valori {max} )"

#: includes/fields/class-acf-field-relationship.php:41
msgid "Loading"
msgstr "Caricamento"

#: includes/fields/class-acf-field-relationship.php:42
msgid "No matches found"
msgstr "Nessun risultato"

#: includes/fields/class-acf-field-relationship.php:424
msgid "Select post type"
msgstr "Seleziona Post Type"

#: includes/fields/class-acf-field-relationship.php:450
msgid "Select taxonomy"
msgstr "Seleziona Tassonomia"

#: includes/fields/class-acf-field-relationship.php:540
msgid "Search..."
msgstr "Ricerca ..."

#: includes/fields/class-acf-field-relationship.php:652
msgid "Filters"
msgstr "Filtri"

#: includes/fields/class-acf-field-relationship.php:658
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Tipo Post"

#: includes/fields/class-acf-field-relationship.php:659
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:763
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "Tassonomie"

#: includes/fields/class-acf-field-relationship.php:666
msgid "Elements"
msgstr "Elementi"

#: includes/fields/class-acf-field-relationship.php:667
msgid "Selected elements will be displayed in each result"
msgstr "Gli elementi selezionati verranno visualizzati in ogni risultato"

#: includes/fields/class-acf-field-relationship.php:678
msgid "Minimum posts"
msgstr "Post minimi"

#: includes/fields/class-acf-field-relationship.php:687
msgid "Maximum posts"
msgstr "Post massimi"

#: includes/fields/class-acf-field-relationship.php:791
#: pro/fields/class-acf-field-gallery.php:800
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s richiede la selezione di almeno %s"
msgstr[1] "%s richiede le selezioni di almeno %s"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:785
msgctxt "noun"
msgid "Select"
msgstr "Seleziona"

#: includes/fields/class-acf-field-select.php:40
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Un risultato disponibile, premi invio per selezionarlo."

#: includes/fields/class-acf-field-select.php:41
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d risultati disponibili, usa i tasti freccia su e giù per scorrere."

#: includes/fields/class-acf-field-select.php:42
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nessun riscontro trovato"

#: includes/fields/class-acf-field-select.php:43
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Per favore inserire 1 o più caratteri"

#: includes/fields/class-acf-field-select.php:44
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "﻿Inserisci %d o più caratteri"

#: includes/fields/class-acf-field-select.php:45
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Per favore cancella 1 carattere"

#: includes/fields/class-acf-field-select.php:46
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Cancellare %d caratteri"

#: includes/fields/class-acf-field-select.php:47
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Puoi selezionare solo 1 elemento"

#: includes/fields/class-acf-field-select.php:48
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "È possibile selezionare solo %d elementi"

#: includes/fields/class-acf-field-select.php:49
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Caricamento altri risultati&hellip;"

#: includes/fields/class-acf-field-select.php:50
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Cercando&hellip;"

#: includes/fields/class-acf-field-select.php:51
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Caricamento fallito"

#: includes/fields/class-acf-field-select.php:387
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "UI stilizzata"

#: includes/fields/class-acf-field-select.php:397
msgid "Use AJAX to lazy load choices?"
msgstr "Usa AJAX per le scelte di carico lazy?"

#: includes/fields/class-acf-field-select.php:413
msgid "Specify the value returned"
msgstr "Specificare il valore restituito"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Separatore"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Scheda"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Posizione"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Definire un endpoint per le schede precedenti da interrompere. Questo "
"avvierà un nuovo gruppo di schede."

#: includes/fields/class-acf-field-taxonomy.php:713
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Nessun %s"

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "None"
msgstr "Nessuno"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Select the taxonomy to be displayed"
msgstr "Seleziona la Tassonomia da mostrare"

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Appearance"
msgstr "Aspetto"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Select the appearance of this field"
msgstr "Seleziona l'aspetto per questo Campo"

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Multiple Values"
msgstr "Valori Multipli"

#: includes/fields/class-acf-field-taxonomy.php:781
msgid "Multi Select"
msgstr "Selezione Multipla"

#: includes/fields/class-acf-field-taxonomy.php:783
msgid "Single Value"
msgstr "Valore Singolo"

#: includes/fields/class-acf-field-taxonomy.php:784
msgid "Radio Buttons"
msgstr "Bottoni Radio"

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "Create Terms"
msgstr "Crea Termini"

#: includes/fields/class-acf-field-taxonomy.php:804
msgid "Allow new terms to be created whilst editing"
msgstr "Abilita la creazione di nuovi Termini"

#: includes/fields/class-acf-field-taxonomy.php:813
msgid "Save Terms"
msgstr "Salva Termini"

#: includes/fields/class-acf-field-taxonomy.php:814
msgid "Connect selected terms to the post"
msgstr "Collega i Termini selezionati al Post"

#: includes/fields/class-acf-field-taxonomy.php:823
msgid "Load Terms"
msgstr "Carica Termini"

#: includes/fields/class-acf-field-taxonomy.php:824
msgid "Load value from posts terms"
msgstr "Carica valori dai Termini del Post"

#: includes/fields/class-acf-field-taxonomy.php:838
msgid "Term Object"
msgstr "Oggetto Termine"

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Term ID"
msgstr "ID Termine"

#: includes/fields/class-acf-field-taxonomy.php:898
msgid "Error."
msgstr "Errore."

#: includes/fields/class-acf-field-taxonomy.php:898
#, php-format
msgid "User unable to add new %s"
msgstr "Utente non abilitato ad aggiungere %s"

#: includes/fields/class-acf-field-taxonomy.php:911
#, php-format
msgid "%s already exists"
msgstr "%s esiste già"

#: includes/fields/class-acf-field-taxonomy.php:952
#, php-format
msgid "%s added"
msgstr "%s aggiunto"

#: includes/fields/class-acf-field-taxonomy.php:998
msgid "Add"
msgstr "Aggiungi"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Testo"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Limite Carattere"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Lasciare vuoto per nessun limite"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Area di Testo"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Righe"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Imposta le righe dell'area di testo"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Selettore di tempo"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Vero / Falso"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Visualizza il testo a fianco alla casella di controllo"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Testo Attivo"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Testo visualizzato quando è attivo"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "Testo Disattivo"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "Testo mostrato quando inattivo"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Il valore deve essere una URL valida"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Utente"

#: includes/fields/class-acf-field-user.php:394
msgid "Filter by role"
msgstr "Filtra per ruolo"

#: includes/fields/class-acf-field-user.php:402
msgid "All user roles"
msgstr "Tutti i ruoli utente"

#: includes/fields/class-acf-field-user.php:433
msgid "User Array"
msgstr "Array utente"

#: includes/fields/class-acf-field-user.php:434
msgid "User Object"
msgstr "Oggetto utente"

#: includes/fields/class-acf-field-user.php:435
msgid "User ID"
msgstr "ID Utente"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Editor Wysiwyg"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual"
msgstr "Visuale"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Testo"

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Click to initialize TinyMCE"
msgstr "Clicca per inizializzare TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Tabs"
msgstr "Schede"

#: includes/fields/class-acf-field-wysiwyg.php:424
msgid "Visual & Text"
msgstr "Visuale e Testuale"

#: includes/fields/class-acf-field-wysiwyg.php:425
msgid "Visual Only"
msgstr "Solo Visuale"

#: includes/fields/class-acf-field-wysiwyg.php:426
msgid "Text Only"
msgstr "Solo Testuale"

#: includes/fields/class-acf-field-wysiwyg.php:433
msgid "Toolbar"
msgstr "Toolbar"

#: includes/fields/class-acf-field-wysiwyg.php:443
msgid "Show Media Upload Buttons?"
msgstr "Mostra Bottoni caricamento Media?"

#: includes/fields/class-acf-field-wysiwyg.php:453
msgid "Delay initialization?"
msgstr "Ritardo inizializzazione?"

#: includes/fields/class-acf-field-wysiwyg.php:454
msgid "TinyMCE will not be initalized until field is clicked"
msgstr ""
"TinyMCE non sarà inizializzato fino a quando il campo non viene cliccato"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr "Modifica Field Group"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Valida Email"

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:573 pro/options-page.php:81
msgid "Update"
msgstr "Aggiorna"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Post aggiornato"

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr "Spam Rilevato"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Post"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Pagina"

#: includes/locations.php:96
msgid "Forms"
msgstr "Moduli"

#: includes/locations.php:247
msgid "is equal to"
msgstr "è uguale a"

#: includes/locations.php:248
msgid "is not equal to"
msgstr "non è uguale a"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Allegato"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Tutti i formati %s"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Commento"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Ruolo Utente corrente"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Super Admin"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Utente corrente"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Autenticato"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Visualizzando Frond-end"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Visualizzando Back-end"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Menu Elemento"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Posizione Menu"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Menu"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Genitore Pagina"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Template Pagina"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Template Default"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Tipo di Pagina"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "Pagina Principale"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "Pagina Post"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "Pagina di primo livello (no Genitori)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "Pagina Genitore (ha Figli)"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "Pagina Figlio (ha Genitore)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Categoria Post"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Formato Post"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Stato Post"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Tassonomia Post"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Template Post"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Form Utente"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Aggiungi / Modifica"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Registra"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Ruolo Utente"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widget"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "Il valore %s è richiesto"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Pubblica"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Nessun Field Group personalizzato trovato in questa Pagina Opzioni. <a href="
"\"%s\">Crea un Field Group personalizzato</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Errore</b>.Impossibile connettersi al server di aggiornamento"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Aggiornamenti"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Disattivare Licenza"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Attiva Licenza"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Informazioni Licenza"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Per sbloccare gli aggiornamenti, si prega di inserire la chiave di licenza "
"qui sotto. Se non hai una chiave di licenza, si prega di vedere <a href=\"%s"
"\" target=\"_blank\">Dettagli e prezzi</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Chiave di licenza"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informazioni di aggiornamento"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Versione corrente"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Ultima versione"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Aggiornamento Disponibile"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Aggiorna Plugin"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Inserisci il tuo codice di licenza per sbloccare gli aggiornamenti"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Ricontrollare"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Avviso di Aggiornamento"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Clona"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Selezionare uno o più campi che si desidera clonare"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Visualizza"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Specificare lo stile utilizzato per il rendering del campo clona"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr ""
"Gruppo (Visualizza campi selezionati in un gruppo all'interno di questo "
"campo)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Senza interruzione (sostituisce questo campo con i campi selezionati)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Etichette verranno visualizzate come %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Prefisso Etichetta Campo"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "I valori verranno salvati come %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Prefisso Nomi Campo"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Campo sconosciuto"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Field Group sconosciuto"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Tutti i campi dal %s field group"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:174
#: pro/fields/class-acf-field-repeater.php:462
msgid "Add Row"
msgstr "Aggiungi Riga"

#: pro/fields/class-acf-field-flexible-content.php:34
msgid "layout"
msgstr "layout"

#: pro/fields/class-acf-field-flexible-content.php:35
msgid "layouts"
msgstr "layout"

#: pro/fields/class-acf-field-flexible-content.php:36
msgid "remove {layout}?"
msgstr "rimuovi {layout}?"

#: pro/fields/class-acf-field-flexible-content.php:37
msgid "This field requires at least {min} {identifier}"
msgstr "Questo campoQuesto campo richiede almeno {min} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:38
msgid "This field has a limit of {max} {identifier}"
msgstr "Questo campo ha un limite di {max} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:39
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Questo campo richiede almeno {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:40
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr "Massimo {label} limite raggiunto ({max} {identifier})"

#: pro/fields/class-acf-field-flexible-content.php:41
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} disponibile (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:42
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} richiesto (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:43
msgid "Flexible Content requires at least 1 layout"
msgstr "Flexible Content richiede almeno 1 layout"

#: pro/fields/class-acf-field-flexible-content.php:273
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Clicca il bottone \"%s\" qui sotto per iniziare a creare il layout"

#: pro/fields/class-acf-field-flexible-content.php:406
msgid "Add layout"
msgstr "Aggiungi Layout"

#: pro/fields/class-acf-field-flexible-content.php:407
msgid "Remove layout"
msgstr "Rimuovi Layout"

#: pro/fields/class-acf-field-flexible-content.php:408
#: pro/fields/class-acf-field-repeater.php:298
msgid "Click to toggle"
msgstr "Clicca per alternare"

#: pro/fields/class-acf-field-flexible-content.php:548
msgid "Reorder Layout"
msgstr "Riordina Layout"

#: pro/fields/class-acf-field-flexible-content.php:548
msgid "Reorder"
msgstr "Riordina"

#: pro/fields/class-acf-field-flexible-content.php:549
msgid "Delete Layout"
msgstr "Cancella Layout"

#: pro/fields/class-acf-field-flexible-content.php:550
msgid "Duplicate Layout"
msgstr "Duplica Layout"

#: pro/fields/class-acf-field-flexible-content.php:551
msgid "Add New Layout"
msgstr "Aggiungi Nuovo Layout"

#: pro/fields/class-acf-field-flexible-content.php:622
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:635
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:662
#: pro/fields/class-acf-field-repeater.php:458
msgid "Button Label"
msgstr "Etichetta Bottone"

#: pro/fields/class-acf-field-flexible-content.php:671
msgid "Minimum Layouts"
msgstr "Layout Minimi"

#: pro/fields/class-acf-field-flexible-content.php:680
msgid "Maximum Layouts"
msgstr "Layout Massimi"

#: pro/fields/class-acf-field-gallery.php:41
msgid "Add Image to Gallery"
msgstr "Aggiungi Immagine alla Galleria"

#: pro/fields/class-acf-field-gallery.php:45
msgid "Maximum selection reached"
msgstr "Selezione massima raggiunta"

#: pro/fields/class-acf-field-gallery.php:321
msgid "Length"
msgstr "Lunghezza"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Didascalia"

#: pro/fields/class-acf-field-gallery.php:373
msgid "Alt Text"
msgstr "Testo Alt"

#: pro/fields/class-acf-field-gallery.php:544
msgid "Add to gallery"
msgstr "Aggiungi a Galleria"

#: pro/fields/class-acf-field-gallery.php:548
msgid "Bulk actions"
msgstr "Azioni in blocco"

#: pro/fields/class-acf-field-gallery.php:549
msgid "Sort by date uploaded"
msgstr "Ordina per aggiornamento data"

#: pro/fields/class-acf-field-gallery.php:550
msgid "Sort by date modified"
msgstr "Ordina per data modifica"

#: pro/fields/class-acf-field-gallery.php:551
msgid "Sort by title"
msgstr "Ordina per titolo"

#: pro/fields/class-acf-field-gallery.php:552
msgid "Reverse current order"
msgstr "Ordine corrente inversa"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Close"
msgstr "Chiudi"

#: pro/fields/class-acf-field-gallery.php:624
msgid "Minimum Selection"
msgstr "Seleziona Minima"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Maximum Selection"
msgstr "Seleziona Massima"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Insert"
msgstr "Inserisci"

#: pro/fields/class-acf-field-gallery.php:643
msgid "Specify where new attachments are added"
msgstr "Specificare dove vengono aggiunti nuovi allegati"

#: pro/fields/class-acf-field-gallery.php:647
msgid "Append to the end"
msgstr "Aggiungere alla fine"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Prepend to the beginning"
msgstr "Anteporre all'inizio"

#: pro/fields/class-acf-field-repeater.php:36
msgid "Minimum rows reached ({min} rows)"
msgstr "Righe minime raggiunte ({min} righe)"

#: pro/fields/class-acf-field-repeater.php:37
msgid "Maximum rows reached ({max} rows)"
msgstr "Righe massime raggiunte ({max} righe)"

#: pro/fields/class-acf-field-repeater.php:335
msgid "Add row"
msgstr "Aggiungi riga"

#: pro/fields/class-acf-field-repeater.php:336
msgid "Remove row"
msgstr "Rimuovi riga"

#: pro/fields/class-acf-field-repeater.php:411
msgid "Collapsed"
msgstr "Collassata"

#: pro/fields/class-acf-field-repeater.php:412
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Selezionare un campo secondario da visualizzare quando la riga è collassata"

#: pro/fields/class-acf-field-repeater.php:422
msgid "Minimum Rows"
msgstr "Righe Minime"

#: pro/fields/class-acf-field-repeater.php:432
msgid "Maximum Rows"
msgstr "Righe Massime"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Nessuna Pagina Opzioni esistente"

#: pro/options-page.php:51
msgid "Options"
msgstr "Opzioni"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Opzioni Aggiornate"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Per attivare gli aggiornamenti, per favore inserisci la tua chiave di "
"licenza nella pagina <a href=\"%s\">Aggiornamenti</a>. Se non hai una chiave "
"di licenza, per favore vedi <a href=\"%s\">dettagli e prezzi</a>."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "No conditional fields available"
#~ msgstr "Non ci sono campi condizionali disponibili"

#~ msgid "Parent fields"
#~ msgstr "Campi genitore"

#~ msgid "Sibling fields"
#~ msgstr "Campi di pari livello"

#~ msgid "Left Aligned"
#~ msgstr "Allineamento a sinistra"

#~ msgid "Locating"
#~ msgstr "Localizzazione"

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Valori minimi raggiunti ( valori {min} )"

#~ msgid "Taxonomy Term"
#~ msgstr "Termine Tassonomia"

#~ msgid "No toggle fields available"
#~ msgstr "Nessun Campo Toggle disponibile"

#~ msgid "Export Field Groups to PHP"
#~ msgstr ""
#~ "Esporta \n"
#~ "Field Group\n"
#~ " di PHP"

#~ msgid "Download export file"
#~ msgstr "Scarica file di esportazione"

#~ msgid "Generate export code"
#~ msgstr "Generare codice di esportazione"

#~ msgid "Import"
#~ msgstr "Importa"

#~ msgid "No embed found for the given URL."
#~ msgstr "Nessun embed trovato per l'URL specificato."

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Il campo scheda visualizzerà correttamente quando aggiunto a un campo "
#~ "ripetitore stile di tabella o disposizione flessibile in campo dei "
#~ "contenuti"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "Usa \"Campi Scheda\" per organizzare al meglio la vostra schermata di "
#~ "modifica raggruppando i campi insieme."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Tutti i campi che seguono questo \"campo scheda\" (o finché un altro "
#~ "\"campo tab \" viene definito) verranno raggruppati utilizzando "
#~ "l'etichetta di questo campo come intestazione scheda."

#~ msgid "End-point"
#~ msgstr "Punto finale"

#~ msgid "Use this field as an end-point and start a new group of tabs"
#~ msgstr ""
#~ "Utilizzare questo campo come un punto finale e iniziare un nuovo gruppo "
#~ "di schede"

#~ msgid "Getting Started"
#~ msgstr "Guida introduttiva"

#~ msgid "Field Types"
#~ msgstr "Tipi di Field"

#~ msgid "Functions"
#~ msgstr "Funzioni"

#~ msgid "Actions"
#~ msgstr "Azioni"

#~ msgid "Features"
#~ msgstr "Caratteristiche"

#~ msgid "How to"
#~ msgstr "Come fare"

#~ msgid "Tutorials"
#~ msgstr "Tutorial"

#~ msgid "FAQ"
#~ msgstr "FAQ"

#~ msgid "Term meta upgrade not possible (termmeta table does not exist)"
#~ msgstr ""
#~ "Non è possibile l'aggiornamento del meta termine (la tabella termmeta non "
#~ "esiste)"

#~ msgid "Error"
#~ msgstr "Errore"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "1 campo richiede attenzione."
#~ msgstr[1] "%d campi richiedono attenzione."

#~ msgid ""
#~ "Error validating ACF PRO license URL (website does not match). Please re-"
#~ "activate your license"
#~ msgstr ""
#~ "Errore durante la convalida dell'URL della licenza di ACF PRO (sito web "
#~ "non corrisponde). Si prega di riattivare la licenza"

#~ msgid "See what's new"
#~ msgstr "Guarda cosa c'è di nuovo"

#~ msgid "Disabled"
#~ msgstr "Disabilitato"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Disabilitato <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Disabilitato <span class=\"count\">(%s)</span>"

#~ msgid "'How to' guides"
#~ msgstr "Guide del 'come si fa'"

#~ msgid "Created by"
#~ msgstr "Creato da"

#~ msgid "Text shown when not active"
#~ msgstr "Testo visualizzato quando non è attivo"

#~ msgid ""
#~ "Error validating license URL (website does not match). Please re-activate "
#~ "your license"
#~ msgstr ""
#~ "Errore nella convalida licenza URL (sito Web non corrisponde). Si prega "
#~ "di ri-attivare la licenza"

#~ msgid "Error loading update"
#~ msgstr "Errore durante il caricamento."

#~ msgid "eg. Show extra content"
#~ msgstr "es. Mostra contenuti extra"

#~ msgid "Select"
#~ msgstr "Seleziona"

#~ msgctxt "Field label"
#~ msgid "Clone"
#~ msgstr "Clona"

#~ msgctxt "Field instruction"
#~ msgid "Clone"
#~ msgstr "Clona"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Errore di connessione</b>. Spiacenti, per favore riprova"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr ""
#~ "<b>Successo</b>. Lo strumento di importazione ha aggiunto %s Field Group: "
#~ "%s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Attenzione</b>. Lo strumento di importazione ha trovato %s \n"
#~ "Field Group\n"
#~ " già esistenti e sono stati ignorati: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Aggiorna ACF"

#~ msgid "Upgrade"
#~ msgstr "Aggiornamento"

#~ msgid ""
#~ "The following sites require a DB upgrade. Check the ones you want to "
#~ "update and then click “Upgrade Database”."
#~ msgstr ""
#~ "I seguenti siti necessitano di un aggiornamento Database. Seleziona "
#~ "quelli da aggiornare e clicca \"Aggiorna Database\""

#~ msgid "Done"
#~ msgstr "Fatto"

#~ msgid "Today"
#~ msgstr "Oggi"

#~ msgid "Show a different month"
#~ msgstr "Mostra un altro mese"

#~ msgid "See what's new in"
#~ msgstr "Guarda cosa c'è di nuovo"

#~ msgid "version"
#~ msgstr "versione"

#~ msgid "Upgrading data to"
#~ msgstr "Aggiornare i dati a"

#~ msgid "Return format"
#~ msgstr "Formato ritorno"

#~ msgid "uploaded to this post"
#~ msgstr "caricare a questo post"

#~ msgid "File Name"
#~ msgstr "Nome file"

#~ msgid "File Size"
#~ msgstr "Dimensione file"

#~ msgid "No File selected"
#~ msgstr "Nessun file selezionato"

#~ msgid "Save Options"
#~ msgstr "Salva Opzioni"

#~ msgid "License"
#~ msgstr "Licenza"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Per sbloccare gli aggiornamenti, inserisci il tuo codice di licenza di "
#~ "seguito. Se non si dispone di una chiave di licenza, si prega di "
#~ "consultare"

#~ msgid "details & pricing"
#~ msgstr "dettagli & prezzi"

#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "Per attivare gli aggiornamenti, inserisci il tuo codice di licenza sulla "
#~ "pagina <a href=\"%s\">Aggiornamenti</a>. Se non si dispone di una chiave "
#~ "di licenza, si prega di consultare <a href=\"%s\">dettagli & prezzi</a>"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "http://www.advancedcustomfields.com/"
#~ msgstr "http://www.advancedcustomfields.com/"

#~ msgid "elliot condon"
#~ msgstr "elliot condon"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Trascina per riordinare"

#~ msgid "Add new %s "
#~ msgstr "Aggiungi %s "

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Si prega di notare che tutto il testo viene prima passato attraverso la "
#~ "funzione wp"

#~ msgid "Warning"
#~ msgstr "Attenzione"

#~ msgid "Import / Export"
#~ msgstr "Importa / Esporta"

#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr "I Field Group sono creati in ordine dal più basso al più alto"
