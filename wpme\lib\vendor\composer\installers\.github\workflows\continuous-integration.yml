name: "Continuous Integration"

on:
  - push
  - pull_request

env:
  COMPOSER_FLAGS: "--ansi --no-interaction --no-progress --prefer-dist"
  SYMFONY_PHPUNIT_REMOVE_RETURN_TYPEHINT: "1"

jobs:
  tests:
    name: "CI"

    runs-on: ubuntu-latest

    strategy:
      matrix:
        php-version:
          - "5.3"
          - "5.4"
          - "5.5"
          - "5.6"
          - "7.0"
          - "7.1"
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"
          - "8.1"
        dependencies: [locked]
        include:
          - php-version: "5.3"
            dependencies: lowest
          - php-version: "8.1"
            dependencies: lowest

    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"

      - name: "Install PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "${{ matrix.php-version }}"
          tools: composer:snapshot

      - name: Get composer cache directory
        id: composercache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache dependencies
        uses: actions/cache@v2
        with:
          path: ${{ steps.composercache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.json') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: "Handle lowest dependencies update"
        if: "contains(matrix.dependencies, 'lowest')"
        run: "echo \"COMPOSER_FLAGS=$COMPOSER_FLAGS --prefer-lowest\" >> $GITHUB_ENV"

      - name: "Upgrade phpunit-bridge if needed for php 8 lowest build"
        if: "contains(matrix.php-version, '8.')"
        run: |
          composer require symfony/phpunit-bridge:^5.3.3 --dev --no-update

      - name: "Install latest dependencies"
        run: |
          # Remove PHPStan as it requires a newer PHP
          composer remove phpstan/phpstan phpstan/phpstan-phpunit --dev --no-update
          composer update ${{ env.COMPOSER_FLAGS }}

      - name: "Run tests"
        run: "vendor/bin/simple-phpunit --verbose"
