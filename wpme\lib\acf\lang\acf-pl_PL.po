msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2018-09-06 12:21+1000\n"
"PO-Revision-Date: 2018-10-01 21:47+0200\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <da<PERSON><PERSON>@zielonka.pro>\n"
"Language: pl_PL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.0.3\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 "
"|| n%100>=20) ? 1 : 2);\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:80
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:385 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Grupy pól"

#: acf.php:386
msgid "Field Group"
msgstr "Grupa pól"

#: acf.php:387 acf.php:419 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New"
msgstr "Dodaj nową"

#: acf.php:388
msgid "Add New Field Group"
msgstr "Dodaj nową grupę pól"

#: acf.php:389
msgid "Edit Field Group"
msgstr "Edytuj grupę pól"

#: acf.php:390
msgid "New Field Group"
msgstr "Nowa grupa pól"

#: acf.php:391
msgid "View Field Group"
msgstr "Zobacz grupę pól"

#: acf.php:392
msgid "Search Field Groups"
msgstr "Szukaj grup pól"

#: acf.php:393
msgid "No Field Groups found"
msgstr "Nie znaleziono grupy pól"

#: acf.php:394
msgid "No Field Groups found in Trash"
msgstr "Brak grup pól w koszu"

#: acf.php:417 includes/admin/admin-field-group.php:202
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Pola"

#: acf.php:418
msgid "Field"
msgstr "Pole"

#: acf.php:420
msgid "Add New Field"
msgstr "Dodaj nowe pole"

#: acf.php:421
msgid "Edit Field"
msgstr "Edytuj pole"

#: acf.php:422 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr "Nowe pole"

#: acf.php:423
msgid "View Field"
msgstr "Zobacz pole"

#: acf.php:424
msgid "Search Fields"
msgstr "Szukaj pól"

#: acf.php:425
msgid "No Fields found"
msgstr "Nie znaleziono pól"

#: acf.php:426
msgid "No Fields found in Trash"
msgstr "Nie znaleziono pól w koszu"

#: acf.php:465 includes/admin/admin-field-group.php:384
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Nieaktywne"

#: acf.php:470
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Nieaktywne <span class=\"count\">(%s)</span>"
msgstr[1] "Nieaktywne <span class=\"count\">(%s)</span>"
msgstr[2] "Nieaktywnych <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Grupa pól została zaktualizowana."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Grupa pól została usunięta."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Grupa pól została opublikowana."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Grupa pól została zapisana."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Grupa pól została dodana."

#: includes/admin/admin-field-group.php:76
msgid "Field group scheduled for."
msgstr "Grupa pól została zaplanowana na."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Szkic grupy pól został zaktualizowany."

#: includes/admin/admin-field-group.php:153
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Ciąg znaków \"field_\" nie może zostać użyty na początku nazwy pola"

#: includes/admin/admin-field-group.php:154
msgid "This field cannot be moved until its changes have been saved"
msgstr "To pole nie może zostać przeniesione zanim zmiany nie zostaną zapisane"

#: includes/admin/admin-field-group.php:155
msgid "Field group title is required"
msgstr "Tytuł grupy pól jest wymagany"

#: includes/admin/admin-field-group.php:156
msgid "Move to trash. Are you sure?"
msgstr "Przenieś do kosza. Jesteś pewny?"

#: includes/admin/admin-field-group.php:157
msgid "No toggle fields available"
msgstr "Pola przełączania niedostępne"

#: includes/admin/admin-field-group.php:158
msgid "Move Custom Field"
msgstr "Przenieś pole"

#: includes/admin/admin-field-group.php:159
msgid "Checked"
msgstr "Zaznaczone"

#: includes/admin/admin-field-group.php:160 includes/api/api-field.php:289
msgid "(no label)"
msgstr "(brak etykiety)"

#: includes/admin/admin-field-group.php:161
msgid "(this field)"
msgstr "(to pole)"

#: includes/admin/admin-field-group.php:162
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "kopia"

#: includes/admin/admin-field-group.php:163
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:4073
msgid "or"
msgstr "lub"

#: includes/admin/admin-field-group.php:164
msgid "Null"
msgstr "Null"

#: includes/admin/admin-field-group.php:203
msgid "Location"
msgstr "Lokacja"

#: includes/admin/admin-field-group.php:204
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Ustawienia"

#: includes/admin/admin-field-group.php:354
msgid "Field Keys"
msgstr "Klucze pola"

#: includes/admin/admin-field-group.php:384
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Aktywne"

#: includes/admin/admin-field-group.php:746
msgid "Move Complete."
msgstr "Przenoszenie zakończone."

#: includes/admin/admin-field-group.php:747
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Pole %s znajduje się teraz w grupie pól %s"

#: includes/admin/admin-field-group.php:748
msgid "Close Window"
msgstr "Zamknij okno"

#: includes/admin/admin-field-group.php:789
msgid "Please select the destination for this field"
msgstr "Proszę wybrać miejsce przeznaczenia dla tego pola"

#: includes/admin/admin-field-group.php:796
msgid "Move Field"
msgstr "Przenieś pole"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Aktywny <span class=\"count\">(%s)</span>"
msgstr[1] "Aktywne <span class=\"count\">(%s)</span>"
msgstr[2] "Aktywnych <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr "Grupa pól została zduplikowana. %s"

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Grupa pól %s została zduplikowana."
msgstr[1] "Grupy pól %s zostały zduplikowane."
msgstr[2] "Grup pól %s zostały zduplikowane."

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr "Grupa pól została zsynchronizowana. %s"

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "%s grupa pól została zsynchronizowana."
msgstr[1] "%s grupy pól zostały zsynchronizowane."
msgstr[2] "%s grup pól zostało zsynchronizowanych."

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Synchronizacja możliwa"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:370
msgid "Title"
msgstr "Tytuł"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:397
msgid "Description"
msgstr "Opis"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Status"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Dostosuj WordPressa korzystając z potężnych, profesjonalnych i intuicyjnych "
"pól."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Dziennik zmian"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Zobacz co nowego w <a href=\"%s\">wersji %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Zasoby"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Witryna"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Dokumentacja"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Pomoc"

#: includes/admin/admin-field-groups.php:623
#: includes/admin/views/settings-info.php:84
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Dziękujemy za tworzenie z <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:667
msgid "Duplicate this item"
msgstr "Duplikuj to pole"

#: includes/admin/admin-field-groups.php:667
#: includes/admin/admin-field-groups.php:683
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate"
msgstr "Duplikuj"

#: includes/admin/admin-field-groups.php:700
#: includes/fields/class-acf-field-google-map.php:164
#: includes/fields/class-acf-field-relationship.php:674
msgid "Search"
msgstr "Szukaj"

#: includes/admin/admin-field-groups.php:759
#, php-format
msgid "Select %s"
msgstr "Wybierz %s"

#: includes/admin/admin-field-groups.php:767
msgid "Synchronise field group"
msgstr "Synchronizuj grupę pól"

#: includes/admin/admin-field-groups.php:767
#: includes/admin/admin-field-groups.php:797
msgid "Sync"
msgstr "Synchronizacja"

#: includes/admin/admin-field-groups.php:779
msgid "Apply"
msgstr "Zastosuj"

#: includes/admin/admin-field-groups.php:797
msgid "Bulk Actions"
msgstr "Akcje na wielu"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Narzędzia"

#: includes/admin/admin-upgrade.php:47 includes/admin/admin-upgrade.php:94
#: includes/admin/admin-upgrade.php:156
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Aktualizuj bazę danych"

#: includes/admin/admin-upgrade.php:180
msgid "Review sites & upgrade"
msgstr "Strona opinii i aktualizacji"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "Własne pola"

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Dodatki"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr "<b>Błąd</b>. Nie można załadować listy dodatków"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Informacja"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Co nowego"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Eksportuj grupy pól"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "Utwórz PHP"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Nie zaznaczono żadnej grupy pól"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "Wyeksportowano 1 grupę pól."
msgstr[1] "Wyeksportowano %s grupy pól."
msgstr[2] "Wyeksportowano %s grup pól."

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Wybierz grupy pól"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Wybierz grupy pól, które chcesz wyeksportować, a następnie wybierz metodę "
"eksportu. Użyj przycisku pobierania aby wyeksportować do pliku .json, który "
"można następnie zaimportować do innej instalacji ACF. Użyj przycisku generuj "
"do wyeksportowania ustawień do kodu PHP, który można umieścić w motywie."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Plik eksportu"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Poniższy kod może być użyty do rejestracji lokalnej wersji wybranej grupy "
"lub grup pól. Lokalna grupa pól może dostarczyć wiele korzyści takich jak "
"szybszy czas ładowania, możliwość wersjonowania i dynamiczne pola/"
"ustawienia. Wystarczy skopiować i wkleić poniższy kod do pliku functions.php "
"Twojego motywu lub dołączyć go do zewnętrznego pliku."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Skopiuj do schowka"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "Skopiowano"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Importuj grupy pól"

#: includes/admin/tools/class-acf-admin-tool-import.php:61
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Wybierz plik JSON Advanced Custom Fields, który chcesz zaimportować. Gdy "
"klikniesz przycisk importu poniżej, ACF zaimportuje grupy pól."

#: includes/admin/tools/class-acf-admin-tool-import.php:66
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "Wybierz plik"

#: includes/admin/tools/class-acf-admin-tool-import.php:76
msgid "Import File"
msgstr "Plik importu"

#: includes/admin/tools/class-acf-admin-tool-import.php:100
#: includes/fields/class-acf-field-file.php:170
msgid "No file selected"
msgstr "Nie zaznaczono żadnego pliku"

#: includes/admin/tools/class-acf-admin-tool-import.php:113
msgid "Error uploading file. Please try again"
msgstr "Błąd przesyłania pliku. Proszę spróbować ponownie"

#: includes/admin/tools/class-acf-admin-tool-import.php:122
msgid "Incorrect file type"
msgstr "Błędny typ pliku"

#: includes/admin/tools/class-acf-admin-tool-import.php:139
msgid "Import file empty"
msgstr "Importowany plik jest pusty"

#: includes/admin/tools/class-acf-admin-tool-import.php:247
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Zaimportowano 1 grupę pól"
msgstr[1] "Zaimportowano %s grupy pól"
msgstr[2] "Zaimportowano %s grup pól"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Wyświetlaj pola warunkowo"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Pokaż to pole jeśli"

#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr "oraz"

#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Dodaj grupę warunków"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:424
#: pro/fields/class-acf-field-repeater.php:294
msgid "Drag to reorder"
msgstr "Przeciągnij aby zmienić kolejność"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Edytuj pole"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:357
msgid "Edit"
msgstr "Edytuj"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Duplikuj to pole"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Przenieś pole do innej grupy"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Przenieś"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Usuń pole"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete"
msgstr "Usuń"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Etykieta pola"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Ta nazwa będzie widoczna na stronie edycji"

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Nazwa pola"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Pojedyncze słowo, bez spacji. Dozwolone są myślniki i podkreślniki"

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Typ pola"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Instrukcje"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "Instrukcje dla autorów. Będą widoczne w trakcie wprowadzania danych"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Wymagane?"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Atrybuty kontenera"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "szerokość"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "class"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Zamknij to pole"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Kolejność"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:428
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Label"
msgstr "Etykieta"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:939
#: pro/fields/class-acf-field-flexible-content.php:610
msgid "Name"
msgstr "Nazwa"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Klucz"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Typ"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Brak pól. Kliknij przycisk <strong>+ Dodaj pole</strong> aby utworzyć "
"pierwsze pole."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Dodaj pole"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Warunki"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Utwórz zestaw warunków, które określą w których miejscach będą wykorzystane "
"zdefiniowane tutaj własne pola"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Styl"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standardowy (WP metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Bezpodziałowy (brak metaboxa)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Pozycja"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Wysoka (pod tytułem)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normalna (pod edytorem)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Boczna"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Umieszczenie etykiet"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Wyrównanie do góry"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Wyrównanie do lewej"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Umieszczenie instrukcji"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Pod etykietami"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Pod polami"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Nr w kolejności."

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Grupy pól z niższym numerem pojawią się pierwsze"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Wyświetlany na liście grupy pól"

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "Odnośnik bezpośredni"

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "Edytor treści"

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "Wypis"

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "Dyskusja"

#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "Komentarze"

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "Wersje"

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "Slug"

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "Autor"

#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "Format"

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "Atrybuty strony"

#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:688
msgid "Featured Image"
msgstr "Obrazek wyróżniający"

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "Kategorie"

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "Tagi"

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "Wyślij trackbacki"

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "Ukryj na stronie edycji"

#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Wybierz</b> elementy, które chcesz <b>ukryć</b> na stronie edycji."

#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Jeśli na stronie edycji znajduje się kilka grup pól, zostaną zastosowane "
"ustawienia pierwszej z nich. (pierwsza grupa pól to ta, która ma najniższy "
"numer w kolejności)"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Następujące witryny wymagają aktualizacji bazy danych. Zaznacz te, które "
"chcesz zaktualizować i kliknij %s."

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "Aktualizacja witryn"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Witryna"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Witryna wymaga aktualizacji bazy danych z %s na %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr "Ta witryna jest aktualna"

#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Aktualizacja bazy danych zakończona. <a href=\"%s\">Wróć do kokpitu sieci</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr "Proszę wybrać co najmniej jedną witrynę do uaktualnienia."

#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Zdecydowanie zaleca się wykonanie kopii zapasowej bazy danych przed "
"kontynuowaniem. Czy na pewno chcesz uruchomić aktualizacje teraz?"

#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr "Aktualizowanie danych do wersji %s"

#: includes/admin/views/html-admin-page-upgrade-network.php:167
msgid "Upgrade complete."
msgstr "Aktualizacja zakończona."

#: includes/admin/views/html-admin-page-upgrade-network.php:176
#: includes/admin/views/html-admin-page-upgrade-network.php:185
#: includes/admin/views/html-admin-page-upgrade.php:78
#: includes/admin/views/html-admin-page-upgrade.php:87
msgid "Upgrade failed."
msgstr "Aktualizacja nie powiodła się."

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Czytam zadania aktualizacji..."

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Aktualizacja bazy danych zakończona. <a href=\"%s\">Zobacz co nowego</a>"

#: includes/admin/views/html-admin-page-upgrade.php:116
#: includes/ajax/class-acf-ajax-upgrade.php:33
msgid "No updates available."
msgstr "Brak dostępnych aktualizacji."

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Pokaż tą grupę pól jeśli"

#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Pole powtarzalne"

#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Elastyczne treść"

#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galeria"

#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Strona opcji"

#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr "Wymagana jest aktualizacja bazy danych"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "Dziękujemy za aktualizacje do %s v%s!"

#: includes/admin/views/html-notice-upgrade.php:22
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr "Ta wersja zawiera ulepszenia bazy danych i wymaga uaktualnienia."

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""
"Upewnij się także, że wszystkie dodatki premium (%s) zostały wcześniej "
"zaktualizowane do najnowszych wersji."

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Pobierz i instaluj"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Zainstalowano"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Witamy w Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Dziękujemy za aktualizację! ACF %s jest większy i lepszy niż kiedykolwiek "
"wcześniej. Mamy nadzieję, że go polubisz."

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "Lepsze odczucia w użytkowaniu"

#: includes/admin/views/settings-info.php:19
msgid "Improved Usability"
msgstr "Zwiększona użyteczność"

#: includes/admin/views/settings-info.php:20
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Użycie popularnej biblioteki Select2 poprawiło zarówno użyteczność jak i "
"szybkość wielu typów pól wliczając obiekty wpisów, odnośniki stron, "
"taksonomie i pola wyboru."

#: includes/admin/views/settings-info.php:24
msgid "Improved Design"
msgstr "Ulepszony wygląd"

#: includes/admin/views/settings-info.php:25
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"Wiele pól przeszło graficzne odświeżenie, aby ACF wyglądał lepiej niż "
"kiedykolwiek! Zmiany warte uwagi są widoczne w galerii, polach relacji i "
"polach oEmbed (nowość)!"

#: includes/admin/views/settings-info.php:29
msgid "Improved Data"
msgstr "Ulepszona struktura danych"

#: includes/admin/views/settings-info.php:30
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Przeprojektowanie architektury danych pozwoliła polom podrzędnym być "
"niezależnymi od swoich rodziców. Pozwala to na przeciąganie i upuszczanie "
"pól pomiędzy rodzicami!"

#: includes/admin/views/settings-info.php:38
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Do widzenia Dodatki. Dzień dobry PRO"

#: includes/admin/views/settings-info.php:41
msgid "Introducing ACF PRO"
msgstr "Przedstawiamy ACF PRO"

#: includes/admin/views/settings-info.php:42
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""
"Zmieniliśmy sposób funkcjonowania wersji premium - teraz jest dostarczana w "
"ekscytujący sposób!"

#: includes/admin/views/settings-info.php:43
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Wszystkie 4 dodatki premium zostały połączone w nową <a href=\"%s\">wersję "
"Pro ACF</a>. W obu licencjach, osobistej i deweloperskiej, funkcjonalność "
"premium jest bardziej przystępna niż kiedykolwiek wcześniej!"

#: includes/admin/views/settings-info.php:47
msgid "Powerful Features"
msgstr "Potężne funkcje"

#: includes/admin/views/settings-info.php:48
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO zawiera zaawansowane funkcje, takie jak powtarzalne dane, elastyczne "
"układy treści, piękne galerie i możliwość tworzenia dodatkowych stron opcji "
"administracyjnych!"

#: includes/admin/views/settings-info.php:49
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Przeczytaj więcej o <a href=\"%s\">możliwościach ACF PRO</a>."

#: includes/admin/views/settings-info.php:53
msgid "Easy Upgrading"
msgstr "Łatwa aktualizacja"

#: includes/admin/views/settings-info.php:54
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr ""
"Ulepszenie wersji do ACF PRO jest łatwe. Wystarczy zakupić licencję online i "
"pobrać wtyczkę!"

#: includes/admin/views/settings-info.php:55
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""
"Napisaliśmy również <a href=\"%s\">przewodnik aktualizacji</a> wyjaśniający "
"wiele zagadnień, jednak jeśli masz jakieś pytanie skontaktuj się z nami na "
"stronie <a href=\"%s\">wsparcia technicznego</a>."

#: includes/admin/views/settings-info.php:64
msgid "New Features"
msgstr "Nowe funkcje"

#: includes/admin/views/settings-info.php:69
msgid "Link Field"
msgstr "Pole linku"

#: includes/admin/views/settings-info.php:70
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr ""
"Pole linku zapewnia prosty sposób wybrać lub określić łącze (adres URL, "
"atrybut 'title', atrybut 'target')."

#: includes/admin/views/settings-info.php:74
msgid "Group Field"
msgstr "Pole grupy"

#: includes/admin/views/settings-info.php:75
msgid "The Group field provides a simple way to create a group of fields."
msgstr "Pole grupy zapewnia prosty sposób tworzenia grupy pól."

#: includes/admin/views/settings-info.php:79
msgid "oEmbed Field"
msgstr "Pole oEmbed"

#: includes/admin/views/settings-info.php:80
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr ""
"Pole oEmbed pozwala w łatwy sposób osadzać filmy, obrazy, tweety, audio i "
"inne treści."

#: includes/admin/views/settings-info.php:84
msgid "Clone Field"
msgstr "Pole klonowania"

#: includes/admin/views/settings-info.php:85
msgid "The clone field allows you to select and display existing fields."
msgstr "Pole klonowania umożliwia zaznaczanie i wyświetlanie istniejących pól."

#: includes/admin/views/settings-info.php:89
msgid "More AJAX"
msgstr "Więcej technologii AJAX"

#: includes/admin/views/settings-info.php:90
msgid "More fields use AJAX powered search to speed up page loading."
msgstr "Więcej pól korzysta z AJAX, aby przyspieszyć ładowanie stron."

#: includes/admin/views/settings-info.php:94
msgid "Local JSON"
msgstr "Lokalny JSON"

#: includes/admin/views/settings-info.php:95
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr ""
"Nowy zautomatyzowany eksport do JSON ma poprawioną szybkość i pozwala na "
"synchronizację."

#: includes/admin/views/settings-info.php:99
msgid "Easy Import / Export"
msgstr "Łatwy Import / Eksport"

#: includes/admin/views/settings-info.php:100
msgid "Both import and export can easily be done through a new tools page."
msgstr ""
"Zarówno import, jak i eksport można łatwo wykonać za pomocą nowej strony "
"narzędzi."

#: includes/admin/views/settings-info.php:104
msgid "New Form Locations"
msgstr "Nowe lokalizacje formularzy"

#: includes/admin/views/settings-info.php:105
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr ""
"Pola można teraz mapować na menu, pozycji menu, komentarzy, widżetów i "
"wszystkich formularzy użytkowników!"

#: includes/admin/views/settings-info.php:109
msgid "More Customization"
msgstr "Więcej dostosowywania"

#: includes/admin/views/settings-info.php:110
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr ""
"Dodano nowe akcje i filtry PHP (i JS), aby poszerzyć zakres personalizacji."

#: includes/admin/views/settings-info.php:114
msgid "Fresh UI"
msgstr "Fresh UI"

#: includes/admin/views/settings-info.php:115
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr ""
"Cała wtyczka została odświeżone, dodano nowe typy pól, ustawienia i wygląd!"

#: includes/admin/views/settings-info.php:119
msgid "New Settings"
msgstr "Nowe ustawienia"

#: includes/admin/views/settings-info.php:120
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr ""
"Zostały dodane ustawienia grup pól dotyczące, Aktywności, Pozycji etykiet "
"oraz Pozycji instrukcji i Opisu."

#: includes/admin/views/settings-info.php:124
msgid "Better Front End Forms"
msgstr "Lepszy wygląd formularzy (Front End Forms)"

#: includes/admin/views/settings-info.php:125
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr ""
"acf_form() może teraz utworzyć nowy wpis po przesłaniu i zawiera wiele "
"nowych ustawień."

#: includes/admin/views/settings-info.php:129
msgid "Better Validation"
msgstr "Lepsza walidacja"

#: includes/admin/views/settings-info.php:130
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr "Walidacja pól jest wykonana w PHP + AJAX a nie tylko w JS."

#: includes/admin/views/settings-info.php:134
msgid "Moving Fields"
msgstr "Przenoszenie pól"

#: includes/admin/views/settings-info.php:135
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr ""
"Nowa funkcjonalność pozwala na przenoszenie pól pomiędzy grupami i rodzicami."

#: includes/admin/views/settings-info.php:146
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Uważamy, że pokochasz zmiany wprowadzone w wersji %s."

#: includes/api/api-helpers.php:1046
msgid "Thumbnail"
msgstr "Miniatura"

#: includes/api/api-helpers.php:1047
msgid "Medium"
msgstr "Średni"

#: includes/api/api-helpers.php:1048
msgid "Large"
msgstr "Duży"

#: includes/api/api-helpers.php:1097
msgid "Full Size"
msgstr "Pełny rozmiar"

#: includes/api/api-helpers.php:1339 includes/api/api-helpers.php:1912
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(brak tytułu)"

#: includes/api/api-helpers.php:3994
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Szerokość obrazu musi mieć co najmniej %dpx."

#: includes/api/api-helpers.php:3999
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Szerokość obrazu nie może przekraczać %dpx."

#: includes/api/api-helpers.php:4015
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Wysokość obrazu musi mieć co najmniej %dpx."

#: includes/api/api-helpers.php:4020
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Wysokość obrazu nie może przekraczać %dpx."

#: includes/api/api-helpers.php:4038
#, php-format
msgid "File size must be at least %s."
msgstr "Rozmiar pliku musi wynosić co najmniej %s."

#: includes/api/api-helpers.php:4043
#, php-format
msgid "File size must must not exceed %s."
msgstr "Rozmiar pliku nie może przekraczać %s."

#: includes/api/api-helpers.php:4077
#, php-format
msgid "File type must be %s."
msgstr "Plik musi spełniać kryteria typu %s."

#: includes/assets.php:172
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Wprowadzone przez Ciebie zmiany przepadną jeśli przejdziesz do innej strony"

#: includes/assets.php:175 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr "Wybierz"

#: includes/assets.php:176
msgctxt "verb"
msgid "Edit"
msgstr "Edytuj"

#: includes/assets.php:177
msgctxt "verb"
msgid "Update"
msgstr "Aktualizuj"

#: includes/assets.php:178
msgid "Uploaded to this post"
msgstr "Przesłane do tego wpisu"

#: includes/assets.php:179
msgid "Expand Details"
msgstr "Rozwiń szczegóły"

#: includes/assets.php:180
msgid "Collapse Details"
msgstr "Zwiń szczegóły"

#: includes/assets.php:181
msgid "Restricted"
msgstr "Ograniczone"

#: includes/assets.php:182 includes/fields/class-acf-field-image.php:67
msgid "All images"
msgstr "Wszystkie obrazy"

#: includes/assets.php:185
msgid "Validation successful"
msgstr "Walidacja zakończona sukcesem"

#: includes/assets.php:186 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Walidacja nie powiodła się"

#: includes/assets.php:187
msgid "1 field requires attention"
msgstr "1 pole wymaga uwagi"

#: includes/assets.php:188
#, php-format
msgid "%d fields require attention"
msgstr "%d pól wymaga uwagi"

#: includes/assets.php:191
msgid "Are you sure?"
msgstr "Czy na pewno?"

#: includes/assets.php:192 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Tak"

#: includes/assets.php:193 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Nie"

#: includes/assets.php:194 includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-image.php:141
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:358
#: pro/fields/class-acf-field-gallery.php:546
msgid "Remove"
msgstr "Usuń"

#: includes/assets.php:195
msgid "Cancel"
msgstr "Anuluj"

#: includes/assets.php:198
msgid "Has any value"
msgstr "Ma dowolną wartość"

#: includes/assets.php:199
msgid "Has no value"
msgstr "Nie ma wartości"

#: includes/assets.php:200
msgid "Value is equal to"
msgstr "Wartość jest równa"

#: includes/assets.php:201
msgid "Value is not equal to"
msgstr "Wartość nie jest równa"

#: includes/assets.php:202
msgid "Value matches pattern"
msgstr "Wartość musi pasować do wzoru"

#: includes/assets.php:203
msgid "Value contains"
msgstr "Wartość zawiera"

#: includes/assets.php:204
msgid "Value is greater than"
msgstr "Wartość jest większa niż"

#: includes/assets.php:205
msgid "Value is less than"
msgstr "Wartość jest mniejsza niż"

#: includes/assets.php:206
msgid "Selection is greater than"
msgstr "Wybór jest większy niż"

#: includes/assets.php:207
msgid "Selection is less than"
msgstr "Wybór jest mniejszy niż"

#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "Typ pola nie istnieje"

#: includes/fields.php:308
msgid "Unknown"
msgstr "Nieznane"

#: includes/fields.php:349
msgid "Basic"
msgstr "Podstawowe"

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "Treść"

#: includes/fields.php:351
msgid "Choice"
msgstr "Wybór"

#: includes/fields.php:352
msgid "Relational"
msgstr "Relacyjne"

#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:567
#: pro/fields/class-acf-field-flexible-content.php:616
#: pro/fields/class-acf-field-repeater.php:443
msgid "Layout"
msgstr "Układ"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Zwijane panele"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Otwarte"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Pokaż ten zwijany panel jako otwarty po załadowaniu strony."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Multi-expand"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Zezwól, aby ten zwijany panel otwierał się bez zamykania innych."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Punkt końcowy"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Zdefiniuj punkt końcowy dla zatrzymania poprzedniego panelu zwijanego. Ten "
"panel zwijany nie będzie widoczny."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Grupa przycisków"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:359
msgid "Choices"
msgstr "Wybory"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:360
msgid "Enter each choice on a new line."
msgstr "Wpisz każdy z wyborów w osobnej linii."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:360
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Aby uzyskać większą kontrolę, można określić zarówno wartość i etykietę w "
"niniejszy sposób:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:360
msgid "red : Red"
msgstr "czerwony : Czerwony"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-taxonomy.php:784
#: includes/fields/class-acf-field-user.php:409
msgid "Allow Null?"
msgstr "Zezwolić na pustą wartość Null?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-select.php:368
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:381
msgid "Default Value"
msgstr "Domyślna wartość"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:150
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Appears when creating a new post"
msgstr "Wyświetlane podczas tworzenia nowego wpisu"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Poziomy"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Pionowy"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:205
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Return Value"
msgstr "Zwracana wartość"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Określ zwracaną wartość na stronie (front-end)"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:427
msgid "Value"
msgstr "Wartość"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:429
msgid "Both (Array)"
msgstr "Oba (Array)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Checkbox"
msgstr "Wybór (checkbox)"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Przełącz wszystko"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Dodaj nowy wybór"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Zezwól na niestandardowe"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "Zezwalaj na dodawanie \"niestandardowych\" wartości"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Zapisz niestandardowe"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "Zapisz \"niestandardowe\" wartości tego pola wyboru"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each default value on a new line"
msgstr "Wpisz każdą domyślną wartość w osobnej linii"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Przełącznik (Toggle)"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"Dołącz dodatkowe pole wyboru, aby grupowo włączać/wyłączać wszystkie pola "
"wyboru"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Wybór koloru"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Wyczyść"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Domyślna wartość"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Wybierz kolor"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Bieżący Kolor"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Wybór daty"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Gotowe"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Dzisiaj"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "Dalej"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Wstecz"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Tydz"

#: includes/fields/class-acf-field-date_picker.php:180
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Format wyświetlania"

#: includes/fields/class-acf-field-date_picker.php:181
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Wyświetlany format przy edycji wpisu"

#: includes/fields/class-acf-field-date_picker.php:189
#: includes/fields/class-acf-field-date_picker.php:220
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Niestandardowe:"

#: includes/fields/class-acf-field-date_picker.php:199
msgid "Save Format"
msgstr "Zapisz format"

#: includes/fields/class-acf-field-date_picker.php:200
msgid "The format used when saving a value"
msgstr "Format używany podczas zapisywania wartości"

#: includes/fields/class-acf-field-date_picker.php:210
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:715
#: includes/fields/class-acf-field-select.php:422
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:428
msgid "Return Format"
msgstr "Zwracany format"

#: includes/fields/class-acf-field-date_picker.php:211
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "Wartość zwracana przez funkcje w szablonie"

#: includes/fields/class-acf-field-date_picker.php:229
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "Tydzień zaczyna się od"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Wybieranie daty i godziny"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Wybierz czas"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Czas"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Godzina"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Minuta"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Sekunda"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisekunda"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosekunda"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Strefa czasu"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Teraz"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Gotowe"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Wybierz"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "E-mail"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Placeholder (tekst zastępczy)"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Pojawia się w polu formularza"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Przed polem (prefiks)"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:189
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Pojawia się przed polem formularza"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Za polem (sufiks)"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:198
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Pojawia się za polem formularza"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Plik"

#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "Edytuj plik"

#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "Aktualizuj plik"

#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr "Nazwa pliku"

#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:265
#: includes/fields/class-acf-field-image.php:294
#: pro/fields/class-acf-field-gallery.php:705
#: pro/fields/class-acf-field-gallery.php:734
msgid "File size"
msgstr "Wielkość pliku"

#: includes/fields/class-acf-field-file.php:170
msgid "Add File"
msgstr "Dodaj plik"

#: includes/fields/class-acf-field-file.php:221
msgid "File Array"
msgstr "Tablica pliku (Array)"

#: includes/fields/class-acf-field-file.php:222
msgid "File URL"
msgstr "Adres URL pliku"

#: includes/fields/class-acf-field-file.php:223
msgid "File ID"
msgstr "ID pliku"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:230
#: pro/fields/class-acf-field-gallery.php:670
msgid "Library"
msgstr "Biblioteka"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:671
msgid "Limit the media library choice"
msgstr "Ograniczenie wyborów z biblioteki"

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:236
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:676
msgid "All"
msgstr "Wszystkie"

#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-image.php:237
#: pro/fields/class-acf-field-gallery.php:677
msgid "Uploaded to post"
msgstr "Przesłane do wpisu"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:244
#: pro/fields/class-acf-field-gallery.php:684
msgid "Minimum"
msgstr "Minimum"

#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-file.php:256
msgid "Restrict which files can be uploaded"
msgstr "Określ jakie pliki mogą być przesyłane"

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:273
#: pro/fields/class-acf-field-gallery.php:713
msgid "Maximum"
msgstr "Maksimum"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:302
#: pro/fields/class-acf-field-gallery.php:742
msgid "Allowed file types"
msgstr "Dozwolone typy plików"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:743
msgid "Comma separated list. Leave blank for all types"
msgstr "Lista rozdzielana przecinkami. Pozostaw puste dla wszystkich typów"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Mapa Google"

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "Przepraszamy, ta przeglądarka nie obsługuje geolokalizacji"

#: includes/fields/class-acf-field-google-map.php:165
msgid "Clear location"
msgstr "Wyczyść lokalizację"

#: includes/fields/class-acf-field-google-map.php:166
msgid "Find current location"
msgstr "Znajdź aktualną lokalizację"

#: includes/fields/class-acf-field-google-map.php:169
msgid "Search for address..."
msgstr "Szukaj adresu..."

#: includes/fields/class-acf-field-google-map.php:199
#: includes/fields/class-acf-field-google-map.php:210
msgid "Center"
msgstr "Wyśrodkuj"

#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-google-map.php:211
msgid "Center the initial map"
msgstr "Wyśrodkuj początkową mapę"

#: includes/fields/class-acf-field-google-map.php:222
msgid "Zoom"
msgstr "Zbliżenie"

#: includes/fields/class-acf-field-google-map.php:223
msgid "Set the initial zoom level"
msgstr "Ustaw początkowe zbliżenie"

#: includes/fields/class-acf-field-google-map.php:232
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:696
#: pro/fields/class-acf-field-gallery.php:725
msgid "Height"
msgstr "Wysokość"

#: includes/fields/class-acf-field-google-map.php:233
msgid "Customise the map height"
msgstr "Dostosuj wysokość mapy"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Grupa"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:379
msgid "Sub Fields"
msgstr "Pola podrzędne"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Określ style stosowane to renderowania wybranych pól"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:627
#: pro/fields/class-acf-field-repeater.php:451
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:626
#: pro/fields/class-acf-field-repeater.php:450
msgid "Table"
msgstr "Tabela"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:628
#: pro/fields/class-acf-field-repeater.php:452
msgid "Row"
msgstr "Wiersz"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Obraz"

#: includes/fields/class-acf-field-image.php:64
msgid "Select Image"
msgstr "Wybierz obraz"

#: includes/fields/class-acf-field-image.php:65
msgid "Edit Image"
msgstr "Edytuj obraz"

#: includes/fields/class-acf-field-image.php:66
msgid "Update Image"
msgstr "Aktualizuj obraz"

#: includes/fields/class-acf-field-image.php:157
msgid "No image selected"
msgstr "Nie wybrano obrazu"

#: includes/fields/class-acf-field-image.php:157
msgid "Add Image"
msgstr "Dodaj obraz"

#: includes/fields/class-acf-field-image.php:211
msgid "Image Array"
msgstr "Tablica obrazów (Array)"

#: includes/fields/class-acf-field-image.php:212
msgid "Image URL"
msgstr "Adres URL obrazu"

#: includes/fields/class-acf-field-image.php:213
msgid "Image ID"
msgstr "ID obrazu"

#: includes/fields/class-acf-field-image.php:220
msgid "Preview Size"
msgstr "Rozmiar podglądu"

#: includes/fields/class-acf-field-image.php:221
msgid "Shown when entering data"
msgstr "Widoczny podczas wprowadzania danych"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:685
#: pro/fields/class-acf-field-gallery.php:714
msgid "Restrict which images can be uploaded"
msgstr "Określ jakie obrazy mogą być przesyłane"

#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Width"
msgstr "Szerokość"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Link"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Wybierz link"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Otwiera się w nowym oknie/karcie"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Tablica linków (Array)"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "Adres URL linku"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Wiadomość"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Nowe linie"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Kontroluje jak nowe linie są renderowane"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Automatycznie dodaj akapity"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Automatycznie dodaj &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Brak formatowania"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "Dodawaj znaki ucieczki do HTML (escape HTML)"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""
"Zezwól aby znaczniki HTML były wyświetlane jako widoczny tekst, a nie "
"renderowane"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Liczba"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:158
msgid "Minimum Value"
msgstr "Minimalna wartość"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:168
msgid "Maximum Value"
msgstr "Maksymalna wartość"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:178
msgid "Step Size"
msgstr "Wielkość kroku"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Wartość musi być liczbą"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Wartość musi być równa lub wyższa od %d"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Wartość musi być równa lub niższa od %d"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "Wprowadź adres URL"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Rozmiar osadzenia"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Link do strony"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Archiwa"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:961
msgid "Parent"
msgstr "Rodzic"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:641
msgid "Filter by Post Type"
msgstr "Filtruj wg typu wpisu"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:649
msgid "All post types"
msgstr "Wszystkie typy wpisów"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:655
msgid "Filter by Taxonomy"
msgstr "Filtruj wg taksonomii"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:663
msgid "All taxonomies"
msgstr "Wszystkie taksonomie"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Pozwól na adresy URL archiwów"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:387
#: includes/fields/class-acf-field-user.php:419
msgid "Select multiple values?"
msgstr "Możliwość wyboru wielu wartości?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Hasło"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:720
msgid "Post Object"
msgstr "Obiekt wpisu"

#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:721
msgid "Post ID"
msgstr "ID wpisu"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Przycisk opcji (radio)"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Inne"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr ""
"Dodaj pole \"inne\" aby zezwolić na wartości definiowane przez użytkownika"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Zapisz inne"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "Dopisz zapisaną wartość pola \"inne\" do wyborów tego pola"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Zakres"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "Relacja"

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "Maksymalna liczba wartości została przekroczona ( {max} wartości )"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "Ładowanie"

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "Nie znaleziono pasujących wyników"

#: includes/fields/class-acf-field-relationship.php:441
msgid "Select post type"
msgstr "Wybierz typ wpisu"

#: includes/fields/class-acf-field-relationship.php:467
msgid "Select taxonomy"
msgstr "Wybierz taksonomię"

#: includes/fields/class-acf-field-relationship.php:557
msgid "Search..."
msgstr "Szukaj..."

#: includes/fields/class-acf-field-relationship.php:669
msgid "Filters"
msgstr "Filtry"

#: includes/fields/class-acf-field-relationship.php:675
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Typ wpisu"

#: includes/fields/class-acf-field-relationship.php:676
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:754
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "Taksonomia"

#: includes/fields/class-acf-field-relationship.php:683
msgid "Elements"
msgstr "Elementy"

#: includes/fields/class-acf-field-relationship.php:684
msgid "Selected elements will be displayed in each result"
msgstr "Wybrane elementy będą wyświetlone przy każdym wyniku"

#: includes/fields/class-acf-field-relationship.php:695
msgid "Minimum posts"
msgstr "Minimum wpisów"

#: includes/fields/class-acf-field-relationship.php:704
msgid "Maximum posts"
msgstr "Maksimum wpisów"

#: includes/fields/class-acf-field-relationship.php:808
#: pro/fields/class-acf-field-gallery.php:815
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s wymaga dokonania przynajmniej %s wyboru"
msgstr[1] "%s wymaga dokonania przynajmniej %s wyborów"
msgstr[2] "%s wymaga dokonania przynajmniej %s wyborów"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:776
msgctxt "noun"
msgid "Select"
msgstr "Wybór"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Dostępny jest jeden wynik. Aby go wybrać, wciśnij klawisz enter."

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "Dostępnych wyników - %d. Użyj strzałek w górę i w dół, aby nawigować."

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Nie znaleziono wyników"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Wpisz 1 lub więcej znaków"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Wpisz %d lub więcej znaków"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Proszę usunąć 1 znak"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Proszę usunąć %d znaki/ów"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Możesz wybrać tylko 1 element"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Możesz wybrać tylko %d elementy/ów"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Ładuję więcej wyników&hellip;"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Szukam&hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Ładowanie zakończone niepowodzeniem"

#: includes/fields/class-acf-field-select.php:397
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Ostylowany interfejs użytkownika"

#: includes/fields/class-acf-field-select.php:407
msgid "Use AJAX to lazy load choices?"
msgstr "Użyć technologii AJAX do wczytywania wyników?"

#: includes/fields/class-acf-field-select.php:423
msgid "Specify the value returned"
msgstr "Określ zwracaną wartość"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Separator"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Zakładka"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Położenie"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr "Użyj tego pola jako punkt końcowy i zacznij nową grupę zakładek."

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "Brak %s"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Select the taxonomy to be displayed"
msgstr "Wybierz taksonomię do wyświetlenia"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Appearance"
msgstr "Wygląd"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Select the appearance of this field"
msgstr "Określ wygląd tego pola"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Multiple Values"
msgstr "Wiele wartości"

#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Multi Select"
msgstr "Wybór wielokrotny"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Single Value"
msgstr "Pojedyncza wartość"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Radio Buttons"
msgstr "Przycisk opcji (radio)"

#: includes/fields/class-acf-field-taxonomy.php:799
msgid "Create Terms"
msgstr "Tworzenie terminów taksonomii"

#: includes/fields/class-acf-field-taxonomy.php:800
msgid "Allow new terms to be created whilst editing"
msgstr "Pozwól na tworzenie nowych terminów taksonomii podczas edycji"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Save Terms"
msgstr "Zapisz terminy taksonomii"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Connect selected terms to the post"
msgstr "Przypisz wybrane terminy taksonomii do wpisu"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Load Terms"
msgstr "Wczytaj terminy taksonomii"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Load value from posts terms"
msgstr "Wczytaj wartości z terminów taksonomii z wpisu"

#: includes/fields/class-acf-field-taxonomy.php:834
msgid "Term Object"
msgstr "Obiekt terminu (WP_Term)"

#: includes/fields/class-acf-field-taxonomy.php:835
msgid "Term ID"
msgstr "ID terminu"

#: includes/fields/class-acf-field-taxonomy.php:885
#, php-format
msgid "User unable to add new %s"
msgstr "Użytkownik nie może dodać nowych %s"

#: includes/fields/class-acf-field-taxonomy.php:895
#, php-format
msgid "%s already exists"
msgstr "%s już istnieje"

#: includes/fields/class-acf-field-taxonomy.php:927
#, php-format
msgid "%s added"
msgstr "Dodano %s"

#: includes/fields/class-acf-field-taxonomy.php:973
msgid "Add"
msgstr "Dodaj"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Tekst"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Limit znaków"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Pozostaw puste w przypadku braku limitu"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Obszar tekstowy"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Wiersze"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Określa wysokość obszaru tekstowego"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Wybieranie daty i godziny"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Prawda / Fałsz"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "Wyświetla tekst obok pola wyboru (checkbox)"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Tekst, gdy włączone"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Tekst wyświetlany, gdy jest aktywne"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "Tekst, gdy wyłączone"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "Tekst wyświetlany, gdy jest nieaktywne"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Url"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Wartość musi być poprawnym adresem URL"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Użytkownik"

#: includes/fields/class-acf-field-user.php:394
msgid "Filter by role"
msgstr "Filtruj wg roli"

#: includes/fields/class-acf-field-user.php:402
msgid "All user roles"
msgstr "Wszystkie role użytkownika"

#: includes/fields/class-acf-field-user.php:433
msgid "User Array"
msgstr "Tablica użytkowników (Array)"

#: includes/fields/class-acf-field-user.php:434
msgid "User Object"
msgstr "Obiekt użytkownika"

#: includes/fields/class-acf-field-user.php:435
msgid "User ID"
msgstr "ID użytkownika"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Edytor WYSIWYG"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual"
msgstr "Wizualny"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Tekstowy"

#: includes/fields/class-acf-field-wysiwyg.php:337
msgid "Click to initialize TinyMCE"
msgstr "Kliknij, aby zainicjować TinyMCE"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Tabs"
msgstr "Zakładki"

#: includes/fields/class-acf-field-wysiwyg.php:395
msgid "Visual & Text"
msgstr "Wizualna i Tekstowa"

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Visual Only"
msgstr "Tylko wizualna"

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Text Only"
msgstr "Tylko tekstowa"

#: includes/fields/class-acf-field-wysiwyg.php:404
msgid "Toolbar"
msgstr "Pasek narzędzi"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr "Wyświetlić przyciski Dodawania mediów?"

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr "Opóźnić inicjowanie?"

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "TinyMCE nie zostanie zainicjowane do momentu kliknięcia pola"

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:301
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr "Edytuj grupę pól"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "Waliduj E-mail"

#: includes/forms/form-front.php:103 pro/fields/class-acf-field-gallery.php:588
#: pro/options-page.php:81
msgid "Update"
msgstr "Aktualizuj"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Wpis zaktualizowany"

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr "Wykryto Spam"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Wpis"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Strona"

#: includes/locations.php:96
msgid "Forms"
msgstr "Formularze"

#: includes/locations.php:243
msgid "is equal to"
msgstr "jest równe"

#: includes/locations.php:244
msgid "is not equal to"
msgstr "jest inne niż"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Załącznik"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Wszystkie formaty %s"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Komentarz"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Rola bieżącego użytkownika"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Super Administrator"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Bieżący użytkownik"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Zalogowany"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Wyświetla stronę (front-end)"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Wyświetla kokpit (back-end)"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Element menu"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Menu"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Pozycje menu"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Wiele menu"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Rodzic strony"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Szablon strony"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Domyślny szablon"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Typ strony"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "Strona główna"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "Strona wpisów"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "Strona najwyższego poziomu (brak rodzica)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "Strona będąca rodzicem (posiada potomne)"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "Strona będąca potomną (ma rodziców)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Kategoria wpisu"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Format wpisu"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Status wpisu"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Taksonomia wpisu"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Szablon wpisu"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Formularz użytkownika"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Dodaj / Edytuj"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Zarejestruj"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Rola użytkownika"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Widżet"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s wartość jest wymagana"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Opublikuj"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Żadna grupa pól nie została dodana do tej strony opcji. <a href=\"%s"
"\">Utwórz grupę własnych pól</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Błąd</b>. Nie można połączyć z serwerem aktualizacji"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Aktualizacje"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Deaktywuj licencję"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Aktywuj licencję"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Informacje o licencji"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Żeby odblokować aktualizacje proszę podać swój klucz licencyjny poniżej. "
"Jeśli nie posiadasz klucza prosimy zapoznać się ze <a href=\"%s\" target="
"\"_blank\">szczegółami i cennikiem</a>."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Klucz licencyjny"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Informacje o aktualizacji"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Zainstalowana wersja"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Najnowsza wersja"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Dostępna aktualizacja"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Aktualizuj wtyczkę"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr ""
"Proszę wpisać swój klucz licencyjny powyżej aby odblokować aktualizacje"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Sprawdź ponownie"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Informacje o aktualizacji"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Klon"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Wybierz jedno lub więcej pól które chcesz sklonować"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Wyświetl"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Określ styl wykorzystywany do stosowania w klonowanych polach"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grupuj (wyświetla wybrane pola w grupie)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Ujednolicenie (zastępuje to pole wybranymi polami)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Etykiety będą wyświetlane jako %s"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Prefiks Etykiet Pól"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Wartości będą zapisane jako %s"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Prefiks Nazw Pól"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Nieznane pole"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Nieznana grupa pól"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "Wszystkie pola z grupy pola %s"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:463
msgid "Add Row"
msgstr "Dodaj wiersz"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:938
#: pro/fields/class-acf-field-flexible-content.php:1020
msgid "layout"
msgid_plural "layouts"
msgstr[0] "układ"
msgstr[1] "układy"
msgstr[2] "układów"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "układy"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:937
#: pro/fields/class-acf-field-flexible-content.php:1019
msgid "This field requires at least {min} {label} {identifier}"
msgstr "To pole wymaga przynajmniej {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "To pole ma ograniczenie {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} dostępne (max {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} wymagane (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "Elastyczne pole wymaga przynajmniej 1 układu"

#: pro/fields/class-acf-field-flexible-content.php:302
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr "Kliknij przycisk \"%s\" poniżej, aby zacząć tworzyć nowy układ"

#: pro/fields/class-acf-field-flexible-content.php:427
msgid "Add layout"
msgstr "Dodaj układ"

#: pro/fields/class-acf-field-flexible-content.php:428
msgid "Remove layout"
msgstr "Usuń układ"

#: pro/fields/class-acf-field-flexible-content.php:429
#: pro/fields/class-acf-field-repeater.php:296
msgid "Click to toggle"
msgstr "Kliknij, aby przełączyć"

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder Layout"
msgstr "Zmień kolejność układów"

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder"
msgstr "Zmień kolejność"

#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete Layout"
msgstr "Usuń układ"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate Layout"
msgstr "Duplikuj układ"

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New Layout"
msgstr "Dodaj nowy układ"

#: pro/fields/class-acf-field-flexible-content.php:643
msgid "Min"
msgstr "Min"

#: pro/fields/class-acf-field-flexible-content.php:656
msgid "Max"
msgstr "Max"

#: pro/fields/class-acf-field-flexible-content.php:683
#: pro/fields/class-acf-field-repeater.php:459
msgid "Button Label"
msgstr "Etykieta przycisku"

#: pro/fields/class-acf-field-flexible-content.php:692
msgid "Minimum Layouts"
msgstr "Minimalna liczba układów"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "Maksymalna liczba układów"

#: pro/fields/class-acf-field-gallery.php:71
msgid "Add Image to Gallery"
msgstr "Dodaj obraz do galerii"

#: pro/fields/class-acf-field-gallery.php:72
msgid "Maximum selection reached"
msgstr "Maksimum ilości wyborów osiągnięte"

#: pro/fields/class-acf-field-gallery.php:336
msgid "Length"
msgstr "Długość"

#: pro/fields/class-acf-field-gallery.php:379
msgid "Caption"
msgstr "Etykieta"

#: pro/fields/class-acf-field-gallery.php:388
msgid "Alt Text"
msgstr "Tekst alternatywny"

#: pro/fields/class-acf-field-gallery.php:559
msgid "Add to gallery"
msgstr "Dodaj do galerii"

#: pro/fields/class-acf-field-gallery.php:563
msgid "Bulk actions"
msgstr "Działania na wielu"

#: pro/fields/class-acf-field-gallery.php:564
msgid "Sort by date uploaded"
msgstr "Sortuj po dacie przesłania"

#: pro/fields/class-acf-field-gallery.php:565
msgid "Sort by date modified"
msgstr "Sortuj po dacie modyfikacji"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Sort by title"
msgstr "Sortuj po tytule"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Reverse current order"
msgstr "Odwróć aktualną kolejność"

#: pro/fields/class-acf-field-gallery.php:585
msgid "Close"
msgstr "Zamknij"

#: pro/fields/class-acf-field-gallery.php:639
msgid "Minimum Selection"
msgstr "Minimalna liczba wybranych elementów"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Maximum Selection"
msgstr "Maksymalna liczba wybranych elementów"

#: pro/fields/class-acf-field-gallery.php:657
msgid "Insert"
msgstr "Wstaw"

#: pro/fields/class-acf-field-gallery.php:658
msgid "Specify where new attachments are added"
msgstr "Określ gdzie są dodawane nowe załączniki"

#: pro/fields/class-acf-field-gallery.php:662
msgid "Append to the end"
msgstr "Dodaj na końcu"

#: pro/fields/class-acf-field-gallery.php:663
msgid "Prepend to the beginning"
msgstr "Dodaj do początku"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:656
msgid "Minimum rows reached ({min} rows)"
msgstr "Osiągnięto minimum liczby wierszy ( {min} wierszy )"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "Osiągnięto maksimum liczby wierszy ( {max} wierszy )"

#: pro/fields/class-acf-field-repeater.php:333
msgid "Add row"
msgstr "Dodaj wiersz"

#: pro/fields/class-acf-field-repeater.php:334
msgid "Remove row"
msgstr "Usuń wiersz"

#: pro/fields/class-acf-field-repeater.php:412
msgid "Collapsed"
msgstr "Zwinięty"

#: pro/fields/class-acf-field-repeater.php:413
msgid "Select a sub field to show when row is collapsed"
msgstr ""
"Wybierz pole podrzędne, które mają być pokazane kiedy wiersz jest zwinięty"

#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum Rows"
msgstr "Minimalna liczba wierszy"

#: pro/fields/class-acf-field-repeater.php:433
msgid "Maximum Rows"
msgstr "Minimalna liczba wierszy"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Strona opcji nie istnieje"

#: pro/options-page.php:51
msgid "Options"
msgstr "Opcje"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Ustawienia zostały zaktualizowane"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Żeby włączyć aktualizacje, proszę podać swój klucz licencyjny na stronie <a "
"href=\"%s\">Aktualizacji</a>. Jeśli nie posiadasz klucza, prosimy zapoznać "
"się ze <a href=\"%s\">szczegółami i cennikiem</a>."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "Error validating request"
#~ msgstr "Błąd podczas walidacji żądania"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Aktualizacja bazy danych Advanced Custom Fields"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Zanim zaczniesz korzystać z niesamowitych funkcji prosimy o "
#~ "zaktualizowanie bazy danych do najnowszej wersji."

#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Aby aktualizacja była łatwa, <a href=\"%s\">zaloguj się do swojego konta</"
#~ "a> i pobierz darmową kopię ACF PRO!"

#~ msgid "Under the Hood"
#~ msgstr "Pod maską"

#~ msgid "Smarter field settings"
#~ msgstr "Sprytniejsze ustawienia pól"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr "ACF teraz zapisuje ustawienia pól jako osobny obiekt wpisu"

#~ msgid "Better version control"
#~ msgstr "Lepsza kontrola wersji"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "Nowy zautomatyzowany eksport do JSON pozwala na wersjonowanie ustawień pól"

#~ msgid "Swapped XML for JSON"
#~ msgstr "Zmiana XML na JSON"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "Import / Eksport teraz korzysta z JSON zamiast XML"

#~ msgid "New Forms"
#~ msgstr "Nowe formularze"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "Dodano nowe pole do osadzania zawartości"

#~ msgid "New Gallery"
#~ msgstr "Nowa galeria"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "Pola galerii przeszły niezbędny facelifting"

#~ msgid "Relationship Field"
#~ msgstr "Pole relacji"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr ""
#~ "Nowe ustawienia pola relacji dla \"Filtrów\" (Wyszukiwarka, Typ Wpisu, "
#~ "Taksonomia)"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Nowe grupy archiwów do wyboru dla pola page_link"

#~ msgid "Better Options Pages"
#~ msgstr "Lepsze strony opcji"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Nowe funkcje dla strony opcji pozwalają tworzyć strony w menu będące "
#~ "rodzicami oraz potomnymi."

#~ msgid "Parent fields"
#~ msgstr "Pola nadrzędne"

#~ msgid "Sibling fields"
#~ msgstr "Pola tego samego poziomu"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Eksportuj grupy pól do PHP"

#~ msgid "Download export file"
#~ msgstr "Pobierz plik eksportu"

#~ msgid "Generate export code"
#~ msgstr "Generuj kod eksportu"

#~ msgid "Import"
#~ msgstr "Import"

#~ msgid "Locating"
#~ msgstr "Lokalizacja"

#~ msgid "Error."
#~ msgstr "Błąd."

#~ msgid "No embed found for the given URL."
#~ msgstr "Nie znaleziono osadzenia dla podanego URLa."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "Minimalna wartość została przekroczona ( {min} )"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Pole zakładki będzie wyświetlane nieprawidłowo jeśli zostanie dodano do "
#~ "pola powtarzalnego wyświetlanego jako tabela lub do elastycznego pola"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr "Użyj \"Pola zakładki\" aby uporządkować ekran edycji grupując pola."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Wszystkie pola po tym \"polu zakładki\" (lub przed następnym \"polem "
#~ "zakładki\") zostaną zgrupowane razem używając etykiety tego pola jako "
#~ "nagłówka."

#~ msgid "None"
#~ msgstr "Brak"

#~ msgid "Taxonomy Term"
#~ msgstr "Termin taksonomii"

#~ msgid "remove {layout}?"
#~ msgstr "usunąć {layout}?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "To pole wymaga przynamniej {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "Maksimum {label} limit osiągnięty ({max} {identifier})"

#~ msgid "Disabled"
#~ msgstr "Wyłączone"

#~ msgid "Disabled <span class=\"count\">(%s)</span>"
#~ msgid_plural "Disabled <span class=\"count\">(%s)</span>"
#~ msgstr[0] "Wyłączony: <span class=\"count\">(%s)</span>"
#~ msgstr[1] "Wyłączonych: <span class=\"count\">(%s)</span>"
#~ msgstr[2] "Wyłączonych: <span class=\"count\">(%s)</span>"

#~ msgid "Getting Started"
#~ msgstr "Pierwsze kroki"

#~ msgid "Field Types"
#~ msgstr "Rodzaje pól"

#~ msgid "Functions"
#~ msgstr "Funkcje"

#~ msgid "Actions"
#~ msgstr "Akcje"

#~ msgid "'How to' guides"
#~ msgstr "Wskazówki 'how-to'"

#~ msgid "Tutorials"
#~ msgstr "Poradniki"

#~ msgid "FAQ"
#~ msgstr "Najczęściej zadawane pytania (FAQ)"

#~ msgid "Created by"
#~ msgstr "Stworzone przez"

#~ msgid "Error loading update"
#~ msgstr "Błąd ładowania aktualizacji"

#~ msgid "Error"
#~ msgstr "Błąd"

#~ msgid "See what's new"
#~ msgstr "Zobacz co nowego"

#~ msgid "eg. Show extra content"
#~ msgstr "np. Wyświetl dodatkową treść"

#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "1 pole wymaga uwagi."
#~ msgstr[1] "%d pola wymagają uwagi."
#~ msgstr[2] "%d pól wymaga uwagi."

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Sukces</b>. Narzędzie importu dodało %s grup pól: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Ostrzeżenie</b>. Narzędzie importu wykryło %s już istniejących grup "
#~ "pól i je pominęło: %s"

#~ msgid "Upgrade ACF"
#~ msgstr "Aktualizuj ACF"

#~ msgid "Upgrade"
#~ msgstr "Aktualizacja"

#~ msgid ""
#~ "The following sites require a DB upgrade. Check the ones you want to "
#~ "update and then click “Upgrade Database”."
#~ msgstr ""
#~ "Następujące strony wymagają aktualizacji bazy danych. Zaznacz te które "
#~ "chcesz aktualizować i kliknij 'Aktualizuj bazę danych\"."

#~ msgid "Select"
#~ msgstr "Wybór (select)"

#~ msgid "Done"
#~ msgstr "Gotowe"

#~ msgid "Today"
#~ msgstr "Dzisiaj"

#~ msgid "Show a different month"
#~ msgstr "Pokaż inny miesiąc"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Błąd połączenia</b>. Przepraszamy, spróbuj ponownie"

#~ msgid "See what's new in"
#~ msgstr "Zobacz co słychać nowego w"

#~ msgid "version"
#~ msgstr "wersja"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Przeciągnij i zmień kolejność"

#~ msgid "Upgrading data to"
#~ msgstr "Aktualizacja danych do"

#~ msgid "Return format"
#~ msgstr "Zwracany format"

#~ msgid "uploaded to this post"
#~ msgstr "przesłane do tego wpisu"

#~ msgid "File Name"
#~ msgstr "Nazwa pliku"

#~ msgid "File Size"
#~ msgstr "Rozmiar pliku"

#~ msgid "No File selected"
#~ msgstr "Nie wybrano pliku"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr ""
#~ "Proszę pamiętać, że wszystkie teksty najpierw przepuszczane są przez "
#~ "funkcje WP"

#~ msgid "Warning"
#~ msgstr "Ostrzeżenie"

#~ msgid "Add new %s "
#~ msgstr "Dodaj nowe %s"

#~ msgid "Save Options"
#~ msgstr "Zapisz opcje"

#~ msgid "License"
#~ msgstr "Licencja"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "W celu odblokowania aktualizacji proszę wpisać swój numer licencji "
#~ "poniżej. Jeśli nie masz klucza proszę zobacz"

#~ msgid "details & pricing"
#~ msgstr "szczegóły i ceny"

#~ msgid ""
#~ "To enable updates, please enter your license key on the <a href=\"%s"
#~ "\">Updates</a> page. If you don't have a licence key, please see <a href="
#~ "\"%s\">details & pricing</a>"
#~ msgstr ""
#~ "Aby włączyć aktualizację proszę wpisać swój klucz licencji na stronie <a "
#~ "href=\"%s\">Aktualizacje</a>. Jeśli nie posiadasz klucza proszę zobaczyć "
#~ "<a href=\"%s\">szczegóły i ceny</a>"

#~ msgid "Advanced Custom Fields Pro"
#~ msgstr "Advanced Custom Fields Pro"

#~ msgid "http://www.advancedcustomfields.com/"
#~ msgstr "http://www.advancedcustomfields.com/"

#~ msgid "elliot condon"
#~ msgstr "elliot condon"

#, fuzzy
#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr ""
#~ "Grupy pól są tworzone w kolejności <br />od najniższej do najwyższej."

#, fuzzy
#~ msgid "ACF PRO Required"
#~ msgstr "Wymagane?"

#, fuzzy
#~ msgid "Update Database"
#~ msgstr "Aktualizuj bazę danych"

#, fuzzy
#~ msgid "Data Upgrade"
#~ msgstr "Aktualizacja"

#, fuzzy
#~ msgid "image"
#~ msgstr "Obrazek"

#, fuzzy
#~ msgid "relationship"
#~ msgstr "Relacja"

#, fuzzy
#~ msgid "title_is_required"
#~ msgstr "Grupa pól została opublikowana."

#, fuzzy
#~ msgid "move_field"
#~ msgstr "Zapisz pole"

#, fuzzy
#~ msgid "flexible_content"
#~ msgstr "Elastyczna treść"

#, fuzzy
#~ msgid "gallery"
#~ msgstr "Galeria"

#, fuzzy
#~ msgid "repeater"
#~ msgstr "Pole powtarzalne"

#~ msgid "Custom field updated."
#~ msgstr "Włąsne pole zostało zaktualizowane."

#~ msgid "Custom field deleted."
#~ msgstr "Własne pole zostało usunięte."

#, fuzzy
#~ msgid "Import/Export"
#~ msgstr "Import / Eksport"

#, fuzzy
#~ msgid "Attachment Details"
#~ msgstr "ID załącznika"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "Walidacja nie powiodła się. Jedno lub więcej pól jest wymaganych."

#~ msgid "Field group restored to revision from %s"
#~ msgstr "Grupa pól została przywróćona z wersji %s"

#~ msgid "No ACF groups selected"
#~ msgstr "Nie zaznaczono żadnej grupy pól"

#~ msgid "Add Fields to Edit Screens"
#~ msgstr "Dodaj pola do stron edycji"

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr ""
#~ "Przeczytaj dokumentację, naucz się funkcji i poznaj parę tricków, które "
#~ "mogą przydać Ci się w Twoim kolejnym projekcie."

#~ msgid "View the ACF website"
#~ msgstr "Odwiedź stronę wtyczki"

#~ msgid "Vote"
#~ msgstr "Głosuj"

#~ msgid "Follow"
#~ msgstr "Śledź"

#~ msgid "Add File to Field"
#~ msgstr "Dodaj plik do pola"

#~ msgid "Add Image to Field"
#~ msgstr "Dodaj zdjęcie do pola"

#~ msgid "Repeater field deactivated"
#~ msgstr "Pole powtarzalne zostało deaktywowane"

#~ msgid "Gallery field deactivated"
#~ msgstr "Galeria została deaktywowana"

#~ msgid "Repeater field activated"
#~ msgstr "Pole powtarzalne zostało aktywowane"

#~ msgid "Options page activated"
#~ msgstr "Strona opcji została aktywowana"

#~ msgid "Flexible Content field activated"
#~ msgstr "Pole z elastyczną zawartością zostało aktywowane"

#~ msgid "Gallery field activated"
#~ msgstr "Galeria została aktywowana"

#~ msgid "License key unrecognised"
#~ msgstr "Klucz licencji nie został rozpoznany"

#~ msgid "Advanced Custom Fields Settings"
#~ msgstr "Ustawienia zaawansowanych własnych pól"

#~ msgid "Activation Code"
#~ msgstr "Kod aktywacyjny"

#~ msgid "Repeater Field"
#~ msgstr "Pole powtarzalne"

#~ msgid "Flexible Content Field"
#~ msgstr "Pole z elastyczną zawartością"

#~ msgid "Gallery Field"
#~ msgstr "Galeria"

#~ msgid ""
#~ "Add-ons can be unlocked by purchasing a license key. Each key can be used "
#~ "on multiple sites."
#~ msgstr ""
#~ "Dodatki można odblokować kupując kod aktywacyjny. Każdy kod aktywacyjny "
#~ "może być wykorzystywany na dowolnej liczbie stron."

#~ msgid "Export Field Groups to XML"
#~ msgstr "Eksportuj Grupy pól do XML"

#~ msgid ""
#~ "ACF will create a .xml export file which is compatible with the native WP "
#~ "import plugin."
#~ msgstr ""
#~ "Wtyczka utworzy plik eksportu .xml, który jest kompatybilny z domyślną "
#~ "wtyczką importu plików."

#~ msgid "Export XML"
#~ msgstr "Eksportuj XML"

#~ msgid "Navigate to the"
#~ msgstr "Przejdź do"

#~ msgid "and select WordPress"
#~ msgstr "i wybierz Wordpress"

#~ msgid "Install WP import plugin if prompted"
#~ msgstr "Zainstaluj wtyczkę importu WP, jeśli zostaniesz o to poproszony"

#~ msgid "Upload and import your exported .xml file"
#~ msgstr "Wgraj i zaimportuj wyeksportowany wcześniej plik .xml"

#~ msgid "Select your user and ignore Import Attachments"
#~ msgstr "Wybierz użytkownika i ignoruj Importowanie załączników"

#~ msgid "That's it! Happy WordPressing"
#~ msgstr "Gotowe!"

#~ msgid "ACF will create the PHP code to include in your theme"
#~ msgstr "ACF wygeneruje kod PHP, który możesz wkleić do swego szablonu"

#~ msgid "Register Field Groups with PHP"
#~ msgstr "Utwórz grupę pól z PHP"

#~ msgid "Copy the PHP code generated"
#~ msgstr "Skopij wygenerowany kod PHP"

#~ msgid "Paste into your functions.php file"
#~ msgstr "Wklej do pliku functions.php"

#~ msgid ""
#~ "To activate any Add-ons, edit and use the code in the first few lines."
#~ msgstr ""
#~ "Aby aktywować dodatki, edytuj i użyj kodu w pierwszych kilku liniach."

#~ msgid "Back to settings"
#~ msgstr "Wróć do ustawień"

#~ msgid ""
#~ "/**\n"
#~ " * Activate Add-ons\n"
#~ " * Here you can enter your activation codes to unlock Add-ons to use in "
#~ "your theme. \n"
#~ " * Since all activation codes are multi-site licenses, you are allowed to "
#~ "include your key in premium themes. \n"
#~ " * Use the commented out code to update the database with your activation "
#~ "code. \n"
#~ " * You may place this code inside an IF statement that only runs on theme "
#~ "activation.\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Aktywuj dodatki\n"
#~ " * Możesz tu wpisać kody aktywacyjne uruchamiające dodatkowe funkcje. \n"
#~ " * W związku z tym, że kody są na dowolną ilość licencji, możesz je "
#~ "stosować także w płatnych szablonach. \n"
#~ " * Użyj kodu aby zaktualizować bazę danych. \n"
#~ " * Możesz umieścić ten kod w funkcjach if, które uruchamiają się np. przy "
#~ "aktywacji szablonu.\n"
#~ " */"

#~ msgid ""
#~ "/**\n"
#~ " * Register field groups\n"
#~ " * The register_field_group function accepts 1 array which holds the "
#~ "relevant data to register a field group\n"
#~ " * You may edit the array as you see fit. However, this may result in "
#~ "errors if the array is not compatible with ACF\n"
#~ " * This code must run every time the functions.php file is read\n"
#~ " */"
#~ msgstr ""
#~ "/**\n"
#~ " * Zarejestruj grupy pól\n"
#~ " * Funkcja register_field_group akceptuje 1 ciąg zmiennych, która zawiera "
#~ "wszystkie dane służące rejestracji grupy\n"
#~ " * Możesz edytować tę zmienną i dopasowywać ją do swoich potrzeb. Ale "
#~ "może to też powodować błąd jeśli ta zmienna nie jest kompatybilna z ACF\n"
#~ " * Kod musi być uruchamiany każdorazowo w pliku functions.php\n"
#~ " */"

#~ msgid "requires a database upgrade"
#~ msgstr "wymagana jest aktualizacja bazy danych"

#~ msgid "why?"
#~ msgstr "dlaczego?"

#~ msgid "Please"
#~ msgstr "Proszę"

#~ msgid "backup your database"
#~ msgstr "zrobić kopię zapasową bazy danych"

#~ msgid "then click"
#~ msgstr "a następnie kliknąć"

#~ msgid "Modifying field group options 'show on page'"
#~ msgstr "Modyfikacje opcji grupy pól 'pokaż na stronie'"

#~ msgid "No choices to choose from"
#~ msgstr "Brak możliwościi wyboru"

#~ msgid "Red"
#~ msgstr "Czerwony"

#~ msgid "Blue"
#~ msgstr "Niebieski"

#~ msgid "blue : Blue"
#~ msgstr "niebieski : Niebieski"

#~ msgid "File Updated."
#~ msgstr "Plik został zaktualizowany."

#~ msgid "Media attachment updated."
#~ msgstr "Załącznik został zaktualizowany."

#~ msgid "Add Selected Files"
#~ msgstr "Dodaj zaznaczone pliki"

#~ msgid "+ Add Row"
#~ msgstr "+ Dodaj rząd"

#~ msgid "Field Order"
#~ msgstr "Kolejność pola"

#~ msgid ""
#~ "No fields. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."
#~ msgstr ""
#~ "Brak pól. Kliknij przycisk \"+ Dodaj pole podrzędne\" aby utworzyć "
#~ "pierwsze własne pole."

#~ msgid "Docs"
#~ msgstr "Dokumentacja"

#~ msgid "Close Sub Field"
#~ msgstr "Zamknij pole"

#~ msgid "+ Add Sub Field"
#~ msgstr "+ Dodaj pole podrzędne"

#~ msgid "Alternate Text"
#~ msgstr "Tekst alternatywny"

#~ msgid "Thumbnail is advised"
#~ msgstr "Zalecana jest miniatura."

#~ msgid "Image Updated"
#~ msgstr "Zdjęcie zostało zaktualizowane."

#~ msgid "Grid"
#~ msgstr "Siatka"

#~ msgid "List"
#~ msgstr "Lista"

#~ msgid "Image already exists in gallery"
#~ msgstr "To zdjęcie już jest w galerii."

#~ msgid "Image Updated."
#~ msgstr "Zdjęcie zostało zaktualizowane."

#~ msgid "No images selected"
#~ msgstr "Nie wybrano obrazków"

#~ msgid "Add selected Images"
#~ msgstr "Dodaj zaznaczone obrazki"

#~ msgid ""
#~ "Filter posts by selecting a post type<br />\n"
#~ "\t\t\t\tTip: deselect all post types to show all post type's posts"
#~ msgstr ""
#~ "Filtruj wpisy wybierając typ wpisu<br />\n"
#~ "\t\t\t\tPodpowiedź: nie zaznaczenie żadnego typu wpisów spowoduje "
#~ "wyświetlenie wszystkich"

#~ msgid "Set to -1 for infinite"
#~ msgstr "Wpisanie -1 oznacza nieskończoność"

#~ msgid "Repeater Fields"
#~ msgstr "Pola powtarzalne"

#~ msgid "Table (default)"
#~ msgstr "Tabela (domyślne)"

#~ msgid "Define how to render html tags"
#~ msgstr "Określ jak traktować znaczniki HTML"

#~ msgid "HTML"
#~ msgstr "HTML"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "Określ jak traktować znaczniki HTML / nowe wiersze"

#~ msgid "eg. dd/mm/yy. read more about"
#~ msgstr "np. dd/mm/rr. czytaj więcej"

#~ msgid "Page Specific"
#~ msgstr "Związane ze stronami"

#~ msgid "Post Specific"
#~ msgstr "Związane z typem wpisu"

#~ msgid "Taxonomy (Add / Edit)"
#~ msgstr "Taksonomia (Dodaj / Edytuj)"

#~ msgid "Media (Edit)"
#~ msgstr "Medium (Edytuj)"

#~ msgid "match"
#~ msgstr "pasuje"

#~ msgid "all"
#~ msgstr "wszystkie"

#~ msgid "of the above"
#~ msgstr "do pozostałych"

#~ msgid "Unlock options add-on with an activation code"
#~ msgstr "Odblokuj dodatkowe opcje z kodem aktywacyjnym"

#~ msgid "Normal"
#~ msgstr "Normalna"

#~ msgid "No Metabox"
#~ msgstr "Bez metabox"

#~ msgid "Everything Fields deactivated"
#~ msgstr "Pola do wszystkiego zostały deaktywowane"

#~ msgid "Everything Fields activated"
#~ msgstr "Pola do wszystkiego zostały aktywowane"

#~ msgid "Row Limit"
#~ msgstr "Limit rzędów"

#~ msgid "required"
#~ msgstr "wymagane"

#~ msgid "Show on page"
#~ msgstr "Wyświetl na stronie"

#~ msgid ""
#~ "Watch tutorials, read documentation, learn the API code and find some "
#~ "tips &amp; tricks for your next web project."
#~ msgstr ""
#~ "Obejrzyj tutorial, przeczytaj dokumentację, naucz się API i poznaj parę "
#~ "tricków do przydatnych w Twoim kolejnym projekcie."

#~ msgid "View the plugins website"
#~ msgstr "Odwiedź witrynę wtyczki"

#~ msgid ""
#~ "Join the growing community over at the support forum to share ideas, "
#~ "report bugs and keep up to date with ACF"
#~ msgstr ""
#~ "Dołącz do rosnącej społeczności użytkowników i forum pomocy, aby dzielić "
#~ "się pomysłami, zgłąszać błedy i być na bierząco z tą wtyczką."

#~ msgid "View the Support Forum"
#~ msgstr "Zobacz forum pomocy"

#~ msgid "Developed by"
#~ msgstr "Opracowana przez"

#~ msgid "Vote for ACF"
#~ msgstr "Głosuj na tę wtyczkę"

#~ msgid "Twitter"
#~ msgstr "Twitter"

#~ msgid "Blog"
#~ msgstr "Blog"

#~ msgid "Unlock Special Fields."
#~ msgstr "Odblokuj pola specjalne"

#~ msgid ""
#~ "Special Fields can be unlocked by purchasing an activation code. Each "
#~ "activation code can be used on multiple sites."
#~ msgstr ""
#~ "Pola specjalne można odblokować kupując kod aktywacyjny. Każdy kod "
#~ "aktywacyjny może być wykorzystywany wielokrotnie."

#~ msgid "Visit the Plugin Store"
#~ msgstr "Odwiedź sklep wtyczki"

#~ msgid "Unlock Fields"
#~ msgstr "Odblokuj pola"

#~ msgid "Have an ACF export file? Import it here."
#~ msgstr "Wyeksportowałeś plik z polami? Możesz go zaimportować tutaj."

#~ msgid ""
#~ "Want to create an ACF export file? Just select the desired ACF's and hit "
#~ "Export"
#~ msgstr ""
#~ "Chcesz stworzyć i wyeksportować plik z polami? Wybierz pola i kliknij "
#~ "Eksport"

#~ msgid ""
#~ "No fields. Click the \"+ Add Field button\" to create your first field."
#~ msgstr ""
#~ "Brak pól. Kliknij przycisk \"+ Dodaj pole\" aby utworzyć pierwsze własne "
#~ "pole."

#~ msgid ""
#~ "Special Fields can be unlocked by purchasing a license key. Each key can "
#~ "be used on multiple sites."
#~ msgstr ""
#~ "Pola specjalne można odblokować kupując kod aktywacyjny. Każdy kod "
#~ "aktywacyjny może być wykorzystywany wielokrotnie."

#~ msgid "Select which ACF groups to export"
#~ msgstr "Wybierz, które grupy chcesz wyeksportować"

#~ msgid ""
#~ "Have an ACF export file? Import it here. Please note that v2 and v3 .xml "
#~ "files are not compatible."
#~ msgstr ""
#~ "Wyeksportowałeś plik z polami? Zaimportuj go tutaj. Zwróć uwagę, że "
#~ "wersje 2 i 3 plików .xml nie są ze sobą kompatybilne."

#~ msgid "Import your .xml file"
#~ msgstr "Zaimportuj plik .xml"

#~ msgid "Display your field group with or without a box"
#~ msgstr "Wyświetl grupę pól w ramce lub bez niej"

#~ msgid "Save"
#~ msgstr "Zapisz"

#~ msgid "No Options"
#~ msgstr "Brak opcji"

#~ msgid "Sorry, it seems there are no fields for this options page."
#~ msgstr "Przykro mi, ale ta strona opcji nie zawiera pól."

#~ msgid ""
#~ "Enter your choices one per line<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tRed<br />\n"
#~ "\t\t\t\tBlue<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tor<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tred : Red<br />\n"
#~ "\t\t\t\tblue : Blue"
#~ msgstr ""
#~ "Wpisz dostęne opcje, każdy w odrębnym rzędzie<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tCzerwony<br />\n"
#~ "\t\t\t\tNiebieski<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tor<br />\n"
#~ "\t\t\t\t<br />\n"
#~ "\t\t\t\tczerwony : Czerwony<br />\n"
#~ "\t\t\t\tniebieski : Niebieski"

#~ msgid "continue editing ACF"
#~ msgstr "kontynuuj edycję"

#~ msgid "Adv Upgrade"
#~ msgstr "Zaawansowana aktualizacja"
