{"name": "timber/timber", "type": "library", "description": "Plugin to write WordPress themes w Object-Oriented Code and the Twig Template Engine", "keywords": ["timber", "twig", "themes", "templating"], "homepage": "http://timber.upstatement.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://upstatement.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://connorburton.com"}], "support": {"issues": "https://github.com/timber/timber/issues", "source": "https://github.com/timber/timber", "docs": "https://timber.github.io/docs/"}, "require": {"php": ">=5.3.0|7.*", "twig/twig": "^1.41|^2.10", "upstatement/routes": "0.5", "composer/installers": "~1.0", "asm89/twig-cache-extension": "~1.0"}, "require-dev": {"johnpbloch/wordpress": "*", "phpunit/phpunit": "5.7.16|6.*", "squizlabs/php_codesniffer": "3.*", "wp-coding-standards/wpcs": "^2.0", "wpackagist-plugin/advanced-custom-fields": "5.*", "wpackagist-plugin/co-authors-plus": "3.2.*|3.4.*", "dealerdirect/phpcodesniffer-composer-installer": "^0.5.0"}, "suggest": {"satooshi/php-coveralls": "1.0.* for code coverage"}, "autoload": {"psr-4": {"Timber\\": "lib/"}}, "autoload-dev": {"classmap": ["tests/"], "exclude-from-classmap": ["tests/php"]}, "repositories": [{"type": "composer", "url": "https://wpackagist.org"}], "scripts": {"lint": "phpcs --runtime-set ignore_errors_on_exit 1 --runtime-set ignore_warnings_on_exit 1"}}