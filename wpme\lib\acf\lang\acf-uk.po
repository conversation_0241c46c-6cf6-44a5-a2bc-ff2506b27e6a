msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.2.9\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2017-10-04 14:50+1000\n"
"PO-Revision-Date: 2018-02-06 10:06+1000\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: skinik <<EMAIL>>\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && n"
"%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"
"X-Generator: Poedit 1.8.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:67
msgid "Advanced Custom Fields"
msgstr "Додаткові поля Pro"

#: acf.php:369 includes/admin/admin.php:117
msgid "Field Groups"
msgstr "Групи полів"

#: acf.php:370
msgid "Field Group"
msgstr "Група полів"

#: acf.php:371 acf.php:403 includes/admin/admin.php:118
#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New"
msgstr "Додати нову"

#: acf.php:372
msgid "Add New Field Group"
msgstr "Додати нову групу полів"

#: acf.php:373
msgid "Edit Field Group"
msgstr "Редагувати групу полів"

#: acf.php:374
msgid "New Field Group"
msgstr "Нова група полів"

#: acf.php:375
msgid "View Field Group"
msgstr "Переглянути групу полів"

#: acf.php:376
msgid "Search Field Groups"
msgstr "Шукати групи полів"

#: acf.php:377
msgid "No Field Groups found"
msgstr "Не знайдено груп полів"

#: acf.php:378
msgid "No Field Groups found in Trash"
msgstr "У кошику немає груп полів"

#: acf.php:401 includes/admin/admin-field-group.php:182
#: includes/admin/admin-field-group.php:275
#: includes/admin/admin-field-groups.php:510
#: pro/fields/class-acf-field-clone.php:807
msgid "Fields"
msgstr "Поля"

#: acf.php:402
msgid "Field"
msgstr "Поле"

#: acf.php:404
msgid "Add New Field"
msgstr "Додати нове поле"

#: acf.php:405
msgid "Edit Field"
msgstr "Редагувати поле"

#: acf.php:406 includes/admin/views/field-group-fields.php:41
#: includes/admin/views/settings-info.php:105
msgid "New Field"
msgstr "Нове поле"

#: acf.php:407
msgid "View Field"
msgstr "Переглянути\t поле"

#: acf.php:408
msgid "Search Fields"
msgstr "Шукати поля"

#: acf.php:409
msgid "No Fields found"
msgstr "Не знайдено полів"

#: acf.php:410
msgid "No Fields found in Trash"
msgstr "Не знайдено полів у кошику"

#: acf.php:449 includes/admin/admin-field-group.php:390
#: includes/admin/admin-field-groups.php:567
msgid "Inactive"
msgstr "Неактивно"

#: acf.php:454
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Неактивні <span class=count>(%s)</span>"
msgstr[1] "Неактивні <span class=count>(%s)</span>"
msgstr[2] "Неактивні <span class=count>(%s)</span>"

#: includes/admin/admin-field-group.php:68
#: includes/admin/admin-field-group.php:69
#: includes/admin/admin-field-group.php:71
msgid "Field group updated."
msgstr "Групу полів оновлено."

#: includes/admin/admin-field-group.php:70
msgid "Field group deleted."
msgstr "Групу полів видалено."

#: includes/admin/admin-field-group.php:73
msgid "Field group published."
msgstr "Групу полів опубліковано."

#: includes/admin/admin-field-group.php:74
msgid "Field group saved."
msgstr "Групу полів збережено."

#: includes/admin/admin-field-group.php:75
msgid "Field group submitted."
msgstr "Групу полів надіслано."

#: includes/admin/admin-field-group.php:76
#, fuzzy
msgid "Field group scheduled for."
msgstr "Групу полів збережено."

#: includes/admin/admin-field-group.php:77
msgid "Field group draft updated."
msgstr "Чернетку групи полів оновлено."

#: includes/admin/admin-field-group.php:183
msgid "Location"
msgstr "Розміщення"

#: includes/admin/admin-field-group.php:184
msgid "Settings"
msgstr "Налаштування"

#: includes/admin/admin-field-group.php:269
msgid "Move to trash. Are you sure?"
msgstr "Перемістити в кошик. Ви впевнені?"

#: includes/admin/admin-field-group.php:270
msgid "checked"
msgstr ""

#: includes/admin/admin-field-group.php:271
msgid "No toggle fields available"
msgstr ""

#: includes/admin/admin-field-group.php:272
msgid "Field group title is required"
msgstr "Заголовок обов’язковий"

#: includes/admin/admin-field-group.php:273
#: includes/api/api-field-group.php:751
msgid "copy"
msgstr "копіювати"

#: includes/admin/admin-field-group.php:274
#: includes/admin/views/field-group-field-conditional-logic.php:54
#: includes/admin/views/field-group-field-conditional-logic.php:154
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3964
msgid "or"
msgstr "або"

#: includes/admin/admin-field-group.php:276
msgid "Parent fields"
msgstr "Батьківські поля"

#: includes/admin/admin-field-group.php:277
msgid "Sibling fields"
msgstr ""

#: includes/admin/admin-field-group.php:278
msgid "Move Custom Field"
msgstr "Перемістити поле"

#: includes/admin/admin-field-group.php:279
msgid "This field cannot be moved until its changes have been saved"
msgstr ""

#: includes/admin/admin-field-group.php:280
msgid "Null"
msgstr ""

#: includes/admin/admin-field-group.php:281 includes/input.php:258
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""

#: includes/admin/admin-field-group.php:282
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr ""

#: includes/admin/admin-field-group.php:360
msgid "Field Keys"
msgstr ""

#: includes/admin/admin-field-group.php:390
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Активно"

#: includes/admin/admin-field-group.php:801
msgid "Move Complete."
msgstr "Переміщення завершене."

#: includes/admin/admin-field-group.php:802
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "Поле «%s» можете знайти у групі «%s»"

#: includes/admin/admin-field-group.php:803
msgid "Close Window"
msgstr "Закрити вікно"

#: includes/admin/admin-field-group.php:844
msgid "Please select the destination for this field"
msgstr "Будь ласка, оберіть групу, в яку перемістити"

#: includes/admin/admin-field-group.php:851
msgid "Move Field"
msgstr "Перемістити поле"

#: includes/admin/admin-field-groups.php:74
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Активні <span class=\"count\">(%s)</span>"
msgstr[1] "Активні <span class=\"count\">(%s)</span>"
msgstr[2] "Активні <span class=\"count\">(%s)</span>"

#: includes/admin/admin-field-groups.php:142
#, php-format
msgid "Field group duplicated. %s"
msgstr ""

#: includes/admin/admin-field-groups.php:146
#, php-format
msgid "%s field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-field-groups.php:227
#, php-format
msgid "Field group synchronised. %s"
msgstr ""

#: includes/admin/admin-field-groups.php:231
#, php-format
msgid "%s field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/admin/admin-field-groups.php:394
#: includes/admin/admin-field-groups.php:557
msgid "Sync available"
msgstr "Доступна синхронізація"

#: includes/admin/admin-field-groups.php:507 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:355
msgid "Title"
msgstr "Заголовок"

#: includes/admin/admin-field-groups.php:508
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/install-network.php:21
#: includes/admin/views/install-network.php:29
#: pro/fields/class-acf-field-gallery.php:382
msgid "Description"
msgstr "Опис"

#: includes/admin/admin-field-groups.php:509
msgid "Status"
msgstr "Статус"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:607
msgid "Customise WordPress with powerful, professional and intuitive fields."
msgstr ""
"Розширте можливості WordPress за допомогою потужних, професійних та "
"інтуїтивно зрозумілих полів."

#: includes/admin/admin-field-groups.php:609
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Список змін"

#: includes/admin/admin-field-groups.php:614
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "Перегляньте що нового у <a href=%s>версії %s</a>."

#: includes/admin/admin-field-groups.php:617
msgid "Resources"
msgstr "Документація"

#: includes/admin/admin-field-groups.php:619
msgid "Website"
msgstr "Сайт"

#: includes/admin/admin-field-groups.php:620
msgid "Documentation"
msgstr "Документація"

#: includes/admin/admin-field-groups.php:621
msgid "Support"
msgstr "Підтримка"

#: includes/admin/admin-field-groups.php:623
msgid "Pro"
msgstr "Про"

#: includes/admin/admin-field-groups.php:628
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "Спасибі за використання <a href=\"%s\">ACF</a>."

#: includes/admin/admin-field-groups.php:668
msgid "Duplicate this item"
msgstr "Дублювати цей елемент"

#: includes/admin/admin-field-groups.php:668
#: includes/admin/admin-field-groups.php:684
#: includes/admin/views/field-group-field.php:49
#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate"
msgstr "Дублювати"

#: includes/admin/admin-field-groups.php:701
#: includes/fields/class-acf-field-google-map.php:112
#: includes/fields/class-acf-field-relationship.php:656
msgid "Search"
msgstr "Пошук"

#: includes/admin/admin-field-groups.php:760
#, php-format
msgid "Select %s"
msgstr ""

#: includes/admin/admin-field-groups.php:768
msgid "Synchronise field group"
msgstr ""

#: includes/admin/admin-field-groups.php:768
#: includes/admin/admin-field-groups.php:798
msgid "Sync"
msgstr ""

#: includes/admin/admin-field-groups.php:780
msgid "Apply"
msgstr "Застосувати"

#: includes/admin/admin-field-groups.php:798
msgid "Bulk Actions"
msgstr "Масові дії"

#: includes/admin/admin.php:113
#: includes/admin/views/field-group-options.php:118
msgid "Custom Fields"
msgstr "Додаткові поля"

#: includes/admin/install-network.php:88 includes/admin/install.php:70
#: includes/admin/install.php:121
msgid "Upgrade Database"
msgstr "Оновити базу даних"

#: includes/admin/install-network.php:140
msgid "Review sites & upgrade"
msgstr ""

#: includes/admin/install.php:187
msgid "Error validating request"
msgstr ""

#: includes/admin/install.php:210 includes/admin/views/install.php:105
msgid "No updates available."
msgstr "Немає оновлень."

#: includes/admin/settings-addons.php:51
#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Доповнення"

#: includes/admin/settings-addons.php:87
msgid "<b>Error</b>. Could not load add-ons list"
msgstr ""

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Інформація"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Що нового"

#: includes/admin/settings-tools.php:50
#: includes/admin/views/settings-tools-export.php:19
#: includes/admin/views/settings-tools.php:31
msgid "Tools"
msgstr "Інструменти"

#: includes/admin/settings-tools.php:147 includes/admin/settings-tools.php:380
msgid "No field groups selected"
msgstr "Не обрано груп полів"

#: includes/admin/settings-tools.php:184
#: includes/fields/class-acf-field-file.php:155
msgid "No file selected"
msgstr "Файл не обрано"

#: includes/admin/settings-tools.php:197
msgid "Error uploading file. Please try again"
msgstr "Помилка завантаження файлу. Спробуйте знову"

#: includes/admin/settings-tools.php:206
msgid "Incorrect file type"
msgstr "Невірний тип файлу"

#: includes/admin/settings-tools.php:223
msgid "Import file empty"
msgstr "Файл імпорту порожній"

#: includes/admin/settings-tools.php:331
#, fuzzy, php-format
#| msgid "Import Field Groups"
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "Імпортувати групи полів"
msgstr[1] "Імпортувати групи полів"
msgstr[2] "Імпортувати групи полів"

#: includes/admin/views/field-group-field-conditional-logic.php:28
msgid "Conditional Logic"
msgstr "Умовна логіка"

#: includes/admin/views/field-group-field-conditional-logic.php:54
msgid "Show this field if"
msgstr "Показувати поле, якщо"

#: includes/admin/views/field-group-field-conditional-logic.php:103
#: includes/locations.php:247
msgid "is equal to"
msgstr "дорівнює"

#: includes/admin/views/field-group-field-conditional-logic.php:104
#: includes/locations.php:248
msgid "is not equal to"
msgstr "не дорівнює"

#: includes/admin/views/field-group-field-conditional-logic.php:141
#: includes/admin/views/html-location-rule.php:80
msgid "and"
msgstr "та"

#: includes/admin/views/field-group-field-conditional-logic.php:156
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Додати групу умов"

#: includes/admin/views/field-group-field.php:41
#: pro/fields/class-acf-field-flexible-content.php:403
#: pro/fields/class-acf-field-repeater.php:296
msgid "Drag to reorder"
msgstr "Перетягніть, щоб змінити порядок"

#: includes/admin/views/field-group-field.php:45
#: includes/admin/views/field-group-field.php:48
msgid "Edit field"
msgstr "Редагувати поле"

#: includes/admin/views/field-group-field.php:48
#: includes/fields/class-acf-field-file.php:137
#: includes/fields/class-acf-field-image.php:122
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:342
msgid "Edit"
msgstr "Редагувати"

#: includes/admin/views/field-group-field.php:49
msgid "Duplicate field"
msgstr "Дублювати поле"

#: includes/admin/views/field-group-field.php:50
msgid "Move field to another group"
msgstr "Перемістити поле до іншої групи"

#: includes/admin/views/field-group-field.php:50
msgid "Move"
msgstr "Перемістити"

#: includes/admin/views/field-group-field.php:51
msgid "Delete field"
msgstr "Видалити поле"

#: includes/admin/views/field-group-field.php:51
#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete"
msgstr "Видалити"

#: includes/admin/views/field-group-field.php:67
msgid "Field Label"
msgstr "Назва поля"

#: includes/admin/views/field-group-field.php:68
msgid "This is the name which will appear on the EDIT page"
msgstr "Ця назва відображується на сторінці редагування"

#: includes/admin/views/field-group-field.php:77
msgid "Field Name"
msgstr "Ярлик"

#: includes/admin/views/field-group-field.php:78
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Одне слово, без пробілів. Можете використовувати нижнє підкреслення."

#: includes/admin/views/field-group-field.php:87
msgid "Field Type"
msgstr "Тип поля"

#: includes/admin/views/field-group-field.php:98
#: includes/fields/class-acf-field-tab.php:88
msgid "Instructions"
msgstr "Інструкція"

#: includes/admin/views/field-group-field.php:99
msgid "Instructions for authors. Shown when submitting data"
msgstr "Напишіть короткий опис для поля"

#: includes/admin/views/field-group-field.php:108
msgid "Required?"
msgstr "Обов’язкове?"

#: includes/admin/views/field-group-field.php:131
msgid "Wrapper Attributes"
msgstr "Атрибути обгортки"

#: includes/admin/views/field-group-field.php:137
msgid "width"
msgstr "ширина"

#: includes/admin/views/field-group-field.php:152
msgid "class"
msgstr "клас"

#: includes/admin/views/field-group-field.php:165
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:177
msgid "Close Field"
msgstr "Закрити поле"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Порядок"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:415
#: includes/fields/class-acf-field-radio.php:306
#: includes/fields/class-acf-field-select.php:432
#: pro/fields/class-acf-field-flexible-content.php:582
msgid "Label"
msgstr "Ярлик"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:964
#: pro/fields/class-acf-field-flexible-content.php:595
msgid "Name"
msgstr "Назва"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Ключ"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Тип"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Ще немає полів. Для створення полів натисніть <strong>+ Додати поле</strong>."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Додати поле"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Умови"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Створіть набір умов, щоб визначити де використовувати  ці додаткові поля"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Стиль"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Стандартний (WP метабокс)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Спрощений (без метабоксу)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Розташування"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Вгорі (під заголовком)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Стандартно (після тектового редактора)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Збоку"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Розміщення ярликів"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:102
msgid "Top aligned"
msgstr "Зверху"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:103
msgid "Left aligned"
msgstr "Зліва"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Розміщення інструкцій"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Під ярликами"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Під полями"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Порядок розташування"

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Групи полів з нижчим порядком з’являться спочатку"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Відображається на сторінці груп полів"

#: includes/admin/views/field-group-options.php:107
msgid "Hide on screen"
msgstr "Ховати на екрані"

#: includes/admin/views/field-group-options.php:108
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "<b>Оберіть</b> що <b>ховати</b> з екрану редагування/створення."

#: includes/admin/views/field-group-options.php:108
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Якщо декілька груп полів відображаються на екрані редагування, то "
"використовуватимуться параметри першої групи. (з найменшим порядковим "
"номером)"

#: includes/admin/views/field-group-options.php:115
msgid "Permalink"
msgstr "Постійне посилання "

#: includes/admin/views/field-group-options.php:116
msgid "Content Editor"
msgstr "Редактор матеріалу"

#: includes/admin/views/field-group-options.php:117
msgid "Excerpt"
msgstr "Витяг"

#: includes/admin/views/field-group-options.php:119
msgid "Discussion"
msgstr "Дискусія"

#: includes/admin/views/field-group-options.php:120
msgid "Comments"
msgstr "Коментарі"

#: includes/admin/views/field-group-options.php:121
msgid "Revisions"
msgstr "Ревізії"

#: includes/admin/views/field-group-options.php:122
msgid "Slug"
msgstr "Ярлик URL"

#: includes/admin/views/field-group-options.php:123
msgid "Author"
msgstr "Автор"

#: includes/admin/views/field-group-options.php:124
msgid "Format"
msgstr "Формат"

#: includes/admin/views/field-group-options.php:125
msgid "Page Attributes"
msgstr "Атрибути сторінки"

#: includes/admin/views/field-group-options.php:126
#: includes/fields/class-acf-field-relationship.php:670
msgid "Featured Image"
msgstr "Головне зображення"

#: includes/admin/views/field-group-options.php:127
msgid "Categories"
msgstr "Категорії"

#: includes/admin/views/field-group-options.php:128
msgid "Tags"
msgstr "Теґи"

#: includes/admin/views/field-group-options.php:129
msgid "Send Trackbacks"
msgstr "Надіслати трекбеки"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Показувати групу полів, якщо"

#: includes/admin/views/install-network.php:4
msgid "Upgrade Sites"
msgstr "Оновити сайти"

#: includes/admin/views/install-network.php:9
#: includes/admin/views/install.php:3
msgid "Advanced Custom Fields Database Upgrade"
msgstr ""

#: includes/admin/views/install-network.php:11
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""

#: includes/admin/views/install-network.php:20
#: includes/admin/views/install-network.php:28
msgid "Site"
msgstr "Сайт"

#: includes/admin/views/install-network.php:48
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr ""

#: includes/admin/views/install-network.php:50
msgid "Site is up to date"
msgstr "Сайт оновлено"

#: includes/admin/views/install-network.php:63
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""

#: includes/admin/views/install-network.php:102
#: includes/admin/views/install-notice.php:42
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""

#: includes/admin/views/install-network.php:158
msgid "Upgrade complete"
msgstr "Оновлення завершено"

#: includes/admin/views/install-network.php:162
#: includes/admin/views/install.php:9
#, php-format
msgid "Upgrading data to version %s"
msgstr "Оновлення даних до версії %s"

#: includes/admin/views/install-notice.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Повторювальне поле"

#: includes/admin/views/install-notice.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Гнучкий вміст"

#: includes/admin/views/install-notice.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Галерея"

#: includes/admin/views/install-notice.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Сторінка опцій"

#: includes/admin/views/install-notice.php:26
msgid "Database Upgrade Required"
msgstr "Необхідно оновити базу даних"

#: includes/admin/views/install-notice.php:28
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr ""

#: includes/admin/views/install-notice.php:28
msgid ""
"Before you start using the new awesome features, please update your database "
"to the newest version."
msgstr ""

#: includes/admin/views/install-notice.php:31
#, php-format
msgid ""
"Please also ensure any premium add-ons (%s) have first been updated to the "
"latest version."
msgstr ""

#: includes/admin/views/install.php:7
msgid "Reading upgrade tasks..."
msgstr ""

#: includes/admin/views/install.php:11
#, php-format
msgid "Database Upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "Завантажити і встановити"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Встановлено"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Вітаємо у Advanced Custom Fields"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr "Дякуємо за оновлення! ACF %s став ще кращим!"

#: includes/admin/views/settings-info.php:17
msgid "A smoother custom field experience"
msgstr ""

#: includes/admin/views/settings-info.php:22
msgid "Improved Usability"
msgstr ""

#: includes/admin/views/settings-info.php:23
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""

#: includes/admin/views/settings-info.php:27
msgid "Improved Design"
msgstr ""

#: includes/admin/views/settings-info.php:28
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""

#: includes/admin/views/settings-info.php:32
msgid "Improved Data"
msgstr ""

#: includes/admin/views/settings-info.php:33
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""

#: includes/admin/views/settings-info.php:39
msgid "Goodbye Add-ons. Hello PRO"
msgstr "До побачення доповнення. Привіт PRO"

#: includes/admin/views/settings-info.php:44
msgid "Introducing ACF PRO"
msgstr ""

#: includes/admin/views/settings-info.php:45
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""

#: includes/admin/views/settings-info.php:46
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""

#: includes/admin/views/settings-info.php:50
msgid "Powerful Features"
msgstr "Потужні можливості"

#: includes/admin/views/settings-info.php:51
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""

#: includes/admin/views/settings-info.php:52
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "Прочитайте більше про <a href=\"%s\">можливості ACF PRO</a>."

#: includes/admin/views/settings-info.php:56
msgid "Easy Upgrading"
msgstr "Легке оновлення"

#: includes/admin/views/settings-info.php:57
#, php-format
msgid ""
"To help make upgrading easy, <a href=\"%s\">login to your store account</a> "
"and claim a free copy of ACF PRO!"
msgstr ""

#: includes/admin/views/settings-info.php:58
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>"
msgstr ""

#: includes/admin/views/settings-info.php:66
msgid "Under the Hood"
msgstr "Під капотом"

#: includes/admin/views/settings-info.php:71
msgid "Smarter field settings"
msgstr ""

#: includes/admin/views/settings-info.php:72
msgid "ACF now saves its field settings as individual post objects"
msgstr ""

#: includes/admin/views/settings-info.php:76
msgid "More AJAX"
msgstr "Більше AJAX"

#: includes/admin/views/settings-info.php:77
msgid "More fields use AJAX powered search to speed up page loading"
msgstr ""

#: includes/admin/views/settings-info.php:81
msgid "Local JSON"
msgstr "Локальний JSON"

#: includes/admin/views/settings-info.php:82
msgid "New auto export to JSON feature improves speed"
msgstr ""

#: includes/admin/views/settings-info.php:88
msgid "Better version control"
msgstr ""

#: includes/admin/views/settings-info.php:89
msgid ""
"New auto export to JSON feature allows field settings to be version "
"controlled"
msgstr ""

#: includes/admin/views/settings-info.php:93
msgid "Swapped XML for JSON"
msgstr ""

#: includes/admin/views/settings-info.php:94
msgid "Import / Export now uses JSON in favour of XML"
msgstr ""

#: includes/admin/views/settings-info.php:98
msgid "New Forms"
msgstr "Нові форми"

#: includes/admin/views/settings-info.php:99
msgid "Fields can now be mapped to comments, widgets and all user forms!"
msgstr ""

#: includes/admin/views/settings-info.php:106
msgid "A new field for embedding content has been added"
msgstr ""

#: includes/admin/views/settings-info.php:110
msgid "New Gallery"
msgstr "Нова галерея"

#: includes/admin/views/settings-info.php:111
msgid "The gallery field has undergone a much needed facelift"
msgstr ""

#: includes/admin/views/settings-info.php:115
msgid "New Settings"
msgstr "Нові налаштування"

#: includes/admin/views/settings-info.php:116
msgid ""
"Field group settings have been added for label placement and instruction "
"placement"
msgstr ""

#: includes/admin/views/settings-info.php:122
msgid "Better Front End Forms"
msgstr ""

#: includes/admin/views/settings-info.php:123
msgid "acf_form() can now create a new post on submission"
msgstr ""

#: includes/admin/views/settings-info.php:127
msgid "Better Validation"
msgstr "Поліпшена перевірка"

#: includes/admin/views/settings-info.php:128
msgid "Form validation is now done via PHP + AJAX in favour of only JS"
msgstr "Перевірка форми відбувається на PHP + AJAX"

#: includes/admin/views/settings-info.php:132
#, fuzzy
msgid "Relationship Field"
msgstr "Закрити поле"

#: includes/admin/views/settings-info.php:133
msgid ""
"New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
msgstr ""

#: includes/admin/views/settings-info.php:139
msgid "Moving Fields"
msgstr "Переміщення полів"

#: includes/admin/views/settings-info.php:140
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents"
msgstr ""

#: includes/admin/views/settings-info.php:144
#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Посилання на сторінку"

#: includes/admin/views/settings-info.php:145
msgid "New archives group in page_link field selection"
msgstr ""

#: includes/admin/views/settings-info.php:149
msgid "Better Options Pages"
msgstr "Краща сторінка опцій"

#: includes/admin/views/settings-info.php:150
msgid ""
"New functions for options page allow creation of both parent and child menu "
"pages"
msgstr ""

#: includes/admin/views/settings-info.php:159
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "Думаємо, Вам сподобаються зміни у %s."

#: includes/admin/views/settings-tools-export.php:23
msgid "Export Field Groups to PHP"
msgstr "Експортувати групи полів в код PHP"

#: includes/admin/views/settings-tools-export.php:27
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""

#: includes/admin/views/settings-tools.php:5
msgid "Select Field Groups"
msgstr "Оберіть групи полів"

#: includes/admin/views/settings-tools.php:35
msgid "Export Field Groups"
msgstr "Експортувати групи полів"

#: includes/admin/views/settings-tools.php:38
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Виберіть групи полів, які Ви хочете експортувати, а далі оберіть бажаний "
"метод експорту. Використовуйте кнопку завантаження для експорту в файл ."
"json, який можна імпортувати до іншої інсталяції ACF. Використовуйте кнопку "
"генерації для експорту в код PHP, який ви можете розмістити у своїй темі."

#: includes/admin/views/settings-tools.php:50
msgid "Download export file"
msgstr "Завантажити файл експорту"

#: includes/admin/views/settings-tools.php:51
msgid "Generate export code"
msgstr "Створити код експорту"

#: includes/admin/views/settings-tools.php:64
msgid "Import Field Groups"
msgstr "Імпортувати групи полів"

#: includes/admin/views/settings-tools.php:67
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"Виберіть JSON файл, який Ви хотіли б імпортувати. При натисканні кнопки "
"імпорту, нижче, ACF буде імпортовано групи полів."

#: includes/admin/views/settings-tools.php:77
#: includes/fields/class-acf-field-file.php:35
msgid "Select File"
msgstr "Оберіть файл"

#: includes/admin/views/settings-tools.php:86
msgid "Import"
msgstr "Імпорт"

#: includes/api/api-helpers.php:856
msgid "Thumbnail"
msgstr "Мініатюра"

#: includes/api/api-helpers.php:857
msgid "Medium"
msgstr "Середній"

#: includes/api/api-helpers.php:858
msgid "Large"
msgstr "Великий"

#: includes/api/api-helpers.php:907
msgid "Full Size"
msgstr "Повний розмір"

#: includes/api/api-helpers.php:1248 includes/api/api-helpers.php:1831
#: pro/fields/class-acf-field-clone.php:992
msgid "(no title)"
msgstr "(без заголовку)"

#: includes/api/api-helpers.php:1868
#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:268
#: includes/fields/class-acf-field-taxonomy.php:986
#, fuzzy
#| msgid "Page Parent"
msgid "Parent"
msgstr "Батьківська сторінка"

#: includes/api/api-helpers.php:3885
#, php-format
msgid "Image width must be at least %dpx."
msgstr ""

#: includes/api/api-helpers.php:3890
#, php-format
msgid "Image width must not exceed %dpx."
msgstr ""

#: includes/api/api-helpers.php:3906
#, php-format
msgid "Image height must be at least %dpx."
msgstr ""

#: includes/api/api-helpers.php:3911
#, php-format
msgid "Image height must not exceed %dpx."
msgstr ""

#: includes/api/api-helpers.php:3929
#, php-format
msgid "File size must be at least %s."
msgstr ""

#: includes/api/api-helpers.php:3934
#, php-format
msgid "File size must must not exceed %s."
msgstr ""

#: includes/api/api-helpers.php:3968
#, fuzzy, php-format
msgid "File type must be %s."
msgstr "Тип поля не існує"

#: includes/fields.php:144
msgid "Basic"
msgstr "Загальне"

#: includes/fields.php:145 includes/forms/form-front.php:47
msgid "Content"
msgstr "Вміст"

#: includes/fields.php:146
msgid "Choice"
msgstr "Вибір"

#: includes/fields.php:147
msgid "Relational"
msgstr ""

#: includes/fields.php:148
msgid "jQuery"
msgstr ""

#: includes/fields.php:149
#: includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:384
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:285
#: pro/fields/class-acf-field-clone.php:839
#: pro/fields/class-acf-field-flexible-content.php:552
#: pro/fields/class-acf-field-flexible-content.php:601
#: pro/fields/class-acf-field-repeater.php:450
msgid "Layout"
msgstr "Шаблон структури"

#: includes/fields.php:326
msgid "Field type does not exist"
msgstr "Тип поля не існує"

#: includes/fields.php:326
msgid "Unknown"
msgstr "Невідомо"

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Група кнопок"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:368
msgid "Choices"
msgstr "Варіанти вибору"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "Enter each choice on a new line."
msgstr "У кожному рядку по варіанту"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "For more control, you may specify both a value and label like this:"
msgstr "Для більшого контролю, Ви можете вказати маркувати значення:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:369
msgid "red : Red"
msgstr "red : Червоний"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:412
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:386
#: includes/fields/class-acf-field-taxonomy.php:793
#: includes/fields/class-acf-field-user.php:408
msgid "Allow Null?"
msgstr "Дозволити порожнє значення?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:375
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:276
#: includes/fields/class-acf-field-range.php:148
#: includes/fields/class-acf-field-select.php:377
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:410
msgid "Default Value"
msgstr "Значення за замовчуванням"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:277
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:411
msgid "Appears when creating a new post"
msgstr "З'являється при створенні нового матеріалу"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:391
#: includes/fields/class-acf-field-radio.php:292
msgid "Horizontal"
msgstr "Горизонтально"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:390
#: includes/fields/class-acf-field-radio.php:291
msgid "Vertical"
msgstr "Вертикально"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:408
#: includes/fields/class-acf-field-file.php:200
#: includes/fields/class-acf-field-image.php:188
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:299
#: includes/fields/class-acf-field-taxonomy.php:833
msgid "Return Value"
msgstr "Повернення значення"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:409
#: includes/fields/class-acf-field-file.php:201
#: includes/fields/class-acf-field-image.php:189
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:300
msgid "Specify the returned value on front end"
msgstr ""

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-radio.php:305
#: includes/fields/class-acf-field-select.php:431
msgid "Value"
msgstr "Значення"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:416
#: includes/fields/class-acf-field-radio.php:307
#: includes/fields/class-acf-field-select.php:433
msgid "Both (Array)"
msgstr "Галочка"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:780
msgid "Checkbox"
msgstr "Галочка"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Вибрати все"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Додати новий вибір"

#: includes/fields/class-acf-field-checkbox.php:353
#, fuzzy
#| msgid "Allow Null?"
msgid "Allow Custom"
msgstr "Дозволити порожнє значення?"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:364
#, fuzzy
#| msgid "Move Custom Field"
msgid "Save Custom"
msgstr "Перемістити поле"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:376
#: includes/fields/class-acf-field-select.php:378
msgid "Enter each default value on a new line"
msgstr "Введіть значення. Одне значення в одному рядку"

#: includes/fields/class-acf-field-checkbox.php:398
msgid "Toggle"
msgstr ""

#: includes/fields/class-acf-field-checkbox.php:399
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Вибір кольору"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Очистити"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Значення за замовчуванням"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Обрати колір"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Поточна колір"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Вибір дати"

#: includes/fields/class-acf-field-date_picker.php:33
#, fuzzy
#| msgid "Done"
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Готово"

#: includes/fields/class-acf-field-date_picker.php:34
#, fuzzy
#| msgid "Today"
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Сьогодні"

#: includes/fields/class-acf-field-date_picker.php:35
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:36
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:37
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:207
#: includes/fields/class-acf-field-date_time_picker.php:181
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Формат показу"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:182
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:216
#: includes/fields/class-acf-field-date_picker.php:247
#: includes/fields/class-acf-field-date_time_picker.php:191
#: includes/fields/class-acf-field-date_time_picker.php:208
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
#, fuzzy
#| msgid "Custom Fields"
msgid "Custom:"
msgstr "Додаткові поля"

#: includes/fields/class-acf-field-date_picker.php:226
msgid "Save Format"
msgstr "Зберегти формат"

#: includes/fields/class-acf-field-date_picker.php:227
msgid "The format used when saving a value"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:237
#: includes/fields/class-acf-field-date_time_picker.php:198
#: includes/fields/class-acf-field-post_object.php:432
#: includes/fields/class-acf-field-relationship.php:697
#: includes/fields/class-acf-field-select.php:426
#: includes/fields/class-acf-field-time_picker.php:124
msgid "Return Format"
msgstr "Формат повернення"

#: includes/fields/class-acf-field-date_picker.php:238
#: includes/fields/class-acf-field-date_time_picker.php:199
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr ""

#: includes/fields/class-acf-field-date_picker.php:256
#: includes/fields/class-acf-field-date_time_picker.php:215
msgid "Week Starts On"
msgstr "Тиждень починається з"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Вибір дати і часу"

#: includes/fields/class-acf-field-date_time_picker.php:33
#, fuzzy
#| msgid "Close Field"
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Закрити поле"

#: includes/fields/class-acf-field-date_time_picker.php:34
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:35
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:36
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:37
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:38
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:39
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:40
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:41
#, fuzzy
#| msgid "No"
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Ні"

#: includes/fields/class-acf-field-date_time_picker.php:42
#, fuzzy
#| msgid "Done"
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Готово"

#: includes/fields/class-acf-field-date_time_picker.php:43
#, fuzzy
#| msgid "Select File"
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Оберіть файл"

#: includes/fields/class-acf-field-date_time_picker.php:45
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:46
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:49
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr ""

#: includes/fields/class-acf-field-date_time_picker.php:50
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr ""

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr ""

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr ""

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Показується, якщо поле порожнє"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:187
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Перед полем"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Розміщується на початку поля"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:196
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Після поля"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Розміщується в кінці поля"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Файл"

#: includes/fields/class-acf-field-file.php:36
msgid "Edit File"
msgstr "Редагувати файл"

#: includes/fields/class-acf-field-file.php:37
msgid "Update File"
msgstr "Оновити файл"

#: includes/fields/class-acf-field-file.php:38
#: includes/fields/class-acf-field-image.php:43 includes/media.php:57
#: pro/fields/class-acf-field-gallery.php:44
msgid "Uploaded to this post"
msgstr "Завантажено до цього матеріалу"

#: includes/fields/class-acf-field-file.php:126
msgid "File name"
msgstr "Назва файлу"

#: includes/fields/class-acf-field-file.php:130
#: includes/fields/class-acf-field-file.php:233
#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: pro/fields/class-acf-field-gallery.php:690
#: pro/fields/class-acf-field-gallery.php:719
msgid "File size"
msgstr "Розмір файлу"

#: includes/fields/class-acf-field-file.php:139
#: includes/fields/class-acf-field-image.php:124
#: includes/fields/class-acf-field-link.php:140 includes/input.php:269
#: pro/fields/class-acf-field-gallery.php:343
#: pro/fields/class-acf-field-gallery.php:531
msgid "Remove"
msgstr "Видалити"

#: includes/fields/class-acf-field-file.php:155
msgid "Add File"
msgstr "Додати файл"

#: includes/fields/class-acf-field-file.php:206
msgid "File Array"
msgstr "Масив файлу"

#: includes/fields/class-acf-field-file.php:207
msgid "File URL"
msgstr "URL файлу"

#: includes/fields/class-acf-field-file.php:208
msgid "File ID"
msgstr "ID файлу"

#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:213
#: pro/fields/class-acf-field-gallery.php:655
msgid "Library"
msgstr "Бібліотека"

#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:214
#: pro/fields/class-acf-field-gallery.php:656
msgid "Limit the media library choice"
msgstr ""

#: includes/fields/class-acf-field-file.php:221
#: includes/fields/class-acf-field-image.php:219
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:661
msgid "All"
msgstr "Все"

#: includes/fields/class-acf-field-file.php:222
#: includes/fields/class-acf-field-image.php:220
#: pro/fields/class-acf-field-gallery.php:662
msgid "Uploaded to post"
msgstr "Завантажено до матеріалу"

#: includes/fields/class-acf-field-file.php:229
#: includes/fields/class-acf-field-image.php:227
#: pro/fields/class-acf-field-gallery.php:669
msgid "Minimum"
msgstr "Мінімум"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-file.php:241
msgid "Restrict which files can be uploaded"
msgstr ""

#: includes/fields/class-acf-field-file.php:240
#: includes/fields/class-acf-field-image.php:256
#: pro/fields/class-acf-field-gallery.php:698
msgid "Maximum"
msgstr "Максимум"

#: includes/fields/class-acf-field-file.php:251
#: includes/fields/class-acf-field-image.php:285
#: pro/fields/class-acf-field-gallery.php:727
msgid "Allowed file types"
msgstr "Дозволені типи файлів"

#: includes/fields/class-acf-field-file.php:252
#: includes/fields/class-acf-field-image.php:286
#: pro/fields/class-acf-field-gallery.php:728
msgid "Comma separated list. Leave blank for all types"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google карта"

#: includes/fields/class-acf-field-google-map.php:40
msgid "Locating"
msgstr "Розміщення"

#: includes/fields/class-acf-field-google-map.php:41
msgid "Sorry, this browser does not support geolocation"
msgstr "Вибачте, цей браузер не підтримує автоматичне визначення локації"

#: includes/fields/class-acf-field-google-map.php:113
msgid "Clear location"
msgstr "Очистити розміщення"

#: includes/fields/class-acf-field-google-map.php:114
msgid "Find current location"
msgstr ""

#: includes/fields/class-acf-field-google-map.php:117
msgid "Search for address..."
msgstr "Шукати адресу..."

#: includes/fields/class-acf-field-google-map.php:147
#: includes/fields/class-acf-field-google-map.php:158
msgid "Center"
msgstr "Центрування"

#: includes/fields/class-acf-field-google-map.php:148
#: includes/fields/class-acf-field-google-map.php:159
msgid "Center the initial map"
msgstr "Початкове розміщення карти"

#: includes/fields/class-acf-field-google-map.php:170
msgid "Zoom"
msgstr "Збільшення"

#: includes/fields/class-acf-field-google-map.php:171
msgid "Set the initial zoom level"
msgstr "Вкажіть початковий масштаб"

#: includes/fields/class-acf-field-google-map.php:180
#: includes/fields/class-acf-field-image.php:239
#: includes/fields/class-acf-field-image.php:268
#: includes/fields/class-acf-field-oembed.php:281
#: pro/fields/class-acf-field-gallery.php:681
#: pro/fields/class-acf-field-gallery.php:710
msgid "Height"
msgstr "Висота"

#: includes/fields/class-acf-field-google-map.php:181
msgid "Customise the map height"
msgstr "Налаштуйте висоту карти"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Група"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:389
msgid "Sub Fields"
msgstr "Дочірні поля"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:840
msgid "Specify the style used to render the selected fields"
msgstr ""

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:845
#: pro/fields/class-acf-field-flexible-content.php:612
#: pro/fields/class-acf-field-repeater.php:458
msgid "Block"
msgstr "Блок"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:846
#: pro/fields/class-acf-field-flexible-content.php:611
#: pro/fields/class-acf-field-repeater.php:457
msgid "Table"
msgstr "Таблиця"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:847
#: pro/fields/class-acf-field-flexible-content.php:613
#: pro/fields/class-acf-field-repeater.php:459
msgid "Row"
msgstr "Рядок"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Зображення"

#: includes/fields/class-acf-field-image.php:40
msgid "Select Image"
msgstr "Обрати зображення"

#: includes/fields/class-acf-field-image.php:41
#: pro/fields/class-acf-field-gallery.php:42
msgid "Edit Image"
msgstr "Редагувати зображення"

#: includes/fields/class-acf-field-image.php:42
#: pro/fields/class-acf-field-gallery.php:43
msgid "Update Image"
msgstr "Оновити зображення"

#: includes/fields/class-acf-field-image.php:44
msgid "All images"
msgstr "Усі зображення"

#: includes/fields/class-acf-field-image.php:140
msgid "No image selected"
msgstr "Зображення не обрано"

#: includes/fields/class-acf-field-image.php:140
msgid "Add Image"
msgstr "Додати зображення"

#: includes/fields/class-acf-field-image.php:194
msgid "Image Array"
msgstr "Масив зображення"

#: includes/fields/class-acf-field-image.php:195
msgid "Image URL"
msgstr "URL зображення"

#: includes/fields/class-acf-field-image.php:196
msgid "Image ID"
msgstr "ID зображення"

#: includes/fields/class-acf-field-image.php:203
msgid "Preview Size"
msgstr "Розмір мініатюр"

#: includes/fields/class-acf-field-image.php:204
msgid "Shown when entering data"
msgstr ""

#: includes/fields/class-acf-field-image.php:228
#: includes/fields/class-acf-field-image.php:257
#: pro/fields/class-acf-field-gallery.php:670
#: pro/fields/class-acf-field-gallery.php:699
msgid "Restrict which images can be uploaded"
msgstr ""

#: includes/fields/class-acf-field-image.php:231
#: includes/fields/class-acf-field-image.php:260
#: includes/fields/class-acf-field-oembed.php:270
#: pro/fields/class-acf-field-gallery.php:673
#: pro/fields/class-acf-field-gallery.php:702
msgid "Width"
msgstr "Ширина"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Посилання"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Оберіть посилання"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr ""

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Масив посилання"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "URL посилання"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Повідомлення"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Перенесення рядків"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Вкажіть спосіб обробки нових рядків"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Автоматично додавати абзаци"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Автоматичне перенесення рядків (додається теґ &lt;br&gt;)"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Без форматування"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr ""

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr ""

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Число"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:157
msgid "Minimum Value"
msgstr "Мінімальне значення"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:167
msgid "Maximum Value"
msgstr "Максимальне значення"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:177
msgid "Step Size"
msgstr "Розмір кроку"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Значення має бути числом"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr ""

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr ""

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr ""

#: includes/fields/class-acf-field-oembed.php:219
msgid "Enter URL"
msgstr "Введіть URL"

#: includes/fields/class-acf-field-oembed.php:234
#: includes/fields/class-acf-field-taxonomy.php:898
msgid "Error."
msgstr "Помилка."

#: includes/fields/class-acf-field-oembed.php:234
msgid "No embed found for the given URL."
msgstr ""

#: includes/fields/class-acf-field-oembed.php:267
#: includes/fields/class-acf-field-oembed.php:278
msgid "Embed Size"
msgstr "Розмір вставки"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Архіви"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:384
#: includes/fields/class-acf-field-relationship.php:623
msgid "Filter by Post Type"
msgstr "Фільтр за типом матеріалу"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:392
#: includes/fields/class-acf-field-relationship.php:631
msgid "All post types"
msgstr "Всі типи матеріалів"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:398
#: includes/fields/class-acf-field-relationship.php:637
msgid "Filter by Taxonomy"
msgstr "Фільтр за типом таксономією"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:406
#: includes/fields/class-acf-field-relationship.php:645
msgid "All taxonomies"
msgstr "Всі таксономії"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr ""

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:422
#: includes/fields/class-acf-field-select.php:396
#: includes/fields/class-acf-field-user.php:418
msgid "Select multiple values?"
msgstr "Дозволити множинний вибір?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Пароль"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:702
msgid "Post Object"
msgstr ""

#: includes/fields/class-acf-field-post_object.php:438
#: includes/fields/class-acf-field-relationship.php:703
msgid "Post ID"
msgstr "ID публікації"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr ""

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Інше"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Додати вибір 'Інше', для користувацьких значень"

#: includes/fields/class-acf-field-radio.php:265
#, fuzzy
msgid "Save Other"
msgstr "Зберегти інше"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr ""

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Діапазон (Range)"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:37
msgid "Minimum values reached ( {min} values )"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:38
msgid "Maximum values reached ( {max} values )"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:39
msgid "Loading"
msgstr "Завантаження"

#: includes/fields/class-acf-field-relationship.php:40
msgid "No matches found"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:423
msgid "Select post type"
msgstr "Вибір типу матеріалу"

#: includes/fields/class-acf-field-relationship.php:449
msgid "Select taxonomy"
msgstr "Вибір таксономії"

#: includes/fields/class-acf-field-relationship.php:539
msgid "Search..."
msgstr "Шукати..."

#: includes/fields/class-acf-field-relationship.php:651
msgid "Filters"
msgstr "Фільтри"

#: includes/fields/class-acf-field-relationship.php:657
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Тип матеріалу"

#: includes/fields/class-acf-field-relationship.php:658
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:763
msgid "Taxonomy"
msgstr "Таксономія"

#: includes/fields/class-acf-field-relationship.php:665
msgid "Elements"
msgstr "Елементи"

#: includes/fields/class-acf-field-relationship.php:666
msgid "Selected elements will be displayed in each result"
msgstr ""

#: includes/fields/class-acf-field-relationship.php:677
msgid "Minimum posts"
msgstr "Мінімум матеріалів"

#: includes/fields/class-acf-field-relationship.php:686
msgid "Maximum posts"
msgstr "Максимум матеріалів"

#: includes/fields/class-acf-field-relationship.php:790
#: pro/fields/class-acf-field-gallery.php:800
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] ""
msgstr[1] ""
msgstr[2] ""

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:785
#, fuzzy
#| msgid "Select File"
msgctxt "noun"
msgid "Select"
msgstr "Оберіть файл"

#: includes/fields/class-acf-field-select.php:38
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr ""

#: includes/fields/class-acf-field-select.php:39
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr ""

#: includes/fields/class-acf-field-select.php:40
#, fuzzy
#| msgid "No Fields found"
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Не знайдено полів"

#: includes/fields/class-acf-field-select.php:41
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:42
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:43
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr ""

#: includes/fields/class-acf-field-select.php:44
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr ""

#: includes/fields/class-acf-field-select.php:45
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr ""

#: includes/fields/class-acf-field-select.php:46
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr ""

#: includes/fields/class-acf-field-select.php:47
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr ""

#: includes/fields/class-acf-field-select.php:48
#, fuzzy
#| msgid "Search Fields"
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Шукати поля"

#: includes/fields/class-acf-field-select.php:49
#, fuzzy
#| msgid "Loading"
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Завантаження"

#: includes/fields/class-acf-field-select.php:255 includes/media.php:54
#, fuzzy
#| msgid "Select File"
msgctxt "verb"
msgid "Select"
msgstr "Оберіть файл"

#: includes/fields/class-acf-field-select.php:406
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Покращений стиль"

#: includes/fields/class-acf-field-select.php:416
msgid "Use AJAX to lazy load choices?"
msgstr "Використати AJAX для завантаження значень?"

#: includes/fields/class-acf-field-select.php:427
msgid "Specify the value returned"
msgstr ""

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Розділювач"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Вкладка"

#: includes/fields/class-acf-field-tab.php:82
msgid ""
"The tab field will display incorrectly when added to a Table style repeater "
"field or flexible content field layout"
msgstr ""

#: includes/fields/class-acf-field-tab.php:83
msgid ""
"Use \"Tab Fields\" to better organize your edit screen by grouping fields "
"together."
msgstr ""

#: includes/fields/class-acf-field-tab.php:84
msgid ""
"All fields following this \"tab field\" (or until another \"tab field\" is "
"defined) will be grouped together using this field's label as the tab "
"heading."
msgstr ""

#: includes/fields/class-acf-field-tab.php:98
msgid "Placement"
msgstr "Розміщення"

#: includes/fields/class-acf-field-tab.php:110
msgid "End-point"
msgstr ""

#: includes/fields/class-acf-field-tab.php:111
msgid "Use this field as an end-point and start a new group of tabs"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:713
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:732
msgid "None"
msgstr "Нічого"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Select the taxonomy to be displayed"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:773
msgid "Appearance"
msgstr "Вигляд"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Select the appearance of this field"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:779
msgid "Multiple Values"
msgstr "Множинний вибір"

#: includes/fields/class-acf-field-taxonomy.php:781
msgid "Multi Select"
msgstr "Множинний вибір"

#: includes/fields/class-acf-field-taxonomy.php:783
msgid "Single Value"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:784
msgid "Radio Buttons"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:803
msgid "Create Terms"
msgstr "Створити терміни"

#: includes/fields/class-acf-field-taxonomy.php:804
msgid "Allow new terms to be created whilst editing"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:813
msgid "Save Terms"
msgstr "Зберегти терміни"

#: includes/fields/class-acf-field-taxonomy.php:814
msgid "Connect selected terms to the post"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:823
msgid "Load Terms"
msgstr "Завантажити терміни"

#: includes/fields/class-acf-field-taxonomy.php:824
msgid "Load value from posts terms"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:838
msgid "Term Object"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:839
msgid "Term ID"
msgstr "ID терміну"

#: includes/fields/class-acf-field-taxonomy.php:898
#, php-format
msgid "User unable to add new %s"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:911
#, php-format
msgid "%s already exists"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:952
#, php-format
msgid "%s added"
msgstr ""

#: includes/fields/class-acf-field-taxonomy.php:997
msgid "Add"
msgstr "Додати"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Текст"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Ліміт символів"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Щоб зняти обмеження — нічого не вказуйте тут"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Область тексту"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Рядки"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Вкажіть висоту текстового блоку"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Вибір часу"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Так / Ні"

#: includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159 includes/input.php:267
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Так"

#: includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:169 includes/input.php:268
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Ні"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:155
#, fuzzy
#| msgid "Text"
msgid "On Text"
msgstr "Текст"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr ""

#: includes/fields/class-acf-field-true_false.php:165
#, fuzzy
#| msgid "Text"
msgid "Off Text"
msgstr "Текст"

#: includes/fields/class-acf-field-true_false.php:166
msgid "Text shown when inactive"
msgstr ""

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr ""

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Значення має бути адресою URl"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Користувач"

#: includes/fields/class-acf-field-user.php:393
msgid "Filter by role"
msgstr "Фільтр за ролями"

#: includes/fields/class-acf-field-user.php:401
msgid "All user roles"
msgstr "Всі ролі користувачів"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Візуальний редактор"

#: includes/fields/class-acf-field-wysiwyg.php:359
msgid "Visual"
msgstr "Візуальний"

#: includes/fields/class-acf-field-wysiwyg.php:360
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:366
msgid "Click to initialize TinyMCE"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Tabs"
msgstr "Вкладки"

#: includes/fields/class-acf-field-wysiwyg.php:424
msgid "Visual & Text"
msgstr "Візуальний і Текстовий"

#: includes/fields/class-acf-field-wysiwyg.php:425
msgid "Visual Only"
msgstr "Візуальний лише"

#: includes/fields/class-acf-field-wysiwyg.php:426
msgid "Text Only"
msgstr "Лише текст"

#: includes/fields/class-acf-field-wysiwyg.php:433
msgid "Toolbar"
msgstr "Панель інструментів"

#: includes/fields/class-acf-field-wysiwyg.php:443
msgid "Show Media Upload Buttons?"
msgstr "Показувати кнопки завантаження файлів?"

#: includes/fields/class-acf-field-wysiwyg.php:453
msgid "Delay initialization?"
msgstr ""

#: includes/fields/class-acf-field-wysiwyg.php:454
msgid "TinyMCE will not be initalized until field is clicked"
msgstr ""

#: includes/forms/form-comment.php:166 includes/forms/form-post.php:303
#: pro/admin/admin-options-page.php:308
msgid "Edit field group"
msgstr "Редагувати групу полів"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr ""

#: includes/forms/form-front.php:103
#: pro/fields/class-acf-field-gallery.php:573 pro/options-page.php:81
msgid "Update"
msgstr "Оновити"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Матеріал оновлено"

#: includes/forms/form-front.php:229
msgid "Spam Detected"
msgstr ""

#: includes/input.php:259
msgid "Expand Details"
msgstr "Показати деталі"

#: includes/input.php:260
msgid "Collapse Details"
msgstr "Сховати деталі"

#: includes/input.php:261
msgid "Validation successful"
msgstr ""

#: includes/input.php:262 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr ""

#: includes/input.php:263
msgid "1 field requires attention"
msgstr ""

#: includes/input.php:264
#, php-format
msgid "%d fields require attention"
msgstr ""

#: includes/input.php:265
msgid "Restricted"
msgstr ""

#: includes/input.php:266
msgid "Are you sure?"
msgstr "Ви впевнені?"

#: includes/input.php:270
msgid "Cancel"
msgstr "Скасувати"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Публікація"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Сторінка"

#: includes/locations.php:96
msgid "Forms"
msgstr "Форми"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Вкладення"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr ""

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Коментар"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Поточна роль користувача"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Головний адмін"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Поточний користувач"

#: includes/locations/class-acf-location-current-user.php:97
#, fuzzy
msgid "Logged in"
msgstr "Роль залоґованого користувача"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr ""

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr ""

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Елемент меню"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Меню"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Розміщення меню"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Меню"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Батьківська сторінка"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Шаблон сторінки"

#: includes/locations/class-acf-location-page-template.php:98
#: includes/locations/class-acf-location-post-template.php:151
msgid "Default Template"
msgstr "Стандартний шаблон"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Тип сторінки"

#: includes/locations/class-acf-location-page-type.php:145
msgid "Front Page"
msgstr "Головна сторінка"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Posts Page"
msgstr "Сторінка з публікаціями"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Top Level Page (no parent)"
msgstr "Верхній рівень сторінки (без батьків)"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Parent Page (has children)"
msgstr "Батьківська сторінка (має дочірні)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Child Page (has parent)"
msgstr "Дочірня сторінка (має батьківську)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Категорія"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Формат"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Статус матеріалу"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Таксономія"

#: includes/locations/class-acf-location-post-template.php:27
#, fuzzy
#| msgid "Page Template"
msgid "Post Template"
msgstr "Шаблон сторінки"

#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy Term"
msgstr "Термін таксономії"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Форма користувача"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Додати / Редагувати"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Реєстрація"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Роль користувача"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Віджет"

#: includes/media.php:55
msgctxt "verb"
msgid "Edit"
msgstr "Редагувати"

#: includes/media.php:56
msgctxt "verb"
msgid "Update"
msgstr "Оновити"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr ""

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Додаткові поля Pro"

#: pro/admin/admin-options-page.php:200
msgid "Publish"
msgstr "Опублікувати"

#: pro/admin/admin-options-page.php:206
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Немає полів для цієї сторінки опцій. <a href=\"%s\">Створити групу "
"додаткових полів</a>"

#: pro/admin/admin-settings-updates.php:78
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b>Помилка</b>. Неможливо під’єднатися до сервера оновлення"

#: pro/admin/admin-settings-updates.php:162
#: pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Оновлення"

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Деактивувати ліцензію"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Активувати ліцензію"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Інформація про ліцензію"

#: pro/admin/views/html-settings-updates.php:20
#, fuzzy, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Щоб розблокувати оновлення, будь ласка, введіть код ліцензії. Якщо не маєте "
"ліцензії, перегляньте"

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Код ліцензії"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Інформація про оновлення"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Поточна версія"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "Остання версія"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Доступні оновлення"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Оновити плаґін"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr "Будь ласка, введіть код ліцензії, щоб розблокувати оновлення"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Перевірити знову"

#: pro/admin/views/html-settings-updates.php:117
#, fuzzy
msgid "Upgrade Notice"
msgstr "Оновити базу даних"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Клон"

#: pro/fields/class-acf-field-clone.php:808
msgid "Select one or more fields you wish to clone"
msgstr ""

#: pro/fields/class-acf-field-clone.php:825
msgid "Display"
msgstr "Таблиця"

#: pro/fields/class-acf-field-clone.php:826
msgid "Specify the style used to render the clone field"
msgstr ""

#: pro/fields/class-acf-field-clone.php:831
#, fuzzy
#| msgid "Please select the field group you wish this field to move to"
msgid "Group (displays selected fields in a group within this field)"
msgstr "Будь ласка, оберіть групу полів куди Ви хочете перемістити це поле"

#: pro/fields/class-acf-field-clone.php:832
msgid "Seamless (replaces this field with selected fields)"
msgstr ""

#: pro/fields/class-acf-field-clone.php:853
#, php-format
msgid "Labels will be displayed as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:856
#, fuzzy
#| msgid "Field Label"
msgid "Prefix Field Labels"
msgstr "Назва поля"

#: pro/fields/class-acf-field-clone.php:867
#, php-format
msgid "Values will be saved as %s"
msgstr ""

#: pro/fields/class-acf-field-clone.php:870
#, fuzzy
#| msgid "Field Name"
msgid "Prefix Field Names"
msgstr "Ярлик"

#: pro/fields/class-acf-field-clone.php:988
msgid "Unknown field"
msgstr "Невідоме поле"

#: pro/fields/class-acf-field-clone.php:1027
#, fuzzy
msgid "Unknown field group"
msgstr "Редагувати групу полів"

#: pro/fields/class-acf-field-clone.php:1031
#, php-format
msgid "All fields from %s field group"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:174
#: pro/fields/class-acf-field-repeater.php:470
msgid "Add Row"
msgstr "Додати рядок"

#: pro/fields/class-acf-field-flexible-content.php:34
#, fuzzy
msgid "layout"
msgstr "Шаблон структури"

#: pro/fields/class-acf-field-flexible-content.php:35
#, fuzzy
msgid "layouts"
msgstr "Шаблон структури"

#: pro/fields/class-acf-field-flexible-content.php:36
msgid "remove {layout}?"
msgstr "видалити {layout}?"

#: pro/fields/class-acf-field-flexible-content.php:37
msgid "This field requires at least {min} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:38
msgid "This field has a limit of {max} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:39
msgid "This field requires at least {min} {label} {identifier}"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:40
msgid "Maximum {label} limit reached ({max} {identifier})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:41
msgid "{available} {label} {identifier} available (max {max})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:42
msgid "{required} {label} {identifier} required (min {min})"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:43
msgid "Flexible Content requires at least 1 layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:273
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:406
msgid "Add layout"
msgstr "Додати шаблон"

#: pro/fields/class-acf-field-flexible-content.php:407
msgid "Remove layout"
msgstr "Видалити шаблон"

#: pro/fields/class-acf-field-flexible-content.php:408
#: pro/fields/class-acf-field-repeater.php:298
msgid "Click to toggle"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder Layout"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:554
msgid "Reorder"
msgstr ""

#: pro/fields/class-acf-field-flexible-content.php:555
msgid "Delete Layout"
msgstr "Видалити шаблон"

#: pro/fields/class-acf-field-flexible-content.php:556
msgid "Duplicate Layout"
msgstr "Дублювати шаблон"

#: pro/fields/class-acf-field-flexible-content.php:557
msgid "Add New Layout"
msgstr "Додати новий шаблон"

#: pro/fields/class-acf-field-flexible-content.php:628
msgid "Min"
msgstr "Мін."

#: pro/fields/class-acf-field-flexible-content.php:641
msgid "Max"
msgstr "Макс."

#: pro/fields/class-acf-field-flexible-content.php:668
#: pro/fields/class-acf-field-repeater.php:466
msgid "Button Label"
msgstr "Текст для кнопки"

#: pro/fields/class-acf-field-flexible-content.php:677
msgid "Minimum Layouts"
msgstr "Мінімум шаблонів"

#: pro/fields/class-acf-field-flexible-content.php:686
msgid "Maximum Layouts"
msgstr "Максимум шаблонів"

#: pro/fields/class-acf-field-gallery.php:41
msgid "Add Image to Gallery"
msgstr "Додати зображення до галереї"

#: pro/fields/class-acf-field-gallery.php:45
#, fuzzy
msgid "Maximum selection reached"
msgstr "Досягнуто максимального вибору"

#: pro/fields/class-acf-field-gallery.php:321
msgid "Length"
msgstr "Довжина"

#: pro/fields/class-acf-field-gallery.php:364
msgid "Caption"
msgstr "Підпис"

#: pro/fields/class-acf-field-gallery.php:373
msgid "Alt Text"
msgstr "Альтернативний текст"

#: pro/fields/class-acf-field-gallery.php:544
msgid "Add to gallery"
msgstr "Додати до галереї"

#: pro/fields/class-acf-field-gallery.php:548
msgid "Bulk actions"
msgstr "Масові дії"

#: pro/fields/class-acf-field-gallery.php:549
msgid "Sort by date uploaded"
msgstr "Сортувати за датою завантаження"

#: pro/fields/class-acf-field-gallery.php:550
msgid "Sort by date modified"
msgstr "Сортувати за датою зміни"

#: pro/fields/class-acf-field-gallery.php:551
msgid "Sort by title"
msgstr "Сортувати за назвою"

#: pro/fields/class-acf-field-gallery.php:552
msgid "Reverse current order"
msgstr "Зворотній поточний порядок"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Close"
msgstr "Закрити"

#: pro/fields/class-acf-field-gallery.php:624
msgid "Minimum Selection"
msgstr "Мінімальна вибірка"

#: pro/fields/class-acf-field-gallery.php:633
msgid "Maximum Selection"
msgstr "Максимальна вибірка"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Insert"
msgstr "Вставити"

#: pro/fields/class-acf-field-gallery.php:643
msgid "Specify where new attachments are added"
msgstr ""

#: pro/fields/class-acf-field-gallery.php:647
msgid "Append to the end"
msgstr "Розміщується в кінці"

#: pro/fields/class-acf-field-gallery.php:648
msgid "Prepend to the beginning"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:36
msgid "Minimum rows reached ({min} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:37
msgid "Maximum rows reached ({max} rows)"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:343
msgid "Add row"
msgstr "Додати рядок"

#: pro/fields/class-acf-field-repeater.php:344
msgid "Remove row"
msgstr "Видалити рядок"

#: pro/fields/class-acf-field-repeater.php:419
#, fuzzy
#| msgid "Collapse Details"
msgid "Collapsed"
msgstr "Сховати деталі"

#: pro/fields/class-acf-field-repeater.php:420
msgid "Select a sub field to show when row is collapsed"
msgstr ""

#: pro/fields/class-acf-field-repeater.php:430
msgid "Minimum Rows"
msgstr "Мінімум рядків"

#: pro/fields/class-acf-field-repeater.php:440
msgid "Maximum Rows"
msgstr "Максимум рядків"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr ""

#: pro/options-page.php:51
msgid "Options"
msgstr "Опції"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Опції оновлено"

#: pro/updates.php:97
#, fuzzy, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Щоб розблокувати оновлення, будь ласка, введіть код ліцензії. Якщо не маєте "
"ліцензії, перегляньте"

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr ""

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr ""

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr ""

#~ msgid "See what's new in"
#~ msgstr "Перегляньте, що нового у"

#~ msgid "version"
#~ msgstr "версії"

#~ msgid "Getting Started"
#~ msgstr "Початок роботи"

#~ msgid "Field Types"
#~ msgstr "Типи полів"

#~ msgid "Functions"
#~ msgstr "Функції"

#~ msgid "Actions"
#~ msgstr "Дії"

#~ msgid "'How to' guides"
#~ msgstr "Інструкції «як зробити»"

#~ msgid "Tutorials"
#~ msgstr "Документація"

#~ msgid "Created by"
#~ msgstr "Плаґін створив"

#~ msgid "Upgrade"
#~ msgstr "Оновити"

#~ msgid "Error"
#~ msgstr "Помилка"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Поля можна перетягувати"

#~ msgid "See what's new"
#~ msgstr "Перегляньте, що нового"

#~ msgid "Show a different month"
#~ msgstr "Показати інший місяць"

#~ msgid "Return format"
#~ msgstr "Формат повернення"

#~ msgid "uploaded to this post"
#~ msgstr "завантажено до цього матеріалу"

#~ msgid "File Size"
#~ msgstr "Розмір файлу"

#~ msgid "No File selected"
#~ msgstr "Файл не обрано"

#~ msgid "Warning"
#~ msgstr "Застереження"

#~ msgid "eg. Show extra content"
#~ msgstr "напр., Показати додаткові поля"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b>Помилка з’єднання</b>. Спробуйте знову"

#~ msgid "Save Options"
#~ msgstr "Зберегти опції"

#~ msgid "License"
#~ msgstr "Ліцензія"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Щоб розблокувати оновлення, будь ласка, введіть код ліцензії. Якщо не "
#~ "маєте ліцензії, перегляньте"

#~ msgid "details & pricing"
#~ msgstr "деталі і ціни"

#~ msgid "Hide / Show All"
#~ msgstr "Сховати / Показати все"

#~ msgid "Show Field Keys"
#~ msgstr "Показати ключі полів"

#~ msgid "Pending Review"
#~ msgstr "Очікує затвердження"

#~ msgid "Draft"
#~ msgstr "Чернетка"

#~ msgid "Future"
#~ msgstr "Заплановано"

#~ msgid "Private"
#~ msgstr "Приватний"

#~ msgid "Revision"
#~ msgstr "Ревізія"

#~ msgid "Trash"
#~ msgstr "В кошику"

#~ msgid "Import / Export"
#~ msgstr "Імпорт / Експорт"

#, fuzzy
#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr "Чим меше число <br> тим вище розміщення"

#, fuzzy
#~ msgid "ACF PRO Required"
#~ msgstr "Обов’язкове?"

#~ msgid "Update Database"
#~ msgstr "Оновити базу даних"

#~ msgid "Data Upgrade"
#~ msgstr "Дані оновлено"

#~ msgid "Data upgraded successfully."
#~ msgstr "Дані успішно оновлено."

#~ msgid "Data is at the latest version."
#~ msgstr "Дані останньої версії."

#~ msgid "Load & Save Terms to Post"
#~ msgstr "Завантажити і зберегти значення до матеріалу"

#, fuzzy
#~ msgid "image"
#~ msgstr "Зображення"

#, fuzzy
#~ msgid "expand_details"
#~ msgstr "Показати деталі"

#, fuzzy
#~ msgid "collapse_details"
#~ msgstr "Сховати деталі"

#, fuzzy
#~ msgid "relationship"
#~ msgstr "Закрити поле"

#, fuzzy
#~ msgid "title_is_required"
#~ msgstr "Заголовок обов’язковий"

#, fuzzy
#~ msgid "move_field"
#~ msgstr "Перемістити поле"

#, fuzzy
#~ msgid "flexible_content"
#~ msgstr "Гнучкий вміст"

#, fuzzy
#~ msgid "gallery"
#~ msgstr "Галерея"

#, fuzzy
#~ msgid "Controls how HTML tags are rendered"
#~ msgstr "Вкажіть спосіб обробки нових рядків"

#~ msgid "Field&nbsp;Groups"
#~ msgstr "Групи полів"

#~ msgid "Attachment Details"
#~ msgstr "Деталі вкладення"

#~ msgid "Custom field updated."
#~ msgstr "Додаткове поле оновлено."

#~ msgid "Custom field deleted."
#~ msgstr "Додаткове поле видалено."

#~ msgid "Import/Export"
#~ msgstr "Імпорт/Експорт"

#~ msgid "Column Width"
#~ msgstr "Ширина колонки"

#~ msgid "Validation Failed. One or more fields below are required."
#~ msgstr "Заповніть всі поля! Одне або декілька полів нижче не заповнено."

#~ msgid "Success"
#~ msgstr "Готово"

#~ msgid "Run the updater"
#~ msgstr "Запустити оновлення"

#~ msgid "Return to custom fields"
#~ msgstr "Повернутися до додаткових полів"

#~ msgid "Size"
#~ msgstr "Розмір"

#~ msgid "Formatting"
#~ msgstr "Форматування"

#~ msgid "Effects value on front end"
#~ msgstr "Як показувати на сайті"

#~ msgid "Convert HTML into tags"
#~ msgstr "Конвертувати в теґи HTML"

#~ msgid "Plain text"
#~ msgstr "Простий текст"

#~ msgid "1 image selected"
#~ msgstr "1 обране зображення"

#~ msgid "%d images selected"
#~ msgstr "%d вибраних зображень"

#~ msgid "Normal"
#~ msgstr "Стандартно"

#~ msgid ""
#~ "Read documentation, learn the functions and find some tips &amp; tricks "
#~ "for your next web project."
#~ msgstr ""
#~ "В документації ви знайдете детальний опис функцій та декілька порад і "
#~ "трюків для кращого використання плаґіну."

#~ msgid "Visit the ACF website"
#~ msgstr "Відвідайте сайт плаґіну"

#~ msgid "Gallery Field"
#~ msgstr "Поле галереї"

#~ msgid "Export XML"
#~ msgstr "Експортувати XML"

#~ msgid "Copy the PHP code generated"
#~ msgstr "Скопіюйте згенерований код PHP"

#~ msgid "Paste into your functions.php file"
#~ msgstr "Вставте  у  <code>functions.php</code>"

#~ msgid "Create PHP"
#~ msgstr "Створити PHP"

#~ msgid "Back to settings"
#~ msgstr "Повернутися до налаштувань"

#~ msgid "requires a database upgrade"
#~ msgstr "потребує оновлення бази даних"

#~ msgid "why?"
#~ msgstr "для чого?"

#~ msgid "Please"
#~ msgstr "Будь ласка,"

#~ msgid "backup your database"
#~ msgstr "створіть резервну копію БД"

#~ msgid "then click"
#~ msgstr "і натискайте цю кнопку"

#~ msgid "Red"
#~ msgstr "Червоний"

#~ msgid "Blue"
#~ msgstr "Синій"

#~ msgid "blue : Blue"
#~ msgstr "blue : Синій"

#, fuzzy
#~ msgid "jQuery date formats"
#~ msgstr "Формат дати"

#~ msgid "File Updated."
#~ msgstr "Файл оновлено."

#~ msgid "+ Add Row"
#~ msgstr "+ Додати рядок"

#~ msgid "Field Order"
#~ msgstr "Порядок полів"

#, fuzzy
#~ msgid ""
#~ "No fields. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."
#~ msgstr ""
#~ "Ще немає полів. Click the \"+ Add Sub Field button\" to create your first "
#~ "field."

#~ msgid "Edit this Field"
#~ msgstr "Редагувати це поле"

#~ msgid "Docs"
#~ msgstr "Документація"

#~ msgid "Close Sub Field"
#~ msgstr "Закрити дочірнє поле"

#~ msgid "+ Add Sub Field"
#~ msgstr "+ Додати дочірнє поле"

#~ msgid "Image Updated"
#~ msgstr "Зображення оновлено"

#~ msgid "Grid"
#~ msgstr "Плитка"

#~ msgid "List"
#~ msgstr "Список"

#~ msgid "Added"
#~ msgstr "Додано"

#~ msgid "Image Updated."
#~ msgstr "Зображення оновлено."

#~ msgid "Add selected Images"
#~ msgstr "Додати обрані зображення"

#~ msgid "Field Instructions"
#~ msgstr "Опис поля"

#~ msgid "Table (default)"
#~ msgstr "Таблиця (за замовчуванням)"

#~ msgid "Define how to render html tags"
#~ msgstr "Оберіть спосіб обробки теґів html"

#~ msgid "Define how to render html tags / new lines"
#~ msgstr "Оберіть спосіб обробки теґів html та переносу рядків"

#~ msgid "Run filter \"the_content\"?"
#~ msgstr "Застосовувати фільтр «the_content»?"

#~ msgid "Page Specific"
#~ msgstr "Сторінки"

#~ msgid "Post Specific"
#~ msgstr "Публікації"

#~ msgid "Taxonomy (Add / Edit)"
#~ msgstr "Тип таксономії (Додати / Редагувати)"

#~ msgid "Media (Edit)"
#~ msgstr "Медіафайл (Редагувати)"

#~ msgid "match"
#~ msgstr "має співпадати"

#~ msgid "all"
#~ msgstr "все"

#~ msgid "of the above"
#~ msgstr "з вищевказаних умов"

#~ msgid "Add Fields to Edit Screens"
#~ msgstr "Додайте поля на сторінку редагування вмісту"

#, fuzzy
#~ msgid "eg. dd/mm/yy. read more about"
#~ msgstr "Напр. dd/mm/yy. read more about"
