msgid ""
msgstr ""
"Project-Id-Version: Advanced Custom Fields Pro v5.7.12\n"
"Report-Msgid-Bugs-To: http://support.advancedcustomfields.com\n"
"POT-Creation-Date: 2019-01-31 12:36+0100\n"
"PO-Revision-Date: 2019-02-15 17:08+0300\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON> <<EMAIL>>\n"
"Language: tr_TR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.1.1\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: acf.php\n"
"X-Textdomain-Support: yes\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: acf.php:80
msgid "Advanced Custom Fields"
msgstr "Advanced Custom Fields"

#: acf.php:363 includes/admin/admin.php:58
msgid "Field Groups"
msgstr "Alan grupları"

#: acf.php:364
msgid "Field Group"
msgstr "Alan grubu"

#: acf.php:365 acf.php:397 includes/admin/admin.php:59
#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New"
msgstr "Yeni ekle"

#: acf.php:366
msgid "Add New Field Group"
msgstr "Yeni alan grubu ekle"

#: acf.php:367
msgid "Edit Field Group"
msgstr "Alan grubunu düzenle"

#: acf.php:368
msgid "New Field Group"
msgstr "Yeni alan grubu"

#: acf.php:369
msgid "View Field Group"
msgstr "Alan grubunu görüntüle"

#: acf.php:370
msgid "Search Field Groups"
msgstr "Alan gruplarında ara"

#: acf.php:371
msgid "No Field Groups found"
msgstr "Hiç alan grubu bulunamadı"

#: acf.php:372
msgid "No Field Groups found in Trash"
msgstr "Çöpte alan grubu bulunamadı"

#: acf.php:395 includes/admin/admin-field-group.php:220
#: includes/admin/admin-field-groups.php:529
#: pro/fields/class-acf-field-clone.php:811
msgid "Fields"
msgstr "Alanlar"

#: acf.php:396
msgid "Field"
msgstr "Alan"

#: acf.php:398
msgid "Add New Field"
msgstr "Yeni elan ekle"

#: acf.php:399
msgid "Edit Field"
msgstr "Alanı düzenle"

#: acf.php:400 includes/admin/views/field-group-fields.php:41
msgid "New Field"
msgstr "Yeni alan"

#: acf.php:401
msgid "View Field"
msgstr "Alanı görüntüle"

#: acf.php:402
msgid "Search Fields"
msgstr "Alanlarda ara"

#: acf.php:403
msgid "No Fields found"
msgstr "Hiç alan bulunamadı"

#: acf.php:404
msgid "No Fields found in Trash"
msgstr "Çöpte alan bulunamadı"

#: acf.php:443 includes/admin/admin-field-group.php:402
#: includes/admin/admin-field-groups.php:586
msgid "Inactive"
msgstr "Etkin değil"

#: acf.php:448
#, php-format
msgid "Inactive <span class=\"count\">(%s)</span>"
msgid_plural "Inactive <span class=\"count\">(%s)</span>"
msgstr[0] "Etkin olmayan <span class=“count”>(%s)</span>"
msgstr[1] "Etkin olmayan <span class=“count”>(%s)</span>"

#: includes/acf-field-functions.php:823
#: includes/admin/admin-field-group.php:178
msgid "(no label)"
msgstr "(etiket yok)"

#: includes/acf-field-group-functions.php:816
#: includes/admin/admin-field-group.php:180
msgid "copy"
msgstr "kopyala"

#: includes/admin/admin-field-group.php:86
#: includes/admin/admin-field-group.php:87
#: includes/admin/admin-field-group.php:89
msgid "Field group updated."
msgstr "Alan grubu güncellendi."

#: includes/admin/admin-field-group.php:88
msgid "Field group deleted."
msgstr "Alan grubu silindi."

#: includes/admin/admin-field-group.php:91
msgid "Field group published."
msgstr "Alan grubu yayımlandı."

#: includes/admin/admin-field-group.php:92
msgid "Field group saved."
msgstr "Alan grubu kaydedildi."

#: includes/admin/admin-field-group.php:93
msgid "Field group submitted."
msgstr "Alan grubu gönderildi."

#: includes/admin/admin-field-group.php:94
msgid "Field group scheduled for."
msgstr "Alan grubu zamanlandı."

#: includes/admin/admin-field-group.php:95
msgid "Field group draft updated."
msgstr "Alan grubu taslağı güncellendi."

#: includes/admin/admin-field-group.php:171
msgid "The string \"field_\" may not be used at the start of a field name"
msgstr "Artık alan isimlerinin başlangıcında “field_” kullanılmayacak"

#: includes/admin/admin-field-group.php:172
msgid "This field cannot be moved until its changes have been saved"
msgstr "Bu alan, üzerinde yapılan değişiklikler kaydedilene kadar taşınamaz"

#: includes/admin/admin-field-group.php:173
msgid "Field group title is required"
msgstr "Alan grubu başlığı gerekli"

#: includes/admin/admin-field-group.php:174
msgid "Move to trash. Are you sure?"
msgstr "Çöpe taşımak istediğinizden emin misiniz?"

#: includes/admin/admin-field-group.php:175
msgid "No toggle fields available"
msgstr "Kullanılabilir aç-kapa alan yok"

#: includes/admin/admin-field-group.php:176
msgid "Move Custom Field"
msgstr "Özel alanı taşı"

#: includes/admin/admin-field-group.php:177
msgid "Checked"
msgstr "İşaretlendi"

#: includes/admin/admin-field-group.php:179
msgid "(this field)"
msgstr "(bu alan)"

#: includes/admin/admin-field-group.php:181
#: includes/admin/views/field-group-field-conditional-logic.php:51
#: includes/admin/views/field-group-field-conditional-logic.php:151
#: includes/admin/views/field-group-locations.php:29
#: includes/admin/views/html-location-group.php:3
#: includes/api/api-helpers.php:3990
msgid "or"
msgstr "veya"

#: includes/admin/admin-field-group.php:182
msgid "Null"
msgstr "Boş"

#: includes/admin/admin-field-group.php:221
msgid "Location"
msgstr "Konum"

#: includes/admin/admin-field-group.php:222
#: includes/admin/tools/class-acf-admin-tool-export.php:295
msgid "Settings"
msgstr "Ayarlar"

#: includes/admin/admin-field-group.php:372
msgid "Field Keys"
msgstr "Alan anahtarları"

#: includes/admin/admin-field-group.php:402
#: includes/admin/views/field-group-options.php:9
msgid "Active"
msgstr "Etkin"

#: includes/admin/admin-field-group.php:771
msgid "Move Complete."
msgstr "Taşıma tamamlandı."

#: includes/admin/admin-field-group.php:772
#, php-format
msgid "The %s field can now be found in the %s field group"
msgstr "%s alanı artık %s alan grubu altında bulunabilir"

#: includes/admin/admin-field-group.php:773
msgid "Close Window"
msgstr "Pencereyi kapat"

#: includes/admin/admin-field-group.php:814
msgid "Please select the destination for this field"
msgstr "Lütfen bu alan için bir hedef seçin"

#: includes/admin/admin-field-group.php:821
msgid "Move Field"
msgstr "Alanı taşı"

#: includes/admin/admin-field-groups.php:89
#, php-format
msgid "Active <span class=\"count\">(%s)</span>"
msgid_plural "Active <span class=\"count\">(%s)</span>"
msgstr[0] "Etkin <span class=“count”>(%s)</span>"
msgstr[1] "Etkin <span class=“count”>(%s)</span>"

#: includes/admin/admin-field-groups.php:156
#, php-format
msgid "Field group duplicated."
msgid_plural "%s field groups duplicated."
msgstr[0] "Alan grubu çoğaltıldı."
msgstr[1] "%s alan grubu çoğaltıldı."

#: includes/admin/admin-field-groups.php:243
#, php-format
msgid "Field group synchronised."
msgid_plural "%s field groups synchronised."
msgstr[0] "Alan grubu eşitlendi."
msgstr[1] "%s alan grubu eşitlendi."

#: includes/admin/admin-field-groups.php:413
#: includes/admin/admin-field-groups.php:576
msgid "Sync available"
msgstr "Eşitleme mevcut"

#: includes/admin/admin-field-groups.php:526 includes/forms/form-front.php:38
#: pro/fields/class-acf-field-gallery.php:372
msgid "Title"
msgstr "Başlık"

#: includes/admin/admin-field-groups.php:527
#: includes/admin/views/field-group-options.php:96
#: includes/admin/views/html-admin-page-upgrade-network.php:38
#: includes/admin/views/html-admin-page-upgrade-network.php:49
#: pro/fields/class-acf-field-gallery.php:399
msgid "Description"
msgstr "Açıklama"

#: includes/admin/admin-field-groups.php:528
msgid "Status"
msgstr "Durum"

#. Description of the plugin/theme
#: includes/admin/admin-field-groups.php:626
msgid "Customize WordPress with powerful, professional and intuitive fields."
msgstr "Güçlü, profesyonel ve sezgisel alanlar ile WordPress'i özelleştirin."

#: includes/admin/admin-field-groups.php:628
#: includes/admin/settings-info.php:76
#: pro/admin/views/html-settings-updates.php:107
msgid "Changelog"
msgstr "Değişiklik kayıtları"

#: includes/admin/admin-field-groups.php:633
#, php-format
msgid "See what's new in <a href=\"%s\">version %s</a>."
msgstr "<a href=\"%s\">%s sürümünde</a> neler yeni bir göz atın."

#: includes/admin/admin-field-groups.php:636
msgid "Resources"
msgstr "Kaynaklar"

#: includes/admin/admin-field-groups.php:638
msgid "Website"
msgstr "Websitesi"

#: includes/admin/admin-field-groups.php:639
msgid "Documentation"
msgstr "Belgeler"

#: includes/admin/admin-field-groups.php:640
msgid "Support"
msgstr "Destek"

#: includes/admin/admin-field-groups.php:642
#: includes/admin/views/settings-info.php:84
msgid "Pro"
msgstr "Pro"

#: includes/admin/admin-field-groups.php:647
#, php-format
msgid "Thank you for creating with <a href=\"%s\">ACF</a>."
msgstr "<a href=\"%s\">ACF</a> ile oluşturduğunuz için teşekkürler."

#: includes/admin/admin-field-groups.php:686
msgid "Duplicate this item"
msgstr "Bu ögeyi çoğalt"

#: includes/admin/admin-field-groups.php:686
#: includes/admin/admin-field-groups.php:702
#: includes/admin/views/field-group-field.php:46
#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate"
msgstr "Çoğalt"

#: includes/admin/admin-field-groups.php:719
#: includes/fields/class-acf-field-google-map.php:165
#: includes/fields/class-acf-field-relationship.php:593
msgid "Search"
msgstr "Ara"

#: includes/admin/admin-field-groups.php:778
#, php-format
msgid "Select %s"
msgstr "Seç %s"

#: includes/admin/admin-field-groups.php:786
msgid "Synchronise field group"
msgstr "Alan grubunu eşitle"

#: includes/admin/admin-field-groups.php:786
#: includes/admin/admin-field-groups.php:816
msgid "Sync"
msgstr "Eşitle"

#: includes/admin/admin-field-groups.php:798
msgid "Apply"
msgstr "Uygula"

#: includes/admin/admin-field-groups.php:816
msgid "Bulk Actions"
msgstr "Toplu eylemler"

#: includes/admin/admin-tools.php:116
#: includes/admin/views/html-admin-tools.php:21
msgid "Tools"
msgstr "Araçlar"

#: includes/admin/admin-upgrade.php:47 includes/admin/admin-upgrade.php:94
#: includes/admin/admin-upgrade.php:156
#: includes/admin/views/html-admin-page-upgrade-network.php:24
#: includes/admin/views/html-admin-page-upgrade.php:26
msgid "Upgrade Database"
msgstr "Veritabanını güncelle"

#: includes/admin/admin-upgrade.php:180
msgid "Review sites & upgrade"
msgstr "Siteleri incele ve güncelle"

#: includes/admin/admin.php:54 includes/admin/views/field-group-options.php:110
msgid "Custom Fields"
msgstr "Özel alanlar"

#: includes/admin/settings-info.php:50
msgid "Info"
msgstr "Bilgi"

#: includes/admin/settings-info.php:75
msgid "What's New"
msgstr "Neler yeni"

#: includes/admin/tools/class-acf-admin-tool-export.php:33
msgid "Export Field Groups"
msgstr "Alan gruplarını dışarı aktar"

#: includes/admin/tools/class-acf-admin-tool-export.php:38
#: includes/admin/tools/class-acf-admin-tool-export.php:342
#: includes/admin/tools/class-acf-admin-tool-export.php:371
msgid "Generate PHP"
msgstr "PHP oluştur"

#: includes/admin/tools/class-acf-admin-tool-export.php:97
#: includes/admin/tools/class-acf-admin-tool-export.php:135
msgid "No field groups selected"
msgstr "Hiç alan grubu seçilmemiş"

#: includes/admin/tools/class-acf-admin-tool-export.php:174
#, php-format
msgid "Exported 1 field group."
msgid_plural "Exported %s field groups."
msgstr[0] "1 alan grubu içeri aktarıldı."
msgstr[1] "%s alan grubu içeri aktarıldı."

#: includes/admin/tools/class-acf-admin-tool-export.php:241
#: includes/admin/tools/class-acf-admin-tool-export.php:269
msgid "Select Field Groups"
msgstr "Alan gruplarını seç"

#: includes/admin/tools/class-acf-admin-tool-export.php:336
msgid ""
"Select the field groups you would like to export and then select your export "
"method. Use the download button to export to a .json file which you can then "
"import to another ACF installation. Use the generate button to export to PHP "
"code which you can place in your theme."
msgstr ""
"Dışa aktarma ve sonra dışa aktarma yöntemini seçtikten sonra alan gruplarını "
"seçin. Sonra başka bir ACF yükleme içe bir .json dosyaya vermek için indirme "
"düğmesini kullanın. Tema yerleştirebilirsiniz PHP kodu aktarma düğmesini "
"kullanın."

#: includes/admin/tools/class-acf-admin-tool-export.php:341
msgid "Export File"
msgstr "Dışarı aktarım dosyası"

#: includes/admin/tools/class-acf-admin-tool-export.php:414
msgid ""
"The following code can be used to register a local version of the selected "
"field group(s). A local field group can provide many benefits such as faster "
"load times, version control & dynamic fields/settings. Simply copy and paste "
"the following code to your theme's functions.php file or include it within "
"an external file."
msgstr ""
"Aşağıdaki kod seçilmiş alan grubu/grupları için yerel bir sürüm kaydetmek "
"için kullanılır. Yerel alan grubu daha hızlı yüklenme süreleri, sürüm "
"yönetimi ve dinamik alanlar/ayarlar gibi faydalar sağlar. Yapmanız gereken "
"bu kodu kopyalayıp temanızın functions.php dosyasına eklemek ya da harici "
"bir dosya olarak temanıza dahil etmek."

#: includes/admin/tools/class-acf-admin-tool-export.php:446
msgid "Copy to clipboard"
msgstr "Panoya kopyala"

#: includes/admin/tools/class-acf-admin-tool-export.php:483
msgid "Copied"
msgstr "Kopyalandı"

#: includes/admin/tools/class-acf-admin-tool-import.php:26
msgid "Import Field Groups"
msgstr "Alan gruplarını içeri aktar"

#: includes/admin/tools/class-acf-admin-tool-import.php:47
msgid ""
"Select the Advanced Custom Fields JSON file you would like to import. When "
"you click the import button below, ACF will import the field groups."
msgstr ""
"İçeri aktarmak istediğiniz Advanced Custom Fields JSON dosyasını seçin. "
"Aşağıdaki içeri aktar tuşuna bastığınızda ACF alan gruplarını içeri "
"aktaracak."

#: includes/admin/tools/class-acf-admin-tool-import.php:52
#: includes/fields/class-acf-field-file.php:57
msgid "Select File"
msgstr "Dosya seç"

#: includes/admin/tools/class-acf-admin-tool-import.php:62
msgid "Import File"
msgstr "Dosyayı içeri aktar"

#: includes/admin/tools/class-acf-admin-tool-import.php:85
#: includes/fields/class-acf-field-file.php:170
msgid "No file selected"
msgstr "Dosya seçilmedi"

#: includes/admin/tools/class-acf-admin-tool-import.php:93
msgid "Error uploading file. Please try again"
msgstr "Dosya yüklenirken hata oluştu. Lütfen tekrar deneyin"

#: includes/admin/tools/class-acf-admin-tool-import.php:98
msgid "Incorrect file type"
msgstr "Geçersiz dosya tipi"

#: includes/admin/tools/class-acf-admin-tool-import.php:107
msgid "Import file empty"
msgstr "İçe aktarılan dosya boş"

#: includes/admin/tools/class-acf-admin-tool-import.php:138
#, php-format
msgid "Imported 1 field group"
msgid_plural "Imported %s field groups"
msgstr[0] "1 alan grubu içeri aktarıldı"
msgstr[1] "%s alan grubu içeri aktarıldı"

#: includes/admin/views/field-group-field-conditional-logic.php:25
msgid "Conditional Logic"
msgstr "Koşullu mantık"

#: includes/admin/views/field-group-field-conditional-logic.php:51
msgid "Show this field if"
msgstr "Alanı bu şart gerçekleşirse göster"

#: includes/admin/views/field-group-field-conditional-logic.php:138
#: includes/admin/views/html-location-rule.php:86
msgid "and"
msgstr "ve"

#: includes/admin/views/field-group-field-conditional-logic.php:153
#: includes/admin/views/field-group-locations.php:31
msgid "Add rule group"
msgstr "Kural grubu ekle"

#: includes/admin/views/field-group-field.php:38
#: pro/fields/class-acf-field-flexible-content.php:424
#: pro/fields/class-acf-field-repeater.php:294
msgid "Drag to reorder"
msgstr "Yeniden düzenlemek için sürükleyin"

#: includes/admin/views/field-group-field.php:42
#: includes/admin/views/field-group-field.php:45
msgid "Edit field"
msgstr "Alanı düzenle"

#: includes/admin/views/field-group-field.php:45
#: includes/fields/class-acf-field-file.php:152
#: includes/fields/class-acf-field-image.php:139
#: includes/fields/class-acf-field-link.php:139
#: pro/fields/class-acf-field-gallery.php:359
msgid "Edit"
msgstr "Düzenle"

#: includes/admin/views/field-group-field.php:46
msgid "Duplicate field"
msgstr "Alanı çoğalt"

#: includes/admin/views/field-group-field.php:47
msgid "Move field to another group"
msgstr "Alanı başka gruba taşı"

#: includes/admin/views/field-group-field.php:47
msgid "Move"
msgstr "Taşı"

#: includes/admin/views/field-group-field.php:48
msgid "Delete field"
msgstr "Alanı sil"

#: includes/admin/views/field-group-field.php:48
#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete"
msgstr "Sil"

#: includes/admin/views/field-group-field.php:65
msgid "Field Label"
msgstr "Alan etiketi"

#: includes/admin/views/field-group-field.php:66
msgid "This is the name which will appear on the EDIT page"
msgstr "Bu isim DÜZENLEME sayfasında görüntülenecek isimdir"

#: includes/admin/views/field-group-field.php:75
msgid "Field Name"
msgstr "Alan adı"

#: includes/admin/views/field-group-field.php:76
msgid "Single word, no spaces. Underscores and dashes allowed"
msgstr "Tek kelime, boşluksuz. Alt çizgi ve tireye izin var"

#: includes/admin/views/field-group-field.php:85
msgid "Field Type"
msgstr "Alan tipi"

#: includes/admin/views/field-group-field.php:96
msgid "Instructions"
msgstr "Yönergeler"

#: includes/admin/views/field-group-field.php:97
msgid "Instructions for authors. Shown when submitting data"
msgstr "Yazarlara gösterilecek talimatlar. Veri gönderirken gösterilir"

#: includes/admin/views/field-group-field.php:106
msgid "Required?"
msgstr "Gerekli mi?"

#: includes/admin/views/field-group-field.php:129
msgid "Wrapper Attributes"
msgstr "Kapsayıcı öznitelikleri"

#: includes/admin/views/field-group-field.php:135
msgid "width"
msgstr "genişlik"

#: includes/admin/views/field-group-field.php:150
msgid "class"
msgstr "sınıf"

#: includes/admin/views/field-group-field.php:163
msgid "id"
msgstr "id"

#: includes/admin/views/field-group-field.php:175
msgid "Close Field"
msgstr "Alanı kapat"

#: includes/admin/views/field-group-fields.php:4
msgid "Order"
msgstr "Sırala"

#: includes/admin/views/field-group-fields.php:5
#: includes/fields/class-acf-field-button-group.php:198
#: includes/fields/class-acf-field-checkbox.php:420
#: includes/fields/class-acf-field-radio.php:311
#: includes/fields/class-acf-field-select.php:433
#: pro/fields/class-acf-field-flexible-content.php:596
msgid "Label"
msgstr "Etiket"

#: includes/admin/views/field-group-fields.php:6
#: includes/fields/class-acf-field-taxonomy.php:939
#: pro/fields/class-acf-field-flexible-content.php:610
msgid "Name"
msgstr "İsim"

#: includes/admin/views/field-group-fields.php:7
msgid "Key"
msgstr "Anahtar"

#: includes/admin/views/field-group-fields.php:8
msgid "Type"
msgstr "Tip"

#: includes/admin/views/field-group-fields.php:14
msgid ""
"No fields. Click the <strong>+ Add Field</strong> button to create your "
"first field."
msgstr ""
"Hiç alan yok. İlk alanınızı oluşturmak için <strong>+ Alan ekle</strong> "
"düğmesine tıklayın."

#: includes/admin/views/field-group-fields.php:31
msgid "+ Add Field"
msgstr "+ Alan ekle"

#: includes/admin/views/field-group-locations.php:9
msgid "Rules"
msgstr "Kurallar"

#: includes/admin/views/field-group-locations.php:10
msgid ""
"Create a set of rules to determine which edit screens will use these "
"advanced custom fields"
msgstr ""
"Bu gelişmiş özel alanları hangi düzenleme ekranlarının kullanacağını "
"belirlemek için bir kural seti oluşturun"

#: includes/admin/views/field-group-options.php:23
msgid "Style"
msgstr "Stil"

#: includes/admin/views/field-group-options.php:30
msgid "Standard (WP metabox)"
msgstr "Standart (WP metabox)"

#: includes/admin/views/field-group-options.php:31
msgid "Seamless (no metabox)"
msgstr "Pürüzsüz (metabox yok)"

#: includes/admin/views/field-group-options.php:38
msgid "Position"
msgstr "Pozisyon"

#: includes/admin/views/field-group-options.php:45
msgid "High (after title)"
msgstr "Yüksek (başlıktan sonra)"

#: includes/admin/views/field-group-options.php:46
msgid "Normal (after content)"
msgstr "Normal (içerikten sonra)"

#: includes/admin/views/field-group-options.php:47
msgid "Side"
msgstr "Yan"

#: includes/admin/views/field-group-options.php:55
msgid "Label placement"
msgstr "Etiket yerleştirme"

#: includes/admin/views/field-group-options.php:62
#: includes/fields/class-acf-field-tab.php:106
msgid "Top aligned"
msgstr "Üste hizalı"

#: includes/admin/views/field-group-options.php:63
#: includes/fields/class-acf-field-tab.php:107
msgid "Left aligned"
msgstr "Sola hizalı"

#: includes/admin/views/field-group-options.php:70
msgid "Instruction placement"
msgstr "Yönerge yerleştirme"

#: includes/admin/views/field-group-options.php:77
msgid "Below labels"
msgstr "Etiketlerin altında"

#: includes/admin/views/field-group-options.php:78
msgid "Below fields"
msgstr "Alanlarının altında"

#: includes/admin/views/field-group-options.php:85
msgid "Order No."
msgstr "Sıra no."

#: includes/admin/views/field-group-options.php:86
msgid "Field groups with a lower order will appear first"
msgstr "Daha düşük sıralamaya sahip alan grupları daha önce görünür"

#: includes/admin/views/field-group-options.php:97
msgid "Shown in field group list"
msgstr "Alan grubu listesinde görüntülenir"

#: includes/admin/views/field-group-options.php:107
msgid "Permalink"
msgstr "Kalıcı bağlantı"

#: includes/admin/views/field-group-options.php:108
msgid "Content Editor"
msgstr "İçerik düzenleyici"

#: includes/admin/views/field-group-options.php:109
msgid "Excerpt"
msgstr "Özet"

#: includes/admin/views/field-group-options.php:111
msgid "Discussion"
msgstr "Tartışma"

#: includes/admin/views/field-group-options.php:112
msgid "Comments"
msgstr "Yorumlar"

#: includes/admin/views/field-group-options.php:113
msgid "Revisions"
msgstr "Sürümler"

#: includes/admin/views/field-group-options.php:114
msgid "Slug"
msgstr "Kısa isim"

#: includes/admin/views/field-group-options.php:115
msgid "Author"
msgstr "Yazar"

#: includes/admin/views/field-group-options.php:116
msgid "Format"
msgstr "Biçim"

#: includes/admin/views/field-group-options.php:117
msgid "Page Attributes"
msgstr "Sayfa öznitelikleri"

#: includes/admin/views/field-group-options.php:118
#: includes/fields/class-acf-field-relationship.php:607
msgid "Featured Image"
msgstr "Öne çıkarılmış görsel"

#: includes/admin/views/field-group-options.php:119
msgid "Categories"
msgstr "Kategoriler"

#: includes/admin/views/field-group-options.php:120
msgid "Tags"
msgstr "Etiketler"

#: includes/admin/views/field-group-options.php:121
msgid "Send Trackbacks"
msgstr "Geri izlemeleri gönder"

#: includes/admin/views/field-group-options.php:128
msgid "Hide on screen"
msgstr "Ekranda gizle"

#: includes/admin/views/field-group-options.php:129
msgid "<b>Select</b> items to <b>hide</b> them from the edit screen."
msgstr "Düzenleme ekranından <b>gizlemek</b> istediğiniz ögeleri <b>seçin</b>."

#: includes/admin/views/field-group-options.php:129
msgid ""
"If multiple field groups appear on an edit screen, the first field group's "
"options will be used (the one with the lowest order number)"
msgstr ""
"Eğer düzenleme ekranında birden çok alan grubu ortaya çıkarsa, ilk alan "
"grubunun seçenekleri kullanılır (en düşük sıralama numarasına sahip olan)"

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#, php-format
msgid ""
"The following sites require a DB upgrade. Check the ones you want to update "
"and then click %s."
msgstr ""
"Şu siteler için VT güncellemesi gerekiyor. Güncellemek istediklerinizi "
"işaretleyin ve %s tuşuna basın."

#: includes/admin/views/html-admin-page-upgrade-network.php:26
#: includes/admin/views/html-admin-page-upgrade-network.php:27
#: includes/admin/views/html-admin-page-upgrade-network.php:92
msgid "Upgrade Sites"
msgstr "Siteleri yükselt"

#: includes/admin/views/html-admin-page-upgrade-network.php:36
#: includes/admin/views/html-admin-page-upgrade-network.php:47
msgid "Site"
msgstr "Site"

#: includes/admin/views/html-admin-page-upgrade-network.php:74
#, php-format
msgid "Site requires database upgrade from %s to %s"
msgstr "Site için %s sürümünden %s sürümüne veritabanı güncellemesi gerekiyor"

#: includes/admin/views/html-admin-page-upgrade-network.php:76
msgid "Site is up to date"
msgstr "Site güncel"

#: includes/admin/views/html-admin-page-upgrade-network.php:93
#, php-format
msgid ""
"Database Upgrade complete. <a href=\"%s\">Return to network dashboard</a>"
msgstr ""
"Veritabanı güncellemesi tamamlandı. <a href=\"%s\">Ağ panosuna geri dön</a>"

#: includes/admin/views/html-admin-page-upgrade-network.php:113
msgid "Please select at least one site to upgrade."
msgstr "Lütfen yükseltmek için en az site seçin."

#: includes/admin/views/html-admin-page-upgrade-network.php:117
#: includes/admin/views/html-notice-upgrade.php:38
msgid ""
"It is strongly recommended that you backup your database before proceeding. "
"Are you sure you wish to run the updater now?"
msgstr ""
"Devam etmeden önce veritabanınızı yedeklemeniz önemle önerilir. "
"Güncelleştiriciyi şimdi çalıştırmak istediğinizden emin misiniz?"

#: includes/admin/views/html-admin-page-upgrade-network.php:144
#: includes/admin/views/html-admin-page-upgrade.php:31
#, php-format
msgid "Upgrading data to version %s"
msgstr "Veri %s sürümüne yükseltiliyor"

#: includes/admin/views/html-admin-page-upgrade-network.php:167
msgid "Upgrade complete."
msgstr "Yükseltme başarılı."

#: includes/admin/views/html-admin-page-upgrade-network.php:176
#: includes/admin/views/html-admin-page-upgrade-network.php:185
#: includes/admin/views/html-admin-page-upgrade.php:78
#: includes/admin/views/html-admin-page-upgrade.php:87
msgid "Upgrade failed."
msgstr "Yükseltme başarısız oldu."

#: includes/admin/views/html-admin-page-upgrade.php:30
msgid "Reading upgrade tasks..."
msgstr "Yükseltme görevlerini okuyor..."

#: includes/admin/views/html-admin-page-upgrade.php:33
#, php-format
msgid "Database upgrade complete. <a href=\"%s\">See what's new</a>"
msgstr ""
"Veritabanı güncellemesi tamamlandı. <a href=\"%s\">Neler yeni bir göz atın</"
"a>"

#: includes/admin/views/html-admin-page-upgrade.php:116
#: includes/ajax/class-acf-ajax-upgrade.php:33
msgid "No updates available."
msgstr "Güncelleme yok."

#: includes/admin/views/html-admin-tools.php:21
msgid "Back to all tools"
msgstr "Tüm araçlara geri dön"

#: includes/admin/views/html-location-group.php:3
msgid "Show this field group if"
msgstr "Bu alan grubunu şu koşulda göster"

#: includes/admin/views/html-notice-upgrade.php:8
#: pro/fields/class-acf-field-repeater.php:25
msgid "Repeater"
msgstr "Tekrarlayıcı"

#: includes/admin/views/html-notice-upgrade.php:9
#: pro/fields/class-acf-field-flexible-content.php:25
msgid "Flexible Content"
msgstr "Esnek içerik"

#: includes/admin/views/html-notice-upgrade.php:10
#: pro/fields/class-acf-field-gallery.php:25
msgid "Gallery"
msgstr "Galeri"

#: includes/admin/views/html-notice-upgrade.php:11
#: pro/locations/class-acf-location-options-page.php:26
msgid "Options Page"
msgstr "Seçenekler sayfası"

#: includes/admin/views/html-notice-upgrade.php:21
msgid "Database Upgrade Required"
msgstr "Veritabanı yükseltmesi gerekiyor"

#: includes/admin/views/html-notice-upgrade.php:22
#, php-format
msgid "Thank you for updating to %s v%s!"
msgstr "%s v%s sürümüne güncellediğiniz için teşekkür ederiz!"

#: includes/admin/views/html-notice-upgrade.php:22
msgid ""
"This version contains improvements to your database and requires an upgrade."
msgstr ""
"Bu sürüm veritabanınız için iyileştirmeler içeriyor ve yükseltme "
"gerektiriyor."

#: includes/admin/views/html-notice-upgrade.php:24
#, php-format
msgid ""
"Please also check all premium add-ons (%s) are updated to the latest version."
msgstr ""
"Lütfen ayrıca premium eklentilerin de (%s) en üst sürüme güncellendiğinden "
"emin olun."

#: includes/admin/views/settings-addons.php:3
msgid "Add-ons"
msgstr "Eklentiler"

#: includes/admin/views/settings-addons.php:17
msgid "Download & Install"
msgstr "İndir ve yükle"

#: includes/admin/views/settings-addons.php:36
msgid "Installed"
msgstr "Yüklendi"

#: includes/admin/views/settings-info.php:3
msgid "Welcome to Advanced Custom Fields"
msgstr "Advanced Custom Fields eklentisine hoş geldiniz"

#: includes/admin/views/settings-info.php:4
#, php-format
msgid ""
"Thank you for updating! ACF %s is bigger and better than ever before. We "
"hope you like it."
msgstr ""
"Güncelleme için teşekkür ederiz! ACF %s zamankinden daha büyük ve daha iyi. "
"Umarız beğenirsiniz."

#: includes/admin/views/settings-info.php:15
msgid "A Smoother Experience"
msgstr "Daha pürüzsüz bir deneyim"

#: includes/admin/views/settings-info.php:19
msgid "Improved Usability"
msgstr "Geliştirilmiş kullanılabilirlik"

#: includes/admin/views/settings-info.php:20
msgid ""
"Including the popular Select2 library has improved both usability and speed "
"across a number of field types including post object, page link, taxonomy "
"and select."
msgstr ""
"Popüler Select2 kütüphanesini ekleyerek yazı nesnesi, sayfa bağlantısı, "
"taksonomi ve seçim kutusu gibi bir çok alan tipinde hem kullanışlılık hem de "
"hız iyileştirmeleri gerçekleşti."

#: includes/admin/views/settings-info.php:24
msgid "Improved Design"
msgstr "Geliştirilmiş tasarım"

#: includes/admin/views/settings-info.php:25
msgid ""
"Many fields have undergone a visual refresh to make ACF look better than "
"ever! Noticeable changes are seen on the gallery, relationship and oEmbed "
"(new) fields!"
msgstr ""
"ACF daha iyi görünsün diye bir çok alan görsel yenilemeden geçirildi! Gözle "
"görülür değişiklikler galeri, ilişki ve oEmbed (yeni) alanlarında!"

#: includes/admin/views/settings-info.php:29
msgid "Improved Data"
msgstr "Geliştirilmiş veri"

#: includes/admin/views/settings-info.php:30
msgid ""
"Redesigning the data architecture has allowed sub fields to live "
"independently from their parents. This allows you to drag and drop fields in "
"and out of parent fields!"
msgstr ""
"Veri mimarisinin yeniden düzenlenmesi sayesinde alt alanlar üst alanlara "
"bağlı olmadan var olabiliyorlar. Bu da üst alanların dışına sürükle bırak "
"yapılabilmesine olanak sağlıyor!"

#: includes/admin/views/settings-info.php:38
msgid "Goodbye Add-ons. Hello PRO"
msgstr "Elveda eklentiler. Merhaba PRO"

#: includes/admin/views/settings-info.php:41
msgid "Introducing ACF PRO"
msgstr "Karşınızda ACF PRO"

#: includes/admin/views/settings-info.php:42
msgid ""
"We're changing the way premium functionality is delivered in an exciting way!"
msgstr ""
"Premium işlevlerin size ulaştırılmasını daha heyecanlı bir hale getiriyoruz!"

#: includes/admin/views/settings-info.php:43
#, php-format
msgid ""
"All 4 premium add-ons have been combined into a new <a href=\"%s\">Pro "
"version of ACF</a>. With both personal and developer licenses available, "
"premium functionality is more affordable and accessible than ever before!"
msgstr ""
"Yeni <a href=\"%s\">ACF Pro sürümününe</a> 4 premium eklenti dahil edildi. "
"Hem kişisel hem geliştirici lisansında, özel beceriler hiç olmadığı kadar "
"edinilebilir ve erişilebilir!"

#: includes/admin/views/settings-info.php:47
msgid "Powerful Features"
msgstr "Güçlü özellikler"

#: includes/admin/views/settings-info.php:48
msgid ""
"ACF PRO contains powerful features such as repeatable data, flexible content "
"layouts, a beautiful gallery field and the ability to create extra admin "
"options pages!"
msgstr ""
"ACF PRO, tekrarlanabilir veri, esnek içerik yerleşimleri, harika bir galeri "
"alanı ve ekstra yönetim seçenekleri sayfaları oluşturma gibi güçlü "
"özellikler içerir!"

#: includes/admin/views/settings-info.php:49
#, php-format
msgid "Read more about <a href=\"%s\">ACF PRO features</a>."
msgstr "<a href=\"%s\">ACF PRO özellikleri</a> hakkında daha fazlasını okuyun."

#: includes/admin/views/settings-info.php:53
msgid "Easy Upgrading"
msgstr "Kolay yükseltme"

#: includes/admin/views/settings-info.php:54
msgid ""
"Upgrading to ACF PRO is easy. Simply purchase a license online and download "
"the plugin!"
msgstr ""
"ACF PRO’ya yükseltmek çok kolay. Çevrimiçi bir lisans satın alın ve "
"eklentiyi indirin!"

#: includes/admin/views/settings-info.php:55
#, php-format
msgid ""
"We also wrote an <a href=\"%s\">upgrade guide</a> to answer any questions, "
"but if you do have one, please contact our support team via the <a href=\"%s"
"\">help desk</a>."
msgstr ""
"Her türlü soruya cevap verebilecek <a href=\"%s\">bir yükseltme rehberi</a> "
"hazırladık, fakat yine de bir sorunuz varsa lütfen <a href=\"%s\">yardım "
"masası</a>nı kullanarak destek ekibimize danışın."

#: includes/admin/views/settings-info.php:64
msgid "New Features"
msgstr "Yeni özellikler"

#: includes/admin/views/settings-info.php:69
msgid "Link Field"
msgstr "Bağlantı alanı"

#: includes/admin/views/settings-info.php:70
msgid ""
"The Link field provides a simple way to select or define a link (url, title, "
"target)."
msgstr ""
"Bağlantı alanı bir bağlantı (adres, başlık, hedef) seçmek ya da tanımlamak "
"için basit bir yol sunar."

#: includes/admin/views/settings-info.php:74
msgid "Group Field"
msgstr "Grup alanı"

#: includes/admin/views/settings-info.php:75
msgid "The Group field provides a simple way to create a group of fields."
msgstr "Grup alanı birden çok alanı basitçe gruplamanıza olanak sağlar."

#: includes/admin/views/settings-info.php:79
msgid "oEmbed Field"
msgstr "oEmbed alanı"

#: includes/admin/views/settings-info.php:80
msgid ""
"The oEmbed field allows an easy way to embed videos, images, tweets, audio, "
"and other content."
msgstr ""
"oEmbed alanı videolar, görseller, tweetler, ses ve diğer içeriği kolayca "
"gömebilmenizi sağlar."

#: includes/admin/views/settings-info.php:84
msgid "Clone Field"
msgstr "Kopya alanı"

#: includes/admin/views/settings-info.php:85
msgid "The clone field allows you to select and display existing fields."
msgstr "Kopya alanı var olan alanları seçme ve görüntülemenize olanak sağlar."

#: includes/admin/views/settings-info.php:89
msgid "More AJAX"
msgstr "Daha fazla AJAX"

#: includes/admin/views/settings-info.php:90
msgid "More fields use AJAX powered search to speed up page loading."
msgstr ""
"Sayfa yüklenmesini hızlandırmak adına daha çok alan AJAX ile güçlendirilmiş "
"arama kullanıyor."

#: includes/admin/views/settings-info.php:94
msgid "Local JSON"
msgstr "Yerel JSON"

#: includes/admin/views/settings-info.php:95
msgid ""
"New auto export to JSON feature improves speed and allows for syncronisation."
msgstr ""
"Yeni otomatik JSON dışarı aktarma özelliği ile hız artıyor ve "
"senkronizasyona imkan sağlanıyor."

#: includes/admin/views/settings-info.php:99
msgid "Easy Import / Export"
msgstr "Kolayca içe / dışa aktarma"

#: includes/admin/views/settings-info.php:100
msgid "Both import and export can easily be done through a new tools page."
msgstr ""
"İçeri ve dışarı aktarma işlemleri yeni araçlar sayfasından kolayca "
"yapılabilir."

#: includes/admin/views/settings-info.php:104
msgid "New Form Locations"
msgstr "Yeni form konumları"

#: includes/admin/views/settings-info.php:105
msgid ""
"Fields can now be mapped to menus, menu items, comments, widgets and all "
"user forms!"
msgstr ""
"Alanlar artık menülere, menü elemanlarına, yorumlara, bileşenlere ve tüm "
"kullanıcı formlarına eşlenebiliyor!"

#: includes/admin/views/settings-info.php:109
msgid "More Customization"
msgstr "Daha fazla özelleştirme"

#: includes/admin/views/settings-info.php:110
msgid ""
"New PHP (and JS) actions and filters have been added to allow for more "
"customization."
msgstr ""
"Daha fazla özelleştirmeye izin veren yeni PHP (ve JS) eylem ve filtreleri "
"eklendi."

#: includes/admin/views/settings-info.php:114
msgid "Fresh UI"
msgstr "Taze arayüz"

#: includes/admin/views/settings-info.php:115
msgid ""
"The entire plugin has had a design refresh including new field types, "
"settings and design!"
msgstr ""
"Eklentinin tasarımı yeni alan tipleri, ayarlar ve tasarımı da içerecek "
"şekilde yenilendi!"

#: includes/admin/views/settings-info.php:119
msgid "New Settings"
msgstr "Yeni ayarlar"

#: includes/admin/views/settings-info.php:120
msgid ""
"Field group settings have been added for Active, Label Placement, "
"Instructions Placement and Description."
msgstr ""
"Etkin, etiket yerleşimi, talimatlar yerleşimi ve açıklama için alan grubu "
"ayarları eklendi."

#: includes/admin/views/settings-info.php:124
msgid "Better Front End Forms"
msgstr "Daha iyi ön yüz formları"

#: includes/admin/views/settings-info.php:125
msgid ""
"acf_form() can now create a new post on submission with lots of new settings."
msgstr ""
"acf_form() artık gönderim halinde bir sürü yeni ayar ile yeni bir yazı "
"oluşturabilir."

#: includes/admin/views/settings-info.php:129
msgid "Better Validation"
msgstr "Daha iyi doğrulama"

#: includes/admin/views/settings-info.php:130
msgid "Form validation is now done via PHP + AJAX in favour of only JS."
msgstr "Form doğrulama artık sadece JS yerine PHP + AJAX ile yapılıyor."

#: includes/admin/views/settings-info.php:134
msgid "Moving Fields"
msgstr "Taşınabilir alanlar"

#: includes/admin/views/settings-info.php:135
msgid ""
"New field group functionality allows you to move a field between groups & "
"parents."
msgstr ""
"Yeni gruplama becerisi, bir alanı gruplar ve üst alanlar arasında "
"taşıyabilmenize olanak sağlar."

#: includes/admin/views/settings-info.php:146
#, php-format
msgid "We think you'll love the changes in %s."
msgstr "%s sürümündeki değişiklikleri seveceğinizi düşünüyoruz."

#: includes/api/api-helpers.php:1011
msgid "Thumbnail"
msgstr "Küçük görsel"

#: includes/api/api-helpers.php:1012
msgid "Medium"
msgstr "Orta"

#: includes/api/api-helpers.php:1013
msgid "Large"
msgstr "Büyük"

#: includes/api/api-helpers.php:1062
msgid "Full Size"
msgstr "Tam boyut"

#: includes/api/api-helpers.php:1831 includes/api/api-term.php:147
#: pro/fields/class-acf-field-clone.php:996
msgid "(no title)"
msgstr "(başlıksız)"

#: includes/api/api-helpers.php:3911
#, php-format
msgid "Image width must be at least %dpx."
msgstr "Görsel genişliği en az %dpx olmalı."

#: includes/api/api-helpers.php:3916
#, php-format
msgid "Image width must not exceed %dpx."
msgstr "Görsel genişliği %dpx değerini geçmemeli."

#: includes/api/api-helpers.php:3932
#, php-format
msgid "Image height must be at least %dpx."
msgstr "Görsel yüksekliği en az %dpx olmalı."

#: includes/api/api-helpers.php:3937
#, php-format
msgid "Image height must not exceed %dpx."
msgstr "Görsel yüksekliği %dpx değerini geçmemeli."

#: includes/api/api-helpers.php:3955
#, php-format
msgid "File size must be at least %s."
msgstr "Dosya boyutu en az %s olmalı."

#: includes/api/api-helpers.php:3960
#, php-format
msgid "File size must must not exceed %s."
msgstr "Dosya boyutu %s boyutunu geçmemeli."

#: includes/api/api-helpers.php:3994
#, php-format
msgid "File type must be %s."
msgstr "Dosya tipi %s olmalı."

#: includes/assets.php:168
msgid "The changes you made will be lost if you navigate away from this page"
msgstr ""
"Bu sayfadan başka bir sayfaya geçerseniz yaptığınız değişiklikler kaybolacak"

#: includes/assets.php:171 includes/fields/class-acf-field-select.php:259
msgctxt "verb"
msgid "Select"
msgstr "Seç"

#: includes/assets.php:172
msgctxt "verb"
msgid "Edit"
msgstr "Düzenle"

#: includes/assets.php:173
msgctxt "verb"
msgid "Update"
msgstr "Güncelle"

#: includes/assets.php:174
msgid "Uploaded to this post"
msgstr "Bu yazıya yüklenmiş"

#: includes/assets.php:175
msgid "Expand Details"
msgstr "Ayrıntıları genişlet"

#: includes/assets.php:176
msgid "Collapse Details"
msgstr "Detayları daralt"

#: includes/assets.php:177
msgid "Restricted"
msgstr "Kısıtlı"

#: includes/assets.php:178 includes/fields/class-acf-field-image.php:67
msgid "All images"
msgstr "Tüm görseller"

#: includes/assets.php:181
msgid "Validation successful"
msgstr "Doğrulama başarılı"

#: includes/assets.php:182 includes/validation.php:285
#: includes/validation.php:296
msgid "Validation failed"
msgstr "Doğrulama başarısız"

#: includes/assets.php:183
msgid "1 field requires attention"
msgstr "1 alan dikkatinizi gerektiriyor"

#: includes/assets.php:184
#, php-format
msgid "%d fields require attention"
msgstr "%d alan dikkatinizi gerektiriyor"

#: includes/assets.php:187
msgid "Are you sure?"
msgstr "Emin misiniz?"

#: includes/assets.php:188 includes/fields/class-acf-field-true_false.php:79
#: includes/fields/class-acf-field-true_false.php:159
#: pro/admin/views/html-settings-updates.php:89
msgid "Yes"
msgstr "Evet"

#: includes/assets.php:189 includes/fields/class-acf-field-true_false.php:80
#: includes/fields/class-acf-field-true_false.php:174
#: pro/admin/views/html-settings-updates.php:99
msgid "No"
msgstr "Hayır"

#: includes/assets.php:190 includes/fields/class-acf-field-file.php:154
#: includes/fields/class-acf-field-image.php:141
#: includes/fields/class-acf-field-link.php:140
#: pro/fields/class-acf-field-gallery.php:360
#: pro/fields/class-acf-field-gallery.php:549
msgid "Remove"
msgstr "Kaldır"

#: includes/assets.php:191
msgid "Cancel"
msgstr "İptal"

#: includes/assets.php:194
msgid "Has any value"
msgstr "Herhangi bir değer"

#: includes/assets.php:195
msgid "Has no value"
msgstr "Hiçbir değer"

#: includes/assets.php:196
msgid "Value is equal to"
msgstr "Değer eşitse"

#: includes/assets.php:197
msgid "Value is not equal to"
msgstr "Değer eşit değilse"

#: includes/assets.php:198
msgid "Value matches pattern"
msgstr "Değer bir desenle eşleşir"

#: includes/assets.php:199
msgid "Value contains"
msgstr "Değer içeriyor"

#: includes/assets.php:200
msgid "Value is greater than"
msgstr "Değer daha büyük"

#: includes/assets.php:201
msgid "Value is less than"
msgstr "Değer daha az"

#: includes/assets.php:202
msgid "Selection is greater than"
msgstr "Seçin daha büyük"

#: includes/assets.php:203
msgid "Selection is less than"
msgstr "Seçim daha az"

#: includes/assets.php:206 includes/forms/form-comment.php:166
#: pro/admin/admin-options-page.php:325
msgid "Edit field group"
msgstr "Alan grubunu düzenle"

#: includes/fields.php:308
msgid "Field type does not exist"
msgstr "Var olmayan alan tipi"

#: includes/fields.php:308
msgid "Unknown"
msgstr "Bilinmiyor"

#: includes/fields.php:349
msgid "Basic"
msgstr "Basit"

#: includes/fields.php:350 includes/forms/form-front.php:47
msgid "Content"
msgstr "İçerik"

#: includes/fields.php:351
msgid "Choice"
msgstr "Seçim"

#: includes/fields.php:352
msgid "Relational"
msgstr "İlişkisel"

#: includes/fields.php:353
msgid "jQuery"
msgstr "jQuery"

#: includes/fields.php:354 includes/fields/class-acf-field-button-group.php:177
#: includes/fields/class-acf-field-checkbox.php:389
#: includes/fields/class-acf-field-group.php:474
#: includes/fields/class-acf-field-radio.php:290
#: pro/fields/class-acf-field-clone.php:843
#: pro/fields/class-acf-field-flexible-content.php:567
#: pro/fields/class-acf-field-flexible-content.php:616
#: pro/fields/class-acf-field-repeater.php:443
msgid "Layout"
msgstr "Yerleşim"

#: includes/fields/class-acf-field-accordion.php:24
msgid "Accordion"
msgstr "Akordeon"

#: includes/fields/class-acf-field-accordion.php:99
msgid "Open"
msgstr "Açık"

#: includes/fields/class-acf-field-accordion.php:100
msgid "Display this accordion as open on page load."
msgstr "Sayfa yüklemesi sırasında bu akordeonu açık olarak görüntüle."

#: includes/fields/class-acf-field-accordion.php:109
msgid "Multi-expand"
msgstr "Çoklu genişletme"

#: includes/fields/class-acf-field-accordion.php:110
msgid "Allow this accordion to open without closing others."
msgstr "Bu akordeonun diğerlerini kapatmadan açılmasını sağla."

#: includes/fields/class-acf-field-accordion.php:119
#: includes/fields/class-acf-field-tab.php:114
msgid "Endpoint"
msgstr "Uç nokta"

#: includes/fields/class-acf-field-accordion.php:120
msgid ""
"Define an endpoint for the previous accordion to stop. This accordion will "
"not be visible."
msgstr ""
"Önceki akordeonun durması için bir son nokta tanımlayın. Bu akordeon "
"görüntülenmeyecek."

#: includes/fields/class-acf-field-button-group.php:24
msgid "Button Group"
msgstr "Tuş grubu"

#: includes/fields/class-acf-field-button-group.php:149
#: includes/fields/class-acf-field-checkbox.php:344
#: includes/fields/class-acf-field-radio.php:235
#: includes/fields/class-acf-field-select.php:364
msgid "Choices"
msgstr "Seçimler"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "Enter each choice on a new line."
msgstr "Her seçeneği yeni bir satıra girin."

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "For more control, you may specify both a value and label like this:"
msgstr ""
"Daha fazla kontrol için, hem bir değeri hem de bir etiketi şu şekilde "
"belirtebilirsiniz:"

#: includes/fields/class-acf-field-button-group.php:150
#: includes/fields/class-acf-field-checkbox.php:345
#: includes/fields/class-acf-field-radio.php:236
#: includes/fields/class-acf-field-select.php:365
msgid "red : Red"
msgstr "kirmizi : Kırmızı"

#: includes/fields/class-acf-field-button-group.php:158
#: includes/fields/class-acf-field-page_link.php:513
#: includes/fields/class-acf-field-post_object.php:411
#: includes/fields/class-acf-field-radio.php:244
#: includes/fields/class-acf-field-select.php:382
#: includes/fields/class-acf-field-taxonomy.php:784
#: includes/fields/class-acf-field-user.php:393
msgid "Allow Null?"
msgstr "Boş geçilebilir mi?"

#: includes/fields/class-acf-field-button-group.php:168
#: includes/fields/class-acf-field-checkbox.php:380
#: includes/fields/class-acf-field-color_picker.php:131
#: includes/fields/class-acf-field-email.php:118
#: includes/fields/class-acf-field-number.php:127
#: includes/fields/class-acf-field-radio.php:281
#: includes/fields/class-acf-field-range.php:149
#: includes/fields/class-acf-field-select.php:373
#: includes/fields/class-acf-field-text.php:119
#: includes/fields/class-acf-field-textarea.php:102
#: includes/fields/class-acf-field-true_false.php:135
#: includes/fields/class-acf-field-url.php:100
#: includes/fields/class-acf-field-wysiwyg.php:381
msgid "Default Value"
msgstr "Varsayılan değer"

#: includes/fields/class-acf-field-button-group.php:169
#: includes/fields/class-acf-field-email.php:119
#: includes/fields/class-acf-field-number.php:128
#: includes/fields/class-acf-field-radio.php:282
#: includes/fields/class-acf-field-range.php:150
#: includes/fields/class-acf-field-text.php:120
#: includes/fields/class-acf-field-textarea.php:103
#: includes/fields/class-acf-field-url.php:101
#: includes/fields/class-acf-field-wysiwyg.php:382
msgid "Appears when creating a new post"
msgstr "Yeni bir yazı oluştururken görünür"

#: includes/fields/class-acf-field-button-group.php:183
#: includes/fields/class-acf-field-checkbox.php:396
#: includes/fields/class-acf-field-radio.php:297
msgid "Horizontal"
msgstr "Yatay"

#: includes/fields/class-acf-field-button-group.php:184
#: includes/fields/class-acf-field-checkbox.php:395
#: includes/fields/class-acf-field-radio.php:296
msgid "Vertical"
msgstr "Dikey"

#: includes/fields/class-acf-field-button-group.php:191
#: includes/fields/class-acf-field-checkbox.php:413
#: includes/fields/class-acf-field-file.php:215
#: includes/fields/class-acf-field-image.php:205
#: includes/fields/class-acf-field-link.php:166
#: includes/fields/class-acf-field-radio.php:304
#: includes/fields/class-acf-field-taxonomy.php:829
msgid "Return Value"
msgstr "Dönüş değeri"

#: includes/fields/class-acf-field-button-group.php:192
#: includes/fields/class-acf-field-checkbox.php:414
#: includes/fields/class-acf-field-file.php:216
#: includes/fields/class-acf-field-image.php:206
#: includes/fields/class-acf-field-link.php:167
#: includes/fields/class-acf-field-radio.php:305
msgid "Specify the returned value on front end"
msgstr "Ön yüzden dönecek değeri belirleyin"

#: includes/fields/class-acf-field-button-group.php:197
#: includes/fields/class-acf-field-checkbox.php:419
#: includes/fields/class-acf-field-radio.php:310
#: includes/fields/class-acf-field-select.php:432
msgid "Value"
msgstr "Değer"

#: includes/fields/class-acf-field-button-group.php:199
#: includes/fields/class-acf-field-checkbox.php:421
#: includes/fields/class-acf-field-radio.php:312
#: includes/fields/class-acf-field-select.php:434
msgid "Both (Array)"
msgstr "İkisi de (Dizi)"

#: includes/fields/class-acf-field-checkbox.php:25
#: includes/fields/class-acf-field-taxonomy.php:771
msgid "Checkbox"
msgstr "İşaret kutusu"

#: includes/fields/class-acf-field-checkbox.php:154
msgid "Toggle All"
msgstr "Tümünü aç/kapat"

#: includes/fields/class-acf-field-checkbox.php:221
msgid "Add new choice"
msgstr "Yeni seçenek ekle"

#: includes/fields/class-acf-field-checkbox.php:353
msgid "Allow Custom"
msgstr "Özel değere izin ver"

#: includes/fields/class-acf-field-checkbox.php:358
msgid "Allow 'custom' values to be added"
msgstr "‘Özel’ alanların eklenebilmesine izin ver"

#: includes/fields/class-acf-field-checkbox.php:364
msgid "Save Custom"
msgstr "Özel alanı kaydet"

#: includes/fields/class-acf-field-checkbox.php:369
msgid "Save 'custom' values to the field's choices"
msgstr "‘Özel’ değerleri alanın seçenekleri arasına kaydet"

#: includes/fields/class-acf-field-checkbox.php:381
#: includes/fields/class-acf-field-select.php:374
msgid "Enter each default value on a new line"
msgstr "Her varsayılan değeri yeni bir satıra girin"

#: includes/fields/class-acf-field-checkbox.php:403
msgid "Toggle"
msgstr "Aç - kapat"

#: includes/fields/class-acf-field-checkbox.php:404
msgid "Prepend an extra checkbox to toggle all choices"
msgstr ""
"En başa tüm seçimleri tersine çevirmek için ekstra bir seçim kutusu ekle"

#: includes/fields/class-acf-field-color_picker.php:25
msgid "Color Picker"
msgstr "Renk seçici"

#: includes/fields/class-acf-field-color_picker.php:68
msgid "Clear"
msgstr "Temizle"

#: includes/fields/class-acf-field-color_picker.php:69
msgid "Default"
msgstr "Varsayılan"

#: includes/fields/class-acf-field-color_picker.php:70
msgid "Select Color"
msgstr "Renk seç"

#: includes/fields/class-acf-field-color_picker.php:71
msgid "Current Color"
msgstr "Şu anki renk"

#: includes/fields/class-acf-field-date_picker.php:25
msgid "Date Picker"
msgstr "Tarih seçici"

#: includes/fields/class-acf-field-date_picker.php:59
msgctxt "Date Picker JS closeText"
msgid "Done"
msgstr "Tamam"

#: includes/fields/class-acf-field-date_picker.php:60
msgctxt "Date Picker JS currentText"
msgid "Today"
msgstr "Bugün"

#: includes/fields/class-acf-field-date_picker.php:61
msgctxt "Date Picker JS nextText"
msgid "Next"
msgstr "İleri"

#: includes/fields/class-acf-field-date_picker.php:62
msgctxt "Date Picker JS prevText"
msgid "Prev"
msgstr "Önceki"

#: includes/fields/class-acf-field-date_picker.php:63
msgctxt "Date Picker JS weekHeader"
msgid "Wk"
msgstr "Hf"

#: includes/fields/class-acf-field-date_picker.php:178
#: includes/fields/class-acf-field-date_time_picker.php:183
#: includes/fields/class-acf-field-time_picker.php:109
msgid "Display Format"
msgstr "Gösterim biçimi"

#: includes/fields/class-acf-field-date_picker.php:179
#: includes/fields/class-acf-field-date_time_picker.php:184
#: includes/fields/class-acf-field-time_picker.php:110
msgid "The format displayed when editing a post"
msgstr "Bir yazı düzenlenirken görüntülenecek biçim"

#: includes/fields/class-acf-field-date_picker.php:187
#: includes/fields/class-acf-field-date_picker.php:218
#: includes/fields/class-acf-field-date_time_picker.php:193
#: includes/fields/class-acf-field-date_time_picker.php:210
#: includes/fields/class-acf-field-time_picker.php:117
#: includes/fields/class-acf-field-time_picker.php:132
msgid "Custom:"
msgstr "Özel:"

#: includes/fields/class-acf-field-date_picker.php:197
msgid "Save Format"
msgstr "Biçimi kaydet"

#: includes/fields/class-acf-field-date_picker.php:198
msgid "The format used when saving a value"
msgstr "Bir değer kaydedilirken kullanılacak biçim"

#: includes/fields/class-acf-field-date_picker.php:208
#: includes/fields/class-acf-field-date_time_picker.php:200
#: includes/fields/class-acf-field-post_object.php:431
#: includes/fields/class-acf-field-relationship.php:634
#: includes/fields/class-acf-field-select.php:427
#: includes/fields/class-acf-field-time_picker.php:124
#: includes/fields/class-acf-field-user.php:412
msgid "Return Format"
msgstr "Dönüş biçimi"

#: includes/fields/class-acf-field-date_picker.php:209
#: includes/fields/class-acf-field-date_time_picker.php:201
#: includes/fields/class-acf-field-time_picker.php:125
msgid "The format returned via template functions"
msgstr "Tema işlevlerinden dönen biçim"

#: includes/fields/class-acf-field-date_picker.php:227
#: includes/fields/class-acf-field-date_time_picker.php:217
msgid "Week Starts On"
msgstr "Hafta başlangıcı"

#: includes/fields/class-acf-field-date_time_picker.php:25
msgid "Date Time Picker"
msgstr "Tarih zaman seçici"

#: includes/fields/class-acf-field-date_time_picker.php:68
msgctxt "Date Time Picker JS timeOnlyTitle"
msgid "Choose Time"
msgstr "Zamanı se"

#: includes/fields/class-acf-field-date_time_picker.php:69
msgctxt "Date Time Picker JS timeText"
msgid "Time"
msgstr "Zaman"

#: includes/fields/class-acf-field-date_time_picker.php:70
msgctxt "Date Time Picker JS hourText"
msgid "Hour"
msgstr "Saat"

#: includes/fields/class-acf-field-date_time_picker.php:71
msgctxt "Date Time Picker JS minuteText"
msgid "Minute"
msgstr "Dakika"

#: includes/fields/class-acf-field-date_time_picker.php:72
msgctxt "Date Time Picker JS secondText"
msgid "Second"
msgstr "Saniye"

#: includes/fields/class-acf-field-date_time_picker.php:73
msgctxt "Date Time Picker JS millisecText"
msgid "Millisecond"
msgstr "Milisaniye"

#: includes/fields/class-acf-field-date_time_picker.php:74
msgctxt "Date Time Picker JS microsecText"
msgid "Microsecond"
msgstr "Mikrosaniye"

#: includes/fields/class-acf-field-date_time_picker.php:75
msgctxt "Date Time Picker JS timezoneText"
msgid "Time Zone"
msgstr "Zaman Dilimi"

#: includes/fields/class-acf-field-date_time_picker.php:76
msgctxt "Date Time Picker JS currentText"
msgid "Now"
msgstr "Şimdi"

#: includes/fields/class-acf-field-date_time_picker.php:77
msgctxt "Date Time Picker JS closeText"
msgid "Done"
msgstr "Tamam"

#: includes/fields/class-acf-field-date_time_picker.php:78
msgctxt "Date Time Picker JS selectText"
msgid "Select"
msgstr "Seç"

#: includes/fields/class-acf-field-date_time_picker.php:80
msgctxt "Date Time Picker JS amText"
msgid "AM"
msgstr "AM"

#: includes/fields/class-acf-field-date_time_picker.php:81
msgctxt "Date Time Picker JS amTextShort"
msgid "A"
msgstr "A"

#: includes/fields/class-acf-field-date_time_picker.php:84
msgctxt "Date Time Picker JS pmText"
msgid "PM"
msgstr "PM"

#: includes/fields/class-acf-field-date_time_picker.php:85
msgctxt "Date Time Picker JS pmTextShort"
msgid "P"
msgstr "P"

#: includes/fields/class-acf-field-email.php:25
msgid "Email"
msgstr "E-posta"

#: includes/fields/class-acf-field-email.php:127
#: includes/fields/class-acf-field-number.php:136
#: includes/fields/class-acf-field-password.php:71
#: includes/fields/class-acf-field-text.php:128
#: includes/fields/class-acf-field-textarea.php:111
#: includes/fields/class-acf-field-url.php:109
msgid "Placeholder Text"
msgstr "Yer tutucu metin"

#: includes/fields/class-acf-field-email.php:128
#: includes/fields/class-acf-field-number.php:137
#: includes/fields/class-acf-field-password.php:72
#: includes/fields/class-acf-field-text.php:129
#: includes/fields/class-acf-field-textarea.php:112
#: includes/fields/class-acf-field-url.php:110
msgid "Appears within the input"
msgstr "Girdi alanının içinde görünür"

#: includes/fields/class-acf-field-email.php:136
#: includes/fields/class-acf-field-number.php:145
#: includes/fields/class-acf-field-password.php:80
#: includes/fields/class-acf-field-range.php:188
#: includes/fields/class-acf-field-text.php:137
msgid "Prepend"
msgstr "Önüne ekle"

#: includes/fields/class-acf-field-email.php:137
#: includes/fields/class-acf-field-number.php:146
#: includes/fields/class-acf-field-password.php:81
#: includes/fields/class-acf-field-range.php:189
#: includes/fields/class-acf-field-text.php:138
msgid "Appears before the input"
msgstr "Girdi alanından önce görünür"

#: includes/fields/class-acf-field-email.php:145
#: includes/fields/class-acf-field-number.php:154
#: includes/fields/class-acf-field-password.php:89
#: includes/fields/class-acf-field-range.php:197
#: includes/fields/class-acf-field-text.php:146
msgid "Append"
msgstr "Sonuna ekle"

#: includes/fields/class-acf-field-email.php:146
#: includes/fields/class-acf-field-number.php:155
#: includes/fields/class-acf-field-password.php:90
#: includes/fields/class-acf-field-range.php:198
#: includes/fields/class-acf-field-text.php:147
msgid "Appears after the input"
msgstr "Girdi alanından sonra görünür"

#: includes/fields/class-acf-field-file.php:25
msgid "File"
msgstr "Dosya"

#: includes/fields/class-acf-field-file.php:58
msgid "Edit File"
msgstr "Dosya düzenle"

#: includes/fields/class-acf-field-file.php:59
msgid "Update File"
msgstr "Dosyayı güncelle"

#: includes/fields/class-acf-field-file.php:141
msgid "File name"
msgstr "Dosya adı"

#: includes/fields/class-acf-field-file.php:145
#: includes/fields/class-acf-field-file.php:248
#: includes/fields/class-acf-field-file.php:259
#: includes/fields/class-acf-field-image.php:265
#: includes/fields/class-acf-field-image.php:294
#: pro/fields/class-acf-field-gallery.php:708
#: pro/fields/class-acf-field-gallery.php:737
msgid "File size"
msgstr "Dosya boyutu"

#: includes/fields/class-acf-field-file.php:170
msgid "Add File"
msgstr "Dosya ekle"

#: includes/fields/class-acf-field-file.php:221
msgid "File Array"
msgstr "Dosya dizisi"

#: includes/fields/class-acf-field-file.php:222
msgid "File URL"
msgstr "Dosya adresi"

#: includes/fields/class-acf-field-file.php:223
msgid "File ID"
msgstr "Dosya no"

#: includes/fields/class-acf-field-file.php:230
#: includes/fields/class-acf-field-image.php:230
#: pro/fields/class-acf-field-gallery.php:673
msgid "Library"
msgstr "Kitaplık"

#: includes/fields/class-acf-field-file.php:231
#: includes/fields/class-acf-field-image.php:231
#: pro/fields/class-acf-field-gallery.php:674
msgid "Limit the media library choice"
msgstr "Ortam kitaplığı seçimini sınırlayın"

#: includes/fields/class-acf-field-file.php:236
#: includes/fields/class-acf-field-image.php:236
#: includes/locations/class-acf-location-attachment.php:101
#: includes/locations/class-acf-location-comment.php:79
#: includes/locations/class-acf-location-nav-menu.php:102
#: includes/locations/class-acf-location-taxonomy.php:79
#: includes/locations/class-acf-location-user-form.php:87
#: includes/locations/class-acf-location-user-role.php:111
#: includes/locations/class-acf-location-widget.php:83
#: pro/fields/class-acf-field-gallery.php:679
msgid "All"
msgstr "Tümü"

#: includes/fields/class-acf-field-file.php:237
#: includes/fields/class-acf-field-image.php:237
#: pro/fields/class-acf-field-gallery.php:680
msgid "Uploaded to post"
msgstr "Yazıya yüklendi"

#: includes/fields/class-acf-field-file.php:244
#: includes/fields/class-acf-field-image.php:244
#: pro/fields/class-acf-field-gallery.php:687
msgid "Minimum"
msgstr "En az"

#: includes/fields/class-acf-field-file.php:245
#: includes/fields/class-acf-field-file.php:256
msgid "Restrict which files can be uploaded"
msgstr "Yüklenebilecek dosyaları sınırlandırın"

#: includes/fields/class-acf-field-file.php:255
#: includes/fields/class-acf-field-image.php:273
#: pro/fields/class-acf-field-gallery.php:716
msgid "Maximum"
msgstr "En fazla"

#: includes/fields/class-acf-field-file.php:266
#: includes/fields/class-acf-field-image.php:302
#: pro/fields/class-acf-field-gallery.php:745
msgid "Allowed file types"
msgstr "İzin verilen dosya tipleri"

#: includes/fields/class-acf-field-file.php:267
#: includes/fields/class-acf-field-image.php:303
#: pro/fields/class-acf-field-gallery.php:746
msgid "Comma separated list. Leave blank for all types"
msgstr "Virgül ile ayrılmış liste. Tüm tipler için boş bırakın"

#: includes/fields/class-acf-field-google-map.php:25
msgid "Google Map"
msgstr "Google haritası"

#: includes/fields/class-acf-field-google-map.php:59
msgid "Sorry, this browser does not support geolocation"
msgstr "Üzgünüz, bu tarayıcı konumlandırma desteklemiyor"

#: includes/fields/class-acf-field-google-map.php:166
msgid "Clear location"
msgstr "Konumu temizle"

#: includes/fields/class-acf-field-google-map.php:167
msgid "Find current location"
msgstr "Şu anki konumu bul"

#: includes/fields/class-acf-field-google-map.php:170
msgid "Search for address..."
msgstr "Adres arayın…"

#: includes/fields/class-acf-field-google-map.php:200
#: includes/fields/class-acf-field-google-map.php:211
msgid "Center"
msgstr "Merkez"

#: includes/fields/class-acf-field-google-map.php:201
#: includes/fields/class-acf-field-google-map.php:212
msgid "Center the initial map"
msgstr "Haritayı ortala"

#: includes/fields/class-acf-field-google-map.php:223
msgid "Zoom"
msgstr "Yaklaş"

#: includes/fields/class-acf-field-google-map.php:224
msgid "Set the initial zoom level"
msgstr "Temel yaklaşma seviyesini belirle"

#: includes/fields/class-acf-field-google-map.php:233
#: includes/fields/class-acf-field-image.php:256
#: includes/fields/class-acf-field-image.php:285
#: includes/fields/class-acf-field-oembed.php:268
#: pro/fields/class-acf-field-gallery.php:699
#: pro/fields/class-acf-field-gallery.php:728
msgid "Height"
msgstr "Yükseklik"

#: includes/fields/class-acf-field-google-map.php:234
msgid "Customize the map height"
msgstr "Harita yüksekliğini özelleştir"

#: includes/fields/class-acf-field-group.php:25
msgid "Group"
msgstr "Grup"

#: includes/fields/class-acf-field-group.php:459
#: pro/fields/class-acf-field-repeater.php:379
msgid "Sub Fields"
msgstr "Alt alanlar"

#: includes/fields/class-acf-field-group.php:475
#: pro/fields/class-acf-field-clone.php:844
msgid "Specify the style used to render the selected fields"
msgstr "Seçili alanları görüntülemek için kullanılacak stili belirtin"

#: includes/fields/class-acf-field-group.php:480
#: pro/fields/class-acf-field-clone.php:849
#: pro/fields/class-acf-field-flexible-content.php:627
#: pro/fields/class-acf-field-repeater.php:451
msgid "Block"
msgstr "Blok"

#: includes/fields/class-acf-field-group.php:481
#: pro/fields/class-acf-field-clone.php:850
#: pro/fields/class-acf-field-flexible-content.php:626
#: pro/fields/class-acf-field-repeater.php:450
msgid "Table"
msgstr "Tablo"

#: includes/fields/class-acf-field-group.php:482
#: pro/fields/class-acf-field-clone.php:851
#: pro/fields/class-acf-field-flexible-content.php:628
#: pro/fields/class-acf-field-repeater.php:452
msgid "Row"
msgstr "Satır"

#: includes/fields/class-acf-field-image.php:25
msgid "Image"
msgstr "Görsel"

#: includes/fields/class-acf-field-image.php:64
msgid "Select Image"
msgstr "Görsel seç"

#: includes/fields/class-acf-field-image.php:65
msgid "Edit Image"
msgstr "Görseli düzenle"

#: includes/fields/class-acf-field-image.php:66
msgid "Update Image"
msgstr "Görseli güncelle"

#: includes/fields/class-acf-field-image.php:157
msgid "No image selected"
msgstr "Görsel seçilmedi"

#: includes/fields/class-acf-field-image.php:157
msgid "Add Image"
msgstr "Görsel ekle"

#: includes/fields/class-acf-field-image.php:211
msgid "Image Array"
msgstr "Görsel dizisi"

#: includes/fields/class-acf-field-image.php:212
msgid "Image URL"
msgstr "Görsel adresi"

#: includes/fields/class-acf-field-image.php:213
msgid "Image ID"
msgstr "Görsel no"

#: includes/fields/class-acf-field-image.php:220
msgid "Preview Size"
msgstr "Önizleme boyutu"

#: includes/fields/class-acf-field-image.php:221
msgid "Shown when entering data"
msgstr "Veri girilirken gösterilir"

#: includes/fields/class-acf-field-image.php:245
#: includes/fields/class-acf-field-image.php:274
#: pro/fields/class-acf-field-gallery.php:688
#: pro/fields/class-acf-field-gallery.php:717
msgid "Restrict which images can be uploaded"
msgstr "Hangi görsellerin yüklenebileceğini sınırlandırın"

#: includes/fields/class-acf-field-image.php:248
#: includes/fields/class-acf-field-image.php:277
#: includes/fields/class-acf-field-oembed.php:257
#: pro/fields/class-acf-field-gallery.php:691
#: pro/fields/class-acf-field-gallery.php:720
msgid "Width"
msgstr "Genişlik"

#: includes/fields/class-acf-field-link.php:25
msgid "Link"
msgstr "Bağlantı"

#: includes/fields/class-acf-field-link.php:133
msgid "Select Link"
msgstr "Bağlantı seç"

#: includes/fields/class-acf-field-link.php:138
msgid "Opens in a new window/tab"
msgstr "Yeni pencerede/sekmede açılır"

#: includes/fields/class-acf-field-link.php:172
msgid "Link Array"
msgstr "Bağlantı dizisi"

#: includes/fields/class-acf-field-link.php:173
msgid "Link URL"
msgstr "Bağlantı adresi"

#: includes/fields/class-acf-field-message.php:25
#: includes/fields/class-acf-field-message.php:101
#: includes/fields/class-acf-field-true_false.php:126
msgid "Message"
msgstr "Mesaj"

#: includes/fields/class-acf-field-message.php:110
#: includes/fields/class-acf-field-textarea.php:139
msgid "New Lines"
msgstr "Yeni satırlar"

#: includes/fields/class-acf-field-message.php:111
#: includes/fields/class-acf-field-textarea.php:140
msgid "Controls how new lines are rendered"
msgstr "Yeni satırların nasıl görüntüleneceğini denetler"

#: includes/fields/class-acf-field-message.php:115
#: includes/fields/class-acf-field-textarea.php:144
msgid "Automatically add paragraphs"
msgstr "Otomatik paragraf ekle"

#: includes/fields/class-acf-field-message.php:116
#: includes/fields/class-acf-field-textarea.php:145
msgid "Automatically add &lt;br&gt;"
msgstr "Otomatik ekle &lt;br&gt;"

#: includes/fields/class-acf-field-message.php:117
#: includes/fields/class-acf-field-textarea.php:146
msgid "No Formatting"
msgstr "Biçimlendirme yok"

#: includes/fields/class-acf-field-message.php:124
msgid "Escape HTML"
msgstr "HTML’i güvenli hale getir"

#: includes/fields/class-acf-field-message.php:125
msgid "Allow HTML markup to display as visible text instead of rendering"
msgstr "Görünür metin olarak HTML kodlamasının görüntülenmesine izin ver"

#: includes/fields/class-acf-field-number.php:25
msgid "Number"
msgstr "Sayı"

#: includes/fields/class-acf-field-number.php:163
#: includes/fields/class-acf-field-range.php:158
msgid "Minimum Value"
msgstr "En az değer"

#: includes/fields/class-acf-field-number.php:172
#: includes/fields/class-acf-field-range.php:168
msgid "Maximum Value"
msgstr "En fazla değer"

#: includes/fields/class-acf-field-number.php:181
#: includes/fields/class-acf-field-range.php:178
msgid "Step Size"
msgstr "Adım boyutu"

#: includes/fields/class-acf-field-number.php:219
msgid "Value must be a number"
msgstr "Değer bir sayı olmalı"

#: includes/fields/class-acf-field-number.php:237
#, php-format
msgid "Value must be equal to or higher than %d"
msgstr "Değer %d değerine eşit ya da daha büyük olmalı"

#: includes/fields/class-acf-field-number.php:245
#, php-format
msgid "Value must be equal to or lower than %d"
msgstr "Değer %d değerine eşit ya da daha küçük olmalı"

#: includes/fields/class-acf-field-oembed.php:25
msgid "oEmbed"
msgstr "oEmbed"

#: includes/fields/class-acf-field-oembed.php:216
msgid "Enter URL"
msgstr "Adres girin"

#: includes/fields/class-acf-field-oembed.php:254
#: includes/fields/class-acf-field-oembed.php:265
msgid "Embed Size"
msgstr "Gömme boyutu"

#: includes/fields/class-acf-field-page_link.php:25
msgid "Page Link"
msgstr "Sayfa bağlantısı"

#: includes/fields/class-acf-field-page_link.php:177
msgid "Archives"
msgstr "Arşivler"

#: includes/fields/class-acf-field-page_link.php:269
#: includes/fields/class-acf-field-post_object.php:267
#: includes/fields/class-acf-field-taxonomy.php:961
msgid "Parent"
msgstr "Ebeveyn"

#: includes/fields/class-acf-field-page_link.php:485
#: includes/fields/class-acf-field-post_object.php:383
#: includes/fields/class-acf-field-relationship.php:560
msgid "Filter by Post Type"
msgstr "Yazı tipine göre filtre"

#: includes/fields/class-acf-field-page_link.php:493
#: includes/fields/class-acf-field-post_object.php:391
#: includes/fields/class-acf-field-relationship.php:568
msgid "All post types"
msgstr "Tüm yazı tipleri"

#: includes/fields/class-acf-field-page_link.php:499
#: includes/fields/class-acf-field-post_object.php:397
#: includes/fields/class-acf-field-relationship.php:574
msgid "Filter by Taxonomy"
msgstr "Taksonomiye göre filtre"

#: includes/fields/class-acf-field-page_link.php:507
#: includes/fields/class-acf-field-post_object.php:405
#: includes/fields/class-acf-field-relationship.php:582
msgid "All taxonomies"
msgstr "Tüm taksonomiler"

#: includes/fields/class-acf-field-page_link.php:523
msgid "Allow Archives URLs"
msgstr "Arşivler adresine izin ver"

#: includes/fields/class-acf-field-page_link.php:533
#: includes/fields/class-acf-field-post_object.php:421
#: includes/fields/class-acf-field-select.php:392
#: includes/fields/class-acf-field-user.php:403
msgid "Select multiple values?"
msgstr "Birden çok değer seçilsin mi?"

#: includes/fields/class-acf-field-password.php:25
msgid "Password"
msgstr "Parola"

#: includes/fields/class-acf-field-post_object.php:25
#: includes/fields/class-acf-field-post_object.php:436
#: includes/fields/class-acf-field-relationship.php:639
msgid "Post Object"
msgstr "Yazı nesnesi"

#: includes/fields/class-acf-field-post_object.php:437
#: includes/fields/class-acf-field-relationship.php:640
msgid "Post ID"
msgstr "Yazı No"

#: includes/fields/class-acf-field-radio.php:25
msgid "Radio Button"
msgstr "Radyo düğmesi"

#: includes/fields/class-acf-field-radio.php:254
msgid "Other"
msgstr "Diğer"

#: includes/fields/class-acf-field-radio.php:259
msgid "Add 'other' choice to allow for custom values"
msgstr "Özel değerlere izin vermek için 'diğer' seçeneği ekle"

#: includes/fields/class-acf-field-radio.php:265
msgid "Save Other"
msgstr "Diğerini kaydet"

#: includes/fields/class-acf-field-radio.php:270
msgid "Save 'other' values to the field's choices"
msgstr "‘Diğer’ değerlerini alanın seçenekleri arasına kaydet"

#: includes/fields/class-acf-field-range.php:25
msgid "Range"
msgstr "Aralık"

#: includes/fields/class-acf-field-relationship.php:25
msgid "Relationship"
msgstr "İlişkili"

#: includes/fields/class-acf-field-relationship.php:62
msgid "Maximum values reached ( {max} values )"
msgstr "En yüksek değerlere ulaşıldı ({max} değerleri)"

#: includes/fields/class-acf-field-relationship.php:63
msgid "Loading"
msgstr "Yükleniyor"

#: includes/fields/class-acf-field-relationship.php:64
msgid "No matches found"
msgstr "Eşleşme yok"

#: includes/fields/class-acf-field-relationship.php:411
msgid "Select post type"
msgstr "Yazı tipi seç"

#: includes/fields/class-acf-field-relationship.php:420
msgid "Select taxonomy"
msgstr "Taksonomi seç"

#: includes/fields/class-acf-field-relationship.php:477
msgid "Search..."
msgstr "Ara…"

#: includes/fields/class-acf-field-relationship.php:588
msgid "Filters"
msgstr "Filtreler"

#: includes/fields/class-acf-field-relationship.php:594
#: includes/locations/class-acf-location-post-type.php:27
msgid "Post Type"
msgstr "Yazı tipi"

#: includes/fields/class-acf-field-relationship.php:595
#: includes/fields/class-acf-field-taxonomy.php:28
#: includes/fields/class-acf-field-taxonomy.php:754
#: includes/locations/class-acf-location-taxonomy.php:27
msgid "Taxonomy"
msgstr "Taksonomi"

#: includes/fields/class-acf-field-relationship.php:602
msgid "Elements"
msgstr "Elemanlar"

#: includes/fields/class-acf-field-relationship.php:603
msgid "Selected elements will be displayed in each result"
msgstr "Her sonuç içinde seçilmiş elemanlar görüntülenir"

#: includes/fields/class-acf-field-relationship.php:614
msgid "Minimum posts"
msgstr "En az gönderi"

#: includes/fields/class-acf-field-relationship.php:623
msgid "Maximum posts"
msgstr "En fazla yazı"

#: includes/fields/class-acf-field-relationship.php:727
#: pro/fields/class-acf-field-gallery.php:818
#, php-format
msgid "%s requires at least %s selection"
msgid_plural "%s requires at least %s selections"
msgstr[0] "%s en az %s seçim gerektirir"
msgstr[1] "%s en az %s seçim gerektirir"

#: includes/fields/class-acf-field-select.php:25
#: includes/fields/class-acf-field-taxonomy.php:776
msgctxt "noun"
msgid "Select"
msgstr "Seçim"

#: includes/fields/class-acf-field-select.php:111
msgctxt "Select2 JS matches_1"
msgid "One result is available, press enter to select it."
msgstr "Bir sonuç bulundu, seçmek için enter tuşuna basın."

#: includes/fields/class-acf-field-select.php:112
#, php-format
msgctxt "Select2 JS matches_n"
msgid "%d results are available, use up and down arrow keys to navigate."
msgstr "%d sonuç bulundu. Dolaşmak için yukarı ve aşağı okları kullanın."

#: includes/fields/class-acf-field-select.php:113
msgctxt "Select2 JS matches_0"
msgid "No matches found"
msgstr "Eşleşme yok"

#: includes/fields/class-acf-field-select.php:114
msgctxt "Select2 JS input_too_short_1"
msgid "Please enter 1 or more characters"
msgstr "Lütfen 1 veya daha fazla karakter girin"

#: includes/fields/class-acf-field-select.php:115
#, php-format
msgctxt "Select2 JS input_too_short_n"
msgid "Please enter %d or more characters"
msgstr "Lütfen %d veya daha fazla karakter girin"

#: includes/fields/class-acf-field-select.php:116
msgctxt "Select2 JS input_too_long_1"
msgid "Please delete 1 character"
msgstr "Lütfen 1 karakter silin"

#: includes/fields/class-acf-field-select.php:117
#, php-format
msgctxt "Select2 JS input_too_long_n"
msgid "Please delete %d characters"
msgstr "Lütfen %d karakter silin"

#: includes/fields/class-acf-field-select.php:118
msgctxt "Select2 JS selection_too_long_1"
msgid "You can only select 1 item"
msgstr "Sadece 1 öğe seçebilirsiniz"

#: includes/fields/class-acf-field-select.php:119
#, php-format
msgctxt "Select2 JS selection_too_long_n"
msgid "You can only select %d items"
msgstr "Sadece %d öge seçebilirsiniz"

#: includes/fields/class-acf-field-select.php:120
msgctxt "Select2 JS load_more"
msgid "Loading more results&hellip;"
msgstr "Daha fazla sonuç yükleniyor&hellip;"

#: includes/fields/class-acf-field-select.php:121
msgctxt "Select2 JS searching"
msgid "Searching&hellip;"
msgstr "Aranıyor&hellip;"

#: includes/fields/class-acf-field-select.php:122
msgctxt "Select2 JS load_fail"
msgid "Loading failed"
msgstr "Yükleme başarısız oldu"

#: includes/fields/class-acf-field-select.php:402
#: includes/fields/class-acf-field-true_false.php:144
msgid "Stylised UI"
msgstr "Stilize edilmiş kullanıcı arabirimi"

#: includes/fields/class-acf-field-select.php:412
msgid "Use AJAX to lazy load choices?"
msgstr "Seçimlerin tembel yüklenmesi için AJAX kullanılsın mı?"

#: includes/fields/class-acf-field-select.php:428
msgid "Specify the value returned"
msgstr "Dönecek değeri belirt"

#: includes/fields/class-acf-field-separator.php:25
msgid "Separator"
msgstr "Ayraç"

#: includes/fields/class-acf-field-tab.php:25
msgid "Tab"
msgstr "Sekme"

#: includes/fields/class-acf-field-tab.php:102
msgid "Placement"
msgstr "Konumlandırma"

#: includes/fields/class-acf-field-tab.php:115
msgid ""
"Define an endpoint for the previous tabs to stop. This will start a new "
"group of tabs."
msgstr ""
"Önceki sekmelerin durması için bir uç nokta tanımlayın. Bu yeni sekmeler "
"için bir grup başlatacaktır."

#: includes/fields/class-acf-field-taxonomy.php:714
#, php-format
msgctxt "No terms"
msgid "No %s"
msgstr "%s yok"

#: includes/fields/class-acf-field-taxonomy.php:755
msgid "Select the taxonomy to be displayed"
msgstr "Görüntülenecek taksonomiyi seçin"

#: includes/fields/class-acf-field-taxonomy.php:764
msgid "Appearance"
msgstr "Görünüm"

#: includes/fields/class-acf-field-taxonomy.php:765
msgid "Select the appearance of this field"
msgstr "Bu alanın görünümünü seçin"

#: includes/fields/class-acf-field-taxonomy.php:770
msgid "Multiple Values"
msgstr "Çoklu değer"

#: includes/fields/class-acf-field-taxonomy.php:772
msgid "Multi Select"
msgstr "Çoklu seçim"

#: includes/fields/class-acf-field-taxonomy.php:774
msgid "Single Value"
msgstr "Tek değer"

#: includes/fields/class-acf-field-taxonomy.php:775
msgid "Radio Buttons"
msgstr "Radyo düğmeleri"

#: includes/fields/class-acf-field-taxonomy.php:799
msgid "Create Terms"
msgstr "Terimleri oluştur"

#: includes/fields/class-acf-field-taxonomy.php:800
msgid "Allow new terms to be created whilst editing"
msgstr "Düzenlenirken yeni terimlerin oluşabilmesine izin ver"

#: includes/fields/class-acf-field-taxonomy.php:809
msgid "Save Terms"
msgstr "Terimleri kaydet"

#: includes/fields/class-acf-field-taxonomy.php:810
msgid "Connect selected terms to the post"
msgstr "Seçilmiş terimleri yazıya bağla"

#: includes/fields/class-acf-field-taxonomy.php:819
msgid "Load Terms"
msgstr "Terimleri yükle"

#: includes/fields/class-acf-field-taxonomy.php:820
msgid "Load value from posts terms"
msgstr "Yazının terimlerinden değerleri yükle"

#: includes/fields/class-acf-field-taxonomy.php:834
msgid "Term Object"
msgstr "Terim nesnesi"

#: includes/fields/class-acf-field-taxonomy.php:835
msgid "Term ID"
msgstr "Terim no"

#: includes/fields/class-acf-field-taxonomy.php:885
#, php-format
msgid "User unable to add new %s"
msgstr "Kullanıcı yeni %s ekleyemiyor"

#: includes/fields/class-acf-field-taxonomy.php:895
#, php-format
msgid "%s already exists"
msgstr "%s zaten mevcut"

#: includes/fields/class-acf-field-taxonomy.php:927
#, php-format
msgid "%s added"
msgstr "%s eklendi"

#: includes/fields/class-acf-field-taxonomy.php:973
msgid "Add"
msgstr "Ekle"

#: includes/fields/class-acf-field-text.php:25
msgid "Text"
msgstr "Metin"

#: includes/fields/class-acf-field-text.php:155
#: includes/fields/class-acf-field-textarea.php:120
msgid "Character Limit"
msgstr "Karakter limiti"

#: includes/fields/class-acf-field-text.php:156
#: includes/fields/class-acf-field-textarea.php:121
msgid "Leave blank for no limit"
msgstr "Limit olmaması için boş bırakın"

#: includes/fields/class-acf-field-text.php:181
#: includes/fields/class-acf-field-textarea.php:213
#, php-format
msgid "Value must not exceed %d characters"
msgstr "Değer %d karakteri geçmemelidir"

#: includes/fields/class-acf-field-textarea.php:25
msgid "Text Area"
msgstr "Metin alanı"

#: includes/fields/class-acf-field-textarea.php:129
msgid "Rows"
msgstr "Satırlar"

#: includes/fields/class-acf-field-textarea.php:130
msgid "Sets the textarea height"
msgstr "Metin alanı yüksekliğini ayarla"

#: includes/fields/class-acf-field-time_picker.php:25
msgid "Time Picker"
msgstr "Zaman seçici"

#: includes/fields/class-acf-field-true_false.php:25
msgid "True / False"
msgstr "Doğru / yanlış"

#: includes/fields/class-acf-field-true_false.php:127
msgid "Displays text alongside the checkbox"
msgstr "İşaret kutusunun yanında görüntülenen metin"

#: includes/fields/class-acf-field-true_false.php:155
msgid "On Text"
msgstr "Açık metni"

#: includes/fields/class-acf-field-true_false.php:156
msgid "Text shown when active"
msgstr "Etkinken görüntülenen metin"

#: includes/fields/class-acf-field-true_false.php:170
msgid "Off Text"
msgstr "Kapalı metni"

#: includes/fields/class-acf-field-true_false.php:171
msgid "Text shown when inactive"
msgstr "Etkin değilken görüntülenen metin"

#: includes/fields/class-acf-field-url.php:25
msgid "Url"
msgstr "Web adresi"

#: includes/fields/class-acf-field-url.php:151
msgid "Value must be a valid URL"
msgstr "Değer geçerli bir web adresi olmalı"

#: includes/fields/class-acf-field-user.php:25 includes/locations.php:95
msgid "User"
msgstr "Kullanıcı"

#: includes/fields/class-acf-field-user.php:378
msgid "Filter by role"
msgstr "Kurala göre filtrele"

#: includes/fields/class-acf-field-user.php:386
msgid "All user roles"
msgstr "Bütün kullanıcı rolleri"

#: includes/fields/class-acf-field-user.php:417
msgid "User Array"
msgstr "Kullanıcı dizisi"

#: includes/fields/class-acf-field-user.php:418
msgid "User Object"
msgstr "Kullanıcı nesnesi"

#: includes/fields/class-acf-field-user.php:419
msgid "User ID"
msgstr "Kullanıcı No"

#: includes/fields/class-acf-field-wysiwyg.php:25
msgid "Wysiwyg Editor"
msgstr "Wysiwyg düzenleyici"

#: includes/fields/class-acf-field-wysiwyg.php:330
msgid "Visual"
msgstr "Görsel"

#: includes/fields/class-acf-field-wysiwyg.php:331
msgctxt "Name for the Text editor tab (formerly HTML)"
msgid "Text"
msgstr "Metin"

#: includes/fields/class-acf-field-wysiwyg.php:337
msgid "Click to initialize TinyMCE"
msgstr "TinyMCE hazırlamak için tıklayın"

#: includes/fields/class-acf-field-wysiwyg.php:390
msgid "Tabs"
msgstr "Sekmeler"

#: includes/fields/class-acf-field-wysiwyg.php:395
msgid "Visual & Text"
msgstr "Görsel ve metin"

#: includes/fields/class-acf-field-wysiwyg.php:396
msgid "Visual Only"
msgstr "Sadece görsel"

#: includes/fields/class-acf-field-wysiwyg.php:397
msgid "Text Only"
msgstr "Sadece metin"

#: includes/fields/class-acf-field-wysiwyg.php:404
msgid "Toolbar"
msgstr "Araç çubuğu"

#: includes/fields/class-acf-field-wysiwyg.php:419
msgid "Show Media Upload Buttons?"
msgstr "Ortam yükleme tuşları gösterilsin mi?"

#: includes/fields/class-acf-field-wysiwyg.php:429
msgid "Delay initialization?"
msgstr "Hazırlık geciktirilsin mi?"

#: includes/fields/class-acf-field-wysiwyg.php:430
msgid "TinyMCE will not be initalized until field is clicked"
msgstr "Alan tıklanana kadar TinyMCE hazırlanmayacak"

#: includes/forms/form-front.php:55
msgid "Validate Email"
msgstr "E-postayı doğrula"

#: includes/forms/form-front.php:103 pro/fields/class-acf-field-gallery.php:591
#: pro/options-page.php:81
msgid "Update"
msgstr "Güncelle"

#: includes/forms/form-front.php:104
msgid "Post updated"
msgstr "Yazı güncellendi"

#: includes/forms/form-front.php:230
msgid "Spam Detected"
msgstr "İstenmeyen tespit edildi"

#: includes/locations.php:93 includes/locations/class-acf-location-post.php:27
msgid "Post"
msgstr "Yazı"

#: includes/locations.php:94 includes/locations/class-acf-location-page.php:27
msgid "Page"
msgstr "Sayfa"

#: includes/locations.php:96
msgid "Forms"
msgstr "Formlar"

#: includes/locations.php:243
msgid "is equal to"
msgstr "eşitse"

#: includes/locations.php:244
msgid "is not equal to"
msgstr "eşit değilse"

#: includes/locations/class-acf-location-attachment.php:27
msgid "Attachment"
msgstr "Ek"

#: includes/locations/class-acf-location-attachment.php:109
#, php-format
msgid "All %s formats"
msgstr "Tüm %s biçimleri"

#: includes/locations/class-acf-location-comment.php:27
msgid "Comment"
msgstr "Yorum"

#: includes/locations/class-acf-location-current-user-role.php:27
msgid "Current User Role"
msgstr "Şu anki kullanıcı rolü"

#: includes/locations/class-acf-location-current-user-role.php:110
msgid "Super Admin"
msgstr "Süper yönetici"

#: includes/locations/class-acf-location-current-user.php:27
msgid "Current User"
msgstr "Şu anki kullanıcı"

#: includes/locations/class-acf-location-current-user.php:97
msgid "Logged in"
msgstr "Giriş yapıldı"

#: includes/locations/class-acf-location-current-user.php:98
msgid "Viewing front end"
msgstr "Ön yüz görüntüleniyor"

#: includes/locations/class-acf-location-current-user.php:99
msgid "Viewing back end"
msgstr "Arka yüz görüntüleniyor"

#: includes/locations/class-acf-location-nav-menu-item.php:27
msgid "Menu Item"
msgstr "Menü ögesi"

#: includes/locations/class-acf-location-nav-menu.php:27
msgid "Menu"
msgstr "Menü"

#: includes/locations/class-acf-location-nav-menu.php:109
msgid "Menu Locations"
msgstr "Menü konumları"

#: includes/locations/class-acf-location-nav-menu.php:119
msgid "Menus"
msgstr "Menüler"

#: includes/locations/class-acf-location-page-parent.php:27
msgid "Page Parent"
msgstr "Sayfa ebeveyni"

#: includes/locations/class-acf-location-page-template.php:27
msgid "Page Template"
msgstr "Sayfa şablonu"

#: includes/locations/class-acf-location-page-template.php:87
#: includes/locations/class-acf-location-post-template.php:134
msgid "Default Template"
msgstr "Varsayılan şablon"

#: includes/locations/class-acf-location-page-type.php:27
msgid "Page Type"
msgstr "Sayfa tipi"

#: includes/locations/class-acf-location-page-type.php:146
msgid "Front Page"
msgstr "Ön sayfa"

#: includes/locations/class-acf-location-page-type.php:147
msgid "Posts Page"
msgstr "Yazılar sayfası"

#: includes/locations/class-acf-location-page-type.php:148
msgid "Top Level Page (no parent)"
msgstr "Üst düzey sayfa (ebeveynsiz)"

#: includes/locations/class-acf-location-page-type.php:149
msgid "Parent Page (has children)"
msgstr "Üst sayfa (alt sayfası olan)"

#: includes/locations/class-acf-location-page-type.php:150
msgid "Child Page (has parent)"
msgstr "Alt sayfa (ebeveyni olan)"

#: includes/locations/class-acf-location-post-category.php:27
msgid "Post Category"
msgstr "Yazı kategorisi"

#: includes/locations/class-acf-location-post-format.php:27
msgid "Post Format"
msgstr "Yazı biçimi"

#: includes/locations/class-acf-location-post-status.php:27
msgid "Post Status"
msgstr "Yazı durumu"

#: includes/locations/class-acf-location-post-taxonomy.php:27
msgid "Post Taxonomy"
msgstr "Yazı taksonomisi"

#: includes/locations/class-acf-location-post-template.php:27
msgid "Post Template"
msgstr "Yazı şablonu"

#: includes/locations/class-acf-location-user-form.php:27
msgid "User Form"
msgstr "Kullanıcı formu"

#: includes/locations/class-acf-location-user-form.php:88
msgid "Add / Edit"
msgstr "Ekle / düzenle"

#: includes/locations/class-acf-location-user-form.php:89
msgid "Register"
msgstr "Kaydet"

#: includes/locations/class-acf-location-user-role.php:27
msgid "User Role"
msgstr "Kullanıcı kuralı"

#: includes/locations/class-acf-location-widget.php:27
msgid "Widget"
msgstr "Bileşen"

#: includes/validation.php:364
#, php-format
msgid "%s value is required"
msgstr "%s değeri gerekli"

#. Plugin Name of the plugin/theme
#: pro/acf-pro.php:28
msgid "Advanced Custom Fields PRO"
msgstr "Advanced Custom Fields PRO"

#: pro/admin/admin-options-page.php:198
msgid "Publish"
msgstr "Yayımla"

#: pro/admin/admin-options-page.php:204
#, php-format
msgid ""
"No Custom Field Groups found for this options page. <a href=\"%s\">Create a "
"Custom Field Group</a>"
msgstr ""
"Bu seçenekler sayfası için hiç özel alan grubu bulunamadı. <a href=\"%s"
"\">Bir özel alan grubu oluştur</a>"

#: pro/admin/admin-updates.php:49
msgid "<b>Error</b>. Could not connect to update server"
msgstr "<b> Hata</b>. Güncelleme sunucusu ile bağlantı kurulamadı"

#: pro/admin/admin-updates.php:118 pro/admin/views/html-settings-updates.php:13
msgid "Updates"
msgstr "Güncellemeler"

#: pro/admin/admin-updates.php:191
msgid ""
"<b>Error</b>. Could not authenticate update package. Please check again or "
"deactivate and reactivate your ACF PRO license."
msgstr ""
"<b>Hata</b>. Güncelleme paketi için kimlik doğrulaması yapılamadı. Lütfen "
"ACF PRO lisansınızı kontrol edin ya da lisansınızı etkisizleştirip, tekrar "
"etkinleştirin."

#: pro/admin/views/html-settings-updates.php:7
msgid "Deactivate License"
msgstr "Lisansı devre dışı bırak"

#: pro/admin/views/html-settings-updates.php:7
msgid "Activate License"
msgstr "Lisansı etkinleştir"

#: pro/admin/views/html-settings-updates.php:17
msgid "License Information"
msgstr "Lisans bilgisi"

#: pro/admin/views/html-settings-updates.php:20
#, php-format
msgid ""
"To unlock updates, please enter your license key below. If you don't have a "
"licence key, please see <a href=\"%s\" target=\"_blank\">details & pricing</"
"a>."
msgstr ""
"Güncellemeleri açmak için lisans anahtarınızı aşağıya girin. Eğer bir lisans "
"anahtarınız yoksa lütfen <a href=“%s” target=“_blank”>detaylar ve fiyatlama</"
"a> sayfasına bakın."

#: pro/admin/views/html-settings-updates.php:29
msgid "License Key"
msgstr "Lisans anahtarı"

#: pro/admin/views/html-settings-updates.php:61
msgid "Update Information"
msgstr "Güncelleme bilgisi"

#: pro/admin/views/html-settings-updates.php:68
msgid "Current Version"
msgstr "Mevcut sürüm"

#: pro/admin/views/html-settings-updates.php:76
msgid "Latest Version"
msgstr "En son sürüm"

#: pro/admin/views/html-settings-updates.php:84
msgid "Update Available"
msgstr "Güncelleme mevcut"

#: pro/admin/views/html-settings-updates.php:92
msgid "Update Plugin"
msgstr "Eklentiyi güncelle"

#: pro/admin/views/html-settings-updates.php:94
msgid "Please enter your license key above to unlock updates"
msgstr ""
"Güncelleştirmelerin kilidini açmak için yukardaki alana lisans anahtarını "
"girin"

#: pro/admin/views/html-settings-updates.php:100
msgid "Check Again"
msgstr "Tekrar kontrol et"

#: pro/admin/views/html-settings-updates.php:117
msgid "Upgrade Notice"
msgstr "Yükseltme bildirimi"

#: pro/fields/class-acf-field-clone.php:25
msgctxt "noun"
msgid "Clone"
msgstr "Kopyala"

#: pro/fields/class-acf-field-clone.php:812
msgid "Select one or more fields you wish to clone"
msgstr "Çoğaltmak için bir ya da daha fazla alan seçin"

#: pro/fields/class-acf-field-clone.php:829
msgid "Display"
msgstr "Görüntüle"

#: pro/fields/class-acf-field-clone.php:830
msgid "Specify the style used to render the clone field"
msgstr "Çoğaltılacak alanın görünümü için stili belirleyin"

#: pro/fields/class-acf-field-clone.php:835
msgid "Group (displays selected fields in a group within this field)"
msgstr "Grup (bu alanın içinde seçili alanları grup olarak gösterir)"

#: pro/fields/class-acf-field-clone.php:836
msgid "Seamless (replaces this field with selected fields)"
msgstr "Pürüzsüz (bu alanı seçişmiş olan alanlarla değiştirir)"

#: pro/fields/class-acf-field-clone.php:857
#, php-format
msgid "Labels will be displayed as %s"
msgstr "Etiketler %s olarak görüntülenir"

#: pro/fields/class-acf-field-clone.php:860
msgid "Prefix Field Labels"
msgstr "Alan etiketlerine ön ek ekle"

#: pro/fields/class-acf-field-clone.php:871
#, php-format
msgid "Values will be saved as %s"
msgstr "Değerler %s olarak kaydedilecek"

#: pro/fields/class-acf-field-clone.php:874
msgid "Prefix Field Names"
msgstr "Alan isimlerine ön ek ekle"

#: pro/fields/class-acf-field-clone.php:992
msgid "Unknown field"
msgstr "Bilinmeyen alan"

#: pro/fields/class-acf-field-clone.php:1031
msgid "Unknown field group"
msgstr "Bilinmeyen alan grubu"

#: pro/fields/class-acf-field-clone.php:1035
#, php-format
msgid "All fields from %s field group"
msgstr "%s alan grubundaki tüm alanlar"

#: pro/fields/class-acf-field-flexible-content.php:31
#: pro/fields/class-acf-field-repeater.php:193
#: pro/fields/class-acf-field-repeater.php:463
msgid "Add Row"
msgstr "Satır ekle"

#: pro/fields/class-acf-field-flexible-content.php:73
#: pro/fields/class-acf-field-flexible-content.php:938
#: pro/fields/class-acf-field-flexible-content.php:1020
msgid "layout"
msgid_plural "layouts"
msgstr[0] "yerleşim"
msgstr[1] "yerleşimler"

#: pro/fields/class-acf-field-flexible-content.php:74
msgid "layouts"
msgstr "yerleşimler"

#: pro/fields/class-acf-field-flexible-content.php:77
#: pro/fields/class-acf-field-flexible-content.php:937
#: pro/fields/class-acf-field-flexible-content.php:1019
msgid "This field requires at least {min} {label} {identifier}"
msgstr "Bu alan için en az gereken {min} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:78
msgid "This field has a limit of {max} {label} {identifier}"
msgstr "Bu alan için sınır {max} {label} {identifier}"

#: pro/fields/class-acf-field-flexible-content.php:81
msgid "{available} {label} {identifier} available (max {max})"
msgstr "{available} {label} {identifier} kullanılabilir (en fazla {max})"

#: pro/fields/class-acf-field-flexible-content.php:82
msgid "{required} {label} {identifier} required (min {min})"
msgstr "{required} {label} {identifier} gerekli (min {min})"

#: pro/fields/class-acf-field-flexible-content.php:85
msgid "Flexible Content requires at least 1 layout"
msgstr "Esnek içerik, en az 1 yerleşim gerektirir"

#: pro/fields/class-acf-field-flexible-content.php:302
#, php-format
msgid "Click the \"%s\" button below to start creating your layout"
msgstr ""
"Kendi yerleşiminizi oluşturmaya başlamak için aşağıdaki \"%s \" tuşuna "
"tıklayın"

#: pro/fields/class-acf-field-flexible-content.php:427
msgid "Add layout"
msgstr "Yerleşim ekle"

#: pro/fields/class-acf-field-flexible-content.php:428
msgid "Remove layout"
msgstr "Yerleşimi çıkar"

#: pro/fields/class-acf-field-flexible-content.php:429
#: pro/fields/class-acf-field-repeater.php:296
msgid "Click to toggle"
msgstr "Geçiş yapmak için tıklayın"

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder Layout"
msgstr "Yerleşimi yeniden sırala"

#: pro/fields/class-acf-field-flexible-content.php:569
msgid "Reorder"
msgstr "Yeniden sırala"

#: pro/fields/class-acf-field-flexible-content.php:570
msgid "Delete Layout"
msgstr "Yerleşimi sil"

#: pro/fields/class-acf-field-flexible-content.php:571
msgid "Duplicate Layout"
msgstr "Yerleşimi çoğalt"

#: pro/fields/class-acf-field-flexible-content.php:572
msgid "Add New Layout"
msgstr "Yeni yerleşim ekle"

#: pro/fields/class-acf-field-flexible-content.php:643
msgid "Min"
msgstr "En düşük"

#: pro/fields/class-acf-field-flexible-content.php:656
msgid "Max"
msgstr "En yüksek"

#: pro/fields/class-acf-field-flexible-content.php:683
#: pro/fields/class-acf-field-repeater.php:459
msgid "Button Label"
msgstr "Tuş etiketi"

#: pro/fields/class-acf-field-flexible-content.php:692
msgid "Minimum Layouts"
msgstr "En az yerleşim"

#: pro/fields/class-acf-field-flexible-content.php:701
msgid "Maximum Layouts"
msgstr "En fazla yerleşim"

#: pro/fields/class-acf-field-gallery.php:71
msgid "Add Image to Gallery"
msgstr "Galeriye görsel ekle"

#: pro/fields/class-acf-field-gallery.php:72
msgid "Maximum selection reached"
msgstr "En fazla seçim aşıldı"

#: pro/fields/class-acf-field-gallery.php:338
msgid "Length"
msgstr "Uzunluk"

#: pro/fields/class-acf-field-gallery.php:381
msgid "Caption"
msgstr "Başlık"

#: pro/fields/class-acf-field-gallery.php:390
msgid "Alt Text"
msgstr "Alternatif metin"

#: pro/fields/class-acf-field-gallery.php:562
msgid "Add to gallery"
msgstr "Galeriye ekle"

#: pro/fields/class-acf-field-gallery.php:566
msgid "Bulk actions"
msgstr "Toplu eylemler"

#: pro/fields/class-acf-field-gallery.php:567
msgid "Sort by date uploaded"
msgstr "Yüklenme tarihine göre sırala"

#: pro/fields/class-acf-field-gallery.php:568
msgid "Sort by date modified"
msgstr "Değiştirme tarihine göre sırala"

#: pro/fields/class-acf-field-gallery.php:569
msgid "Sort by title"
msgstr "Başlığa göre sırala"

#: pro/fields/class-acf-field-gallery.php:570
msgid "Reverse current order"
msgstr "Sıralamayı ters çevir"

#: pro/fields/class-acf-field-gallery.php:588
msgid "Close"
msgstr "Kapat"

#: pro/fields/class-acf-field-gallery.php:642
msgid "Minimum Selection"
msgstr "En az seçim"

#: pro/fields/class-acf-field-gallery.php:651
msgid "Maximum Selection"
msgstr "En fazla seçim"

#: pro/fields/class-acf-field-gallery.php:660
msgid "Insert"
msgstr "Ekle"

#: pro/fields/class-acf-field-gallery.php:661
msgid "Specify where new attachments are added"
msgstr "Yeni eklerin nereye ekleneceğini belirtin"

#: pro/fields/class-acf-field-gallery.php:665
msgid "Append to the end"
msgstr "Sona ekle"

#: pro/fields/class-acf-field-gallery.php:666
msgid "Prepend to the beginning"
msgstr "En başa ekleyin"

#: pro/fields/class-acf-field-repeater.php:65
#: pro/fields/class-acf-field-repeater.php:656
msgid "Minimum rows reached ({min} rows)"
msgstr "En az satır sayısına ulaşıldı ({min} satır)"

#: pro/fields/class-acf-field-repeater.php:66
msgid "Maximum rows reached ({max} rows)"
msgstr "En fazla satır değerine ulaşıldı ({max} satır)"

#: pro/fields/class-acf-field-repeater.php:333
msgid "Add row"
msgstr "Satır ekle"

#: pro/fields/class-acf-field-repeater.php:334
msgid "Remove row"
msgstr "Satır çıkar"

#: pro/fields/class-acf-field-repeater.php:412
msgid "Collapsed"
msgstr "Daraltılmış"

#: pro/fields/class-acf-field-repeater.php:413
msgid "Select a sub field to show when row is collapsed"
msgstr "Satır toparlandığında görüntülenecek alt alanı seçin"

#: pro/fields/class-acf-field-repeater.php:423
msgid "Minimum Rows"
msgstr "En az satır"

#: pro/fields/class-acf-field-repeater.php:433
msgid "Maximum Rows"
msgstr "En fazla satır"

#: pro/locations/class-acf-location-options-page.php:79
msgid "No options pages exist"
msgstr "Seçenekler sayfayı mevcut değil"

#: pro/options-page.php:51
msgid "Options"
msgstr "Seçenekler"

#: pro/options-page.php:82
msgid "Options Updated"
msgstr "Seçenekler güncellendi"

#: pro/updates.php:97
#, php-format
msgid ""
"To enable updates, please enter your license key on the <a href=\"%s"
"\">Updates</a> page. If you don't have a licence key, please see <a href=\"%s"
"\">details & pricing</a>."
msgstr ""
"Güncellemeleri etkinleştirmek için lütfen <a href=\"%s\">Güncellemeler</a> "
"sayfasında lisans anahtarınızı girin. Eğer bir lisans anahtarınız yoksa "
"lütfen <a href=\"%s\">detaylar ve fiyatlama</a> sayfasına bakın."

#. Plugin URI of the plugin/theme
msgid "https://www.advancedcustomfields.com/"
msgstr "https://www.advancedcustomfields.com/"

#. Author of the plugin/theme
msgid "Elliot Condon"
msgstr "Elliot Condon"

#. Author URI of the plugin/theme
msgid "http://www.elliotcondon.com/"
msgstr "http://www.elliotcondon.com/"

#~ msgid "%s field group duplicated."
#~ msgid_plural "%s field groups duplicated."
#~ msgstr[0] "%s alan grubu çoğaltıldı."
#~ msgstr[1] "%s alan grubu çoğaltıldı."

#~ msgid "%s field group synchronised."
#~ msgid_plural "%s field groups synchronised."
#~ msgstr[0] "%s alan grubu eşitlendi."
#~ msgstr[1] "%s alan grubu eşitlendi."

#~ msgid "<b>Error</b>. Could not load add-ons list"
#~ msgstr "<b>Hata</b>. Eklenti listesi yüklenemedi"

#~ msgid "Parent fields"
#~ msgstr "Üst alanlar"

#~ msgid "Sibling fields"
#~ msgstr "Kardeş alanlar"

#~ msgid "Error validating request"
#~ msgstr "İstek doğrulanırken hata oluştu"

#~ msgid "Advanced Custom Fields Database Upgrade"
#~ msgstr "Advanced Custom Fields veritabanı güncellemesi"

#~ msgid ""
#~ "Before you start using the new awesome features, please update your "
#~ "database to the newest version."
#~ msgstr ""
#~ "Yeni muhteşem özellikleri kullanmadan önce lütfen veritabanınızı en yeni "
#~ "sürüme güncelleyin."

#~ msgid ""
#~ "To help make upgrading easy, <a href=\"%s\">login to your store account</"
#~ "a> and claim a free copy of ACF PRO!"
#~ msgstr ""
#~ "Yükseltmeyi kolaylaştırmak için <a href=\"%s\">mağaza hesabınıza</a> "
#~ "giriş yapın ve bir adet ücretsiz ACF PRO kopyası edinin!"

#~ msgid "Under the Hood"
#~ msgstr "Kaputun altında"

#~ msgid "Smarter field settings"
#~ msgstr "Daha akıllı alan ayarları"

#~ msgid "ACF now saves its field settings as individual post objects"
#~ msgstr "ACF artık alan ayarlarını münferit yazı nesneleri olarak saklıyor"

#~ msgid "Better version control"
#~ msgstr "Daha iyi sürüm kontrolü"

#~ msgid ""
#~ "New auto export to JSON feature allows field settings to be version "
#~ "controlled"
#~ msgstr ""
#~ "Otomatik JSON dışarı aktarma özelliği sayesinde artık alan ayarları sürüm "
#~ "kontrolü ile yönetilebilir"

#~ msgid "Swapped XML for JSON"
#~ msgstr "XML yerine JSON kullanımına geçildi"

#~ msgid "Import / Export now uses JSON in favour of XML"
#~ msgstr "İçeri / dışarı aktarma artık XML yerine JSON kullanıyor"

#~ msgid "New Forms"
#~ msgstr "Yeni formlar"

#~ msgid "A new field for embedding content has been added"
#~ msgstr "Gömülü içerik için yeni bir alan eklendi"

#~ msgid "New Gallery"
#~ msgstr "Yeni galeri"

#~ msgid "The gallery field has undergone a much needed facelift"
#~ msgstr "Galeri alanı oldukça gerekli bir makyaj ile yenilendi"

#~ msgid "Relationship Field"
#~ msgstr "İlişkili alan"

#~ msgid ""
#~ "New Relationship field setting for 'Filters' (Search, Post Type, Taxonomy)"
#~ msgstr "'Filtreler' için yeni ilişki ayarı (Arama, yazı tipi, taksonomi)"

#~ msgid "New archives group in page_link field selection"
#~ msgstr "Yeni arşivler page_link alanı seçiminde gruplanır"

#~ msgid "Better Options Pages"
#~ msgstr "Daha iyi seçenekler sayfası"

#~ msgid ""
#~ "New functions for options page allow creation of both parent and child "
#~ "menu pages"
#~ msgstr ""
#~ "Seçenekler sayfası için yeni işlevler sayesinde hem üst hem alt menü "
#~ "sayfaları oluşturulabiliyor"

#~ msgid "Export Field Groups to PHP"
#~ msgstr "Alan gruplarını PHP için dışa aktar"

#~ msgid "Download export file"
#~ msgstr "Dışarı aktarma dosyasını indir"

#~ msgid "Generate export code"
#~ msgstr "Dışarı aktarma kodu oluştur"

#~ msgid "Import"
#~ msgstr "İçe aktar"

#~ msgid "Locating"
#~ msgstr "Konum bulunuyor"

#~ msgid "Error."
#~ msgstr "Hata."

#~ msgid "No embed found for the given URL."
#~ msgstr "Verilen adres için gömülecek bir şey bulunamadı."

#~ msgid "Minimum values reached ( {min} values )"
#~ msgstr "En düşün değerlere ulaşıldı ( {min} değerleri )"

#~ msgid ""
#~ "The tab field will display incorrectly when added to a Table style "
#~ "repeater field or flexible content field layout"
#~ msgstr ""
#~ "Bir tablo stili tekrarlayıcı ya da esnek içerik alanı yerleşimi "
#~ "eklendiğinde sekme alanı yanlış görüntülenir"

#~ msgid ""
#~ "Use \"Tab Fields\" to better organize your edit screen by grouping fields "
#~ "together."
#~ msgstr ""
#~ "“Sekme alanları”nı kullanarak düzenleme ekranında alanları gruplayıp daha "
#~ "kolay organize olun."

#~ msgid ""
#~ "All fields following this \"tab field\" (or until another \"tab field\" "
#~ "is defined) will be grouped together using this field's label as the tab "
#~ "heading."
#~ msgstr ""
#~ "Bu “sekme alanı”nı takip eden (ya da başka bir “sekme alanı” tanımlıysa) "
#~ "tüm alanlar sekmenin başlığını etiket olarak kullanarak "
#~ "gruplandırılacaklar."

#~ msgid "None"
#~ msgstr "Yok"

#~ msgid "Taxonomy Term"
#~ msgstr "Taksonomi terimi"

#~ msgid "remove {layout}?"
#~ msgstr "{layout} kaldırılsın mı?"

#~ msgid "This field requires at least {min} {identifier}"
#~ msgstr "Bu alan için en az gereken {min} {identifier}"

#~ msgid "Maximum {label} limit reached ({max} {identifier})"
#~ msgstr "En yüksek {label} sınırına ulaşıldı ({max} {identifier})"

#~ msgid "Getting Started"
#~ msgstr "Başlarken"

#~ msgid "Field Types"
#~ msgstr "Alan Tipleri"

#~ msgid "Functions"
#~ msgstr "Fonksiyonlar"

#~ msgid "Actions"
#~ msgstr "Eylemler"

#~ msgid "Tutorials"
#~ msgstr "Örnekler"

#~ msgid "Error"
#~ msgstr "Hata"

#, fuzzy
#~| msgid "This field requires at least {min} {identifier}"
#~ msgid "1 field requires attention."
#~ msgid_plural "%d fields require attention."
#~ msgstr[0] "Bu alan gerektirir, en azından {min} {identifier}"
#~ msgstr[1] "Bu alan gerektirir, en azından {min} {identifier}"

#~ msgid "Disabled"
#~ msgstr "Etkisiz"

#~ msgid "See what's new in"
#~ msgstr "Neler yeni gözat"

#~ msgid "version"
#~ msgstr "versiyon"

#~ msgid "'How to' guides"
#~ msgstr "Nasıl Yapılır"

#~ msgid "Created by"
#~ msgstr "Oluşturan"

#~ msgid "<b>Success</b>. Import tool added %s field groups: %s"
#~ msgstr "<b>Başarılı</b>. İçe aktarma aracı %s alan gruplarını aktardı: %s"

#~ msgid ""
#~ "<b>Warning</b>. Import tool detected %s field groups already exist and "
#~ "have been ignored: %s"
#~ msgstr ""
#~ "<b>Uyarı </b>. İçe aktarma aracı zaten var olan %s alan gruplarını tespit "
#~ "etti. Bu kayıtlar gözardı edildi: %s"

#~ msgid "Upgrade"
#~ msgstr "Yükselt"

#~ msgid "Drag and drop to reorder"
#~ msgstr "Yeniden sıralama için sürükle ve bırak"

#~ msgid "See what's new"
#~ msgstr "Neler yeni görün"

#~ msgid "Show a different month"
#~ msgstr "Başka bir ay göster"

#~ msgid "Return format"
#~ msgstr "Dönüş formatı"

#~ msgid "uploaded to this post"
#~ msgstr "Bu yazıya yükledi"

#~ msgid "File Size"
#~ msgstr "Dosya Boyutu"

#~ msgid "No File selected"
#~ msgstr "Dosya seçilmedi"

#~ msgid ""
#~ "Please note that all text will first be passed through the wp function "
#~ msgstr "Tüm metin ilk wp fonksiyonu sayesinde geçilecek unutmayın"

#~ msgid "Warning"
#~ msgstr "Uyarı"

#~ msgid "eg. Show extra content"
#~ msgstr "örn. Ekstra içerik göster"

#~ msgid "<b>Connection Error</b>. Sorry, please try again"
#~ msgstr "<b> Bağlantı Hatası </ b>. Üzgünüm, lütfen tekrar deneyin"

#~ msgid "Save Options"
#~ msgstr "Ayarları Kaydet"

#~ msgid "License"
#~ msgstr "Lisans"

#~ msgid ""
#~ "To unlock updates, please enter your license key below. If you don't have "
#~ "a licence key, please see"
#~ msgstr ""
#~ "Güncelleştirmeleri kilidini açmak için, aşağıdaki lisans anahtarını "
#~ "girin. Eğer bir lisans anahtarı yoksa, lütfen"

#~ msgid "details & pricing"
#~ msgstr "detaylar & fiyatlandırma"

#~ msgid "Hide / Show All"
#~ msgstr "Gizle / Hepsini Göster"

#~ msgid "Show Field Keys"
#~ msgstr "Alan Anahtarlarını Göster"

#~ msgid "Pending Review"
#~ msgstr "İnceleme Bekliyor"

#~ msgid "Draft"
#~ msgstr "Taslak"

#~ msgid "Private"
#~ msgstr "Gizli"

#~ msgid "Revision"
#~ msgstr "Revizyon"

#~ msgid "Trash"
#~ msgstr "Çöp"

#, fuzzy
#~ msgid "Field groups are created in order from lowest to highest"
#~ msgstr "Alan grupları oluşturulma sırası <br/> sırayla alttan yukarı"

#~ msgid "ACF PRO Required"
#~ msgstr "ACF PRO Gerekli"

#~ msgid ""
#~ "We have detected an issue which requires your attention: This website "
#~ "makes use of premium add-ons (%s) which are no longer compatible with ACF."
#~ msgstr ""
#~ "Biz dikkat gerektiren bir sorunu tespit ettik: Bu ​​web sitesi artık ACF "
#~ "ile uyumlu olan eklentileriyle (%s) kullanımını kolaylaştırır."

#~ msgid ""
#~ "Don't panic, you can simply roll back the plugin and continue using ACF "
#~ "as you know it!"
#~ msgstr ""
#~ "Panik yapmayın, sadece eklenti geri almak ve bunu bildiğiniz gibi ACF "
#~ "kullanmaya devam edebilirsiniz!"

#~ msgid "Roll back to ACF v%s"
#~ msgstr "ACF v %s ye geri al"

#~ msgid "Learn why ACF PRO is required for my site"
#~ msgstr "ACF PRO Sitem için neden gereklidir öğrenin"

#~ msgid "Update Database"
#~ msgstr "Veritabanını Güncelle"

#~ msgid "Data Upgrade"
#~ msgstr "Veri Yükseltme"

#~ msgid "Data upgraded successfully."
#~ msgstr "Veri başarıyla yükseltildi."

#~ msgid "Data is at the latest version."
#~ msgstr "Verinin en son sürümü."

#~ msgid "1 required field below is empty"
#~ msgid_plural "%s required fields below are empty"
#~ msgstr[0] "%s Gerekli alan boş"

#~ msgid "Load & Save Terms to Post"
#~ msgstr "Yazı Yükleme ve Kaydet Şartları"

#~ msgid ""
#~ "Load value based on the post's terms and update the post's terms on save"
#~ msgstr ""
#~ "Yükleme değeri yazılar için terimlere dayalı ve kaydetme üzerindeki "
#~ "yazılar için şartlarını güncelleyecek"

#, fuzzy
#~ msgid "image"
#~ msgstr "Resim"

#, fuzzy
#~ msgid "expand_details"
#~ msgstr "Ayrıntıları Genişlet"

#, fuzzy
#~ msgid "collapse_details"
#~ msgstr "Detayları Daralt"

#, fuzzy
#~ msgid "relationship"
#~ msgstr "İlişkili"

#, fuzzy
#~ msgid "title_is_required"
#~ msgstr "Alan grubu için başlık gerekli"

#, fuzzy
#~ msgid "move_field"
#~ msgstr "Alanı Taşı"

#, fuzzy
#~ msgid "flexible_content"
#~ msgstr "Esnek İçerik"

#, fuzzy
#~ msgid "gallery"
#~ msgstr "Galeri"

#, fuzzy
#~ msgid "repeater"
#~ msgstr "Tekrarlayıcı"

#, fuzzy
#~ msgid "Controls how HTML tags are rendered"
#~ msgstr "Yeni satırlar nasıl oluşturulacağını denetler"

#~ msgid "Custom field updated."
#~ msgstr "Özel alan güncellendi."

#~ msgid "Custom field deleted."
#~ msgstr "Özel alan silindi."

#~ msgid "Field group duplicated! Edit the new \"%s\" field group."
#~ msgstr "Alan grup çoğaltıldı! Yeni  \"%s \" alan grubu düzenleyin."

#~ msgid "Import/Export"
#~ msgstr "İçe/Dışa Aktar"

#~ msgid "Column Width"
#~ msgstr "Sütun Genişliği"

#~ msgid "Attachment Details"
#~ msgstr "Ek Detayları"
